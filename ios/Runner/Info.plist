<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Quick Tatkal V2</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>quick_tatkal_v2</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb2022198807948455</string>
			</array>
		</dict>
	</array>
	<key>FacebookAppID</key>
	<string>2022198807948455</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Quick Tatkal</string>
	<!-- App Transport Security Settings -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<!-- Required Permissions -->
	<key>NSCameraUsageDescription</key>
	<string>This app needs access to camera to scan QR codes and take photos</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs access to photo library to select images</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs location access to provide location-based services</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app may need microphone access for certain features</string>
	<key>NSContactsUsageDescription</key>
	<string>This app needs access to contacts to share tickets and offers</string>
	<key>NSCalendarsUsageDescription</key>
	<string>This app may need access to calendar to save travel dates</string>
	<key>NSRemindersUsageDescription</key>
	<string>This app may need access to reminders for travel notifications</string>
	<!-- Background modes for notifications -->
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>background-fetch</string>
	</array>
</dict>
</plist>
