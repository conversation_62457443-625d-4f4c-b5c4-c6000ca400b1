name: quick_tatkal_v2
description: "Quick Tatkal"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  get: ^4.6.6
  dio: ^5.4.3+1   # latest stable as of mid-2025
  flutter_inappwebview: ^6.0.0
  url_launcher: ^6.2.5    # For fallback external browser
  mixpanel_flutter: ^2.1.1
  firebase_core: ^3.0.0
  cloud_firestore: ^5.0.0
  firebase_auth: ^5.0.0
  firebase_dynamic_links: ^6.0.0
  flutter_facebook_auth: ^7.1.2
  firebase_analytics: ^11.5.1
  country_code_picker: ^3.0.0
  flutter_html: ^3.0.0-beta.2
  fluttertoast: ^8.2.4
  google_fonts: ^6.1.0
  google_mobile_ads: ^6.0.0
  facebook_app_events: ^0.20.1
  razorpay_flutter: ^1.3.5
  shared_preferences: ^2.2.2
  sqflite: ^2.3.2
  path_provider: ^2.1.2
  path: ^1.9.0
  http: ^1.2.1
  intl: 0.20.2
  connectivity_plus: ^6.0.3
  google_mlkit_text_recognition: ^0.13.0
  # Remove ftpconnect dependency due to intl version incompatibility with Flutter SDK
  #   ftpconnect: ^2.0.1
  flutter_local_notifications: ^19.3.0
  pin_code_fields: ^8.0.0
  device_info_plus: ^11.5.0
  package_info_plus: ^8.3.0
  firebase_messaging: ^15.2.9
  in_app_purchase: ^3.0.7
  firebase_storage: ^12.4.9
  in_app_purchase_android: ^0.4.0+2
  permission_handler: ^12.0.1
  archive: ^4.0.7
  encrypt: ^5.0.1
  crypto: ^3.0.3
  google_sign_in: ^7.1.1
  image: ^4.1.3
  webview_flutter: ^4.2.2
  pinput: ^3.0.1
  flutter_rating_bar: ^4.0.1
  timezone: 0.10.1
  share_plus: ^10.1.2
  shimmer: ^3.0.0
  workmanager: ^0.9.0+2
  video_player: ^2.8.6
  back_button_interceptor: ^7.0.0


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:


  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/text_logo_white.png
    - assets/images/social.png
    - assets/images/trusted.png
    - assets/images/insta1.png
    - assets/images/fb.png
    - assets/images/youtube.png
    - assets/images/sign_up_train.jpg
    - assets/images/google_signin.png
    - assets/images/tatkal_logo_rounded.png
    - assets/images/free_tickets_creative.png
    - assets/images/gold_offer.png
    - assets/stations.txt
    - assets/trains.txt
    - assets/raw/captcha_web.mp4
    - assets/raw/captcha_rc.mp4

    # If you intend to use all db files in db/, you could do just:
    # - assets/db/
    # Add missing background image if required:


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
