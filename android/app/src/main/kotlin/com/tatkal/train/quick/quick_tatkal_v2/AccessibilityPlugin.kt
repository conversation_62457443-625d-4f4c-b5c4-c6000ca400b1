package com.tatkal.train.ticket

import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.util.Log
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.util.HashMap

class AccessibilityPlugin : FlutterPlugin, MethodCallHandler {

    internal lateinit var methodChannel: MethodChannel
    internal lateinit var eventChannel: EventChannel
    internal lateinit var context: Context

    companion object {
        internal const val METHOD_CHANNEL = "accessibility_service"
        internal const val EVENT_CHANNEL = "accessibility_events"
        internal const val TAG = "AccessibilityPlugin"
    }

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        context = flutterPluginBinding.applicationContext

        methodChannel = MethodChannel(flutterPluginBinding.binaryMessenger, METHOD_CHANNEL)
        methodChannel.setMethodCallHandler(this)

        eventChannel = EventChannel(flutterPluginBinding.binaryMessenger, EVENT_CHANNEL)

        // Connect channels to accessibility service if it's running
        MyAccessibilityService.accessibilityService?.let { service ->
            service.setMethodChannel(methodChannel)
            service.setEventChannel(eventChannel)
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        methodChannel.setMethodCallHandler(null)
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "isAccessibilityServiceEnabled" -> {
                val isEnabled = isAccessibilityServiceEnabled(context)
                result.success(isEnabled)
            }

            "openAccessibilitySettings" -> {
                openAccessibilitySettings(context)
                result.success(null)
            }

            "startService" -> {
                try {
                    val formData = call.arguments as? Map<String, Any> ?: emptyMap()
                    startAccessibilityService(context, formData)
                    result.success(null)
                } catch (e: Exception) {
                    result.error("START_SERVICE_ERROR", e.message, null)
                }
            }

            "stopService" -> {
                try {
                    stopAccessibilityService(context)
                    result.success(null)
                } catch (e: Exception) {
                    result.error("STOP_SERVICE_ERROR", e.message, null)
                }
            }

            "getServiceStatus" -> {
                try {
                    val status = MyAccessibilityService.accessibilityService?.let { service ->
                        HashMap<String, Any>().apply {
                            put("isRunning", true)
                            put("currentScreen", service.currentScreen)
                            put("status", service.availability)
                            put("formName", service.formName ?: "")
                            put("journeyDate", service.journeyDate)
                            put("ticketBookFlag", service.ticketBookFlag)
                        }
                    } ?: HashMap<String, Any>().apply {
                        put("isRunning", false)
                    }

                    result.success(status)
                } catch (e: Exception) {
                    result.error("GET_STATUS_ERROR", e.message, null)
                }
            }

            else -> {
                // Try to delegate to accessibility service
                MyAccessibilityService.accessibilityService?.let { service ->
                    val serviceResult = service.handleMethodCall(call.method, call.arguments)
                    result.success(serviceResult)
                } ?: result.notImplemented()
            }
        }
    }

    internal fun isAccessibilityServiceEnabled(context: Context): Boolean {
        return try {
            val service = "${context.packageName}/${MyAccessibilityService::class.java.name}"
            val enabledServices = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )

            !enabledServices.isNullOrEmpty() && enabledServices.contains(service)
        } catch (e: Exception) {
            Log.e(TAG, "Error checking accessibility service status", e)
            false
        }
    }

    internal fun openAccessibilitySettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening accessibility settings", e)
        }
    }

    internal fun startAccessibilityService(context: Context, formData: Map<String, Any>) {
        try {
            // Store form data in SharedPreferences for the service to access
            val sharedPrefs = context.getSharedPreferences("RC", Context.MODE_PRIVATE)
            val editor = sharedPrefs.edit()

            formData.forEach { (key, value) ->
                when (value) {
                    is String -> editor.putString(key, value)
                    is Int -> editor.putInt(key, value)
                    is Boolean -> editor.putBoolean(key, value)
                    is Float -> editor.putFloat(key, value)
                    is Long -> editor.putLong(key, value)
                }
            }
            editor.apply()

            // If service is already running, update it with new data
            MyAccessibilityService.accessibilityService?.let { service ->
                service.formName = formData["formName"] as? String
                Log.d(TAG, "Updated running accessibility service with new form data")
            }

            // Start the target app if needed
            val packageName = formData["targetPackage"] as? String ?: "cris.org.in.prs.ima"
            val launchIntent = context.packageManager.getLaunchIntentForPackage(packageName)
            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(launchIntent)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error starting accessibility service", e)
            throw e
        }
    }

    internal fun stopAccessibilityService(context: Context) {
        try {
            MyAccessibilityService.accessibilityService?.let { service ->
                service.resetVariables()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping accessibility service", e)
            throw e
        }
    }
}