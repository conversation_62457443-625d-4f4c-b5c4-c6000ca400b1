package com.tatkal.train.ticket

import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodChannel

/**
 * Simple holder to share Flutter channels between the plugin and the service.
 * Ensures the accessibility service can attach to channels if it starts after the engine.
 */
object AccessibilityChannelHolder {
    @JvmStatic
    var methodChannel: MethodChannel? = null

    @JvmStatic
    var eventChannel: EventChannel? = null
}
