package com.tatkal.train.ticket

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.annotation.SuppressLint
import android.app.ActivityManager
import android.app.AlertDialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.ComponentName
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.content.SharedPreferences
import android.content.res.AssetFileDescriptor
import android.content.res.Resources
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Rect
import android.media.AudioManager
import android.media.Image
import android.media.MediaPlayer
import android.os.AsyncTask
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.IBinder
import androidx.annotation.NonNull
import androidx.annotation.RequiresApi
import android.preference.PreferenceManager
import android.util.Log
import android.util.SparseArray
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.view.accessibility.AccessibilityWindowInfo
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import com.google.android.gms.tasks.OnFailureListener
import com.google.android.gms.tasks.OnSuccessListener
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.Text
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.TextRecognizer
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.nio.ByteBuffer
import java.nio.charset.Charset
import java.text.SimpleDateFormat
import java.util.ArrayList
import java.util.Arrays
import java.util.Calendar
import java.util.HashMap
import java.util.List
import java.util.Locale
import java.util.Map
import java.util.Timer
import java.util.TimerTask
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel

data class Passenger(
    var name: String = "",
    var age: Int = 0,
    var gender: String = "",
    var berthPreference: String = "",
    var foodChoice: String = "",
    var nationality: String = "IN"
)

data class Child(
    var name: String = "",
    var age: Int = 0,
    var gender: String = ""
)

@RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
class MyAccessibilityService : AccessibilityService() {

    companion object {
        const val TICKET_INFO = "TICKET_INFO"
        var accessibilityService: MyAccessibilityService? = null
        var timeDifference = 0
        var paymentFailed = 0
        var windowWidth = 0
        var windowHeight = 0
        var captchaSolving = 0
        var retryCaptchaManual = false
        var canProceedForCapture = true
        private const val upiPin = "1234"
        private const val THIRD_PARTY_APP_PACKAGE = "cris.org.in.prs.ima"
    }

    // Flutter communication channels
    private var methodChannel: MethodChannel? = null
    private var eventChannel: EventChannel? = null
    private var eventSink: EventChannel.EventSink? = null

    // Service variables - keeping same names as Java implementation
    var mBound = true
    var journeyDateOneTimeFlag = false
    var dateDialogOpened = false
    var dateSelected = false
    var concessionAlertDismissed = false
    var sameMonth = false
    var paymentModeSelected = false
    var paymentEntitySelected = false
    var proceedToPaymentClicked = false
    var childAgeSelectionFlag = 0
    var optBerthSelectionFlag = 0
    var foodSelectionFlag = 0
    var monthDifference = 99
    var currentPassengerIndex = 0
    var currentChildIndex = 0

    private var loginClicked = false
    private var pinEntered = false
    private var quotaDropDownClicked = false
    private var searchTrainClicked = false

    private var stationSelectionFlag = 0
    private var isFillingPassDetails = false

    private var berthPrefSelectionFlag = 0
    private var concessionSelectionFlag = 0
    private var concessionSelected = false
    private var travelClassSelected = false
    private var reviewCaptchaFocused = false
    private var isFillingChildDetails = false

    var dateGridIndex = arrayOf(
        "January", "February", "March", "April", "May", "June", "July",
        "August", "September", "October", "November", "December"
    )

    var journeyDate = ""
    var quotaMap = HashMap<String, String>()

    var walletIds = arrayOf(
        "click_ewallet", "click_mobikwik", "click_paytm_wallet",
        "click_ola_money", "click_jio_money", "click_airtel"
    )

    var otherPgIds = arrayOf(
        "click_ipay", "click_paytm", "click_mobimpp", "click_payu_mpp",
        "click_razor_pay", "click_phone_pe", "click_icici_mpp",
        "click_hdfc_mpp", "click_airpay"
    )

    var bhimId = "click_paytm_upi"

    private var FROM_STN_ID = "ll_from_station_layout"
    private var TO_STN_ID = "ll_to_station_layout"

    var formName: String? = null

    private var rcPin: String? = null
    private var fromStation: String? = null
    private var toStation: String? = null
    private var quota: String? = null
    private var fareLimit = 0
    private var trainNo: String? = null
    private var travelClass: String? = null
    private var boardingStation: String? = null
    private var passenger: Array<Passenger>? = null
    private var child: Array<Child>? = null
    private var mobileNo: String? = null
    private var addrLine1: String? = null
    private var addrLine2: String? = null
    private var addrLine3: String? = null
    private var addrPin: String? = null
    private var addrCity: String? = null
    private var addrPo: String? = null
    private var insurance = 0
    private var autoUpgrade = false
    private var onlyConfirm = false
    private var miscDataFilled = false
    private var paymentType = 0
    private var bookingOption = 0
    private var coachPreferred = false
    private var coachId = ""
    private var pCount = 0
    private var cCount = 0

    private var travelClassMap = HashMap<String, String>()

    private var paymentMode: String? = null
    private var paymentEntity: String? = null

    private var movedToPayment = false

    private var timeStart: Long = 0
    var ticketBookFlag = false
    private var dvSelectionFlag = 0
    private var ticketSavedToServer = false
    private var planMyJourneyClicked = false

    private var captchaResId = ""
    private var submitResId = ""

    private var autofillCaptcha = false
    private var passengerDetailsProceedClicked = false
    private var addressEntered = false
    private var cityClicked = false
    private var postOfficeFlag = 0

    private var login1Clicked = false
    private var login2Clicked = false

    private var journeyDateClicked = false

    var timer: Timer? = null
    var timerTask: TimerTask? = null
    val handler = Handler()
    var clickOption = 0

    var recaptchaTimer: Timer? = null
    var recaptchaTimerTask: TimerTask? = null

    var passTimer: Timer? = null
    var passTimerTask: TimerTask? = null
    var passNextBtn: AccessibilityNodeInfo? = null
    var passWait = false

    var otpTimer: Timer? = null
    var otpTimerTask: TimerTask? = null
    var otpEditText: AccessibilityNodeInfo? = null
    var otpSubmitBtn: AccessibilityNodeInfo? = null
    var otpWait = 0
    var otpTimerCount = 0

    var tatkalWait = false
    var tatkalTimerStarted = false
    var tatkalLongWait = 0
    var trainIndex = 0
    var classIndex = 0
    var trainDateViewId: AccessibilityNodeInfo? = null
    var proceedTatkalViewId: AccessibilityNodeInfo? = null
    var closeClassViewId: AccessibilityNodeInfo? = null
    var othrDtViewId: AccessibilityNodeInfo? = null
    var classRefreshViewId: AccessibilityNodeInfo? = null
    var availability = ""

    var currentScreen = 0
    var flexibleClicked = false
    var screenTaskComplete = BooleanArray(15) { false }
    var traversalString = ""

    private var vpa: String? = null
    private var autoOpenUpi = false
    private var automateUpi = false
    private var walletNo: String? = null
    private var paytmWalletStage = 0
    private var mobikwikWalletStage = 0
    private var irctcWalletStage = 0
    private var otherPaymentStage = 0
    private var upiButtonClicked = false
    private var regPhone: String? = null

    private var paytmUpiClicked = false
    private var paytmVpaFilled = false
    private var paytmVpaVerified = false
    private var upiSubmitButton: AccessibilityNodeInfo? = null

    private var irctcSeconds = -1

    private var boardingStnFlag = 0
    private var tatkalTime = 0
    private var refreshRequired = false

    private var continueButtonClicked = false
    private var reCaptchaShowing = false

    private var makePaymentBtn: AccessibilityNodeInfo? = null
    private var normalReviewCaptcha = 0

    private var passengerPageLogoutWait = false
    private var passengerWaitFlag = false

    private var iAgreeClicked = false
    private var reviewCaptchaClicked = false

    // GPay variables
    private var gPayButtonSkipped = false
    private var selectAutoBank = false
    private var gPayButtonsClicked = 0

    // PhonePe variables
    private var phonepePayClicked = false
    private var phonepeProceedToPayClicked = false
    private var phonepeDefaultBankClicked = false

    // Paytm variables
    private var paytmPayClicked = false
    private var paytmDefaultBankClicked = false

    private var upiPinSubmitted = false

    private var invalidCaptchaFlag = 0
    private var loginCaptchaRetries = 0
    private var bookingCaptchaRetries = 0

    private var captchaTryDone = false

    var loginCaptchaCount = 1
    var bookingCaptchaCount = 1

    var lastCaptchaBmp: Bitmap? = null

    var firstLoginCaptcha = false
    var firstBookingCaptcha = false

    private var previousTraversalString = "NA"
    private var sameScreenCount = 0

    private var rcServiceEventTracked = false

    var waitForPTResponse = 0
    private var paymentAutofill = false

    override fun onServiceConnected() {
        super.onServiceConnected()

        accessibilityService = this

        val info = AccessibilityServiceInfo()
        info.eventTypes =
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED or AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED
        info.packageNames = arrayOf(
            "com.tatkal.train.ticket",
            THIRD_PARTY_APP_PACKAGE,
            "com.phonepe.app",
            "com.google.android.apps.nbu.paisa.user",
            "net.one97.paytm",
            "in.org.npci.upiapp"
        )
        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
        info.notificationTimeout = 100

        this.serviceInfo = info

        initializeVariables()

        Log.d("AccessibilityService", "Service connected successfully")

        // Notify Flutter that service is connected
        sendEventToFlutter(
            HashMap<String, Any>().apply {
                put("type", "service_connected")
                put("status", "connected")
            }
        )
    }


    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    override fun onAccessibilityEvent(event: AccessibilityEvent) {
        try {
            if (captchaSolving == 1 || captchaSolving == 2) {
                return
            }

            if (rootInActiveWindow != null) {
                autofill(event.source, rootInActiveWindow, this)
            }
        } catch (e: Exception) {
            Log.e("AccessibilityService", "Error in onAccessibilityEvent", e)
        }
    }

    override fun onInterrupt() {
        Log.d("AccessibilityService", "Service interrupted")
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    fun autofill(
        sourceNode: AccessibilityNodeInfo?,
        source: AccessibilityNodeInfo?,
        context: Context
    ) {
        if (waitForPTResponse == 1 || ticketBookFlag) {
            return
        }

        try {
            source?.let {
                detectAndHandleScreens(it)
            }
        } catch (e: Exception) {
            Log.e("AccessibilityService", "Error in autofill", e)
        }
    }

    private fun detectAndHandleScreens(source: AccessibilityNodeInfo) {
        initVariables()

        val loginTextViewId = findNodeInWindows("tv_action_right1")
        val irctcPinViewId = findNodeInWindows("et_valid_pin")
        val planMyJourneyViewId = findNodeInWindows("my_journey_ll")

        when {
            loginTextViewId?.isNotEmpty() == true && !screenTaskComplete[0] -> {
                homeScreen(source)
            }

            irctcPinViewId?.isNotEmpty() == true -> {
                loginScreen(source)
            }

            planMyJourneyViewId?.isNotEmpty() == true -> {
                planMyJourneyScreen(source)
            }
        }
    }

    private fun homeScreen(source: AccessibilityNodeInfo) {
        val loginTextViewId = findNodeInWindows("tv_action_right1")

        if (loginTextViewId?.isNotEmpty() == true && !loginClicked) {
            currentScreen = 1
            resetVariables()

            loginClicked = true
            loginTextViewId[0].performAction(AccessibilityNodeInfo.ACTION_CLICK)
            screenTaskComplete[0] = true

            sendStatusToFlutter("Login clicked")
        }
    }

    private fun loginScreen(source: AccessibilityNodeInfo) {
        val irctcPinViewId = findNodeInWindows("et_valid_pin")

        if (irctcPinViewId?.isNotEmpty() == true && !pinEntered) {
            currentScreen = 2
            pinEntered = true

            val bundle = Bundle()
            bundle.putCharSequence(
                AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE,
                rcPin
            )

            sendStatusToFlutter("PIN entered")
        }
    }

    private fun planMyJourneyScreen(source: AccessibilityNodeInfo) {
        val planMyJourneyViewId = findNodeInWindows("my_journey_ll")

        if (planMyJourneyViewId?.isNotEmpty() == true) {
            currentScreen = 3

            planMyJourneyClicked = true
            planMyJourneyViewId[0].performAction(AccessibilityNodeInfo.ACTION_CLICK)
            screenTaskComplete[2] = true

            sendStatusToFlutter("Filling journey details")
        }
    }

    @SuppressLint("Range")
    fun initVariables() {
        if (formName == null) {
            initializeVariables()

            val sp = applicationContext.getSharedPreferences("RC", Context.MODE_PRIVATE)
            formName = sp.getString("FORM_NAME", "")
        }
    }

    private fun initializeVariables() {
        travelClassMap["SL"] = "Sleeper"
        travelClassMap["3A"] = "AC 3 Tier"
        travelClassMap["2A"] = "AC 2 Tier"
        travelClassMap["1A"] = "AC First Class"
        travelClassMap["EC"] = "Exec. Chair Car"
        travelClassMap["FC"] = "First Class"
        travelClassMap["3E"] = "AC 3 Economy"
        travelClassMap["2S"] = "Second Sitting"
        travelClassMap["CC"] = "AC Chair car"
        travelClassMap["EA"] = "Anubhuti Class"
        travelClassMap["VS"] = "Vistadome Non AC"
        travelClassMap["VC"] = "Vistadome Chair car"
        travelClassMap["EV"] = "Vistadome AC"

        quotaMap["GN"] = "tv_general"
        quotaMap["LD"] = "tv_ladies"
        quotaMap["TQ"] = "tv_tatkal"
        quotaMap["SS"] = "tv_senior_citizen"
        quotaMap["PT"] = "tv_premium_tatkal"
        quotaMap["HP"] = "tv_ph_handicap"
        quotaMap["DP"] = "tv_general"
    }

    fun resetVariables() {
        pinEntered = false
        planMyJourneyClicked = false
        quotaDropDownClicked = false
        dateDialogOpened = false
        dateSelected = false
        sameMonth = false
        journeyDateOneTimeFlag = false
        isFillingPassDetails = false
        isFillingChildDetails = false
        concessionAlertDismissed = false
        concessionSelected = false
        proceedToPaymentClicked = false
        travelClassSelected = false
        reviewCaptchaFocused = false
        paymentModeSelected = false
        paymentEntitySelected = false
        ticketSavedToServer = false
        ticketBookFlag = false
        passengerDetailsProceedClicked = false
        addressEntered = false
        cityClicked = false
        login1Clicked = false
        login2Clicked = false

        dvSelectionFlag = 0
        currentChildIndex = 0
        stationSelectionFlag = 0
        monthDifference = 99
        currentPassengerIndex = 0
        berthPrefSelectionFlag = 0
        concessionSelectionFlag = 0
        optBerthSelectionFlag = 0
        foodSelectionFlag = 0
        childAgeSelectionFlag = 0
        timeStart = 0
        postOfficeFlag = 0
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    private fun findNodeInWindows(nodeResId: String): java.util.List<AccessibilityNodeInfo>? {
        for (window in windows) {
            val node = window.root
            if (node != null) {
                val nodes =
                    node.findAccessibilityNodeInfosByViewId("$THIRD_PARTY_APP_PACKAGE:id/$nodeResId")
                if (nodes.isNotEmpty()) {
                    return nodes as java.util.List<AccessibilityNodeInfo>
                }
            }
        }
        return null
    }

    // Flutter communication methods
    fun setMethodChannel(channel: MethodChannel) {
        methodChannel = channel
    }

    fun setEventChannel(channel: EventChannel) {
        eventChannel = channel
        eventChannel?.setStreamHandler(object : EventChannel.StreamHandler {
            override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                eventSink = events
            }

            override fun onCancel(arguments: Any?) {
                eventSink = null
            }
        })
    }

    private fun sendEventToFlutter(event: HashMap<String, Any>) {
        handler.post {
            eventSink?.success(event)
        }
    }

    private fun sendStatusToFlutter(status: String) {
        sendEventToFlutter(
            HashMap<String, Any>().apply {
                put("type", "status_event")
                put("status", status)
            }
        )
    }

    fun handleMethodCall(method: String, arguments: Any?): Any? {
        return when (method) {
            "isAccessibilityServiceEnabled" -> true
            "getServiceStatus" -> HashMap<String, Any>().apply {
                put("isRunning", true)
                put("currentScreen", currentScreen)
                put("status", availability)
            }

            else -> null
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        accessibilityService = null

        timer?.cancel()
        recaptchaTimer?.cancel()
        passTimer?.cancel()
        otpTimer?.cancel()

        sendEventToFlutter(
            HashMap<String, Any>().apply {
                put("type", "service_disconnected")
                put("status", "disconnected")
            }
        )
    }
}