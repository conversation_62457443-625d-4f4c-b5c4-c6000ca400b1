class LoginHandler {
  String manipulate(List<int> list, int r, int factor) {
    int m = r - factor;
    int s = r * r;
    int v = s * s + r + 2;
    String vc = String.fromCharCode(v);

    List<int> values = [
      s * 7 + 2,
      (s + 1) * 11,
      (s + 1) * 10,
      s * 13 - 3,
      s * 12 + 3,
      s * 12 - 3,
      s * 11 + 1,
      s * 5 + 2,
      s * 11 - 2
    ];
    String comp = "";

    for (int i = 0; i < 15; i++) {
      String c;
      if (i < 9) {
        c = String.fromCharCode(values[i]);
      } else {
        c = String.fromCharCode(values[(i + 1) % 9]);
      }
      comp += c;
    }

    String str = "";
    for (int i in list) {
      str += String.fromCharCode(i % 128);
    }
    int l = str.length ~/ 2;

    String t;
    if (factor == -1) {
      t = str.substring(l) + str.substring(0, l).toUpperCase().trim() + comp + vc;
    } else {
      t = str.substring(l) + str.substring(0, l).toUpperCase().trim() + comp +
          m.toString();
    }
    return t;
  }
}
