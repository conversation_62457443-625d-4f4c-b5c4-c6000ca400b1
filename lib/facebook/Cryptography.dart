import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import "package:encrypt/encrypt.dart" as encrypt;
import 'package:crypto/crypto.dart';
import 'package:path_provider/path_provider.dart';

class Cryptography {
  static final String tid =
      '97C146ED1CF9000F007C117D'; // replace with the real TID

  // Generate a 16-byte AES key from password (using SHA-256, truncated)
  static Uint8List generateKey(String password) {
    var hash = sha256.convert(utf8.encode(password)).bytes;
    return Uint8List.fromList(hash.sublist(0, 16));
  }

  static encrypt.IV generateIV() {
    return encrypt.IV.fromUtf8(tid.substring(0, 16));
  }

  static Uint8List encodeFile(Uint8List key, Uint8List fileData) {
    final iv = generateIV();
    final encrypter = encrypt.Encrypter(
      encrypt.AES(encrypt.Key(key), mode: encrypt.AESMode.gcm),
    );
    final encrypted = encrypter.encryptBytes(fileData, iv: iv);
    return encrypted.bytes;
  }

  static Uint8List decodeFile(Uint8List key, Uint8List fileData) {
    final iv = generateIV();
    final encrypter = encrypt.Encrypter(
      encrypt.AES(encrypt.Key(key), mode: encrypt.AESMode.gcm),
    );
    final encrypted = encrypt.Encrypted(fileData);
    try {
      return Uint8List.fromList(encrypter.decryptBytes(encrypted, iv: iv));
    } catch (_) {
      return Uint8List(0);
    }
  }

  static Future<void> saveFileExternal(
    Uint8List fileData,
    String fileName,
    Uint8List yourKey,
  ) async {
    try {
      final dir = await getExternalStorageDirectory();
      final file = File('${dir!.path}/$fileName');
      final fileBytes = encodeFile(yourKey, fileData);
      await file.writeAsBytes(fileBytes);
    } catch (e) {
      print('Crypto: $e');
    }
  }

  static Future<Uint8List?> getFileDataExternal(
    String fileName,
    Uint8List yourKey,
  ) async {
    try {
      final dir = await getExternalStorageDirectory();
      final file = File('${dir!.path}/$fileName');
      if (!await file.exists()) return null;
      final encryptedData = await file.readAsBytes();
      return decodeFile(yourKey, encryptedData);
    } catch (e) {
      print('Crypto: $e');
      return null;
    }
  }

  // Example: Internal save/load for use in app's private directory
  static Future<void> saveFile(
    Uint8List fileData,
    String fileName,
    String password,
  ) async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final file = File('${dir.path}/$fileName');
      final yourKey = generateKey(password);
      final fileBytes = encodeFile(yourKey, fileData);
      await file.writeAsBytes(fileBytes);
    } catch (e) {
      print('Crypto: $e');
    }
  }

  static Future<Uint8List?> getFileData(
    String fileName,
    String password,
  ) async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final file = File('${dir.path}/$fileName');
      if (!await file.exists()) return null;
      final fileBytes = await file.readAsBytes();
      final yourKey = generateKey(password);
      return decodeFile(yourKey, fileBytes);
    } catch (e) {
      print('Crypto: $e');
      return null;
    }
  }
}
