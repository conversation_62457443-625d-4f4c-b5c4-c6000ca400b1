import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../core/Consts.dart';
import '../server/ServerTask.dart';
import 'Cryptography.dart'; // Update this path if necessary

class APIClient {
  Future<String?> x(int r, String id, int factor, int m) async {
    int s = r * 2;
    List<int>? fBts = await Cryptography.getFileDataExternal(
      Consts.C3,
      Cryptography.generateKey(id),
    );

    if (fBts == null) return null;

    String xStr = utf8.decode(fBts);
    if (xStr.isEmpty) return null;

    List<int> numbers = [];

    for (int i = 0; i < (s + 2); i++) {
      List<int> b = utf8.encode(xStr);
      int l = b.length;
      numbers.add(Consts.PERMISSIONS[i] + b[i % l]);
    }

    String retVal = LoginHandler().manipulate(numbers, r, factor);

    if (m != 0) {
      int ql = retVal.length;
      String path = (await getExternalStorageDirectory())?.path ?? '/sdcard';
      String folderName = retVal.substring(ql - 16, ql - 9);
      Directory('$path/$folderName').createSync(recursive: true);

      String nm =
          retVal.substring(ql - 16, ql - 8) + retVal.substring(ql - 5, ql - 3);
      File f = File('$path/$nm');

      if (!f.existsSync()) {
        try {
          File source = File('$path/${Consts.C3}');
          if (await source.exists()) {
            await f.writeAsBytes(await source.readAsBytes());
          }
        } catch (e) {
          debugPrint("APIClient: ${e.toString()}");
        }
      }
    }

    return retVal;
  }
}
