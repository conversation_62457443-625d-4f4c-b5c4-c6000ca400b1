import 'dart:typed_data';
import "package:image/image.dart" as img;
import 'package:flutter/material.dart';

Future<void> cropAndShow(BuildContext context, Uint8List originalBytes) async {
  // Set these to your desired crop rect
  int captchaX = 50, captchaY = 50, captchaW = 100, captchaH = 60;

  final original = img.decodeImage(originalBytes);
  if (original == null) return;

  final crop = img.copyCrop(
    original,
    x: captchaX,
    y: captchaY,
    width: captchaW,
    height: captchaH,
  );
  final croppedBytes = Uint8List.fromList(img.encodePng(crop));

  showDialog(
    context: context,
    builder: (_) => AlertDialog(content: Image.memory(croppedBytes)),
  );
}

class ImageTransmogrifier {
  static int x = 0;
  static int y = 0;
  static int mWidth = 0;
  static int mHeight = 0;

  static img.Image? captchaCrop;

  // Loads an image from bytes, crops and stores result
  static Future<Uint8List?> cropCaptcha({
    required Uint8List imageBytes, // PNG/JPG
    required int captchaX,
    required int captchaY,
    required int captchaWidth,
    required int captchaHeight,
  }) async {
    final original = img.decodeImage(imageBytes);
    if (original == null) return null;

    final crop = img.copyCrop(
      original,
      x: captchaX,
      y: captchaY,
      width: captchaWidth,
      height: captchaHeight,
    );
    captchaCrop = crop;
    return Uint8List.fromList(img.encodePng(crop)); // or encodeJpg(crop)
  }

  Future<void> cropAndShow(
    BuildContext context,
    Uint8List originalBytes,
  ) async {
    // Set these to your desired crop rect
    int captchaX = 50, captchaY = 50, captchaW = 100, captchaH = 60;

    final original = img.decodeImage(originalBytes);
    if (original == null) return;

    final crop = img.copyCrop(
      original,
      x: captchaX,
      y: captchaY,
      width: captchaW,
      height: captchaH,
    );
    final croppedBytes = Uint8List.fromList(img.encodePng(crop));

    showDialog(
      context: context,
      builder: (_) => AlertDialog(content: Image.memory(croppedBytes)),
    );
  }
}
