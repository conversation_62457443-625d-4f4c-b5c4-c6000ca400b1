import 'dart:io' show Platform;
import 'package:flutter/services.dart';

/// Android integration helpers to mirror Java expectations.
///
/// - Writes named SharedPreferences (LOGIN_SUCCESS.FLAG, RC.FORM_NAME)
/// - Writes default Preference (OPTION)
/// - Launches native MainActivity with FORM_NAME and LANG extras
class AndroidIntegrationBridge {
  static const MethodChannel _prefsChannel =
  MethodChannel('com.tatkal.train.quick/prefs');
  static const MethodChannel _navChannel =
  MethodChannel('com.tatkal.train.quick/nav');

  static Future<void> setNamedInt(String fileName,
      String key,
      int value,) async {
    if (!Platform.isAndroid) return;
    try {
      await _prefsChannel.invokeMethod('setNamedInt', {
        'file': fileName,
        'key': key,
        'value': value,
      });
    } catch (_) {}
  }

  static Future<void> setNamedString(String fileName,
      String key,
      String value,) async {
    if (!Platform.isAndroid) return;
    try {
      await _prefsChannel.invokeMethod('setNamedString', {
        'file': fileName,
        'key': key,
        'value': value,
      });
    } catch (_) {}
  }

  static Future<void> setDefaultInt(String key, int value) async {
    if (!Platform.isAndroid) return;
    try {
      await _prefsChannel.invokeMethod('setDefaultInt', {
        'key': key,
        'value': value,
      });
    } catch (_) {}
  }

  static Future<void> launchAndroidMainActivity({
    required String formName,
    required String language, // 'ENG' or 'HIN'
  }) async {
    if (!Platform.isAndroid) return;
    try {
      await _navChannel.invokeMethod('launchMainActivity', {
        'FORM_NAME': formName,
        'LANG': language,
      });
    } catch (_) {}
  }

  /// Convenience: perform all steps Java expects, then launch MainActivity.
  ///
  /// - FORM_NAME persisted in named prefs (RC)
  /// - FLAG=1 in LOGIN_SUCCESS
  /// - OPTION in default prefs (0 EN, 1 HI)
  /// - then start MainActivity with FORM_NAME and LANG extras
  static Future<void> continueAutomationFromFlutter({
    required String formName,
    required bool isEnglish,
  }) async {
    if (!Platform.isAndroid) return;
    final String lang = isEnglish ? 'ENG' : 'HIN';
    final int option = isEnglish ? 0 : 1;

    await setNamedInt('LOGIN_SUCCESS', 'FLAG', 1);
    await setNamedString('RC', 'FORM_NAME', formName);
    await setDefaultInt('OPTION', option);

    await launchAndroidMainActivity(formName: formName, language: lang);
  }
}
