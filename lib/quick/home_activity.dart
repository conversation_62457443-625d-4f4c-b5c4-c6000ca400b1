import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomeActivity extends StatefulWidget {
  const HomeActivity({Key? key}) : super(key: key);

  @override
  _HomeActivityState createState() => _HomeActivityState();
}

class _HomeActivityState extends State<HomeActivity>
    with SingleTickerProviderStateMixin {
  late List<ListView?> mRecyclerView;
  late RelativeLayoutState relativeLayout;
  String? mQuota;
  int limit = 6;
  int passengerCount = 1;
  int childCount = 0;

  int passengerCountDb = 0;
  int childCountDb = 0;

  late Text paymentMethod;
  late TextEditingController username;
  late TextEditingController password;
  late TextEditingController fromStn;
  late TextEditingController toStn;
  late TextEditingController journeyDate;
  late List<String> trainValues;
  late List<String> quota;
  late List<String> travelClass;
  late TextEditingController train;
  late TextEditingController boardingStn;

  static String? formName;
  String? fNameOld;
  bool find = false;
  bool creditCardSet = false;

  bool passwordsVisible = false;
  late ScrollController scrollController;
  late TabController tabController;

  bool isFormLocked = false;

  late List<TextEditingController?> pName;
  late List<TextEditingController?> pAge;
  late List<TextEditingController?> pDob;
  late List<TextEditingController?> cName;
  late List<TextEditingController?> cardNoValue;
  late List<TextEditingController?> cardValidity;
  late List<TextEditingController?> idNo;
  late TextEditingController coachId;
  late TextEditingController mobileNo;
  late TextEditingController cardNo1;
  late TextEditingController cardNo2;
  late TextEditingController cardNo3;
  late TextEditingController cardNo4;
  late TextEditingController cardHolder;
  late TextEditingController pin;
  late TextEditingController cvv;
  late TextEditingController wUsername;
  late TextEditingController wPassword;
  late TextEditingController txnPassword;
  late TextEditingController nbUsername;
  late TextEditingController nbPassword;
  late TextEditingController icMobile;
  late TextEditingController icEmail;
  late TextEditingController icAddress1;
  late TextEditingController icAddress2;
  late TextEditingController icCity;
  late TextEditingController icState;
  late TextEditingController icZip;
  late TextEditingController icBank;
  late TextEditingController gstIN;
  late TextEditingController gstName;
  late TextEditingController gstFlat;
  late TextEditingController gstStreet;
  late TextEditingController gstPin;
  late TextEditingController gstArea;
  late TextEditingController gstCity;
  late TextEditingController vpa;
  late TextEditingController podMobile;
  late TextEditingController datePickerView;
  late TextEditingController aeMobile;
  late TextEditingController aeEmail;
  late List<TextEditingController> emptyElements;

  @override
  void initState() {
    super.initState();
    mRecyclerView = List.filled(14, null);
    scrollController = ScrollController();
    tabController = TabController(length: 1, vsync: this);
    username = TextEditingController();
    password = TextEditingController();
    fromStn = TextEditingController();
    toStn = TextEditingController();
    journeyDate = TextEditingController();
    train = TextEditingController();
    boardingStn = TextEditingController();
    pName = List.filled(14, null);
    pAge = List.filled(14, null);
    pDob = List.filled(14, null);
    cName = List.filled(14, null);
    cardNoValue = List.filled(14, null);
    cardValidity = List.filled(14, null);
    idNo = List.filled(14, null);
    coachId = TextEditingController();
    mobileNo = TextEditingController();
    cardNo1 = TextEditingController();
    cardNo2 = TextEditingController();
    cardNo3 = TextEditingController();
    cardNo4 = TextEditingController();
    cardHolder = TextEditingController();
    pin = TextEditingController();
    cvv = TextEditingController();
    wUsername = TextEditingController();
    wPassword = TextEditingController();
    txnPassword = TextEditingController();
    nbUsername = TextEditingController();
    nbPassword = TextEditingController();
    icMobile = TextEditingController();
    icEmail = TextEditingController();
    icAddress1 = TextEditingController();
    icAddress2 = TextEditingController();
    icCity = TextEditingController();
    icState = TextEditingController();
    icZip = TextEditingController();
    icBank = TextEditingController();
    gstIN = TextEditingController();
    gstName = TextEditingController();
    gstFlat = TextEditingController();
    gstStreet = TextEditingController();
    gstPin = TextEditingController();
    gstArea = TextEditingController();
    gstCity = TextEditingController();
    vpa = TextEditingController();
    podMobile = TextEditingController();
    datePickerView = TextEditingController();
    aeMobile = TextEditingController();
    aeEmail = TextEditingController();
    emptyElements = List.filled(14, TextEditingController());

    trainValues = ['Rajdhani', 'Shatabdi', 'Duronto', 'Mail'];
    quota = ['General', 'Ladies', 'Tatkal', 'Premium'];
    travelClass = [
      'Sleeper',
      'AC 3 Tier',
      'AC 2 Tier',
      'AC First',
      'Second Sitting',
    ];
  }

  static int getSelection(String value, Map<String, dynamic> map) {
    int count = 0;
    for (var key in map.keys) {
      count++;
      if (value == key.toString()) {
        return count;
      }
    }
    return 0;
  }

  static int getSpinnerIndex(String value, List<String> spinnerItems) {
    try {
      for (int i = 0; i < spinnerItems.length; i++) {
        if (value == spinnerItems[i]) {
          return i;
        }
      }
    } catch (e) {
      return 0;
    }
    return 0;
  }

  static int getRadioButtonId(String value, Map<String, dynamic> map) {
    for (var o in map.keys) {
      String opt = map[o].toString();
      if (value != null && value == opt) {
        return int.parse(o);
      }
    }
    return int.parse(map.keys.first.toString());
  }

  static String convertDateFormat(String dateString) {
    try {
      final parts = dateString.split('-');
      if (parts.length != 3) return '';
      String day = parts[0];
      String month = parts[1];
      String year = parts[2];

      return '$year-$month-$day';
    } catch (e) {
      return '';
    }
  }

  static Future<String> getTicketsFromLocal(context) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final tickets = prefs.getString('TICKETS') ?? '-1';
      final userType = prefs.getString('USER_TYPE') ?? 'FREE_USER';
      String expiry = prefs.getString('EXPIRY') ?? 'NA';
      if (expiry == '') expiry = 'NA';
      return '$tickets|$userType|$expiry';
    } catch (e) {
      return '-1|FREE_USER|NA';
    }
  }

  @override
  void dispose() {
    for (final controller in [
      ...pName,
      ...pAge,
      ...pDob,
      ...cName,
      ...cardNoValue,
      ...cardValidity,
      ...idNo,
      coachId,
      mobileNo,
      cardNo1,
      cardNo2,
      cardNo3,
      cardNo4,
      cardHolder,
      pin,
      cvv,
      wUsername,
      wPassword,
      txnPassword,
      nbUsername,
      nbPassword,
      icMobile,
      icEmail,
      icAddress1,
      icAddress2,
      icCity,
      icState,
      icZip,
      icBank,
      gstIN,
      gstName,
      gstFlat,
      gstStreet,
      gstPin,
      gstArea,
      gstCity,
      vpa,
      podMobile,
      datePickerView,
      aeMobile,
      aeEmail,
      ...emptyElements,
    ]) {
      controller?.dispose();
    }
    scrollController.dispose();
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('IRCTC Form : ${formName ?? ''}'),
        bottom: TabBar(
          controller: tabController,
          indicatorColor: const Color(0xFFFF3377),
          labelColor: const Color(0xFFFFFF00),
          unselectedLabelColor: Colors.white,
          tabs: const [Tab(text: 'Tab 1')],
        ),
        backgroundColor: Theme.of(context).primaryColor,
        actions: [
          IconButton(
            icon: Icon(
              passwordsVisible ? Icons.visibility_off : Icons.visibility,
            ),
            tooltip: passwordsVisible ? 'Hide Passwords' : 'Show Passwords',
            onPressed: () {
              setState(() {
                passwordsVisible = !passwordsVisible;
              });
            },
          ),

          IconButton(
            icon: Icon(isFormLocked ? Icons.lock_open : Icons.lock),
            tooltip: isFormLocked ? 'Unlock Form' : 'Lock Form',
            onPressed: () {
              _showLockDialog(context);
            },
          ),

          IconButton(
            icon: const Icon(Icons.save),
            tooltip: 'Save',
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Save pressed (not implemented)')),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        controller: scrollController,
        child: Container(
          color: Theme.of(context).primaryColor,
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(labelText: 'Train'),
                        value: trainValues.isNotEmpty ? trainValues[0] : null,
                        items: trainValues
                            .map(
                              (v) => DropdownMenuItem(value: v, child: Text(v)),
                            )
                            .toList(),
                        onChanged: (val) {},
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(labelText: 'Quota'),
                        value: quota.isNotEmpty ? quota[0] : null,
                        items: quota
                            .map(
                              (v) => DropdownMenuItem(value: v, child: Text(v)),
                            )
                            .toList(),
                        onChanged: (val) {},
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(labelText: 'Travel Class'),
                        value: travelClass.isNotEmpty ? travelClass[0] : null,
                        items: travelClass
                            .map(
                              (v) => DropdownMenuItem(value: v, child: Text(v)),
                            )
                            .toList(),
                        onChanged: (val) {},
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Row(
                        children: [
                          Checkbox(value: true, onChanged: (v) {}),
                          const Text('Security Checkbox'),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Row(
                        children: [
                          Radio<int>(
                            value: 1,
                            groupValue: 1,
                            onChanged: (v) {},
                          ),
                          const Text('Option 1'),
                          Radio<int>(
                            value: 2,
                            groupValue: 1,
                            onChanged: (v) {},
                          ),
                          const Text('Option 2'),
                        ],
                      ),
                    ),

                    for (int i = 0; i < mRecyclerView.length; i++)
                      _buildRecyclerView(context, i),
                    const SizedBox(height: 50),
                  ],
                ),
              ),
              Positioned(
                left: 10,
                bottom: 10,
                child: Row(
                  children: [
                    const Icon(Icons.info, size: 30),
                    const SizedBox(width: 8),
                    const Text(
                      'Caps lock information',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecyclerView(BuildContext context, int index) {
    return SizedBox(
      height: 150,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: 4,
        itemBuilder: (context, idx) {
          return ListTile(title: Text('Item $idx in Recycler $index'));
        },
      ),
    );
  }

  static Future<void> update(
    int val, {
    required BuildContext context,
    required bool isGoldUser,
    required int ticketsLeft,
    required bool ticketBooked,
    required String userType,
  }) async {
    if (isGoldUser == 2) {}

    int tickets = ticketsLeft;
    if (ticketBooked) {
      if ((userType == 'FREE_USER' || userType == 'COMP_USER') && tickets > 0) {
        tickets--;
      }
    }
  }

  static const String _storageKey = 'FORM_LOCK_PASSWORD';

  Future<void> _showLockDialog(BuildContext context) async {
    final prefs = await SharedPreferences.getInstance();
    final savedPassword = prefs.getString(_storageKey);
    TextEditingController inputController = TextEditingController();
    bool isSet = savedPassword == null;
    bool isUnlock = !isSet;
    String error = '';
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (ctx) {
        return StatefulBuilder(
          builder: (ctx, setState) => AlertDialog(
            title: Text(isSet ? 'Set Password' : 'Unlock Form'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isSet
                      ? 'Set a password for this form:'
                      : 'Enter your form password:',
                ),
                TextField(
                  controller: inputController,
                  obscureText: true,
                  decoration: InputDecoration(
                    hintText: 'Password',
                    errorText: error.isNotEmpty ? error : null,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(ctx).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  if (isSet) {
                    if (inputController.text.isEmpty) {
                      setState(() => error = 'Password cannot be empty');
                      return;
                    }
                    await prefs.setString(_storageKey, inputController.text);
                    Navigator.of(ctx).pop();
                    _showSnackBar('Password set. Form is now locked.');
                    setState(() {
                      isFormLocked = true;
                    });
                  } else {
                    if (inputController.text == savedPassword) {
                      Navigator.of(ctx).pop();
                      _showSnackBar('Unlocked. You can now edit the form.');
                      setState(() {
                        isFormLocked = false;
                        prefs.remove(_storageKey);
                      });
                    } else {
                      setState(() => error = 'Invalid password');
                    }
                  }
                },
                child: Text(isSet ? 'Set Password' : 'Unlock'),
              ),
            ],
          ),
        );
      },
    );
    inputController.dispose();
  }

  void _showSnackBar(String msg) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(msg)));
  }
}

class RelativeLayoutState {}
