import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../core/Consts.dart';
import '../screens/splash_screen.dart';
import '../database/LoginDB.dart';
import '../server/FirestoreFunctions.dart';
import 'package:shared_preferences/shared_preferences.dart';
// AUTHER : LOKESH
// DATE: 06-07-25
// GIT : telestic1

class PhoneFragment {
  bool numberSubmitted = false;
  String phoneNumber = "";
  int code = 0;
  dynamic mixpanel;
  final BuildContext context;
  static _PhoneFragmentDialogState? _currentDialogState;

  PhoneFragment(this.context);

  void enableOTP(bool response) {
    _currentDialogState?._handleEnableOTP(response);
  }

  void postUpdatePhone(String response) {
    _currentDialogState?._handlePostUpdatePhone(response);
  }

  void show() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return _PhoneFragmentDialog(this);
      },
    );
  }

  /// Call this from ProfileScreen to show phone update dialog and await for the result
  static Future<String?> showOnProfile(BuildContext context) async {
    final PhoneFragment frag = PhoneFragment(context);
    final newNumber = await showDialog<String>(
      context: context,
      barrierDismissible: true,
      builder: (dialogContext) => _PhoneFragmentDialog(frag),
    );
    return newNumber;
  }
}

class _PhoneFragmentDialog extends StatefulWidget {
  final PhoneFragment phoneFragment;

  const _PhoneFragmentDialog(this.phoneFragment);

  @override
  _PhoneFragmentDialogState createState() => _PhoneFragmentDialogState();
}

class _PhoneFragmentDialogState extends State<_PhoneFragmentDialog> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();
  final String _selectedCountryCode = "+91";
  bool _isLoading = false;
  String _infoText = "OTP will be sent to your mobile number";
  Color _infoColor = const Color(0xFFAAAAAA);
  bool _isVerifyEnabled = false;
  bool _showOTP = false;

  @override
  void initState() {
    super.initState();
    _phoneController.addListener(_onPhoneChanged);
    PhoneFragment._currentDialogState = this;
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _otpController.dispose();
    PhoneFragment._currentDialogState = null;
    super.dispose();
  }

  void _onPhoneChanged() {
    setState(() {
      _isVerifyEnabled = _phoneController.text.length > 0;
    });
  }

  Future<void> _onVerifyPressed() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      setState(() {
        _infoText = "Please check your network connection";
        _infoColor = const Color(0xFFFF0000);
      });
      return;
    }

    final fullPhoneNumber = _selectedCountryCode + _phoneController.text;

    if (fullPhoneNumber == SplashScreenState.MOBILE_NO) {
      setState(() {
        _infoText = "Mobile number is same";
        _infoColor = const Color(0xFF00FF00);
      });
      return;
    }

    if (!widget.phoneFragment.numberSubmitted) {
      if (SplashScreenState.MOBILE_NO == widget.phoneFragment.phoneNumber) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text("Mobile number same")));
        return;
      }

      setState(() {
        _isLoading = true;
        _isVerifyEnabled = false;
      });

      widget.phoneFragment.code = Random().nextInt(8999) + 1000;
      widget.phoneFragment.phoneNumber = fullPhoneNumber;

      final firestoreFunctions = FirestoreFunctions.withPhoneFragment(
        context,
        widget.phoneFragment,
      );
      firestoreFunctions.verifyMobileUpdate(
        widget.phoneFragment.code,
        widget.phoneFragment.phoneNumber,
      );
    } else {
      final otpEntered = _otpController.text;
      if (otpEntered.isEmpty) {
        setState(() {
          _infoText = "Please enter OTP";
          _infoColor = const Color(0xFFFF0000);
        });
      } else if (otpEntered != widget.phoneFragment.code.toString()) {
        setState(() {
          _infoText = "Incorrect OTP";
          _infoColor = const Color(0xFFFF0000);
        });
      } else {
        // Check if user is properly logged in before updating mobile
        debugPrint(
          "🔍 Checking login state: tid=${SplashScreenState.tid}, loginMethod=${Consts.loginMethod}",
        );

        if ((SplashScreenState.tid == "0" || SplashScreenState.tid.isEmpty) &&
            (Consts.loginMethod == "LATER" || Consts.loginMethod == null)) {
          debugPrint(" User not properly logged in - showing error");
          setState(() {
            _infoText =
                "Please sign in first to update your mobile number. Close this dialog and sign in through Facebook, Google, or Mobile to update your number.";
            _infoColor = const Color(0xFFFF0000);
          });
          return;
        }

        debugPrint(" User is logged in - proceeding with mobile update");
        setState(() {
          _infoText = "Updating phone number...";
          _infoColor = const Color(0xFF00FF00);
          _isLoading = true;
          _isVerifyEnabled = false;
        });

        final firestoreFunctions = FirestoreFunctions.withPhoneFragment(
          context,
          widget.phoneFragment,
        );

        final tidToUse =
            (SplashScreenState.tid == "0" || SplashScreenState.tid.isEmpty)
            ? "1"
            : SplashScreenState.tid;

        debugPrint(" Calling updateMobile with tid: $tidToUse");
        firestoreFunctions.updateMobile(
          tidToUse,
          widget.phoneFragment.phoneNumber,
        );
      }
    }
  }

  void _handleEnableOTP(bool response) {
    setState(() {
      _isLoading = false;
      _isVerifyEnabled = true;

      if (response) {
        widget.phoneFragment.numberSubmitted = true;
        _infoText =
            "Please enter OTP sent to $_selectedCountryCode ${_phoneController.text}";
        _infoColor = const Color(0xFFAAAAAA);
        _showOTP = true;
      } else {
        _infoText = "Error sending OTP. Please try again";
        _infoColor = const Color(0xFFFF0000);
      }
    });
  }

  void _handlePostUpdatePhone(String response) async {
    setState(() {
      _isLoading = false;
      _isVerifyEnabled = true;
    });

    if (response == "DUPLICATE_MOBILE") {
      setState(() {
        _infoText =
            "Mobile number already registered. Please sign in using mobile number to access your account";
        _infoColor = const Color(0xFFFF0000);
      });
    } else if (response == "SUCCESS") {
      SplashScreenState.MOBILE_NO = widget.phoneFragment.phoneNumber;
      await _updateLocalDatabase();
      await _updateSharedPreferences();
      if (mounted) {
        Navigator.of(context).pop(widget.phoneFragment.phoneNumber);
      }
    } else if (response == "INVALID_TID") {
      setState(() {
        _infoText = "Please sign in first to update your mobile number";
        _infoColor = const Color(0xFFFF0000);
      });
    } else if (response == "ERROR") {
      setState(() {
        _infoText = "Error updating phone number. Please try again";
        _infoColor = const Color(0xFFFF0000);
      });
    }
  }

  Future<void> _updateLocalDatabase() async {
    try {
      await LoginDB.deleteAll();
      await LoginDB.closeDB();

      final db = await LoginDB.database;
      await db.insert('MOBILE', {'MOBILE_NO': SplashScreenState.MOBILE_NO});
    } catch (e) {
      debugPrint("Error updating local database: $e");
    }
  }

  Future<void> _updateSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('MOBILE_NO', SplashScreenState.MOBILE_NO);
    } catch (e) {
      debugPrint("Error updating SharedPreferences: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.0)),
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(20.0),
          border: Border.all(width: 2, color: Colors.transparent),
        ),
        child: Container(
          margin: const EdgeInsets.all(2.0),
          decoration: BoxDecoration(
            color: const Color(0xFF424242),
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(top: 10, bottom: 10),
                child: const Text(
                  "Mobile verification",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(left: 20, right: 20, top: 10),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  borderRadius: BorderRadius.circular(20.0),
                ),
                child: Container(
                  margin: const EdgeInsets.all(2.0),
                  decoration: BoxDecoration(
                    color: const Color(0xFF424242),
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: Row(
                    children: [
                      Container(
                        margin: const EdgeInsets.only(
                          left: 10,
                          top: 5,
                          bottom: 5,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              _selectedCountryCode,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(width: 4),
                            const Icon(
                              Icons.arrow_drop_down,
                              color: Color(0xFF8E24AA),
                              size: 32,
                            ),
                          ],
                        ),
                      ),

                      Expanded(
                        child: TextField(
                          controller: _phoneController,
                          keyboardType: TextInputType.number,
                          maxLength: 10,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontFamily: 'sans-serif',
                          ),
                          decoration: const InputDecoration(
                            hintText: "Mobile No",
                            hintStyle: TextStyle(color: Color(0xFFBBBBBB)),
                            border: InputBorder.none,
                            counterText: "",
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 10,
                              vertical: 15,
                            ),
                          ),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              Container(
                margin: const EdgeInsets.only(left: 20, right: 20, top: 5),
                child: Text(
                  _infoText,
                  textAlign: TextAlign.center,
                  style: TextStyle(color: _infoColor, fontSize: 14),
                ),
              ),

              if (_isLoading)
                Container(
                  margin: const EdgeInsets.only(top: 10),
                  child: const SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 3,
                    ),
                  ),
                ),

              if (_showOTP)
                Container(
                  margin: const EdgeInsets.only(left: 30, right: 30, top: 20),
                  child: TextField(
                    controller: _otpController,
                    keyboardType: TextInputType.number,
                    maxLength: 4,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      letterSpacing: 8,
                    ),
                    decoration: const InputDecoration(
                      hintText: "Enter OTP",
                      hintStyle: TextStyle(color: Color(0xFFEEEEEE)),
                      border: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.white),
                      ),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.white),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.white, width: 2),
                      ),
                      contentPadding: EdgeInsets.only(left: 10),
                      counterText: "",
                    ),
                  ),
                ),

              Container(
                margin: const EdgeInsets.all(20),
                child: SizedBox(
                  width: 200,
                  height: 40,
                  child: ElevatedButton(
                    onPressed: _isVerifyEnabled ? _onVerifyPressed : null,
                    style:
                        ElevatedButton.styleFrom(
                          backgroundColor: _isVerifyEnabled
                              ? null
                              : Colors.grey[600],
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20.0),
                          ),
                          elevation: 0,
                        ).copyWith(
                          backgroundColor: _isVerifyEnabled
                              ? MaterialStateProperty.all(Colors.transparent)
                              : MaterialStateProperty.all(Colors.grey[600]),
                        ),
                    child: _isVerifyEnabled
                        ? Container(
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                              borderRadius: BorderRadius.circular(20.0),
                            ),
                            child: Container(
                              margin: const EdgeInsets.all(2.0),
                              decoration: BoxDecoration(
                                color: const Color(0xFF424242),
                                borderRadius: BorderRadius.circular(20.0),
                              ),
                              alignment: Alignment.center,
                              child: const Text(
                                "Verify",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ),
                          )
                        : const Text(
                            "Verify",
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 18,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
