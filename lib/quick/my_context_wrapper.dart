import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MyContextWrapper {
  static const List<String> supportedLanguages = ["en", "hi", "mr", "bn", "gu"];
  static const List<String> languageNames = [
    "English",
    "हिन्दी",
    "मराठी",
    "বাংলা",
    "ગુજરાતી",
  ];

  static Future<Locale> wrapWithLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final selectedOption = prefs.getInt("OPTION") ?? 0;

    if (selectedOption >= 0 && selectedOption < supportedLanguages.length) {
      final languageCode = supportedLanguages[selectedOption];
      return _getLocaleFromLanguageCode(languageCode);
    }

    return const Locale('en');
  }

  static Future<String> getCurrentLanguageName() async {
    final prefs = await SharedPreferences.getInstance();
    final selectedOption = prefs.getInt("OPTION") ?? 0;

    if (selectedOption >= 0 && selectedOption < languageNames.length) {
      return languageNames[selectedOption];
    }

    return "English";
  }

  static Future<String> getCurrentLanguageCode() async {
    final prefs = await SharedPreferences.getInstance();
    final selectedOption = prefs.getInt("OPTION") ?? 0;

    if (selectedOption >= 0 && selectedOption < supportedLanguages.length) {
      return supportedLanguages[selectedOption];
    }

    return "en";
  }

  static Locale _getLocaleFromLanguageCode(String languageCode) {
    switch (languageCode) {
      case "hi":
        return const Locale('hi', 'IN');
      case "mr":
        return const Locale('mr', 'IN');
      case "bn":
        return const Locale('bn', 'BD');
      case "gu":
        return const Locale('gu', 'IN');
      case "en":
      default:
        return const Locale('en', 'US');
    }
  }

  static List<Locale> getSupportedLocales() {
    return supportedLanguages
        .map((code) => _getLocaleFromLanguageCode(code))
        .toList();
  }

  static bool isRTL(String languageCode) {
    return false;
  }

  static TextDirection getTextDirection(String languageCode) {
    return isRTL(languageCode) ? TextDirection.rtl : TextDirection.ltr;
  }

  static Future<void> saveLanguageOption(int option) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt("OPTION", option);
  }

  static Future<void> resetToDefault() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt("OPTION", 0);
  }
}
