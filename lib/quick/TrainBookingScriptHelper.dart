class TrainBookingScriptHelper {
  static String getInitializeTimerTaskScript(int index, int index2) {
    return """javascript:function guruHoJaShuru() {
      var blocks = document.getElementsByTagName('app-train-avl-enq');\n
      var tt = setInterval(function() {
        if(blocks[$index].getElementsByTagName('td')[0].className == 'link' && blocks[$index].getElementsByTagName('td')[1].getElementsByTagName('strong')[1].innerHTML.indexOf('#') == -1) {
          setTimeout(function() {
            blocks[$index].getElementsByTagName('td')[1].getElementsByTagName('div')[0].click();
            Step.printAvl(blocks[$index].getElementsByTagName('td')[1].getElementsByTagName('strong')[1].innerHTML);
            blocks[$index].getElementsByClassName('btnDefault train_Search')[0].click();\n
          }, 500);
          setTimeout(function() {
            document.getElementsByClassName('ui-dialog-footer')[0].getElementsByTagName('button')[0].click();
          }, 502);
          setTimeout(function() {
            try {
              document.getElementsByClassName('ui-dialog-footer')[0].getElementsByTagName('button')[0].click();
            } catch {}}
          , 1000);
          Step.stopTask();
          clearInterval(tt);
        } else if(document.getElementById('preloaderP') == null) {
          setTimeout(function() {
            blocks[$index].getElementsByClassName('ui-menuitem-link ui-corner-all ng-star-inserted')[$index2].getElementsByTagName('div')[0].click();
          }, 300);
        }
      }, 1000);
    }
    guruHoJaShuru()""";
  }

  static String getInitializePymtWaitTimerTaskScript(String paymentChoice, String bankChoice, Map<String, String> paymentChoiceMap) {
    final String paymentMethodLabel = paymentChoiceMap[paymentChoice] ?? "";
    final String bankLabel = bankChoice.replaceAll("&", "&amp;");

    return """javascript:function guruHoJaShuru() {
      var elems = document.getElementById('pay-type').getElementsByTagName('div');\n
      for(i=0;i<elems.length;i++) {\n
          if(elems[i].innerText.indexOf('$paymentMethodLabel') >= 0) {\n
              elems[i].click();\n
              break;\n
          }\n
      }\n
      var blocks = document.getElementsByClassName('border-all no-pad');\n
      for(i=0; i<blocks.length;i++) {\n
          if(blocks[i].getBoundingClientRect().top != 0) {\n
              if(blocks[i].getElementsByTagName('span')[0].innerHTML.toUpperCase().indexOf('$bankLabel'.toUpperCase()) != -1) {\n
                  blocks[i].click();\n
      setTimeout(function() {
                  Step.continueClick();
                }, 600);\n
                  break;\n
              }\n
          }\n
      }\n
    }
    guruHoJaShuru()""";
  }

  static String getStartWaitTimerScript() {
    return """javascript:function aish() {document.getElementsByClassName('train_Search')[0].click();}aish()""";
  }
}
