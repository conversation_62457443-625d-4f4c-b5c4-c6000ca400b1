import 'dart:io';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';

/**
*AUTHOR : LOKESH
* DATE : 17-07-25.
*/
class CustomExceptionHandler {
  dynamic defaultUEH;
  BuildContext? activity;
  String? localPath;

  /*
   * if any of the parameters is null, the respective functionality
   * will not be used
   */
  CustomExceptionHandler(this.localPath, this.activity) {
    this.defaultUEH = null;
    developer.log("CEH Constructor", name: "CEH");
  }

  void uncaughtException(dynamic t, dynamic e) {
    String stacktrace = e.toString();
    if (e.stackTrace != null) {
      stacktrace += '\n' + e.stackTrace.toString();
    }

    String filename = "error_log.txt";

    if (localPath != null) {
      writeToFile(stacktrace, filename, localPath!);
    }

    if (activity != null) {
      Navigator.of(activity!).pushReplacement(
        MaterialPageRoute(
          builder: (context) =>
              TabActivity2(formNameNew: "", formNameOld: "Cliff"),
        ),
      );
    }

    if (defaultUEH != null) {}
  }

  static void writeToFile(
    String stacktrace,
    String filename,
    String localPath,
  ) {
    try {
      Directory myDir = Directory(localPath);
      myDir.createSync(recursive: true);

      File myFile = File('$localPath/$filename');
      myFile.createSync();

      myFile.writeAsStringSync(stacktrace);
    } catch (e) {
      developer.log(e.toString(), name: "CustomExceptionHandler");
    }
  }
}

class TabActivity2 extends StatelessWidget {
  final String formNameNew;
  final String formNameOld;

  const TabActivity2({
    Key? key,
    required this.formNameNew,
    required this.formNameOld,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Tab Activity 2')),
      body: Center(
        child: Text('Form Name New: $formNameNew\nForm Name Old: $formNameOld'),
      ),
    );
  }
}
