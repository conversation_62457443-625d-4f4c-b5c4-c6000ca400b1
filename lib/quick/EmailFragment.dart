import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/Consts.dart';
import '../core/AppConstants.dart';
import '../screens/splash_screen.dart';
import '../database/LoginDB.dart';
import '../server/FirestoreFunctions.dart';

// AUTHER : LOKESH
// DATE: 06-07-25
// GIT : telestic1

class EmailFragment {
  bool emailSubmitted = false;
  String emailAddress = "";
  int code = 0;
  final BuildContext context;
  final VoidCallback? onEmailUpdated;
  static _EmailFragmentDialogState? _currentDialogState;

  EmailFragment(this.context, {this.onEmailUpdated});

  void enableOTP(bool response) {
    _currentDialogState?._handleEnableOTP(response);
  }

  void postUpdateEmail(String response) {
    _currentDialogState?._handlePostUpdateEmail(response);
  }

  void show() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return _EmailFragmentDialog(this);
      },
    );
  }

  Future<void> updateEmail() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('email', emailAddress);
    AppConstants.email = emailAddress;
    SplashScreenState.EMAIL = emailAddress;
    SplashScreenState.PRIMARY_EMAIL = emailAddress;
  }

  Future<void> updateEmailEverywhere() async {
    try {
      SplashScreenState.EMAIL = emailAddress;
      SplashScreenState.PRIMARY_EMAIL = emailAddress;

      AppConstants.email = emailAddress;
      AppConstants.primaryEmail = emailAddress;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('email', emailAddress);
      await prefs.setString('primary_email', emailAddress);
      await prefs.setString('EMAIL', emailAddress);
      await prefs.setString('PRIMARY_EMAIL', emailAddress);

      await AppConstants.saveUserData();

      if (Consts.loginMethod == "EMAIL") {
        await _updateLocalDatabase();
      }

      debugPrint("Email updated everywhere: $emailAddress");
    } catch (e) {
      debugPrint("Error updating email everywhere: $e");
    }
  }

  Future<void> _updateLocalDatabase() async {
    try {
      await LoginDB.deleteAll();
      await LoginDB.closeDB();
    } catch (e) {
      debugPrint("Error updating local database: $e");
    }
  }
}

class _EmailFragmentDialog extends StatefulWidget {
  final EmailFragment emailFragment;

  const _EmailFragmentDialog(this.emailFragment);

  @override
  _EmailFragmentDialogState createState() => _EmailFragmentDialogState();
}

class _EmailFragmentDialogState extends State<_EmailFragmentDialog> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();
  bool _isLoading = false;
  String _infoText = "OTP will be sent to your email address";
  Color _infoColor = const Color(0xFFAAAAAA);
  bool _isVerifyEnabled = false;
  bool _showOTP = false;

  @override
  void initState() {
    super.initState();
    EmailFragment._currentDialogState = this;
    _emailController.addListener(_onEmailChanged);

    _validateAndFixTID();
  }

  void _validateAndFixTID() {
    String currentTID = SplashScreenState.tid;
    debugPrint("Current TID: $currentTID");
    debugPrint("TID Type: ${currentTID.runtimeType}");

    if (!_isNumericTID(currentTID)) {
      debugPrint("Non-numeric TID detected, converting...");
      String numericTID = _convertToNumericTID(currentTID);
      SplashScreenState.tid = numericTID;
      debugPrint("Converted TID: $numericTID");
    }
  }

  bool _isNumericTID(String tid) {
    return int.tryParse(tid) != null;
  }

  String _convertToNumericTID(String alphanumericTID) {
    int hashCode = alphanumericTID.hashCode.abs();

    String numericTID = hashCode.toString();
    if (numericTID.length > 10) {
      numericTID = numericTID.substring(0, 10);
    }

    return numericTID;
  }

  @override
  void dispose() {
    EmailFragment._currentDialogState = null;
    _emailController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  void _onEmailChanged() {
    setState(() {
      _isVerifyEnabled =
          _emailController.text.isNotEmpty &&
          _isValidEmail(_emailController.text);
    });
  }

  bool _isValidEmail(String email) {
    return RegExp(
      r'^[a-zA-z0-9+_.-]+@[a-zA-z0-9-]+\.[a-zA-z0-9-.]+[a-zA-z]+$',
    ).hasMatch(email);
  }

  Future<void> _onVerifyPressed() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      setState(() {
        _infoText = "Please check your network connection";
        _infoColor = const Color(0xFFFF0000);
      });
      return;
    }

    final email = _emailController.text.trim();

    if (email == SplashScreenState.EMAIL) {
      setState(() {
        _infoText = "Email address is same";
        _infoColor = const Color(0xFF00FF00);
      });
      return;
    }

    if (!widget.emailFragment.emailSubmitted) {
      if (SplashScreenState.EMAIL == widget.emailFragment.emailAddress) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text("Email address same")));
        return;
      }

      setState(() {
        _isLoading = true;
        _isVerifyEnabled = false;
      });

      widget.emailFragment.code = Random().nextInt(899999) + 100000;
      widget.emailFragment.emailAddress = email;

      final firestoreFunctions = FirestoreFunctions.withEmailFragment(
        context,
        widget.emailFragment,
      );
      firestoreFunctions.verifyEmailUpdate(
        widget.emailFragment.code,
        widget.emailFragment.emailAddress,
      );
    } else {
      final otpEntered = _otpController.text;
      if (otpEntered.isEmpty) {
        setState(() {
          _infoText = "Please enter OTP";
          _infoColor = const Color(0xFFFF0000);
        });
      } else if (otpEntered != widget.emailFragment.code.toString()) {
        setState(() {
          _infoText = "Incorrect OTP";
          _infoColor = const Color(0xFFFF0000);
        });
      } else {
        setState(() {
          _infoText = "Updating email address...";
          _infoColor = const Color(0xFF00FF00);
          _isLoading = true;
          _isVerifyEnabled = false;
        });

        final firestoreFunctions = FirestoreFunctions.withEmailFragment(
          context,
          widget.emailFragment,
        );
        firestoreFunctions.updateEmail(
          SplashScreenState.tid,
          widget.emailFragment.emailAddress,
          widget.emailFragment.emailAddress,
        );
      }
    }
  }

  void _handleEnableOTP(bool response) {
    setState(() {
      _isLoading = false;
      _isVerifyEnabled = true;

      if (response) {
        widget.emailFragment.emailSubmitted = true;
        _infoText = "Please enter OTP sent to ${_emailController.text}";
        _infoColor = const Color(0xFFAAAAAA);
        _showOTP = true;
      } else {
        _infoText = "Error sending OTP. Please try again";
        _infoColor = const Color(0xFFFF0000);
      }
    });
  }

  void _handlePostUpdateEmail(String response) async {
    setState(() {
      _isLoading = false;
      _isVerifyEnabled = true;
    });

    debugPrint("Update email response: $response");
    debugPrint("Current TID: ${SplashScreenState.tid}");

    if (response == "DUPLICATE_EMAIL") {
      setState(() {
        _infoText =
            "Email address already registered. Please sign in using email to access your account";
        _infoColor = const Color(0xFFFF0000);
      });
    } else if (response == "SUCCESS") {
      await widget.emailFragment.updateEmailEverywhere();

      setState(() {
        _infoText = "Email updated successfully!";
        _infoColor = const Color(0xFF00FF00);
      });

      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          Navigator.of(context).pop();
          widget.emailFragment.onEmailUpdated?.call();
        }
      });
    } else if (response == "INVALID_TID") {
      debugPrint("Invalid TID error - attempting to fix...");

      String originalTID = SplashScreenState.tid;
      if (!_isNumericTID(originalTID)) {
        String fixedTID = _convertToNumericTID(originalTID);
        SplashScreenState.tid = fixedTID;
        debugPrint("Fixed TID from $originalTID to $fixedTID");

        setState(() {
          _infoText = "Retrying with corrected user ID...";
          _infoColor = const Color(0xFFFFAA00);
          _isLoading = true;
          _isVerifyEnabled = false;
        });

        final firestoreFunctions = FirestoreFunctions.withEmailFragment(
          context,
          widget.emailFragment,
        );
        firestoreFunctions.updateEmail(
          SplashScreenState.tid,
          widget.emailFragment.emailAddress,
          widget.emailFragment.emailAddress,
        );
        return;
      }

      setState(() {
        _infoText = "Invalid user ID. Please try signing in again";
        _infoColor = const Color(0xFFFF0000);
      });
    } else {
      setState(() {
        _infoText = "Error updating email address. Please try again";
        _infoColor = const Color(0xFFFF0000);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF424242),
          borderRadius: BorderRadius.circular(15.0),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 10),
              child: const Text(
                "E-mail verification",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            Container(
              margin: const EdgeInsets.only(left: 30, right: 30, top: 20),
              decoration: BoxDecoration(
                color: const Color(0xFF424242),
                border: Border.all(color: const Color(0xFF626262), width: 1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: TextField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                style: const TextStyle(color: Colors.white, fontSize: 16),
                decoration: const InputDecoration(
                  hintText: "Email Address",
                  hintStyle: TextStyle(color: Colors.white70),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.only(
                    left: 10,
                    top: 12,
                    bottom: 12,
                  ),
                ),
              ),
            ),

            Container(
              margin: const EdgeInsets.only(left: 30, right: 30, top: 10),
              child: Text(
                _infoText,
                textAlign: TextAlign.center,
                style: TextStyle(color: _infoColor, fontSize: 14),
              ),
            ),

            if (_isLoading)
              Container(
                margin: const EdgeInsets.only(top: 10),
                child: const SizedBox(
                  width: 30,
                  height: 30,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    strokeWidth: 3,
                  ),
                ),
              ),

            if (_showOTP)
              Container(
                margin: const EdgeInsets.only(left: 30, right: 30, top: 20),
                decoration: BoxDecoration(
                  color: const Color(0xFF424242),
                  border: Border.all(color: const Color(0xFF626262), width: 1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: TextField(
                  controller: _otpController,
                  keyboardType: TextInputType.number,
                  maxLength: 6,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    letterSpacing: 4,
                  ),
                  decoration: const InputDecoration(
                    hintText: "Enter OTP",
                    hintStyle: TextStyle(color: Colors.white70),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.only(
                      left: 10,
                      top: 12,
                      bottom: 12,
                    ),
                    counterText: "",
                  ),
                ),
              ),

            Container(
              margin: const EdgeInsets.all(20),
              child: SizedBox(
                width: 200,
                height: 40,
                child: ElevatedButton(
                  onPressed: _isVerifyEnabled ? _onVerifyPressed : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isVerifyEnabled
                        ? const Color(0xFF6A1B9A)
                        : const Color(0xFF888888),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    elevation: _isVerifyEnabled ? 4 : 0,
                  ),
                  child: Text(
                    "Verify",
                    style: TextStyle(
                      color: _isVerifyEnabled ? Colors.white : Colors.white70,
                      fontSize: 18,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
