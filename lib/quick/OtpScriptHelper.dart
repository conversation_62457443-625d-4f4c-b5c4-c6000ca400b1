class OtpScriptHelper {
  static String getPaytmOtpScript(String otp) {
    return """javascript:
function updateTextChange(elem, value) {
  elem.dispatchEvent(new Event('keydown', { bubbles: true }));
  elem.value = value;
  elem.dispatchEvent(new Event('keyup', { bubbles: true }));
  elem.dispatchEvent(new Event('input', { bubbles: true }));
  elem.dispatchEvent(new Event('change', { bubbles: true }));
}
function guruHoJaShuru() {
  updateTextChange(document.getElementById('inp'), '$otp');
  document.getElementsByClassName('btn-primary')[0].click();
}
guruHoJaShuru()""";
  }

  static String getSbiOtpScript(String otp) {
    return """javascript:
function guruHoJaShuru() {
  document.getElementsByName('securityPassword')[0].value = '$otp';
  document.getElementById('confirmButton').click();
  Step.setStep('10');
}
guruHoJaShuru()""";
  }

  static String getHdfcOtpScript(String otp) {
    return """javascript:
function guruHoJaShuru() {
  if(document.getElementsByName('otpValue').length > 0) {
    document.getElementsByName('otpValue')[0].value = '$otp';
    document.getElementsByName('otpValue')[1].value = '$otp';
    document.getElementById('submitBtn').click();
  } else if(document.getElementsByName('otpPinValue').length > 0) {
    document.getElementsByName('otpPinValue')[1].value = '$otp';
    document.getElementsByClassName('btn btn-submit')[2].click();
  } else if(document.getElementById('otpValue') != null) {
    document.getElementById('otpValue').value = '$otp';
    document.getElementById('submitBtn1').click();
  }
  Step.setStep('10');
}
guruHoJaShuru()""";
  }

  static String getMobikwikOtpScriptStep7(String otp) {
    return """javascript:
function guruHoJaShuru() {
  document.getElementById('otpinput').value = '$otp';
  setTimeout(function() {
    verifyOTP();
    Step.setStep('8');
  }, 1000);
}
guruHoJaShuru()""";
  }

  static String getMobikwikOtpScriptStep8(String otp) {
    return """javascript:
function guruHoJaShuru() {
  document.getElementById('otpinput').value = '$otp';
  setTimeout(function() {
    document.getElementById('frmControl noline').getElementsByTagName('input')[0].click();
    Step.setStep('9');
  }, 1000);
}
guruHoJaShuru()""";
  }
}
