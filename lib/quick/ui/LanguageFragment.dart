import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

import '../../core/helper/mixpanel_manager.dart';
import '../../routes/app_routes.dart';
import '../my_context_wrapper.dart';

class LanguageFragment extends StatefulWidget {
  const LanguageFragment({super.key});

  static Future<void> showLanguageDialog(BuildContext context) async {
    await showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return const LanguageFragment();
      },
    );
  }

  @override
  State<LanguageFragment> createState() => _LanguageFragmentState();
}

class _LanguageFragmentState extends State<LanguageFragment> {
  final List<String> languages = [
    "English",
    "हिन्दी",
    "मराठी",
    "বাংলা",
    "ગુજરાતી",
  ];

  final List<String> lang_firebase_tags = [
    "lang_en",
    "lang_hi",
    "lang_mr",
    "lang_bn",
    "lang_gu",
  ];

  int? currentSelectedIndex;

  @override
  void initState() {
    super.initState();
    _loadCurrentSelection();
  }

  Future<void> _loadCurrentSelection() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final option = prefs.getInt("OPTION") ?? 0;
      setState(() {
        currentSelectedIndex = option;
      });
    } catch (e) {
      setState(() {
        currentSelectedIndex = 0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(24),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 50,
              width: double.infinity,
              decoration: BoxDecoration(
                color: const Color(0xFF6B33F2),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              alignment: Alignment.center,
              child: const Text(
                "Select Language",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            Flexible(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: languages.length,
                itemBuilder: (context, index) {
                  final isSelected = currentSelectedIndex == index;
                  return Container(
                    color: isSelected
                        ? Colors.blue.withOpacity(0.1)
                        : Colors.transparent,
                    child: ListTile(
                      leading: isSelected
                          ? const Icon(Icons.check, color: Colors.blue)
                          : const SizedBox(width: 24),
                      title: Text(
                        languages[index],
                        style: TextStyle(
                          color: isSelected ? Colors.blue : Colors.black,
                          fontWeight: isSelected
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                      onTap: () => _onLanguageSelected(index),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onLanguageSelected(int index) async {
    try {
      MixpanelManager().track("Language changed", {
        "New language": lang_firebase_tags[index],
      });
    } catch (_) {}

    await MyContextWrapper.saveLanguageOption(index);

    await FirebaseAnalytics.instance.logEvent(
      name: lang_firebase_tags[index],
      parameters: {"value": "true"},
    );

    if (mounted) Navigator.of(context).pop();

    Get.offAllNamed(AppRoutes.splash);
  }
}
