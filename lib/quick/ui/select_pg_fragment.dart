import 'package:flutter/material.dart';

// Dummy PremiumActivity to hold tickets/amount (replace with actual logic as needed)
class PremiumActivity {
  static int tickets = 1;
  static double amount = 100.0;
}

class SelectPGFragment extends StatefulWidget {
  const SelectPGFragment({Key? key}) : super(key: key);

  @override
  _SelectPGFragmentState createState() => _SelectPGFragmentState();
}

class _SelectPGFragmentState extends State<SelectPGFragment> {
  late Image razorpay;
  late Image paytm;

  @override
  void initState() {
    super.initState();
    razorpay = Image.asset('assets/razorpay.png', key: const Key('razorpay'));
    paytm = Image.asset('assets/paytm.png', key: const Key('paytm'));
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                GestureDetector(
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Initiating Payment')),
                    );
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => Dashboard(
                          tickets: PremiumActivity.tickets,
                          amount: PremiumActivity.amount,
                          mode: 'RAZORPAY',
                        ),
                      ),
                    );
                  },
                  child: SizedBox(width: 100, height: 64, child: razorpay),
                ),
                GestureDetector(
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Initiating Payment')),
                    );
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => Dashboard(
                          tickets: PremiumActivity.tickets,
                          amount: PremiumActivity.amount,
                          mode: 'PAYTM',
                        ),
                      ),
                    );
                  },
                  child: SizedBox(width: 100, height: 64, child: paytm),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Dummy Dashboard for navigation (replace with actual implementation)
class Dashboard extends StatelessWidget {
  final int tickets;
  final double amount;
  final String mode;

  const Dashboard({
    Key? key,
    required this.tickets,
    required this.amount,
    required this.mode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Dashboard')),
      body: Center(
        child: Text('Tickets: $tickets\nAmount: $amount\nMode: $mode'),
      ),
    );
  }
}
