package com.tatkal.train.quick.ui.availability;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

public class AvailabilityViewModel extends ViewModel {

    private MutableLiveData<String> mText;

    public AvailabilityViewModel() {
        mText = new MutableLiveData<>();
        mText.setValue("This is gallery fragment");
    }

    public LiveData<String> getText() {
        return mText;
    }
}