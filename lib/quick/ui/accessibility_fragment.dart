import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/gestures.dart';

class AccessibilityFragment extends StatefulWidget {
  @override
  _AccessibilityFragmentState createState() => _AccessibilityFragmentState();
}

class _AccessibilityFragmentState extends State<AccessibilityFragment> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          color: Colors.white,
        ),
        child: onCreateView(),
      ),
    );
  }

  Widget onCreateView() {
    Widget acceptTv = setTextViewHTML(
      "To fill details automatically in IRCTC Rail Connect app in order to book the ticket quickly, accessibility service permission is required.<br/><br/>By clicking Agree, you provide consent to retrieve screen content from IRCTC Rail Connect app in order to automate data entry in the app.",
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              acceptTv,
              SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () => cancelOnClick(),
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.grey[300],
                        ),
                        child: Center(
                          child: Text(
                            "Cancel",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => proceedOnClick(),
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.blue,
                        ),
                        child: Center(
                          child: Text(
                            "Proceed",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  void proceedOnClick() async {
    SharedPreferences sp = await SharedPreferences.getInstance();
    await sp.setInt("ACCEPT", 1);

    Map<String, Object> params = {"value": "true"};
    FirebaseAnalytics.instance.logEvent(name: "acc_accept", parameters: params);

    // ((TabActivity2) getActivity()).showAccessibilitySetting();
    Navigator.of(context).pop();
  }

  void cancelOnClick() {
    Map<String, Object> params = {"value": "true"};
    FirebaseAnalytics.instance.logEvent(name: "acc_cancel", parameters: params);
    Navigator.of(context).pop();
  }

  void makeLinkClickable(String url) {
    Map<String, Object> params = {"value": "true"};

    if (url.contains("accessibility_service")) {
      FirebaseAnalytics.instance.logEvent(
        name: "accessibility_view",
        parameters: params,
      );
      launchUrl(Uri.parse(url));
    }
  }

  Widget setTextViewHTML(String html) {
    String text = html.replaceAll("<br/>", "\n").replaceAll("<br>", "\n");

    List<TextSpan> spans = [];
    List<String> parts = text.split("<a>");

    for (int i = 0; i < parts.length; i++) {
      if (i == 0) {
        spans.add(
          TextSpan(
            text: parts[i],
            style: TextStyle(color: Colors.white),
          ),
        );
      } else {
        List<String> linkParts = parts[i].split("</a>");
        if (linkParts.length >= 2) {
          spans.add(
            TextSpan(
              text: linkParts[0],
              style: TextStyle(
                color: Colors.white,
                decoration: TextDecoration.underline,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () => makeLinkClickable(linkParts[0]),
            ),
          );
          if (linkParts[1].isNotEmpty) {
            spans.add(
              TextSpan(
                text: linkParts[1],
                style: TextStyle(color: Colors.white),
              ),
            );
          }
        }
      }
    }

    return RichText(text: TextSpan(children: spans));
  }
}
