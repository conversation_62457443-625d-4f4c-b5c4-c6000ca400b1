import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

import '../../core/Consts.dart';

class GoldDemoFragment extends StatefulWidget {
  const GoldDemoFragment({Key? key}) : super(key: key);

  @override
  State<GoldDemoFragment> createState() => _GoldDemoFragmentState();
}

class _GoldDemoFragmentState extends State<GoldDemoFragment> {
  late VideoPlayerController videoView;
  final String closeTv = 'CLOSE';
  bool _initialized = false;
  double? _videoWidth;
  double? _videoHeight;

  @override
  void initState() {
    super.initState();
    String videoPath;
    if (Consts.videoDemoType == "WEB") {
      videoPath = 'assets/captcha_web.mp4';
    } else if (Consts.videoDemoType == "RC") {
      videoPath = 'assets/captcha_rc.mp4';
    } else {
      videoPath = '';
    }
    videoView = VideoPlayerController.asset(videoPath)
      ..initialize().then((_) {
        videoView.setLooping(true);
        setState(() {
          _initialized = true;
          if (videoView.value.size != null && videoView.value.size.width > 0) {
            _videoWidth = videoView.value.size.width;
            _videoHeight = videoView.value.size.height;
          }
          videoView.play();
        });
      });
  }

  @override
  void dispose() {
    videoView.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double containerWidth =
        MediaQuery.of(context).size.width -
        60; // like Android dialog MATCH_PARENT - 30dp*2 margin
    double videoHeight;
    if (Consts.videoDemoType == "WEB") {
      videoHeight = containerWidth / 2;
    } else if (Consts.videoDemoType == "RC") {
      videoHeight = containerWidth * 3 / 4;
    } else {
      videoHeight = 200;
    }
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
      backgroundColor: Colors.transparent,
      child: Container(
        margin: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFF424259),
          borderRadius: BorderRadius.circular(14),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 30),
            _initialized
                ? Container(
                    width: containerWidth,
                    height: videoHeight,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: VideoPlayer(videoView),
                    ),
                  )
                : Container(
                    width: containerWidth,
                    height: videoHeight,
                    alignment: Alignment.center,
                    child: const CircularProgressIndicator(),
                  ),
            const SizedBox(height: 24),
            GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.black.withOpacity(0.5),
                ),
                child: Text(
                  closeTv,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 18),
          ],
        ),
      ),
    );
  }
}
