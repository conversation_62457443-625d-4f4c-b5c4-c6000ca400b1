import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flutter/services.dart';

// These would come from elsewhere in your real app
class FormActivity2 {
  static String selectedForm = '';
  static String mPassword = '';

  // Add a GlobalKey for accessing spinView (loader)
  static final GlobalKey<_SpinViewState> spinViewKey =
      GlobalKey<_SpinViewState>();
}

class ValidateFragment extends StatefulWidget {
  const ValidateFragment({Key? key}) : super(key: key);

  @override
  _ValidateFragmentState createState() => _ValidateFragmentState();
}

class _ValidateFragmentState extends State<ValidateFragment> {
  String origPwd = '';
  String formName = '';
  final TextEditingController password = TextEditingController();
  final FocusNode passwordFocusNode = FocusNode();
  final ValueNotifier<bool> errorVisible = ValueNotifier<bool>(false);
  final ValueNotifier<String> errorText = ValueNotifier<String>('');
  int inputType = 1; // 1: password, 0: visible

  @override
  void initState() {
    super.initState();
    origPwd = FormActivity2.mPassword;
    formName = FormActivity2.selectedForm;
    // Focus and show keyboard on build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(passwordFocusNode);
      SystemChannels.textInput.invokeMethod('TextInput.show');
    });
  }

  @override
  void dispose() {
    password.dispose();
    passwordFocusNode.dispose();
    errorVisible.dispose();
    errorText.dispose();
    super.dispose();
  }

  void hideKeyboard() {
    FocusScope.of(context).unfocus();
    SystemChannels.textInput.invokeMethod('TextInput.hide');
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Open Form',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: password,
              focusNode: passwordFocusNode,
              obscureText: inputType == 1,
              decoration: InputDecoration(
                labelText: 'Password',
                border: const OutlineInputBorder(),
                suffixIcon: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTapDown: (_) {
                    setState(() => inputType = 0);
                  },
                  onTapUp: (_) {
                    setState(() => inputType = 1);
                  },
                  child: const Icon(Icons.remove_red_eye),
                ),
              ),
              onSubmitted: (_) => _onSubmit(),
            ),
            const SizedBox(height: 10),
            ValueListenableBuilder<bool>(
              valueListenable: errorVisible,
              builder: (context, visible, _) {
                if (!visible) return const SizedBox.shrink();
                return ValueListenableBuilder<String>(
                  valueListenable: errorText,
                  builder: (context, text, _) => Text(
                    text,
                    key: const Key('textView44'),
                    style: const TextStyle(color: Colors.red),
                  ),
                );
              },
            ),
            const SizedBox(height: 18),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton(
                  key: const Key('button'),
                  onPressed: _onSubmit,
                  child: const Text('Validate'),
                ),
                SpinView(key: FormActivity2.spinViewKey),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _onSubmit() {
    String pass = password.text;
    if (origPwd == pass) {
      FormActivity2.spinViewKey.currentState?.setVisible(true);
      Future.delayed(const Duration(milliseconds: 100), () {
        // You would use your own navigation stack here
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (_) =>
                TabActivity2(formNameOld: formName, formNameNew: ''),
          ),
        );
        hideKeyboard();
        Navigator.of(context).pop();
      });
    } else {
      errorText.value = 'Invalid Password';
      errorVisible.value = true;
      password.text = '';
    }
  }
}

// Dummy widgets and classes to mimic your Android structure
class TabActivity2 extends StatelessWidget {
  final String formNameOld;
  final String formNameNew;

  const TabActivity2({
    Key? key,
    required this.formNameOld,
    required this.formNameNew,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Center(child: Text('TabActivity2 - $formNameOld')));
  }
}

class SpinView extends StatefulWidget {
  const SpinView({Key? key}) : super(key: key);

  @override
  _SpinViewState createState() => _SpinViewState();
}

class _SpinViewState extends State<SpinView> {
  bool _visible = false;

  void setVisible(bool v) => setState(() => _visible = v);

  @override
  Widget build(BuildContext context) {
    return _visible
        ? const SizedBox(
            height: 30,
            width: 30,
            child: CircularProgressIndicator(),
          )
        : const SizedBox(height: 30, width: 30);
  }
}
