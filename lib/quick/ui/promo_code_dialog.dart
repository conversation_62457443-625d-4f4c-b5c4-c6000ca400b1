import 'package:flutter/material.dart';

class ConnectionDetector {
  final BuildContext context;

  ConnectionDetector(this.context);

  bool isConnectingToInternet() {
    return true;
  }
}

class FirestoreFunctions {
  static List<String> activePromoCodes = [
    'DISCOUNT2024',
    'TATKAL50',
    'GOLD2024',
  ];
}

class Consts {
  static String PROMO_CODE = '';
}

class PremiumActivity {
  void applyDiscount() {}

  static PremiumActivity? of(BuildContext ctx) => null;
}

class PromoCodeDialog extends StatefulWidget {
  const PromoCodeDialog({Key? key}) : super(key: key);

  @override
  _PromoCodeDialogState createState() => _PromoCodeDialogState();
}

class _PromoCodeDialogState extends State<PromoCodeDialog> {
  late Text info;
  String code = '';
  late TextEditingController codeEt;
  bool isLoading = false;
  late ElevatedButton verify;
  bool verifyEnabled = false;
  String statusMsg = '';
  bool statusVisible = false;
  String infoMsg =
      'Enter Promo Code to get yearly GOLD Pack at discounted price';
  Color statusColor = const Color(0xFFF77F7F);

  @override
  void initState() {
    super.initState();
    codeEt = TextEditingController();
  }

  @override
  void dispose() {
    codeEt.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Promo Code',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: Text(
                infoMsg,
                textAlign: TextAlign.center,
                style: const TextStyle(color: Color(0xFFBD9F5A)),
              ),
            ),
            const SizedBox(height: 18),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: TextField(
                key: const Key('code'),
                controller: codeEt,
                textCapitalization: TextCapitalization.characters,
                style: const TextStyle(color: Colors.white, fontSize: 16),
                decoration: InputDecoration(
                  hintText: 'Enter Code',
                  hintStyle: const TextStyle(color: Colors.white54),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 10),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.amber.shade700),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.amber.shade700),
                  ),
                ),
                onChanged: (val) {
                  String upper = val.toUpperCase();
                  if (val != upper) {
                    codeEt.value = TextEditingValue(
                      text: upper,
                      selection: TextSelection.collapsed(offset: upper.length),
                    );
                  }
                  setState(() {
                    verifyEnabled = upper.length > 3;
                  });
                },
              ),
            ),
            const SizedBox(height: 15),
            if (statusVisible)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Text(
                  statusMsg,
                  style: TextStyle(color: statusColor),
                  key: const Key('status'),
                  textAlign: TextAlign.center,
                ),
              ),
            if (isLoading) ...[
              const SizedBox(height: 10),
              const SizedBox(
                height: 30,
                width: 30,
                child: CircularProgressIndicator(color: Colors.white),
              ),
            ],
            const SizedBox(height: 10),
            ElevatedButton(
              key: const Key('verify'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFBD9F5A),
                fixedSize: const Size(200, 40),
              ),
              onPressed: verifyEnabled
                  ? () async {
                      FocusScope.of(context).unfocus();
                      setState(() {
                        isLoading = true;
                      });
                      await Future.delayed(const Duration(milliseconds: 700));
                      setState(() {
                        isLoading = false;
                      });
                      if (!ConnectionDetector(
                        context,
                      ).isConnectingToInternet()) {
                        setState(() {
                          statusVisible = true;
                          statusMsg = 'Please check your network connection';
                          statusColor = Colors.red;
                        });
                        return;
                      }
                      code = codeEt.text;
                      if (!FirestoreFunctions.activePromoCodes.contains(code)) {
                        setState(() {
                          statusVisible = true;
                          statusMsg = 'Incorrect promo code. Please try again';
                          statusColor = Colors.red;
                        });
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Invalid promo code')),
                        );
                      } else {
                        setState(() {
                          statusVisible = false;
                        });
                        Consts.PROMO_CODE = code;
                        // Simulate: ((PremiumActivity) getActivity()).applyDiscount();
                        // You may want to call `applyDiscount` in a parent widget/state.
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Congratulations!!! Promo code applied successfully',
                            ),
                          ),
                        );
                        Navigator.of(context).pop();
                      }
                    }
                  : null,
              child: const Text(
                'Verify',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}
