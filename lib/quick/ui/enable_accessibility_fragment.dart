import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class EnableAccessibilityFragment extends StatefulWidget {
  @override
  _EnableAccessibilityFragmentState createState() =>
      _EnableAccessibilityFragmentState();
}

class _EnableAccessibilityFragmentState
    extends State<EnableAccessibilityFragment> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          // Using theme_round_dialog background equivalent
          gradient: LinearGradient(
            colors: [Colors.blue.shade800, Colors.blue.shade600],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: onCreateView(),
      ),
    );
  }

  Widget onCreateView() {
    Widget continueBtn = GestureDetector(
      onTap: () => continueBtnOnClick(),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(bottomRight: Radius.circular(15)),
          color: Colors.blue.shade700,
        ),
        child: Center(
          child: Text(
            "Enable",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );

    Widget declineTv = GestureDetector(
      onTap: () => declineTvOnClick(),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(bottomLeft: Radius.circular(15)),
          color: Colors.grey.shade600,
        ),
        child: Center(
          child: Text(
            "Cancel",
            style: TextStyle(fontSize: 18, color: Colors.white),
          ),
        ),
      ),
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Header
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 15),
          child: Text(
            "Accessibility permission required",
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade300,
            ),
          ),
        ),

        // Content
        Padding(
          padding: EdgeInsets.fromLTRB(10, 10, 10, 40),
          child: Column(
            children: [
              // Image placeholder - accessibility_service_permission
              Container(
                width: double.infinity,
                height: 150,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.accessibility,
                  size: 80,
                  color: Colors.grey.shade600,
                ),
              ),

              SizedBox(height: 20),

              // Instructions text
              Container(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  "1. Got to Phone Settings\n2. Tap on Accessibility\n3. Turn on 'Quick Tatkal' option",
                  style: TextStyle(fontSize: 16, color: Colors.white),
                ),
              ),

              SizedBox(height: 20),

              // Note text
              Container(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  "Note: For some devices, find the option under 'Accessibility' > 'Installed services' or 'Downloaded apps'",
                  style: TextStyle(fontSize: 16, color: Colors.grey.shade300),
                ),
              ),
            ],
          ),
        ),

        // Buttons
        Row(
          children: [
            Expanded(child: declineTv),
            Container(width: 1, height: 50, color: Colors.white),
            Expanded(child: continueBtn),
          ],
        ),
      ],
    );
  }

  void continueBtnOnClick() {
    Navigator.of(context).pop();
    // Launch accessibility settings - equivalent to Settings.ACTION_ACCESSIBILITY_SETTINGS
    launchUrl(
      Uri.parse(
        'package:com.android.settings/.Settings\$AccessibilitySettingsActivity',
      ),
    );
  }

  void declineTvOnClick() {
    Navigator.of(context).pop();
  }
}
