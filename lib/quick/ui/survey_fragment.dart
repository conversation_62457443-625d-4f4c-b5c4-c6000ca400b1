import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class SurveyFragment extends StatefulWidget {
  const SurveyFragment({Key? key}) : super(key: key);

  @override
  _SurveyFragmentState createState() => _SurveyFragmentState();
}

class _SurveyFragmentState extends State<SurveyFragment> {
  late final MixpanelAPI mixpanel;

  @override
  void initState() {
    super.initState();
    mixpanel = MixpanelAPI();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.secondary,
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(0),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Padding(
                padding: EdgeInsets.all(15.0),
                child: Text(
                  'User Type',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
              Column(
                children: [
                  GestureDetector(
                    key: const Key('agent_user'),
                    onTap: () async {
                      // Mixpanel log
                      await mixpanel.track('Survey form opened', {
                        'User type': 'Agent',
                      });
                      // Open URL
                      const url = 'https://forms.gle/hfKuQTTtb8VgnRh36';
                      if (await canLaunch(url)) {
                        await launch(url);
                      }
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      color: Theme.of(context).dialogBackgroundColor,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'IRCTC agent / Experienced user',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 5),
                          const Text(
                            "Users looking to earn commission in Quick Tatkal by booking other users' tickets",
                            style: TextStyle(
                              color: Color(0xFFBBBBBB),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    color: Theme.of(context).dialogBackgroundColor,
                    padding: EdgeInsets.zero,
                    child: const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10),
                      child: Divider(
                        color: Colors.white,
                        height: 1,
                        thickness: 1,
                      ),
                    ),
                  ),
                  GestureDetector(
                    key: const Key('regular_user'),
                    onTap: () async {
                      await mixpanel.track('Survey form opened', {
                        'User type': 'Regular',
                      });
                      const url = 'https://forms.gle/1JEfrUhAXMvforMo8';
                      if (await canLaunch(url)) {
                        await launch(url);
                      }
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      color: Theme.of(context).dialogBackgroundColor,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Regular user',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 5),
                          const Text(
                            "Users looking to book tickets from IRCTC agents or other experienced users in Quick Tatkal",
                            style: TextStyle(
                              color: Color(0xFFBBBBBB),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Stub class for MixpanelAPI. Replace with real implementation as needed.
class MixpanelAPI {
  Future<void> track(String event, Map<String, dynamic> props) async {
    // Implement actual Mixpanel call here
    debugPrint('Track: $event, props: $props');
  }
}
