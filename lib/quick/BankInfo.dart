import '../core/Consts.dart';

class BankInfo {
  var context;

  BankInfo([this.context]);

  String debitCardWithPinJS(
    String bankChoice,
    String cardNo,
    String cardHolder,
    String expMon,
    String expYr,
    String pin,
    String cvv,
    String cardType,
    String staticPassword,
    int step,
  ) {
    String jsCode = '';
    String script = '';
    String script1 =
        "if(document.getElementById('debitCardNumber') != null && document.getElementById('debitCardholderName') != null && " +
        "document.getElementById('cardPin') != null && document.getElementById('debiMonth') != null && " +
        "document.getElementById('debiYear') != null && document.getElementById('passline') != null) {" +
        "document.getElementById('debitCardNumber').value = '$cardNo';" +
        "document.getElementById('debitCardholderName').value = '$cardHolder';" +
        "document.getElementById('cardPin').value = '$pin';" +
        "document.getElementById('debiMonth').value = '${int.parse(expMon)}';" +
        "document.getElementById('debiYear').value = '$expYr';" +
        "document.getElementById('passline').focus();" +
        "Step.setStep('8');" +
        "}";

    switch (bankChoice) {
      case Consts.SBI:
      case Consts.CBI:
      case Consts.AXIS:
      case Consts.CANARA_BK:
      case Consts.IND_OVS_BK:
        script = script1;
        break;

      case Consts.PNB:
        script =
            "if(document.getElementById('cnumber') != null && document.getElementById('cname2') != null && " +
            "document.getElementById('cvv2') != null && document.getElementById('expmon') != null && " +
            "document.getElementById('expyr') != null && document.getElementById('submitbtn') != null) {" +
            "document.getElementById('cnumber').value = '$cardNo';" +
            "document.getElementById('cname2').value = '$cardHolder';" +
            "document.getElementById('cvv2').value = '$cvv';" +
            "document.getElementById('expmon').value = '$expMon';" +
            "document.getElementById('expyr').value = '$expYr';" +
            "if('$cvv' != '')" +
            "document.getElementById('submitbtn').click();" +
            "Step.setStep('8');" +
            "}";
        break;

      case Consts.INDIAN_BK:
        script =
            "if(document.getElementById('debitCardNumber') != null && document.getElementById('debitCardholderName') != null && " +
            "document.getElementById('cardPin') != null && document.getElementById('debiMonth') != null && " +
            "document.getElementById('debiYear') != null && document.getElementById('passline') != null) {" +
            "fadeInFadeOut('debitFade');" +
            "document.getElementById('debitCardNumber').value = '$cardNo';" +
            "document.getElementById('debitCardholderName').value = '$cardHolder';" +
            "document.getElementById('cardPin').value = '$pin';" +
            "document.getElementById('debiMonth').value = '${int.parse(expMon)}';" +
            "document.getElementById('debiYear').value = '$expYr';" +
            "document.getElementById('passline').focus();" +
            "Step.setStep('8');" +
            "}";
        break;

      case Consts.UNION_BK:
      case Consts.ANDHRA_BK:
        script =
            "if($step == 6 && document.getElementById('debit') != null) {" +
            "document.getElementById('debit').click();" +
            "Step.setStep('7');" +
            "} else if($step == 7) {" +
            "if(document.getElementsByName('Ecom_Payment_Card_Number').length > 0 && document.getElementsByName('Ecom_Payment_Card_Name').length > 0 && " +
            "document.getElementsByName('Ecom_Payment_Pin').length > 0 && document.getElementsByName('Ecom_Payment_Card_ExpDate_Month').length > 0 && " +
            "document.getElementsByName('Ecom_Payment_Card_ExpDate_Year').length > 0 && document.getElementsByName('Ecom_Captcha_Value').length > 0) {" +
            "document.getElementsByName('Ecom_Payment_Card_Number')[0].value = '$cardNo';" +
            "document.getElementsByName('Ecom_Payment_Card_Name')[0].value = '$cardHolder';" +
            "document.getElementsByName('Ecom_Payment_Pin')[0].value = '$pin';" +
            "document.getElementsByName('Ecom_Payment_Card_ExpDate_Month')[0].value = '${int.parse(expMon)}';" +
            "document.getElementsByName('Ecom_Payment_Card_ExpDate_Year')[0].value = '$expYr';" +
            "document.getElementsByName('Ecom_Captcha_Value')[0].focus();" +
            "Step.setStep('8');" +
            "}" +
            "}";
        break;
      case Consts.BOI:
        script =
            "if($step == 6 && document.getElementsByName('txtBankID').length > 0) {" +
            "document.getElementsByName('txtBankID')[0].click();" +
            "Step.setStep('7');" +
            "} else if($step == 7) {" +
            "if(document.getElementById('cnumber') != null && document.getElementById('cname2') != null && " +
            "document.getElementById('cvv2') != null && document.getElementById('expmon') != null && " +
            "document.getElementById('expyr') != null && document.getElementById('Imgver') != null) {" +
            "document.getElementById('cnumber').value = '$cardNo';" +
            "document.getElementById('cname2').value = '$cardHolder';" +
            "document.getElementById('cvv2').value = '$pin';" +
            "document.getElementById('expmon').value = '$expMon';" +
            "document.getElementById('expyr').value = '$expYr';" +
            "document.getElementById('Imgver').focus();" +
            "Step.setStep('8');" +
            "}" +
            "}";
        break;

      case Consts.CITI_BK:
        script =
            "if(document.getElementById('CITI_CREDIT_CARD') != null && document.getElementById('DOB_EXP1') != null && " +
            "document.getElementById('DOB_EXP2') != null && document.getElementById('DOB_EXP3') != null && " +
            "document.getElementsByName('HtmlCVVNum').length > 0 && document.getElementById('HtmlMonth') != null &&" +
            "document.getElementById('HtmlYear') != null && document.getElementById('submitciti') != null) {" +
            "document.getElementById('CITI_CREDIT_CARD').value = '${cardNo.substring(0, 4)}';" +
            "document.getElementById('DOB_EXP1').value = '${cardNo.substring(4, 8)}';" +
            "document.getElementById('DOB_EXP2').value = '${cardNo.substring(8, 12)}';" +
            "document.getElementById('DOB_EXP3').value = '${cardNo.substring(12)}';" +
            "document.getElementsByName('HtmlCVVNum')[0].value = '$cvv';" +
            "document.getElementById('HtmlMonth').value = '$expMon';" +
            "document.getElementById('HtmlYear').value = '${expYr.substring(2, 4)}';" +
            "if('$cvv' != '')" +
            "document.getElementById('submitciti').click();" +
            "Step.setStep('8');" +
            "}";
        break;

      case Consts.ICICI:
        script =
            "if(document.getElementsByName('CardTypeSelectBox').length > 0 && document.getElementsByName('CardNum1').length > 0 && " +
            "document.getElementsByName('CardNum2').length > 0 && document.getElementsByName('CardNum3').length > 0 && " +
            "document.getElementsByName('CardNum4').length > 0 && document.getElementsByName('NameOnCard').length > 0 && " +
            "document.getElementsByName('CVVNum').length > 0 && document.getElementsByName('ATMPIN').length > 0 && " +
            "document.getElementsByName('ExpDtMon').length > 0 && document.getElementsByName('ExpDtYr').length > 0 && " +
            "document.getElementsByName('btnPay').length > 0) {" +
            "document.getElementsByName('CardTypeSelectBox')[0].value = '$cardType|1';" +
            "document.getElementsByName('CardNum1')[0].value = '${cardNo.substring(0, 4)}';" +
            "document.getElementsByName('CardNum2')[0].value = '${cardNo.substring(4, 8)}';" +
            "document.getElementsByName('CardNum3')[0].value = '${cardNo.substring(8, 12)}';" +
            "document.getElementsByName('CardNum4')[0].value = '${cardNo.substring(12)}';" +
            "document.getElementsByName('NameOnCard')[0].value = '$cardHolder';" +
            "document.getElementsByName('CVVNum')[0].value = '$cvv';" +
            "document.getElementsByName('ATMPIN')[0].value = '$pin';" +
            "document.getElementsByName('ExpDtMon')[0].value = '$expMon';" +
            "document.getElementsByName('ExpDtYr')[0].value = '$expYr';" +
            "if('$cvv' != '' && '$pin' != '')" +
            "document.getElementsByName('btnPay')[0].click();" +
            "Step.setStep('8');" +
            "}";
        break;

      case Consts.HDFC:
        if (step < 8) {
          script =
              "document.getElementById('debitCardNumber').value = '$cardNo';" +
              "document.getElementById('debitCardholderName').value = '$cardHolder';" +
              "document.getElementById('cardPin').value = '$pin';" +
              "document.getElementById('debiMonthSelect').value = '${int.parse(expMon)}';" +
              "document.getElementById('debiYearSelect').value = '$expYr';" +
              "var url = document.getElementById('captcha_image').src;" +
              "Step.solveCaptcha(url, 1, 2);" +
              // "document.getElementsByName('passline')[0].focus();" +
              "Step.setStep('8');";
        } else if (step == 8) {
          script =
              "if(document.getElementById('cmdSubmitStatic') != null) {" +
              "document.getElementsByClassName('tab-section')[1].click();\n" +
              "setTimeout(function() {" +
              "document.getElementById('txtPassword').value = '$staticPassword';\n" +
              "}, 1001);" +
              "document.getElementById('cmdSubmitStatic').click();" +
              "Step.setStep('9');" +
              "}";
        }
        break;
      case Consts.UNITED_BK:
        script =
            "if(document.getElementById('txtboxCardNum') != null && document.getElementById('txtboxUserName') != null && " +
            "document.getElementById('dropdownMonth') != null && document.getElementById('dropdownYear') != null && " +
            "document.getElementById('btnGetOTP') != null && document.getElementById('txtCaptcha') != null) {" +
            "document.getElementById('txtboxCardNum').value = '$cardNo';" +
            "document.getElementById('txtboxUserName').value = '$cardHolder';" +
            "document.getElementById('dropdownMonth').value = '${int.parse(expMon)}';" +
            "document.getElementById('dropdownYear').value = '$expYr';" +
            "document.getElementById('btnGetOTP').click();" +
            "Step.setStep('8');" +
            "}";
        break;
    }

    jsCode =
        "javascript:function guruHoJaShuru() {" +
        script +
        "}" +
        "guruHoJaShuru()";

    return jsCode;
  }

  String netBankingJS(
    String bankName,
    int sbiOpt,
    String corpId,
    String username,
    String password,
    String bankingType,
    int step,
    int index,
  ) {
    String jsCode;
    String script = "";

    String script1 =
        "javascript:function guruHoJaShuru() {\n" +
        "\tfor(i = 0; i < document.getElementsByTagName('form').length; i++) {\n" +
        "\t\tvar f = document.getElementsByTagName('form')[i];\n" +
        "\t\tvar elem = f.getElementsByTagName('input');\n" +
        "\t\tflag = false;\n" +
        "\t\tvar userid;\n" +
        "\t\tfor(j = 0; j < elem.length; j++) {\n" +
        "\t\t\tif(elem[j].type.toLowerCase() == 'password') {\n" +
        "\t\t\t\tflag = true;\n" +
        "\t\t\t\tuserid.value = '$username';\n" +
        "\t\t\t\telem[j].value = '$password';\n" +
        "\t\t\t\t\n" +
        "\t\t\t\tfor(k=j+1; k < elem.length; k++) {\n" +
        "\t\t\t\t\tif(elem[k].type.toLowerCase() == 'submit' || elem[k].type.toLowerCase() == 'button') {\n" +
        "if('$password' != '')" +
        "\t\t\t\t\t\telem[k].click();\n" +
        "\t\t\t\t\t\tStep.setStep('9');\n" +
        "\t\t\t\t\t\treturn;\n" +
        "\t\t\t\t\t}\n" +
        "\t\t\t\t}\n" +
        "\t\t\t\t\n" +
        "\t\t\t} else if(elem[j].type.toLowerCase() == 'text') {\n" +
        "\t\t\t\tuserid = elem[j];\n" +
        "\t\t\t}\n" +
        "\t\t}\n" +
        "\t}\n" +
        "}\n" +
        "\n" +
        "guruHoJaShuru()";
    switch (bankName) {
      case "State Bank of India":
        if (step == 6) {
          script =
              "javascript:function guruHoJaShuru() {\n" +
              "document.getElementById('username').value = '$username';\n" +
              "document.getElementById('label2').value = '$password';\n" +
              "document.getElementsByClassName('btn btn-Yellow')[0].click();" +
              "Step.setStep('7');" +
              "}\n" +
              "guruHoJaShuru()";
        } else if (step == 7) {
          script =
              "javascript:function guruHoJaShuru() {\n" +
              "document.getElementById('Go').click();" +
              "Step.setStep('8');" +
              "}\n" +
              "guruHoJaShuru()";
        } else if (step == 8) {
          script =
              "javascript:function guruHoJaShuru() {\n" +
              "document.getElementById('confirmButton').click();" +
              "Step.setStep('9');" +
              "}\n" +
              "guruHoJaShuru()";
        }

        break;
      case "Nepal SBI Bank Ltd.":
      case "Corporation Bank":
      case "IDBI Bank":
      case "Kotak Mahindra Bank":
      case "ICICI Bank":
      case "AXIS Bank":
      case "Vijaya Bank":
      case "Allahabad Bank":
        script = script1;
        break;
      default:
        break;
    }
    jsCode =
        "javascript:function guruHoJaShuru() {" +
        script +
        "}" +
        "guruHoJaShuru()";
    return jsCode;
  }

  String credirCardJS(
    String bankName,
    String cardNo,
    String cardHolder,
    String expMon,
    String expYr,
    String cvv,
    String cardType,
    int step,
  ) {
    String jsCode = '';
    String script = '';

    String script1 =
        "if(document.getElementById('cardNumber') != null && document.getElementById('cardHolderName') != null && " +
        "document.getElementById('cardPin') != null && document.getElementById('expMonth') != null && " +
        "document.getElementById('expYear') != null && document.getElementById('cvv') != null) {" +
        "document.getElementById('cardNumber').value = '$cardNo';" +
        "document.getElementById('cardHolderName').value = '$cardHolder';" +
        "document.getElementById('cardPin').value = '';" +
        "document.getElementById('expMonth').value = '$expMon';" +
        "document.getElementById('expYear').value = '$expYr';" +
        "document.getElementById('cvv').value = '$cvv';" +
        "Step.setStep('8');" +
        "}";

    switch (bankName) {
      case "State Bank of India":
        script = script1;
        break;

      case "Central Bank of India":
        script = script1;
        break;

      case "AXIS Bank":
        script = script1;
        break;

      case "Canara Bank":
        script = script1;
        break;

      case "Indian Overseas Bank":
        script = script1;
        break;

      case "Punjab National Bank":
        script =
            "if(document.getElementById('cnumber') != null && document.getElementById('cname2') != null && " +
            "document.getElementById('cvv2') != null && document.getElementById('expmon') != null && " +
            "document.getElementById('expyr') != null && document.getElementById('submitbtn') != null) {" +
            "document.getElementById('cnumber').value = '$cardNo';" +
            "document.getElementById('cname2').value = '$cardHolder';" +
            "document.getElementById('cvv2').value = '$cvv';" +
            "document.getElementById('expmon').value = '$expMon';" +
            "document.getElementById('expyr').value = '$expYr';" +
            "if('$cvv' != '')" +
            "document.getElementById('submitbtn').click();" +
            "Step.setStep('8');" +
            "}";
        break;

      case "Indian Bank":
        script =
            "if(document.getElementById('debitCardNumber') != null && document.getElementById('debitCardholderName') != null && " +
            "document.getElementById('cardPin') != null && document.getElementById('debiMonth') != null && " +
            "document.getElementById('debiYear') != null && document.getElementById('passline') != null) {" +
            "fadeInFadeOut('debitFade');" +
            "document.getElementById('debitCardNumber').value = '$cardNo';" +
            "document.getElementById('debitCardholderName').value = '$cardHolder';" +
            "document.getElementById('cardPin').value = '';" +
            "document.getElementById('debiMonth').value = '$expMon';" +
            "document.getElementById('debiYear').value = '$expYr';" +
            "document.getElementById('passline').focus();" +
            "Step.setStep('8');" +
            "}";
        break;

      case "Union Bank of India":
      case "Andhra Bank":
        script =
            "if($step == 6 && document.getElementById('debit') != null) {" +
            "document.getElementById('debit').click();" +
            "Step.setStep('7');" +
            "} else if($step == 7) {" +
            "if(document.getElementsByName('Ecom_Payment_Card_Number').length > 0 && document.getElementsByName('Ecom_Payment_Card_Name').length > 0 && " +
            "document.getElementsByName('Ecom_Payment_Pin').length > 0 && document.getElementsByName('Ecom_Payment_Card_ExpDate_Month').length > 0 && " +
            "document.getElementsByName('Ecom_Payment_Card_ExpDate_Year').length > 0 && document.getElementsByName('Ecom_Captcha_Value').length > 0) {" +
            "document.getElementsByName('Ecom_Payment_Card_Number')[0].value = '$cardNo';" +
            "document.getElementsByName('Ecom_Payment_Card_Name')[0].value = '$cardHolder';" +
            "document.getElementsByName('Ecom_Payment_Pin')[0].value = '';" +
            "document.getElementsByName('Ecom_Payment_Card_ExpDate_Month')[0].value = '$expMon';" +
            "document.getElementsByName('Ecom_Payment_Card_ExpDate_Year')[0].value = '$expYr';" +
            "document.getElementsByName('Ecom_Captcha_Value')[0].focus();" +
            "Step.setStep('8');" +
            "}" +
            "}";
        break;

      case "Bank of India":
        script =
            "if($step == 6 && document.getElementsByName('txtBankID').length > 0) {" +
            "document.getElementsByName('txtBankID')[0].click();" +
            "Step.setStep('7');" +
            "} else if($step == 7) {" +
            "if(document.getElementById('cnumber') != null && document.getElementById('cname2') != null && " +
            "document.getElementById('cvv2') != null && document.getElementById('expmon') != null && " +
            "document.getElementById('expyr') != null && document.getElementById('Imgver') != null) {" +
            "document.getElementById('cnumber').value = '$cardNo';" +
            "document.getElementById('cname2').value = '$cardHolder';" +
            "document.getElementById('cvv2').value = '$cvv';" +
            "document.getElementById('expmon').value = '$expMon';" +
            "document.getElementById('expyr').value = '$expYr';" +
            "document.getElementById('Imgver').focus();" +
            "Step.setStep('8');" +
            "}" +
            "}";
        break;
      case "CITI Bank":
        script =
            "if(document.getElementById('CITI_CREDIT_CARD') != null && document.getElementById('DOB_EXP1') != null && " +
            "document.getElementById('DOB_EXP2') != null && document.getElementById('DOB_EXP3') != null && " +
            "document.getElementsByName('HtmlCVVNum').length > 0 && document.getElementById('HtmlMonth') != null &&" +
            "document.getElementById('HtmlYear') != null && document.getElementById('submitciti') != null) {" +
            "document.getElementById('CITI_CREDIT_CARD').value = '${cardNo.substring(0, 4)}';" +
            "document.getElementById('DOB_EXP1').value = '${cardNo.substring(4, 8)}';" +
            "document.getElementById('DOB_EXP2').value = '${cardNo.substring(8, 12)}';" +
            "document.getElementById('DOB_EXP3').value = '${cardNo.substring(12)}';" +
            "document.getElementsByName('HtmlCVVNum')[0].value = '$cvv';" +
            "document.getElementById('HtmlMonth').value = '$expMon';" +
            "document.getElementById('HtmlYear').value = '${expYr.substring(2, 4)}';" +
            "if('$cvv' != '')" +
            "document.getElementById('submitciti').click();" +
            "Step.setStep('8');" +
            "}";
        break;
      case "ICICI Bank":
        script =
            "if(document.getElementsByName('CardTypeSelectBox').length > 0 && document.getElementsByName('CardNum1').length > 0 && " +
            "document.getElementsByName('CardNum2').length > 0 && document.getElementsByName('CardNum3').length > 0 && " +
            "document.getElementsByName('CardNum4').length > 0 && document.getElementsByName('NameOnCard').length > 0 && " +
            "document.getElementsByName('CVVNum').length > 0 && document.getElementsByName('ATMPIN').length > 0 && " +
            "document.getElementsByName('ExpDtMon').length > 0 && document.getElementsByName('ExpDtYr').length > 0 && " +
            "document.getElementsByName('btnPay').length > 0) {" +
            "document.getElementsByName('CardTypeSelectBox')[0].value = '$cardType|1';" +
            "document.getElementsByName('CardNum1')[0].value = '${cardNo.substring(0, 4)}';" +
            "document.getElementsByName('CardNum2')[0].value = '${cardNo.substring(4, 8)}';" +
            "document.getElementsByName('CardNum3')[0].value = '${cardNo.substring(8, 12)}';" +
            "document.getElementsByName('CardNum4')[0].value = '${cardNo.substring(12)}';" +
            "document.getElementsByName('NameOnCard')[0].value = '$cardHolder';" +
            "document.getElementsByName('CVVNum')[0].value = '$cvv';" +
            "document.getElementsByName('ATMPIN')[0].value = '';" +
            "document.getElementsByName('ExpDtMon')[0].value = '$expMon';" +
            "document.getElementsByName('ExpDtYr')[0].value = '$expYr';" +
            "if('$cvv' != '')" +
            "document.getElementsByName('btnPay')[0].click();" +
            "Step.setStep('8');" +
            "}";
        break;
      case "HDFC Bank":
        if (step < 8) {
          script =
              "document.getElementById('debitCardNumber').value = '$cardNo';" +
              "document.getElementById('debitCardholderName').value = '$cardHolder';" +
              "document.getElementById('cardPin').value = '';" +
              "document.getElementById('debiMonthSelect').value = '${int.parse(expMon)}';" +
              "document.getElementById('debiYearSelect').value = '$expYr';" +
              "var url = document.getElementById('captcha_image').src;" +
              "Step.solveCaptcha(url, 1, 2);" +
              "Step.setStep('8');";
        } else if (step == 8) {
          script =
              "if(document.getElementById('cmdSubmitStatic') != null) {" +
              "document.getElementsByClassName('tab-section')[1].click();\n" +
              "setTimeout(function() {" +
              "document.getElementById('txtPassword').value = '';\n" +
              "}, 1001);" +
              "document.getElementById('cmdSubmitStatic').click();" +
              "Step.setStep('9');" +
              "}";
        }
        break;
      case "United Bank of India":
        script =
            "if(document.getElementById('txtboxCardNum') != null && document.getElementById('txtboxUserName') != null && " +
            "document.getElementById('dropdownMonth') != null && document.getElementById('dropdownYear') != null && " +
            "document.getElementById('btnGetOTP') != null && document.getElementById('txtCaptcha') != null) {" +
            "document.getElementById('txtboxCardNum').value = '$cardNo';" +
            "document.getElementById('txtboxUserName').value = '$cardHolder';" +
            "document.getElementById('dropdownMonth').value = '${int.parse(expMon)}';" +
            "document.getElementById('dropdownYear').value = '$expYr';" +
            "document.getElementById('btnGetOTP').click();" +
            "Step.setStep('8');" +
            "}";
        break;
      default:
        break;
    }

    jsCode =
        "javascript:function guruHoJaShuru() {" +
        script +
        "}" +
        "guruHoJaShuru()";

    return jsCode;
  }

  // Robust UPI/Wallet helpers: try common selectors and trigger app open
  String _buildUpiFillAndSubmit(String upiId) {
    return "(function(){try{" +
        // Try common VPA inputs
        "var inputs = Array.from(document.querySelectorAll('input,textarea'));" +
        "var v=null; for(var i=0;i<inputs.length;i++){var n=(inputs[i].name||'').toLowerCase(); var id=(inputs[i].id||'').toLowerCase();" +
        "if(n.includes('vpa')||n.includes('upi')||id.includes('vpa')||id.includes('upi')){v=inputs[i];break;}}" +
        "if(v){v.focus(); v.value='$upiId'; v.dispatchEvent(new Event('input',{bubbles:true})); v.dispatchEvent(new Event('change',{bubbles:true}));}" +
        // Click any Pay/Submit/Verify button
        "var btns = Array.from(document.querySelectorAll('button,input[type=button],input[type=submit],a'));" +
        "for(var i=0;i<btns.length;i++){var t=(btns[i].innerText||btns[i].value||'').toLowerCase(); if(t.includes('pay')||t.includes('submit')||t.includes('verify')){btns[i].click(); break;}}" +
        // Ask native to open UPI app
        "if(window.Step){ Step.openUpiApp(); }" +
        "}catch(e){};})();";
  }

  String walletJS(
    String walletName,
    String amount,
    String mobile,
    String otp,
    int step,
  ) {
    String script =
        "(function(){try{" +
        // Fill mobile/amount/otp if present
        "var m=document.querySelector('input[name*=mobile],input[id*=mobile]'); if(m){m.value='$mobile'; m.dispatchEvent(new Event('input',{bubbles:true}));}" +
        "var a=document.querySelector('input[name*=amount],input[id*=amount]'); if(a){a.value='$amount'; a.dispatchEvent(new Event('input',{bubbles:true}));}" +
        "var o=document.querySelector('input[name*=otp],input[id*=otp]'); if(o){o.value='$otp';}" +
        // Click any Pay/Proceed
        "var btns = Array.from(document.querySelectorAll('button,input[type=button],input[type=submit],a'));" +
        "for(var i=0;i<btns.length;i++){var t=(btns[i].innerText||btns[i].value||'').toLowerCase(); if(t.includes('pay')||t.includes('proceed')||t.includes('continue')){btns[i].click(); break;}}" +
        "}catch(e){};})();";

    return "javascript:" + script;
  }

  String phonepeUpi(
    String upiId,
    String amount,
    String mobile,
    String otp,
    int step,
  ) {
    final script = _buildUpiFillAndSubmit(upiId);
    return "javascript:" + script;
  }

  String iPayUpi(
    String upiId,
    String amount,
    String mobile,
    String otp,
    int step,
  ) {
    final script = _buildUpiFillAndSubmit(upiId);
    return "javascript:" + script;
  }

  String paytmUpi(
    String upiId,
    String amount,
    String mobile,
    String otp,
    int step,
  ) {
    final script = _buildUpiFillAndSubmit(upiId);
    return "javascript:" + script;
  }

  String pod(
    String podName,
    String amount,
    String mobile,
    String otp,
    int step,
  ) {
    String script =
        "(function(){try{" +
        "var m=document.querySelector('input[name*=mobile],input[id*=mobile]'); if(m){m.value='$mobile'; m.dispatchEvent(new Event('input',{bubbles:true}));}" +
        "var btns = Array.from(document.querySelectorAll('button,input[type=button],input[type=submit],a'));" +
        "for(var i=0;i<btns.length;i++){var t=(btns[i].innerText||btns[i].value||'').toLowerCase(); if(t.includes('confirm')||t.includes('submit')||t.includes('proceed')){btns[i].click(); break;}}" +
        "}catch(e){};})();";
    return "javascript:" + script;
  }
}
