/**
 * AUTHOR : LOKESH
 * DATE : 19-07-25.
 * GIT : telestic1
 */

class CryptoProvider {
  final String name;
  final double version;
  final String info;
  final Map<String, String> _properties = {};

  /**
   * Creates a Provider and puts parameters
   */
  CryptoProvider()
    : name = "Crypto",
      version = 1.0,
      info = "HARMONY (SHA1 digest; SecureRandom; SHA1withDSA signature)" {
    put(
      "SecureRandom.SHA1PRNG",
      "org.apache.harmony.security.provider.crypto.SHA1PRNG_SecureRandomImpl",
    );
    put("SecureRandom.SHA1PRNG ImplementedIn", "Software");
  }

  void put(String key, String value) {
    _properties[key] = value;
  }

  String? get(String key) {
    return _properties[key];
  }

  Map<String, String> getProperties() {
    return Map.from(_properties);
  }
}
