// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get irctc_ipay => 'IRCTC iPay (Credit Card/Debit Card/UPI)';

  @override
  String get ipay_option =>
      'Credit cards/ Debit cards / UPI (Powered by IRCTC)';

  @override
  String get upi_bhim_ussd => 'BHIM/ UPI/ USSD';

  @override
  String get net_banking => 'Net Banking';

  @override
  String get irctc_ewallet => 'IRCTC eWallet';

  @override
  String get payment_gateway_credit_card =>
      'Payment Gateway / Credit Card / Debit Card';

  @override
  String get cash_card_wallets => 'Wallets / Cash Card';

  @override
  String get irctc_prepaid => 'Multiple Payment Service';

  @override
  String get scan_pay => 'Bharat QR / Scan & Pay';

  @override
  String get manual_payment => 'I will fill payment information manually';

  @override
  String get info_secured =>
      '* Your complete payment information is encrypted and secured';

  @override
  String get payment_option => 'Payment Method';

  @override
  String get select_payment_method => 'Select Payment Method';

  @override
  String get autofill_otp => 'Autofill OTP';

  @override
  String get advanced => 'Advanced';

  @override
  String get payment_selection_time => 'Payment selection time';

  @override
  String get payment_info_encrypted =>
      '* Your complete payment information is encrypted and secured';

  @override
  String get hdfc_static_password_hint =>
      'Fastest with HDFC Card Static Password';
}
