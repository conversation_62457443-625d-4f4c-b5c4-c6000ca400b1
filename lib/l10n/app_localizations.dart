import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('en')];

  /// No description provided for @irctc_ipay.
  ///
  /// In en, this message translates to:
  /// **'IRCTC iPay (Credit Card/Debit Card/UPI)'**
  String get irctc_ipay;

  /// No description provided for @ipay_option.
  ///
  /// In en, this message translates to:
  /// **'Credit cards/ Debit cards / UPI (Powered by IRCTC)'**
  String get ipay_option;

  /// No description provided for @upi_bhim_ussd.
  ///
  /// In en, this message translates to:
  /// **'BHIM/ UPI/ USSD'**
  String get upi_bhim_ussd;

  /// No description provided for @net_banking.
  ///
  /// In en, this message translates to:
  /// **'Net Banking'**
  String get net_banking;

  /// No description provided for @irctc_ewallet.
  ///
  /// In en, this message translates to:
  /// **'IRCTC eWallet'**
  String get irctc_ewallet;

  /// No description provided for @payment_gateway_credit_card.
  ///
  /// In en, this message translates to:
  /// **'Payment Gateway / Credit Card / Debit Card'**
  String get payment_gateway_credit_card;

  /// No description provided for @cash_card_wallets.
  ///
  /// In en, this message translates to:
  /// **'Wallets / Cash Card'**
  String get cash_card_wallets;

  /// No description provided for @irctc_prepaid.
  ///
  /// In en, this message translates to:
  /// **'Multiple Payment Service'**
  String get irctc_prepaid;

  /// No description provided for @scan_pay.
  ///
  /// In en, this message translates to:
  /// **'Bharat QR / Scan & Pay'**
  String get scan_pay;

  /// No description provided for @manual_payment.
  ///
  /// In en, this message translates to:
  /// **'I will fill payment information manually'**
  String get manual_payment;

  /// No description provided for @info_secured.
  ///
  /// In en, this message translates to:
  /// **'* Your complete payment information is encrypted and secured'**
  String get info_secured;

  /// No description provided for @payment_option.
  ///
  /// In en, this message translates to:
  /// **'Payment Method'**
  String get payment_option;

  /// No description provided for @select_payment_method.
  ///
  /// In en, this message translates to:
  /// **'Select Payment Method'**
  String get select_payment_method;

  /// No description provided for @autofill_otp.
  ///
  /// In en, this message translates to:
  /// **'Autofill OTP'**
  String get autofill_otp;

  /// No description provided for @advanced.
  ///
  /// In en, this message translates to:
  /// **'Advanced'**
  String get advanced;

  /// No description provided for @payment_selection_time.
  ///
  /// In en, this message translates to:
  /// **'Payment selection time'**
  String get payment_selection_time;

  /// No description provided for @payment_info_encrypted.
  ///
  /// In en, this message translates to:
  /// **'* Your complete payment information is encrypted and secured'**
  String get payment_info_encrypted;

  /// No description provided for @hdfc_static_password_hint.
  ///
  /// In en, this message translates to:
  /// **'Fastest with HDFC Card Static Password'**
  String get hdfc_static_password_hint;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
