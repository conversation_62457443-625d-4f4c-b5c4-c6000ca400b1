import 'package:flutter/material.dart';
import '../services/accessibility_service.dart';

class AccessibilityServiceWidget extends StatefulWidget {
  const AccessibilityServiceWidget({Key? key}) : super(key: key);

  @override
  State<AccessibilityServiceWidget> createState() =>
      _AccessibilityServiceWidgetState();
}

class _AccessibilityServiceWidgetState
    extends State<AccessibilityServiceWidget> {
  final AccessibilityService _accessibilityService =
      AccessibilityService.instance;
  bool _isServiceEnabled = false;
  Map<String, dynamic> _serviceStatus = {};
  String _statusMessage = 'Service not initialized';

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    try {
      await _accessibilityService.initialize();
      await _checkServiceStatus();
      _listenToServiceEvents();
    } catch (e) {
      setState(() {
        _statusMessage = 'Failed to initialize service: $e';
      });
    }
  }

  Future<void> _checkServiceStatus() async {
    try {
      final isEnabled = await _accessibilityService
          .isAccessibilityServiceEnabled();
      final status = await _accessibilityService.getServiceStatus();

      setState(() {
        _isServiceEnabled = isEnabled;
        _serviceStatus = status;
        _statusMessage = isEnabled
            ? 'Service is enabled'
            : 'Service is disabled';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error checking status: $e';
      });
    }
  }

  void _listenToServiceEvents() {
    // Listen to status updates
    _accessibilityService.statusStream.listen((event) {
      setState(() {
        _statusMessage = event['status'] ?? 'Unknown status';
      });
    });

    // Listen to ticket booking events
    _accessibilityService.ticketInfoStream.listen((ticketInfo) {
      _showTicketBookedDialog(ticketInfo);
    });

    // Listen to captcha events
    _accessibilityService.captchaStream.listen((captchaEvent) {
      if (captchaEvent['type'] == 'captcha_solved') {
        setState(() {
          _statusMessage = 'Captcha solved: ${captchaEvent['result']}';
        });
      }
    });
  }

  void _showTicketBookedDialog(Map<String, dynamic> ticketInfo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ticket Booked Successfully!'),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Status: ${ticketInfo['status'] ?? 'Unknown'}'),
            Text('PNR: ${ticketInfo['pnr'] ?? 'N/A'}'),
            Text('Amount: ${ticketInfo['amount'] ?? 'N/A'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _startService() async {
    try {
      // Example form data - in real app, this would come from user input
      final formData = {
        'formName': 'sample_form',
        'rcPin': '1234',
        'fromStation': 'NEW DELHI - NDLS',
        'toStation': 'MUMBAI CENTRAL - BCT',
        'journeyDate': '25-12-2024',
        'quota': 'TQ',
        'trainNo': '12951',
        'travelClass': '3A',
        'mobileNo': '9876543210',
        'targetPackage': 'cris.org.npci.upiapp',
      };

      await _accessibilityService.startService(formData);
      await _checkServiceStatus();

      setState(() {
        _statusMessage = 'Service started with form data';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Failed to start service: $e';
      });
    }
  }

  Future<void> _stopService() async {
    try {
      await _accessibilityService.stopService();
      await _checkServiceStatus();

      setState(() {
        _statusMessage = 'Service stopped';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Failed to stop service: $e';
      });
    }
  }

  Future<void> _openAccessibilitySettings() async {
    try {
      await _accessibilityService.openAccessibilitySettings();
    } catch (e) {
      setState(() {
        _statusMessage = 'Failed to open settings: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Accessibility Service Demo'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Service Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Enabled: ${_isServiceEnabled ? 'Yes' : 'No'}'),
                    Text('Status: $_statusMessage'),
                    if (_serviceStatus.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Current Screen: ${_serviceStatus['currentScreen'] ?? 'N/A'}',
                      ),
                      Text('Form Name: ${_serviceStatus['formName'] ?? 'N/A'}'),
                      Text(
                        'Running: ${_serviceStatus['isRunning'] ?? false ? 'Yes' : 'No'}',
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _openAccessibilitySettings,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text(
                'Open Accessibility Settings',
                style: TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: _isServiceEnabled ? _startService : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text(
                'Start Booking Service',
                style: TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: _isServiceEnabled ? _stopService : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text(
                'Stop Service',
                style: TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: _checkServiceStatus,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text(
                'Refresh Status',
                style: TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(height: 24),
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('1. Tap "Open Accessibility Settings"'),
                    Text('2. Find "Quick Tatkal" in the list'),
                    Text('3. Enable the accessibility service'),
                    Text('4. Return to app and tap "Start Booking Service"'),
                    Text('5. The service will automate ticket booking'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _accessibilityService.dispose();
    super.dispose();
  }
}
