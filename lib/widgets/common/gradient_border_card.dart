import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';

class GradientBorderCard extends StatelessWidget {
  final Widget child;
  final Color color;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const GradientBorderCard({
    Key? key,
    required this.child,
    this.color = kDialogColor,
    this.padding,
    this.margin,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: const EdgeInsets.all(1),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(kBigRadius),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(kBigRadius),
        ),
        padding: padding ?? const EdgeInsets.all(12),
        child: child,
      ),
    );
  }
}