class TrainStatus {
  String? stnName;
  String? stnCode;
  String? schArrival;
  String? actArrival;
  String? delayArrival;
  String? schDep;
  String? actDep;
  String? delayDep;
  String? passed;
  int? isLive;
  int? barImageType;
  int? day;
  int? isHalt;
  bool? _isIntermediate;
  int? intmStations;
  String? distance;
  bool? _isExpanded;

  TrainStatus(
    this.stnName,
    this.stnCode,
    this.schArrival,
    this.actArrival,
    this.delayArrival,
    this.schDep,
    this.actDep,
    this.delayDep,
    this.passed,
  ) {
    this.isLive = -1;
    this.barImageType = -1;
    this.day = -1;
    this.isHalt = -1;
  }

  String? getStnName() {
    return stnName;
  }

  String? getStnCode() {
    return stnCode;
  }

  String? getSchArrival() {
    return schArrival;
  }

  String? getActArrival() {
    return actArrival;
  }

  String? getDelayArrival() {
    return delayArrival;
  }

  String? getSchDep() {
    return schDep;
  }

  String? getActDep() {
    return actDep;
  }

  String? getDelayDep() {
    return delayDep;
  }

  int? getIsLive() {
    return isLive;
  }

  int? getBarImageType() {
    return barImageType;
  }

  void setIsLive(int isLive) {
    this.isLive = isLive;
  }

  void setBarImageType(int barImageType) {
    this.barImageType = barImageType;
  }

  int? getDay() {
    return day;
  }

  void setDay(int day) {
    this.day = day;
  }

  int? getIsHalt() {
    return isHalt;
  }

  void setIsHalt(int isHalt) {
    this.isHalt = isHalt;
  }

  String? getPassed() {
    return passed;
  }

  void setPassed(String passed) {
    this.passed = passed;
  }

  bool? isIntermediate() {
    return _isIntermediate;
  }

  void setIntermediate(bool intermediate) {
    _isIntermediate = intermediate;
  }

  int? getIntmStations() {
    return intmStations;
  }

  void setIntmStations(int intmStations) {
    this.intmStations = intmStations;
  }

  String? getDistance() {
    return distance;
  }

  void setDistance(String distance) {
    this.distance = distance;
  }

  bool? isExpanded() {
    return _isExpanded;
  }

  void setExpanded(bool expanded) {
    _isExpanded = expanded;
  }
}
