import 'TrainClass.dart';

class Train {
  String trainNo;
  String trainName;
  String depTime;
  String depCity;
  String time;
  String arrivalTime;
  String arrivalCity;
  List<TrainClass> classList;

  Train(
    this.trainNo,
    this.trainName,
    this.depTime,
    this.depCity,
    this.time,
    this.arrivalTime,
    this.arrivalCity,
    this.classList,
  );

  String getTrainNo() {
    return trainNo;
  }

  String getTrainName() {
    return trainName;
  }

  String getDepTime() {
    return depTime;
  }

  String getDepCity() {
    return depCity;
  }

  String getTime() {
    return time;
  }

  String getArrivalTime() {
    return arrivalTime;
  }

  String getArrivalCity() {
    return arrivalCity;
  }

  List<TrainClass> getClassList() {
    return classList;
  }
}
