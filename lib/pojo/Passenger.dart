class Passenger {
  String? name;
  int? age;
  String? gender;
  String? berthPref;
  int? seniorCitizen;
  String? meal;
  String? nationality;
  bool? bedRoll;
  String? cardNo;
  String? concession;
  String? dob;
  String? validity;
  String? idType;
  String? idNo;
  int? optBerth;

  String? getName() {
    return name;
  }

  void setName(String name) {
    this.name = name;
  }

  int? getAge() {
    return age;
  }

  void setAge(int age) {
    this.age = age;
  }

  String? getGender() {
    return gender;
  }

  void setGender(String gender) {
    this.gender = gender;
  }

  String? getBerthPref() {
    return berthPref;
  }

  void setBerthPref(String berthPref) {
    this.berthPref = berthPref;
  }

  int? getSeniorCitizen() {
    return seniorCitizen;
  }

  void setSeniorCitizen(int seniorCitizen) {
    this.seniorCitizen = seniorCitizen;
  }

  String? getMeal() {
    return meal;
  }

  void setMeal(String meal) {
    this.meal = meal;
  }

  String? getNationality() {
    return nationality;
  }

  void setNationality(String nationality) {
    this.nationality = nationality;
  }

  bool? isBedRoll() {
    return bedRoll;
  }

  void setBedRoll(bool bedRoll) {
    this.bedRoll = bedRoll;
  }

  String? getCardNo() {
    return cardNo;
  }

  void setCardNo(String cardNo) {
    this.cardNo = cardNo;
  }

  String? getConcession() {
    return concession;
  }

  void setConcession(String concession) {
    this.concession = concession;
  }

  String? getDob() {
    return dob;
  }

  void setDob(String dob) {
    this.dob = dob;
  }

  String? getValidity() {
    return validity;
  }

  void setValidity(String validity) {
    this.validity = validity;
  }

  String? getIdType() {
    return idType;
  }

  void setIdType(String idType) {
    this.idType = idType;
  }

  String? getIdNo() {
    return idNo;
  }

  void setIdNo(String idNo) {
    this.idNo = idNo;
  }

  bool isOptBerth() {
    return (optBerth == 1);
  }

  void setOptBerth(int optBerth) {
    this.optBerth = optBerth;
  }
}
