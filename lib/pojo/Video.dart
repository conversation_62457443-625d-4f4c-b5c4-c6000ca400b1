class Video {
  String videoUrl;
  String thumbUrl;
  String description;
  String info;
  String releaseDate;
  String duration;
  String firebaseEvent;

  Video(
    this.videoUrl,
    this.thumbUrl,
    this.description,
    this.info,
    this.releaseDate,
    this.duration,
    this.firebaseEvent,
  );

  String getVideoUrl() {
    return videoUrl;
  }

  String getThumbUrl() {
    return thumbUrl;
  }

  String getDescription() {
    return description;
  }

  String getInfo() {
    return info;
  }

  String getReleaseDate() {
    return releaseDate;
  }

  String getDuration() {
    return duration;
  }

  String getFirebaseEvent() {
    return firebaseEvent;
  }
}
