import 'dart:io';
import 'package:flutter/foundation.dart';

class SecurityHelper {
  static final SecurityHelper _instance = SecurityHelper._internal();

  factory SecurityHelper() => _instance;

  SecurityHelper._internal();

  // Matches installer and signature logic from Java
  Future<bool> securityCheck() async {
    bool installerSourceValid = false;
    if (kDebugMode) {
      installerSourceValid = true;
    } else {
      // Platform-specific code required via channel
      installerSourceValid = await _isInstallerSourceValid();
    }

    bool signatureValid = await isSignatureValid();

    return installerSourceValid &&
        signatureValid &&
        !(await isBlockedEnvironment()) &&
        !(await isDeviceRooted());
  }

  // Placeholder for release SHA signature validation
  Future<bool> isSignatureValid() async {
    try {
      // Platform channel required: Get APK signature SHA
      final List<String> validSignatures = [
        "J4hT2W+6q8CYoat6xrkVWLh9IsA=",
        "lwSpNlGFnsOoJCYdy7e3vNIAJUo=",
        "9iJdVNAMbVm/TJZunrmLPnZ2vFE=",
        "t1qR2tJ2qKcqy6dxagF6LLNv1TI=",
      ];
      // TODO: Integrate with platform channel to check real signature
      // String currentSignature = await getSignatureFromPlatform();
      // return validSignatures.contains(currentSignature);
      return true; // Assume true for debug
    } catch (e) {
      return false;
    }
  }

  Future<bool> isRunningOnEmulator() async {
    // Platform channel or plugin required for actual emulator check
    // e.g. Use 'emulator_check' plugin for Flutter
    // return await EmulatorCheck.isEmulator;
    if (Platform.isAndroid) {
      // Best effort checks
      List<String> emuIndicators = [
        'generic',
        'vbox',
        'test-keys',
        'google_sdk',
        'droid4x',
        'Emulator',
        'Android SDK built for x86',
        'Genymotion',
        'goldfish',
        'ranchu',
        'sdk',
        'google_sdk',
        'sdk_google',
        'sdk_x86',
        'vbox86p',
        'nox',
      ];
      // TODO: Get device info using 'device_info_plus' and check using above indicators.
      return false;
    }
    return false;
  }

  Future<bool> isBlockedEnvironment() async {
    if (!isEmulatorCheckRequired()) return false;
    return await isRunningOnEmulator();
  }

  bool isEmulatorCheckRequired() {
    return !kDebugMode;
  }

  Future<bool> isDeviceRooted() async {
    // For Android only, check known root paths
    List<String> paths = [
      "/system/app/Superuser.apk",
      "/sbin/su",
      "/system/bin/su",
      "/system/xbin/su",
      "/data/local/xbin/su",
      "/data/local/bin/su",
      "/system/sd/xbin/su",
      "/system/bin/failsafe/su",
      "/data/local/su",
    ];
    try {
      for (var path in paths) {
        if (await File(path).exists()) return true;
      }
    } catch (e) {
      // Ignore errors
    }
    return false;
  }

  // Platform channel stub for installer check
  Future<bool> _isInstallerSourceValid() async {
    // TODO: Implement using platform channel
    // Should call Android getInstallerPackageName and compare to 'com.android.vending'
    return true;
  }
}
