import 'dart:typed_data';
import 'dart:ui';
import 'package:image/image.dart' as img_lib;

class Solver2 {
  static const int w = 203, h = 50;
  static late int correctBackgroundColor;
  static const int expectedNumberLettersForThisType = 6,
      minNumberLettersForThisType = 4;

  static const int tmplNumber = 192;

  static final List<int> wTempl = [
    35,
    34,
    48,
    39,
    38,
    35,
    38,
    38,
    48,
    31,
    23,
    28,
    23,
    28,
    27,
    45,
    46,
    46,
    28,
    28,
    22,
    23,
    20,
    26,
    23,
    28,
    23,
    24,
    24,
    21,
    24,
    25,
    21,
    20,
    28,
    19,
    21,
    21,
    24,
    25,
    26,
    20,
    26,
    28,
    28,
    21,
    28,
    28,
    28,
    32,
    23,
    23,
    22,
    25,
    26,
    24,
    14,
    17,
    21,
    21,
    26,
    26,
    16,
    24,
    22,
    22,
    21,
    19,
    16,
    22,
    21,
    23,
    26,
    27,
    24,
    23,
    20,
    24,
    19,
    19,
    21,
    20,
    20,
    21,
    20,
    21,
    16,
    22,
    33,
    18,
    21,
    22,
    14,
    23,
    23,
    14,
    26,
    25,
    20,
    25,
    20,
    24,
    10,
    25,
    20,
    23,
    21,
    20,
    25,
    24,
    16,
    30,
    19,
    18,
    21,
    17,
    16,
    18,
    19,
    20,
    16,
    25,
    19,
    21,
    20,
    19,
    21,
    16,
    20,
    14,
    20,
    20,
    21,
    15,
    29,
    20,
    19,
    16,
    19,
    18,
    19,
    18,
    19,
    19,
    19,
    14,
    19,
    11,
    17,
    11,
    20,
    12,
    19,
    14,
    15,
    23,
    20,
    19,
    23,
    23,
    19,
    19,
    13,
    11,
    10,
    15,
    20,
    10,
    15,
    15,
    20,
    15,
    15,
    15,
    18,
    25,
    20,
    13,
    18,
    13,
    18,
    12,
    11,
    19,
    25,
    25,
    26,
    25,
    19,
    18,
    26,
    25,
  ];
  static final List<int> hTempl = [
    35,
    35,
    30,
    31,
    30,
    33,
    30,
    30,
    29,
    31,
    31,
    33,
    31,
    36,
    31,
    29,
    30,
    27,
    37,
    35,
    31,
    31,
    24,
    30,
    32,
    29,
    31,
    30,
    30,
    25,
    31,
    32,
    33,
    29,
    30,
    32,
    30,
    30,
    24,
    31,
    30,
    31,
    30,
    30,
    30,
    31,
    30,
    28,
    29,
    23,
    29,
    29,
    27,
    31,
    30,
    30,
    31,
    32,
    31,
    31,
    29,
    29,
    30,
    30,
    29,
    29,
    31,
    29,
    30,
    31,
    29,
    30,
    31,
    30,
    30,
    30,
    31,
    31,
    32,
    30,
    31,
    30,
    31,
    27,
    28,
    31,
    30,
    31,
    22,
    24,
    30,
    30,
    32,
    30,
    30,
    32,
    30,
    30,
    31,
    29,
    24,
    29,
    36,
    30,
    30,
    30,
    30,
    31,
    30,
    32,
    31,
    24,
    30,
    30,
    31,
    31,
    31,
    30,
    23,
    30,
    30,
    30,
    31,
    30,
    30,
    31,
    30,
    32,
    29,
    29,
    31,
    24,
    30,
    29,
    11,
    30,
    23,
    31,
    31,
    24,
    31,
    27,
    23,
    24,
    23,
    30,
    23,
    37,
    30,
    38,
    30,
    30,
    19,
    27,
    29,
    30,
    30,
    30,
    30,
    30,
    30,
    29,
    24,
    31,
    34,
    29,
    30,
    37,
    31,
    31,
    30,
    31,
    29,
    28,
    30,
    30,
    30,
    24,
    30,
    24,
    30,
    24,
    24,
    30,
    30,
    30,
    12,
    30,
    30,
    18,
    12,
    12,
  ];
  static final List<String> retStrTempl = [
    "@",
    "@",
    "Ue",
    "W",
    "W",
    "@",
    "W",
    "W",
    "He",
    "@",
    "6",
    "@",
    "g",
    "Q",
    "G",
    "bY",
    "hY",
    "nY",
    "tJ",
    "Q",
    "b",
    "d",
    "e",
    "R",
    "9",
    "W",
    "Z",
    "D",
    "X",
    "e",
    "K",
    "G",
    "b",
    "9",
    "M",
    "7",
    "g",
    "g",
    "X",
    "G",
    "D",
    "3",
    "M",
    "M",
    "V",
    "q",
    "M",
    "V",
    "M",
    "m",
    "6",
    "K",
    "g",
    "U",
    "K",
    "R",
    "9",
    "3",
    "q",
    "d",
    "D",
    "D",
    "D",
    "R",
    "d",
    "q",
    "h",
    "X",
    "K",
    "G",
    "q",
    "N",
    "X",
    "V",
    "R",
    "N",
    "E",
    "R",
    "E",
    "7",
    "q",
    "9",
    "k",
    "n",
    "6",
    "k",
    "R",
    "Y",
    "m",
    "a",
    "b",
    "R",
    "3",
    "U",
    "U",
    "3",
    "V",
    "Z",
    "k",
    "Y",
    "e",
    "N",
    "J",
    "Y",
    "P",
    "U",
    "P",
    "U",
    "Z",
    "T",
    "4",
    "m",
    "3",
    "F",
    "P",
    "Z",
    "f",
    "Z",
    "u",
    "P",
    "D",
    "H",
    "h",
    "4",
    "b",
    "F",
    "4",
    "k",
    "h",
    "t",
    "L",
    "n",
    "4",
    "t",
    "=",
    "P",
    "a",
    "f",
    "h",
    "e",
    "h",
    "Y",
    "u",
    "n",
    "a",
    "H",
    "u",
    "J",
    "7",
    "J",
    "T",
    "F",
    "=",
    "t",
    "t",
    "H",
    "E",
    "7",
    "H",
    "H",
    "7",
    "7",
    "r",
    "L",
    "J",
    "t",
    "E",
    "J",
    "f",
    "f",
    "E",
    "f",
    "t",
    "t",
    "F",
    "T",
    "E",
    "r",
    "F",
    "r",
    "F",
    "r",
    "r",
    "L",
    "T",
    "T",
    "=",
    "T",
    "L",
    "=",
    "=",
    "=",
  ];

  // vertDensTempl will be computed from templates at runtime
  static final List<List<List<bool>>> templates = List.generate(
    tmplNumber,
    (_) => List.generate(50, (_) => List.generate(50, (_) => false)),
  );

  final List<List<bool>> biteArr = List.generate(
    w,
    (_) => List.generate(h, (_) => false),
  );
  final List<int> vertDensThisCaptcha1 = List.filled(w, 0);
  final List<String> findedLetters1 = List.filled(
    expectedNumberLettersForThisType,
    "",
  );
  final List<int> findedXs1 = List.filled(expectedNumberLettersForThisType, -1);

  // Will be computed after building templates
  late final List<List<int>> vertDensTempl;

  // Packed template data copied from Java (top and bottom halves)
  static const String _kTopPacked = r"""
long[][] etByteImage_Top = {new long[] {2095104L, 16776704L, 67108736L, 268435392L, 536870880L, 1073741792L, 2131754992L, 4228906992L, 4161785848L, 4028621820L, 4060084478L, 3254777982L, 3254777982L, 3288333374L, 2214591550L, 2277538847L, 2277522975L, 125845023L, 125836831L, 58727438L, 58727438L, 62921742L, 66321422L, 67107854L, 134217230L, 3489660750L, 4026531820L, 4294967292L, 4294508540L, 3354984440L, 3354984440L, 67108848L, 67108832L, 33554368L, 16777088L},new long[] {2095104L, 16776704L, 67108736L, 268435392L, 536870880L, 1073741792L, 2131754992L, 4228906992L, 4161785848L, 4028621820L, 4060084476L, 3254777982L, 3254777982L, 3288333374L, 2214591550L, 2277538847L, 2277522975L, 125845023L, 125836831L, 58727438L, 58727438L, 62921742L, 66321422L, 67107854L, 134217230L, 3489660750L, 4026531820L, 4294967292L, 4294508540L, 3354984440L, 3354919928L, 66978800L, 66979808L, 33554368L},new long[] {16777208L, 32505862L, 133693446L, 536346630L, 536870910L, 1073741822L, 1073741822L, 1072693248L, 1056964608L, 503316480L, 469762048L, 469762048L, 469762048L, 469762048L, 469762048L, 503316480L, 520093696L, 535822336L, 536870904L, 534773761L, 267386880L, 267386880L, 134217720L, 67108863L, 16777214L, 8387584L, 8387584L, 8388096L, 33554176L, 67108736L, 134217600L, 268435392L, 268435392L, 536807360L, 1040648128L, 1040648128L, 1007093696L, 1007093696L, 1007126464L, 1040699328L, 1057488832L, 520355776L, 520226752L, 520226752L, 260179904L, 235142656L, 100793344L, 126976L},
... (rest of Java etByteImage_Top content) ...
""";
  static const String _kBottomPacked = r"""
long[][] etByteImage_Bottom = {new long[] {0L, 0L, 0L, 0L, 0L, 0L, 0L, 1L, 3L, 7L, 7L, 7L, 15L, 15L, 15L, 15L, 15L, 0L, 0L, 0L, 0L, 0L, 14L, 14L, 14L, 15L, 15L, 15L, 7L, 7L, 3L, 0L, 0L, 0L, 0L},new long[] {0L, 0L, 0L, 0L, 0L, 0L, 0L, 1L, 3L, 7L, 7L, 7L, 15L, 15L, 15L, 15L, 15L, 0L, 0L, 0L, 0L, 0L, 14L, 14L, 14L, 15L, 15L, 15L, 7L, 7L, 3L, 0L, 0L, 0L},new long[] {0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L},
... (rest of Java etByteImage_Bottom content) ...
""";

  static List<List<int>> _parsePacked(String packed) {
    final values = <int>[];
    int sign = 1;
    int current = 0;
    bool inNumber = false;
    for (int i = 0; i < packed.length; i++) {
      final c = packed.codeUnitAt(i);
      if (c == 45) {
        // '-'
        sign = -1;
      } else if (c >= 48 && c <= 57) {
        inNumber = true;
        current = current * 10 + (c - 48);
      } else {
        if (inNumber) {
          values.add(sign * current);
          current = 0;
          sign = 1;
          inNumber = false;
        }
      }
    }
    if (inNumber) {
      values.add(sign * current);
    }
    final result = <List<int>>[];
    int idx = 0;
    for (int i = 0; i < tmplNumber; i++) {
      final len = wTempl[i];
      result.add(values.sublist(idx, idx + len));
      idx += len;
    }
    return result;
  }

  Solver2() {
    correctBackgroundColor = const Color.fromARGB(255, 4, 65, 76).value;
    // Initialize templates from packed bit arrays
    final top = _parsePacked(_kTopPacked);
    final bottom = _parsePacked(_kBottomPacked);
    for (int i = 0; i < tmplNumber; i++) {
      final wC = wTempl[i];
      for (int x = 0; x < wC; x++) {
        final vTop = top[i][x];
        for (int y = 0; y < 32; y++) {
          templates[i][x][y] = ((1 << y) & vTop) != 0;
        }
        final vBottom = bottom[i][x];
        for (int y = 32; y < hTempl[i]; y++) {
          templates[i][x][y] = ((1 << (y - 31)) & vBottom) != 0;
        }
      }
    }
    // Compute vertical densities per template column
    vertDensTempl = List.generate(
      tmplNumber,
      (i) => List.generate(wTempl[i], (x) {
        int c = 0;
        for (int y = 0; y < hTempl[i]; y++) {
          if (templates[i][x][y]) c++;
        }
        return c;
      }),
    );
    print("Initialisation done");
  }

  void getVertDens() {
    for (int x = 0; x < w; x++) {
      vertDensThisCaptcha1[x] = 0;
      for (int y = 0; y < h; y++) {
        if (biteArr[x][y]) {
          vertDensThisCaptcha1[x]++;
        }
      }
    }
  }

  String solve(Uint8List imgBytes) {
    final img = img_lib.decodeImage(imgBytes);
    if (img == null) {
      print("Could not decode image bytes");
      return "";
    }
    if (img.width != w || img.height != h) {
      print("Incorrect size");
      return "";
    }
    if (getPixelArgb(img, 0, 0) != correctBackgroundColor ||
        getPixelArgb(img, w - 1, 0) != correctBackgroundColor ||
        getPixelArgb(img, w - 1, h - 1) != correctBackgroundColor ||
        getPixelArgb(img, 0, h - 1) != correctBackgroundColor) {
      print("Incorrect image type");
      return "";
    }
    int x, x0, y, y0, wEtalon, hEtalon, i;
    binarisation(img);

    for (i = 0; i < expectedNumberLettersForThisType; i++) {
      findedLetters1[i] = "";
      findedXs1[i] = -1;
    }
    getVertDens();
    int yStart = 0, yFin = 55;
    int j = 0;
    for (i = 0; i < tmplNumber; i++) {
      wEtalon = wTempl[i];
      hEtalon = hTempl[i];
      for (x0 = 0; (x0 <= 200 && x0 <= (w - wEtalon)); x0++) {
        bool thisSymbGoodByDens = true;
        for (x = 0; ((x < wEtalon) && (thisSymbGoodByDens)); x++) {
          if (vertDensTempl[i][x] > vertDensThisCaptcha1[x + x0]) {
            thisSymbGoodByDens = false;
          }
        }
        if (thisSymbGoodByDens) {
          for (y0 = yStart; ((y0 <= yFin) && (y0 <= (h - hEtalon))); y0++) {
            bool goodResult = true;
            for (x = 0; ((x < wEtalon) && (goodResult)); x++) {
              for (y = 0; ((y < hEtalon) && (goodResult)); y++) {
                if (templates[i][x][y]) {
                  if (!biteArr[x + x0][y + y0]) {
                    goodResult = false;
                  }
                }
              }
            }
            if (goodResult) {
              findedLetters1[j] = retStrTempl[i];
              findedXs1[j] = x0 + (0.5 * wTempl[i]).round();
              weFindALeter(j, x0, y0, wEtalon, hEtalon);
              j++;
              if (j == expectedNumberLettersForThisType) {
                return orderingRes(j);
              }
            }
          }
        }
      }
    }
    return orderingRes(j);
  }

  void weFindALeter(int j, int x0, int y0, int wEtalon, int hEtalon) {
    for (int y = 0; y < h; y++) {
      for (int x = x0 + 2; x < x0 + wEtalon - 1; x++) {
        biteArr[x][y] = false;
      }
    }
    for (int y = y0 + hEtalon ~/ 2 - 5; y < y0 + hEtalon ~/ 2 + 5; y++) {
      for (int x = x0 + 1; x < x0 + 2; x++) {
        biteArr[x][y] = false;
      }
      for (int x = x0 + wEtalon - 1; x < x0 + wEtalon; x++) {
        biteArr[x][y] = false;
      }
    }
    int minDist = 30;
    for (int i = 0; i < j - 1; i++) {
      int curDist = (findedXs1[j] - findedXs1[i]).abs();
      if (curDist < minDist) {
        int xMin = findedXs1[j] < findedXs1[i] ? findedXs1[j] : findedXs1[i];
        int xMax = findedXs1[j] > findedXs1[i] ? findedXs1[j] : findedXs1[i];
        for (int x = xMin; x < xMax; x++) {
          for (int y = 0; y < h; y++) {
            biteArr[x][y] = false;
          }
        }
      }
    }
    getVertDens();
  }

  String orderingRes(int n) {
    String res = "";
    String swapStr;
    int swapInt;
    for (int i = 0; i < n - 1; i++) {
      for (int j = 0; j < n - 1; j++) {
        if (findedXs1[j] > findedXs1[j + 1]) {
          swapInt = findedXs1[j + 1];
          findedXs1[j + 1] = findedXs1[j];
          findedXs1[j] = swapInt;

          swapStr = findedLetters1[j + 1];
          findedLetters1[j + 1] = findedLetters1[j];
          findedLetters1[j] = swapStr;
        }
      }
    }

    for (int i = 0; i < n; i++) {
      res += findedLetters1[i];
    }
    if (res.length < minNumberLettersForThisType) return "";
    return res;
  }

  void binarisation(img_lib.Image img) {
    for (int x0 = 0; x0 < w; x0++) {
      biteArr[x0][0] = true;
      biteArr[x0][h - 1] = true;
    }
    for (int x0 = 1; x0 < w - 1; x0++) {
      for (int y0 = 1; y0 < h - 1; y0++) {
        bool haveOneNotStandColor = false;
        int firstColor = getPixelArgb(img, x0, y0);
        for (int x = x0 - 1; x < x0 + 2 && !haveOneNotStandColor; x++) {
          for (int y = y0 - 1; y < y0 + 2 && !haveOneNotStandColor; y++) {
            int clr = getPixelArgb(img, x, y);
            if (clr != firstColor) {
              haveOneNotStandColor = true;
              break;
            }
          }
        }
        biteArr[x0][y0] = haveOneNotStandColor;
      }
    }
  }

  int getPixelArgb(img_lib.Image img, int x, int y) {
    final p = img.getPixel(x, y);
    final a = p.a.toInt();
    final r = p.r.toInt();
    final g = p.g.toInt();
    final b = p.b.toInt();
    return (a << 24) | (r << 16) | (g << 8) | b;
  }
}
