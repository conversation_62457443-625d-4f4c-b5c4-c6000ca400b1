import 'package:flutter/material.dart';
import '../pojo/AutocompleteTrain.dart';

// Equivalent to AutocompleteTrainAdapter with same variables and methods
typedef OnTrainSelected = void Function(AutocompleteTrain train);

class AutocompleteTrainAdapter extends StatelessWidget {
  // trains: the list of train objects to show
  final List<AutocompleteTrain> trains;

  // onTrainSelected: callback for item tap (use for search dialog selection)
  final OnTrainSelected? onTrainSelected;

  const AutocompleteTrainAdapter({
    Key? key,
    required this.trains,
    this.onTrainSelected,
  }) : super(key: key);

  // Mimics MyViewHolder in Java
  static Widget MyViewHolder({
    required AutocompleteTrain trainInfo,
    required VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Card(
          child: Container(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      trainInfo.getTrainNo(),
                      key: const Key('trainNo'),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        trainInfo.getTrainName(),
                        key: const Key('trainName'),
                        style: const TextStyle(fontSize: 16),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          Text(
                            trainInfo.getSrcStn(),
                            key: const Key('fromStn'),
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          if (trainInfo.getSrcArrival().isNotEmpty) ...[
                            const SizedBox(width: 8),
                            Text(
                              trainInfo.getSrcArrival(),
                              key: const Key('fromArr'),
                              style: const TextStyle(color: Colors.grey),
                            ),
                          ],
                        ],
                      ),
                    ),
                    const Text('→'),
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            trainInfo.getDestStn(),
                            key: const Key('toStn'),
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          if (trainInfo.getDestArrival().isNotEmpty) ...[
                            const SizedBox(width: 8),
                            Text(
                              trainInfo.getDestArrival(),
                              key: const Key('toArr'),
                              style: const TextStyle(color: Colors.grey),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Equivalent to getView(int position, View view, ViewGroup parent)
  Widget getView(int position, Widget? view, BuildContext parent) {
    final AutocompleteTrain trainInfo = trains[position];
    return MyViewHolder(
      trainInfo: trainInfo,
      onTap: onTrainSelected != null ? () => onTrainSelected!(trainInfo) : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: trains.length,
      itemBuilder: (context, position) {
        return getView(position, null, context);
      },
    );
  }
}
