import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:quick_tatkal_v2/screens/splash_screen.dart';
import 'package:timezone/data/latest_all.dart' as tz;

import '../core/helper/mixpanel_manager.dart';

class NotificationWorker {
  final BuildContext context;
  final MixpanelManager mixpanel;

  NotificationWorker(this.context) : mixpanel = MixpanelManager();

  /// Equivalent to Java's doWork
  Future<void> doWork() async {
    await sendNotification();
  }

  /// Equivalent to sendNotification()
  Future<void> sendNotification() async {
    mixpanel.track("Renewal notification sent");
    try {
      await FirebaseAnalytics.instance.logEvent(
        name: "renewal_notification_sent",
      );
    } catch (_) {}
    await showRenewalNotification();
  }

  /// Show the notification as per getRenewalNotification
  Future<void> showRenewalNotification() async {
    const notificationChannelId = "20001";
    const notificationChannelName = "Renewals";
    const title = "\uD83D\uDD52 Pack Expired! Renew Now \uD83C\uDF1F";
    const message =
        "Your Quick Tatkal subscription has ended! \uD83D\uDE22 "
        "Renew now to keep enjoying all the exclusive benefits and features. Tap here to renew and stay on top while booking tatkal tickets! \uD83D\uDE80\n";
    final FlutterLocalNotificationsPlugin notifications =
        FlutterLocalNotificationsPlugin();

    tz.initializeTimeZones();
    await notifications.initialize(
      const InitializationSettings(
        android: AndroidInitializationSettings('qt_notif_3'),
        iOS: DarwinInitializationSettings(),
      ),
      onDidReceiveNotificationResponse: (NotificationResponse details) async {
        if (details.payload == 'RENEW') {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const SplashScreen(),
              settings: const RouteSettings(arguments: {'ORIGIN': 'RENEW'}),
            ),
            (route) => false,
          );
        }
      },
    );

    const androidChannel = AndroidNotificationChannel(
      notificationChannelId,
      notificationChannelName,
      description: 'Renewal reminders for Quick Tatkal',
      importance: Importance.high,
      enableLights: true,
      ledColor: Colors.cyan,
      enableVibration: true,
    );
    await notifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(androidChannel);

    await notifications.show(
      20001,
      title,
      message,
      NotificationDetails(
        android: AndroidNotificationDetails(
          androidChannel.id,
          androidChannel.name,
          channelDescription: androidChannel.description,
          importance: Importance.high,
          priority: Priority.high,
          icon: 'qt_notif_3',
          styleInformation: BigTextStyleInformation(message),
          largeIcon: const DrawableResourceAndroidBitmap('qt_notif_3'),
        ),
        iOS: const DarwinNotificationDetails(),
      ),
      payload: 'RENEW',
    );
  }
}
