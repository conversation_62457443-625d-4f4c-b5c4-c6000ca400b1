import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter/material.dart';

class Cryptography {
  static const String _key = "akanksha"; // Default key from Java code

  /// Generate encryption key (matches Java generateKey method)
  static Uint8List generateKey(String keyString) {
    var bytes = utf8.encode(keyString);
    var digest = md5.convert(bytes);
    return Uint8List.fromList(digest.bytes);
  }

  /// Encrypt password using AES encryption
  static String encryptPassword(String password) {
    if (password.isEmpty) return "";
    try {
      final keyBytes = generateKey(_key);
      final key = encrypt.Key(keyBytes);
      final iv = encrypt.IV.fromSecureRandom(16);
      final encrypter = encrypt.Encrypter(encrypt.AES(key));

      final encrypted = encrypter.encrypt(password, iv: iv);

      // Combine IV and encrypted data
      final combined = Uint8List.fromList([...iv.bytes, ...encrypted.bytes]);
      return base64.encode(combined);
    } catch (e) {
      throw Exception('Encryption failed: $e');
    }
  }

  /// Decrypt password using AES decryption
  static String decryptPassword(String encryptedPassword) {
    if (encryptedPassword.isEmpty) return "";
    try {
      final keyBytes = generateKey(_key);
      final key = encrypt.Key(keyBytes);
      final encrypter = encrypt.Encrypter(encrypt.AES(key));

      final combined = base64.decode(encryptedPassword);

      // Extract IV and encrypted data
      final iv = encrypt.IV(Uint8List.fromList(combined.sublist(0, 16)));
      final encryptedData = Uint8List.fromList(combined.sublist(16));

      final encrypted = encrypt.Encrypted(encryptedData);
      return encrypter.decrypt(encrypted, iv: iv);
    } catch (e) {
      throw Exception('Decryption failed: $e');
    }
  }

  /// Encode data for storage (matches Java encodeFile method)
  static String encodeFile(String keyString, String data) {
    try {
      final keyBytes = generateKey(keyString);
      final key = encrypt.Key(keyBytes);
      final iv = encrypt.IV.fromSecureRandom(16);
      final encrypter = encrypt.Encrypter(encrypt.AES(key));

      final encrypted = encrypter.encrypt(data, iv: iv);

      // Combine IV and encrypted data
      final combined = Uint8List.fromList([...iv.bytes, ...encrypted.bytes]);
      return base64.encode(combined);
    } catch (e) {
      throw Exception('Encoding failed: $e');
    }
  }

  /// Decode data from storage (matches Java decodeFile method)
  static String decodeFile(String keyString, String encodedData) {
    try {
      final keyBytes = generateKey(keyString);
      final key = encrypt.Key(keyBytes);
      final encrypter = encrypt.Encrypter(encrypt.AES(key));

      final combined = base64.decode(encodedData);

      // Extract IV and encrypted data
      final iv = encrypt.IV(Uint8List.fromList(combined.sublist(0, 16)));
      final encryptedData = Uint8List.fromList(combined.sublist(16));

      final encrypted = encrypt.Encrypted(encryptedData);
      return encrypter.decrypt(encrypted, iv: iv);
    } catch (e) {
      throw Exception('Decoding failed: $e');
    }
  }

  /// Validate IRCTC username format
  static bool isValidUsername(String username) {
    if (username.isEmpty || username.length < 3) return false;

    // Basic validation - can be enhanced based on IRCTC requirements
    final regex = RegExp(r'^[a-zA-Z0-9][a-zA-Z0-9._-]*[a-zA-Z0-9]$');
    return regex.hasMatch(username);
  }

  /// Validate password strength
  static bool isValidPassword(String password) {
    if (password.isEmpty || password.length < 8) return false;

    // Basic validation - IRCTC typically requires:
    // - At least 8 characters
    // - At least one uppercase letter
    // - At least one lowercase letter
    // - At least one digit
    // - At least one special character
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialCharacters = password.contains(
      RegExp(r'[!@#$%^&*(),.?":{}|<>]'),
    );

    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }

  /// Get password strength score (0-4)
  static int getPasswordStrength(String password) {
    int score = 0;

    if (password.length >= 8) score++;
    if (password.contains(RegExp(r'[A-Z]'))) score++;
    if (password.contains(RegExp(r'[a-z]'))) score++;
    if (password.contains(RegExp(r'[0-9]'))) score++;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score++;

    return score > 4 ? 4 : score;
  }

  /// Get password strength text
  static String getPasswordStrengthText(int strength) {
    switch (strength) {
      case 0:
      case 1:
        return 'Weak';
      case 2:
        return 'Fair';
      case 3:
        return 'Good';
      case 4:
        return 'Strong';
      default:
        return 'Unknown';
    }
  }

  /// Get password strength color
  static Color getPasswordStrengthColor(int strength) {
    switch (strength) {
      case 0:
      case 1:
        return Colors.red;
      case 2:
        return Colors.orange;
      case 3:
        return Colors.blue;
      case 4:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
