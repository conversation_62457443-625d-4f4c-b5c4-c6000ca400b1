import 'dart:typed_data';
import 'dart:ui';

class Solver {
  static const int w = 203, h = 50;
  static int correctBackgroundColor = Color.fromARGB(255, 4, 65, 76).value;
  static const int expectedNumberLettersForThisType = 6,
      minNumberLettersForThisType = 4;

  static const int tmplNumber = 76;

  // TODO: Fill out these constants with actual values from the Java arrays
  static const List<int> wTempl = [];
  static const List<int> hTempl = [];
  static const List<String> retStrTempl = [];
  static const List<List<int>> vertDensTempl = [];
  static final List<List<List<bool>>> templates = List.generate(
    tmplNumber,
    (i) => List.generate(
      50,
      (x) => List.generate(50, (y) => false, growable: false),
      growable: false,
    ),
    growable: false,
  );

  // state
  late List<List<bool>> biteArr;
  late List<int> vertDensThisCaptcha1;
  late List<String> findedLetters1;
  late List<int> findedXs1;

  Solver() {
    biteArr = List.generate(w, (_) => List.filled(h, false), growable: false);
    vertDensThisCaptcha1 = List.filled(w, 0);
    findedLetters1 = List.filled(expectedNumberLettersForThisType, '');
    findedXs1 = List.filled(expectedNumberLettersForThisType, -1);
    // TODO: Assign template data exactly as Java (too large for inline, insert all arrays here)
    // (template arrays assignments must exactly reflect the Java version)
  }

  void getVertDens() {
    for (int x = 0; x < w; x++) {
      vertDensThisCaptcha1[x] = 0;
      for (int y = 0; y < h; y++) {
        if (biteArr[x][y]) vertDensThisCaptcha1[x]++;
      }
    }
  }

  String solve(Image img) {
    // In Flutter, we use bytes, so must decode/convert: this assumes by-pixel access.
    // User must provide a decoded Image or utility for getPixel(x,y)
    if (img.width != w || img.height != h) {
      print("Incorrect size");
      return "";
    }
    // (Assume getPixel routine exists)
    if (getPixel(img, 0, 0) != correctBackgroundColor ||
        getPixel(img, w - 1, 0) != correctBackgroundColor ||
        getPixel(img, w - 1, h - 1) != correctBackgroundColor ||
        getPixel(img, 0, h - 1) != correctBackgroundColor) {
      print("Incorrect image type");
      return "";
    }
    binarisation(img);
    for (int i = 0; i < expectedNumberLettersForThisType; i++) {
      findedLetters1[i] = "";
      findedXs1[i] = -1;
    }
    getVertDens();
    int yStart = 0, yFin = 55;
    int j = 0;
    for (int i = 0; i < tmplNumber; i++) {
      // Skip if constants are not filled
      if (wTempl.isEmpty ||
          hTempl.isEmpty ||
          vertDensTempl.isEmpty ||
          retStrTempl.isEmpty) {
        throw Exception(
          'Fill in the arrays (wTempl, hTempl, retStrTempl, vertDensTempl) for functional parity.',
        );
      }
      int wEtalon = wTempl[i];
      int hEtalon = hTempl[i];
      for (int x0 = 0; (x0 <= 200 && x0 <= (w - wEtalon)); x0++) {
        bool thisSymbGoodByDens = true;
        for (int x = 0; ((x < wEtalon) && thisSymbGoodByDens); x++) {
          if (vertDensTempl[i][x] > vertDensThisCaptcha1[x + x0]) {
            thisSymbGoodByDens = false;
          }
        }
        if (thisSymbGoodByDens) {
          for (int y0 = yStart; ((y0 <= yFin) && (y0 <= (h - hEtalon))); y0++) {
            bool goodResult = true;
            for (int x = 0; ((x < wEtalon) && goodResult); x++) {
              for (int y = 0; ((y < hEtalon) && goodResult); y++) {
                if (templates[i][x][y]) {
                  if (!biteArr[x + x0][y + y0]) {
                    goodResult = false;
                  }
                }
              }
            }
            if (goodResult) {
              findedLetters1[j] = retStrTempl[i];
              findedXs1[j] = x0 + (0.5 * wTempl[i]).round();
              weFindALeter(j, x0, y0, wEtalon, hEtalon);
              j++;
              if (j == expectedNumberLettersForThisType) return orderingRes(j);
            }
          }
        }
      }
    }
    return orderingRes(j);
  }

  void weFindALeter(int j, int x0, int y0, int wEtalon, int hEtalon) {
    for (int y = 0; y < h; y++) {
      for (int x = x0 + 2; x < x0 + wEtalon - 1; x++) {
        biteArr[x][y] = false;
      }
    }
    for (int y = y0 + hEtalon ~/ 2 - 5; y < y0 + hEtalon ~/ 2 + 5; y++) {
      for (int x = x0 + 1; x < x0 + 2; x++) {
        biteArr[x][y] = false;
      }
      for (int x = x0 + wEtalon - 1; x < x0 + wEtalon; x++) {
        biteArr[x][y] = false;
      }
    }
    int minDist = 30;
    for (int i = 0; i < j - 1; i++) {
      int curDist = (findedXs1[j] - findedXs1[i]).abs();
      if (curDist < minDist) {
        int xMin = findedXs1[j] < findedXs1[i] ? findedXs1[j] : findedXs1[i];
        int xMax = findedXs1[j] > findedXs1[i] ? findedXs1[j] : findedXs1[i];
        for (int x = xMin; x < xMax; x++) {
          for (int y = 0; y < h; y++) {
            biteArr[x][y] = false;
          }
        }
      }
    }
    getVertDens();
  }

  String orderingRes(int n) {
    String res = "";
    for (int i = 0; i < n - 1; i++) {
      for (int j = 0; j < n - 1; j++) {
        if (findedXs1[j] > findedXs1[j + 1]) {
          int swapInt = findedXs1[j + 1];
          findedXs1[j + 1] = findedXs1[j];
          findedXs1[j] = swapInt;

          String swapStr = findedLetters1[j + 1];
          findedLetters1[j + 1] = findedLetters1[j];
          findedLetters1[j] = swapStr;
        }
      }
    }
    for (int i = 0; i < n; i++) res += findedLetters1[i];
    if (res.length < minNumberLettersForThisType) return "";
    return res;
  }

  void binarisation(Image img) {
    // This must call getPixel utility as Flutter Image itself doesn't expose per-pixel access directly.
    // User must supply byte-level decoded RGBA pixel access to work.
    for (int x0 = 0; x0 < w; x0++) {
      biteArr[x0][0] = true;
      biteArr[x0][h - 1] = true;
    }
    for (int x0 = 1; x0 < w - 1; x0++) {
      for (int y0 = 1; y0 < h - 1; y0++) {
        bool haveOneNotStandColor = false;
        int firstColor = getPixel(img, x0, y0);
        for (int x = x0 - 1; (x < (x0 + 2) && !haveOneNotStandColor); x++) {
          for (int y = y0 - 1; (y < (y0 + 2) && !haveOneNotStandColor); y++) {
            int clr = getPixel(img, x, y);
            if (clr != firstColor) {
              haveOneNotStandColor = true;
            }
          }
        }
        biteArr[x0][y0] = haveOneNotStandColor;
      }
    }
  }

  int getPixel(Image img, int x, int y) {
    // Replace with function that fetches pixel color from image bytes data
    throw UnimplementedError(
      'getPixel must be implemented with image bytes in Flutter',
    );
  }
}

// TODO: Fill in wTempl, hTempl, retStrTempl, vertDensTempl, templates arrays for full 1-to-1 port from Java.
