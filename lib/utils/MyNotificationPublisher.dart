import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_local_notifications_platform_interface/flutter_local_notifications_platform_interface.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:quick_tatkal_v2/screens/splash_screen.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest_all.dart' as tz;

import '../core/AppConstants.dart';

class MyNotificationPublisher {
  static const String NOTIFICATION_ID = "notification-id";
  static const String NOTIFICATION = "notification";

  /// Call this to show the notification now (or schedule if needed; here as immediate)
  static Future<void> showNotification(
    BuildContext context, {
    int notificationId = 10003,
  }) async {
    if (AppConstants.isGoldUser == 2) {
      return;
    }
    // Setup
    final FlutterLocalNotificationsPlugin notifications =
        FlutterLocalNotificationsPlugin();

    // Ensure proper initialization & timezone info
    tz.initializeTimeZones();
    await notifications.initialize(
      const InitializationSettings(
        android: AndroidInitializationSettings('qt_notif_3'),
        iOS: DarwinInitializationSettings(),
      ),
      onDidReceiveNotificationResponse: (NotificationResponse details) async {
        if (details.payload == 'PAYMENT') {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const SplashScreen(),
              settings: RouteSettings(arguments: {'ORIGIN': 'PAYMENT'}),
            ),
            (route) => false,
          );
        }
      },
    );

    // Alternate Hindi/English messages based on date like Java
    final date = DateTime.now().day % 4;

    String title, message;
    if (date < 2) {
      title = "💰 Have you tried Quick Tatkal GOLD Pack?";
      message =
          "😍 Select a pack and get more ticket bookings and also stand a maximum chance for confirmed tatkal bookings with all the premium features\n\nClick to explore GOLD Pack, Premium Pack & Starter Pack 👉";
    } else {
      title = "💰 Kya aap ne Quick Tatkal GOLD Pack try kiya?";
      message =
          "😍 Nahi kiya to abhi kijiye. Paaye unlimited bookings ke saath Captcha autofill, OTP autofill jaise aur bhi advanced features\n\nAbhi try kare GOLD Pack, Premium Pack ya Starter Pack 👉";
    }

    // Publish event (for analytics; this is safe even if the Dart context is not alive)
    try {
      await FirebaseAnalytics.instance.logEvent(name: "g_n_impr");
    } catch (_) {}

    // Build notification
    const androidChannel = AndroidNotificationChannel(
      '10003',
      'Payment',
      description: 'Quick Tatkal Payment Recommendation',
      importance: Importance.high,
      enableLights: true,
      ledColor: Colors.cyan,
      enableVibration: true,
    );

    await notifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(androidChannel);

    await notifications.show(
      notificationId,
      title,
      message,
      NotificationDetails(
        android: AndroidNotificationDetails(
          androidChannel.id,
          androidChannel.name,
          channelDescription: androidChannel.description,
          importance: Importance.high,
          priority: Priority.high,
          icon: 'qt_notif_3',
          styleInformation: BigTextStyleInformation(message),
          largeIcon: const DrawableResourceAndroidBitmap('qt_notif_3'),
        ),
        iOS: const DarwinNotificationDetails(),
      ),
      payload: 'PAYMENT',
    );
  }

  static Future<void> scheduleNotification(
    BuildContext context, {
    required int notificationId,
    required DateTime scheduledTime,
    String? title,
    String? message,
  }) async {
    if (AppConstants.isGoldUser == 2) {
      return;
    }
    tz.initializeTimeZones();
    final FlutterLocalNotificationsPlugin notifications =
        FlutterLocalNotificationsPlugin();

    await notifications.initialize(
      const InitializationSettings(
        android: AndroidInitializationSettings('qt_notif_3'),
        iOS: DarwinInitializationSettings(),
      ),
      onDidReceiveNotificationResponse: (NotificationResponse details) async {
        if (details.payload == 'PAYMENT') {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const SplashScreen(),
              settings: RouteSettings(arguments: {'ORIGIN': 'PAYMENT'}),
            ),
            (route) => false,
          );
        }
      },
    );

    const androidChannel = AndroidNotificationChannel(
      '10003',
      'Payment',
      description: 'Quick Tatkal Payment Recommendation',
      importance: Importance.high,
      enableLights: true,
      ledColor: Colors.cyan,
      enableVibration: true,
    );

    await notifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(androidChannel);

    final _title = title ?? "💰 Quick Tatkal Offer!";
    final _message =
        message ??
        "😍 Explore GOLD Pack for premium features and maximum chance for confirmed tatkal bookings. Tap for details 👉";
    try {
      await FirebaseAnalytics.instance.logEvent(name: "g_n_impr");
    } catch (_) {}

    await notifications.zonedSchedule(
      notificationId,
      _title,
      _message,
      tz.TZDateTime.from(scheduledTime, tz.local),
      NotificationDetails(
        android: AndroidNotificationDetails(
          androidChannel.id,
          androidChannel.name,
          channelDescription: androidChannel.description,
          importance: Importance.high,
          priority: Priority.high,
          icon: 'qt_notif_3',
          styleInformation: BigTextStyleInformation(_message),
          largeIcon: const DrawableResourceAndroidBitmap('qt_notif_3'),
        ),
        iOS: const DarwinNotificationDetails(),
      ),
      payload: 'PAYMENT',
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      matchDateTimeComponents: null,
    );
  }
}
