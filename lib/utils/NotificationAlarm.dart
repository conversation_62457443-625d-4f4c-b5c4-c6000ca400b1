import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

class NotificationAlarm {
  final BuildContext context;
  final String date;
  final String source;
  final String pnrStatus;
  final String pnrNo;

  NotificationAlarm({
    required this.context,
    required this.date,
    required this.source,
    required this.pnrStatus,
    required this.pnrNo,
  });

  Future<void> setNotificationTask(String depTime) async {
    final timeParts = depTime.split(":");
    if (timeParts.length < 2) {
      debugPrint("Invalid depTime format");
      return;
    }

    final int hour = int.tryParse(timeParts[0]) ?? 0;
    final int minute = int.tryParse(timeParts[1]) ?? 0;

    final now = DateTime.now();
    DateTime scheduledTime = DateTime(
      now.year,
      now.month,
      now.day,
      hour,
      minute,
    );

    if (scheduledTime.isBefore(now)) {
      scheduledTime = scheduledTime.add(const Duration(days: 1));
    }

    final tz.TZDateTime tzTime = tz.TZDateTime.from(scheduledTime, tz.local);

    await flutterLocalNotificationsPlugin.zonedSchedule(
      0, // ID
      'Train Departure Alert',
      'Train departs at $depTime\nPNR: $pnrNo\nFrom: $source\nStatus: $pnrStatus',
      tzTime,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'train_channel_id',
          'Train Alerts',
          channelDescription: 'Train departure notifications',
          importance: Importance.max,
          priority: Priority.high,
          ticker: 'ticker',
        ),
      ),
      androidScheduleMode:
          AndroidScheduleMode.exactAllowWhileIdle, // ✅ REQUIRED
      matchDateTimeComponents: null,
      payload: 'Train Departure',
    );
  }
}
