import 'dart:io';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';


class ReadTask {
  final BuildContext context;
  int progressBarType = 0;
  int downloadID = 0;
  static const String fileUrl =
      'http://www.afrestudios.com/quick-tatkal/quickotp/Quick_OTP.apk';
  static const String apkFileName = 'Quick_OTP.apk';

  ReadTask(this.context);

  void showAlert() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Download Quick OTP'),
          content: const Text(
            'Download Quick OTP app by Afre Studios to enable OTP autofill. Download now?',
          ),
          // Replace this with Image.asset or supported icon if you want a custom icon
          // icon: Icon(Icons.security),
          actions: <Widget>[
            TextButton(
              child: const Text('Get Quick OTP'),
              onPressed: () async {
                Navigator.of(context).pop(); // Close dialog
                if (await canLaunchUrl(Uri.parse(fileUrl))) {
                  await launchUrl(
                    Uri.parse(fileUrl),
                    mode: LaunchMode.externalApplication,
                  );
                } else {
                  // Handle failure to launch
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Could not open download link'),
                    ),
                  );
                }
                // TODO: For direct download and APK install from inside app,
                // you would need to use background download plugins and a method channel for APK install.
                // Flutter can't install APK files itself for security reasons, but you can prompt user.
              },
            ),
            TextButton(
              child: const Text('No'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _launchInBrowser() async {
    if (await canLaunchUrl(Uri.parse(fileUrl))) {
      await launchUrl(Uri.parse(fileUrl), mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not open download link')),
      );
    }
  }

  Future<void> _downloadAndInstall() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (ctx) => const Center(child: CircularProgressIndicator()),
    );
    try {
      Directory? dir;
      if (Platform.isAndroid) {
        dir = await getExternalStorageDirectory();
      } else {
        dir = await getApplicationDocumentsDirectory();
      }
      if (dir == null) throw Exception('Storage not available');
      String filePath = '${dir.path}/$apkFileName';
      await Dio().download(fileUrl, filePath);
      Navigator.of(context).pop();

      if (Platform.isAndroid) {
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('APK install only supported on Android.'),
          ),
        );
      }
    } catch (e) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to download/install APK: $e')),
      );
    }
  }
}
