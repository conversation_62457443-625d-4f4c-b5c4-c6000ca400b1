import '../core/state/booking_state.dart';

class ScriptGenerator {
  
  String generateAutomationScript(BookingState state) {
    return '''
      (function() {
        try {
          var currentScreen = detectCurrentScreen();
          
          switch (currentScreen) {
            case 'login':
              handleLoginScreen();
              break;
            case 'journey':
              handleJourneyScreen();
              break;
            case 'trains':
              handleTrainSelectionScreen();
              break;
            case 'passenger':
              handlePassengerScreen();
              break;
            case 'payment':
              handlePaymentScreen();
              break;
            case 'review':
              handleReviewScreen();
              break;
          }
          
          function detectCurrentScreen() {
            var url = window.location.href.toLowerCase();
            var bodyText = document.body ? document.body.innerText.toLowerCase() : '';
            
            if (url.includes('login') || bodyText.includes('sign in')) return 'login';
            if (url.includes('train-search') || bodyText.includes('plan my journey')) return 'journey';
            if (bodyText.includes('train list') || bodyText.includes('select train')) return 'trains';
            if (bodyText.includes('passenger details')) return 'passenger';
            if (bodyText.includes('payment method')) return 'payment';
            if (bodyText.includes('review booking')) return 'review';
            
            return 'unknown';
          }
          
          function handleLoginScreen() {
            // Login automation logic
          }
          
          function handleJourneyScreen() {
            // Journey automation logic
          }
          
          // ... other handlers
          
        } catch (e) {
          console.error('Automation error:', e);
        }
      })();
    ''';
  }
  
  String generateLoginScript(String username, String password) {
    return '''
      (function() {
        var usernameField = document.querySelector("input[formControlName='userid']");
        var passwordField = document.querySelector("input[formControlName='password']");
        var loginBtn = document.querySelector('button[type="submit"]');
        
        if (usernameField && usernameField.value === '') {
          usernameField.focus();
          usernameField.value = '$username';
          usernameField.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        if (passwordField && passwordField.value === '' && usernameField.value === '$username') {
          passwordField.focus();
          passwordField.value = '$password';
          passwordField.dispatchEvent(new Event('input', { bubbles: true }));
          
          setTimeout(function() {
            if (loginBtn && !loginBtn.disabled) {
              loginBtn.click();
            }
          }, 500);
        }
      })();
    ''';
  }
  
  String generateJourneyScript(String from, String to, String date) {
    return '''
      (function() {
        var fromInput = document.querySelector("input[formControlName='fromStation']");
        var toInput = document.querySelector("input[formControlName='toStation']");
        var dateInput = document.querySelector("input[formControlName='journeyDate']");
        var searchBtn = document.querySelector('.search-btn');
        
        if (fromInput && fromInput.value === '') {
          fromInput.focus();
          fromInput.value = '$from';
          fromInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        if (toInput && toInput.value === '') {
          toInput.focus();
          toInput.value = '$to';
          toInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        if (dateInput && dateInput.value === '') {
          dateInput.focus();
          dateInput.value = '$date';
          dateInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        setTimeout(function() {
          if (searchBtn && !searchBtn.disabled) {
            searchBtn.click();
          }
        }, 1000);
      })();
    ''';
  }
}