import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:typed_data';
import 'dart:developer' as developer;
import 'package:shared_preferences/shared_preferences.dart';

class AccessibilityAutomationService {
  static AccessibilityAutomationService? _instance;

  static AccessibilityAutomationService get instance =>
      _instance ??= AccessibilityAutomationService._internal();

  AccessibilityAutomationService._internal();

  // Automation state (equivalent to Java class variables)
  bool _isEnabled = false;
  String _currentScreen = 'unknown';
  int _automationStep = 0;
  Map<String, dynamic> _formData = {};

  // Screen detection patterns (Java equivalent: traversalString analysis)
  final Map<String, List<String>> _screenSignatures = {
    'login': ['sign in', 'login', 'userid', 'password', 'captcha'],
    'journey': [
      'from station',
      'to station',
      'journey date',
      'plan my journey',
    ],
    'trains': ['train list', 'select train', 'availability', 'class', 'fare'],
    'passenger': [
      'passenger details',
      'add passenger',
      'traveller',
      'name',
      'age',
    ],
    'payment': [
      'payment method',
      'select bank',
      'proceed to pay',
      'payment gateway',
    ],
    'review': ['review booking', 'journey details', 'make payment', 'confirm'],
    'captcha': ['captcha', 'enter text', 'verification', 'image verification'],
    'booking': ['booking confirmation', 'pnr', 'ticket', 'booking status'],
  };

  // Automation flags (Java equivalent: various boolean flags)
  final Map<String, bool> _automationFlags = {
    'loginClicked': false,
    'journeyDetailsFilled': false,
    'trainSelected': false,
    'passengerDetailsFilled': false,
    'paymentMethodSelected': false,
    'reviewCompleted': false,
    'captchaSolved': false,
    'bookingCompleted': false,
  };

  // Current automation context
  int _currentPassengerIndex = 0;
  int _currentChildIndex = 0;
  int _retryCount = 0;
  Timer? _automationTimer;
  StreamController<Map<String, dynamic>>? _statusController;

  Stream<Map<String, dynamic>> get statusStream =>
      _statusController?.stream ?? const Stream.empty();

  /// Initialize the accessibility service
  Future<void> initialize(Map<String, dynamic> formData) async {
    _formData = formData;
    _isEnabled = true;
    _statusController = StreamController<Map<String, dynamic>>.broadcast();

    developer.log('AccessibilityAutomationService initialized');
    _broadcastStatus('Accessibility automation initialized', 'info');
  }

  /// Start automation monitoring (Java equivalent: onAccessibilityEvent)
  void startAutomation() {
    if (!_isEnabled) return;

    _automationTimer = Timer.periodic(const Duration(milliseconds: 1500), (
      timer,
    ) {
      // This would be called from WebView JavaScript bridge
      // _performAutomationCycle();
    });

    developer.log('Accessibility automation started');
    _broadcastStatus('Automation monitoring started', 'info');
  }

  /// Stop automation
  void stopAutomation() {
    _automationTimer?.cancel();
    _automationTimer = null;
    _isEnabled = false;

    developer.log('Accessibility automation stopped');
    _broadcastStatus('Automation stopped', 'info');
  }

  /// Handle screen detection and automation (Java equivalent: autofill method)
  Map<String, dynamic> processScreenContent(
    String htmlContent,
    String currentUrl,
  ) {
    if (!_isEnabled) return {'action': 'none'};

    String detectedScreen = _detectScreen(htmlContent, currentUrl);

    if (detectedScreen != _currentScreen) {
      _currentScreen = detectedScreen;
      _onScreenChanged(detectedScreen);
    }

    return _generateAutomationScript(detectedScreen);
  }

  /// Detect current screen type (Java equivalent: screen detection logic)
  String _detectScreen(String htmlContent, String currentUrl) {
    String content = htmlContent.toLowerCase();
    String url = currentUrl.toLowerCase();

    // Check URL patterns first
    if (url.contains('login') || url.contains('signin')) return 'login';
    if (url.contains('train-search') || url.contains('journey'))
      return 'journey';
    if (url.contains('passenger') || url.contains('psgn')) return 'passenger';
    if (url.contains('payment') || url.contains('pymt')) return 'payment';
    if (url.contains('review') || url.contains('booking')) return 'review';
    if (url.contains('confirmation') || url.contains('pnr')) return 'booking';

    // Check content patterns
    for (var screenType in _screenSignatures.keys) {
      var signatures = _screenSignatures[screenType]!;
      int matches = signatures.where((sig) => content.contains(sig)).length;

      if (matches >= (signatures.length * 0.4)) {
        // 40% match threshold
        return screenType;
      }
    }

    return 'unknown';
  }

  /// Handle screen change events
  void _onScreenChanged(String screenType) {
    _automationStep = _getStepForScreen(screenType);
    developer.log('Screen changed to: $screenType (step $_automationStep)');
    _broadcastStatus('Screen detected: $screenType', 'screen_change', {
      'screen': screenType,
      'step': _automationStep,
    });
  }

  /// Get automation step for screen type
  int _getStepForScreen(String screenType) {
    switch (screenType) {
      case 'login':
        return 1;
      case 'journey':
        return 2;
      case 'trains':
        return 3;
      case 'passenger':
        return 4;
      case 'payment':
        return 5;
      case 'review':
        return 6;
      case 'booking':
        return 7;
      default:
        return 0;
    }
  }

  /// Generate automation JavaScript based on current screen (Java equivalent: screen-specific automation)
  Map<String, dynamic> _generateAutomationScript(String screenType) {
    switch (screenType) {
      case 'login':
        return _generateLoginAutomation();
      case 'journey':
        return _generateJourneyAutomation();
      case 'trains':
        return _generateTrainSelectionAutomation();
      case 'passenger':
        return _generatePassengerAutomation();
      case 'payment':
        return _generatePaymentAutomation();
      case 'review':
        return _generateReviewAutomation();
      case 'captcha':
        return _generateCaptchaAutomation();
      default:
        return {'action': 'wait'};
    }
  }

  /// Generate login automation (Java equivalent: loginScreen method)
  Map<String, dynamic> _generateLoginAutomation() {
    if (_automationFlags['loginClicked'] == true) {
      return {'action': 'wait'};
    }

    return {
      'action': 'login',
      'username': _formData['username'] ?? '',
      'password': _formData['password'] ?? '',
      'script':
          '''
        function performLogin() {
          var usernameField = document.querySelector("input[formControlName='userid'], #userId, input[name='userid']");
          var passwordField = document.querySelector("input[formControlName='password'], #pwd, input[name='password']");
          var loginBtn = document.querySelector('button[type="submit"], .search_btn, .loginButton');
          
          if (usernameField && passwordField && loginBtn) {
            if (usernameField.value === '' && '${_formData['username'] ?? ''}' !== '') {
              usernameField.focus();
              usernameField.value = '${_formData['username'] ?? ''}';
              usernameField.dispatchEvent(new Event('input', { bubbles: true }));
              
              setTimeout(function() {
                if (passwordField.value === '' && '${_formData['password'] ?? ''}' !== '') {
                  passwordField.focus();
                  passwordField.value = '${_formData['password'] ?? ''}';
                  passwordField.dispatchEvent(new Event('input', { bubbles: true }));
                  
                  setTimeout(function() {
                    if (loginBtn && !loginBtn.disabled) {
                      loginBtn.click();
                      Step.updateAutomationState('loginClicked', {});
                      return true;
                    }
                  }, 500);
                }
              }, 300);
            }
          }
          return false;
        }
        performLogin();
      ''',
    };
  }

  /// Generate journey planning automation (Java equivalent: stnSelectionScreen method)
  Map<String, dynamic> _generateJourneyAutomation() {
    if (_automationFlags['journeyDetailsFilled'] == true) {
      return {'action': 'wait'};
    }

    return {
      'action': 'journey',
      'from': _formData['fromStation'] ?? '',
      'to': _formData['toStation'] ?? '',
      'date': _formData['journeyDate'] ?? '',
      'script':
          '''
        function fillJourneyDetails() {
          var fromInput = document.querySelector("input[placeholder*='From'], #fromStation");
          var toInput = document.querySelector("input[placeholder*='To'], #toStation");
          var dateInput = document.querySelector("input[placeholder*='Date'], #journeyDate");
          var searchBtn = document.querySelector('.train_Search, .search-btn, button[type="submit"]');
          
          var filled = 0;
          var total = 3;
          
          if (fromInput && fromInput.value === '' && '${_formData['fromStation'] ?? ''}' !== '') {
            fromInput.focus();
            fromInput.value = '${_formData['fromStation'] ?? ''}';
            fromInput.dispatchEvent(new Event('input', { bubbles: true }));
            filled++;
          }
          
          if (toInput && toInput.value === '' && '${_formData['toStation'] ?? ''}' !== '') {
            setTimeout(function() {
              toInput.focus();
              toInput.value = '${_formData['toStation'] ?? ''}';
              toInput.dispatchEvent(new Event('input', { bubbles: true }));
              filled++;
            }, 300);
          }
          
          if (dateInput && dateInput.value === '' && '${_formData['journeyDate'] ?? ''}' !== '') {
            setTimeout(function() {
              dateInput.focus();
              dateInput.value = '${_formData['journeyDate'] ?? ''}';
              dateInput.dispatchEvent(new Event('input', { bubbles: true }));
              filled++;
            }, 600);
          }
          
          setTimeout(function() {
            if (filled === total && searchBtn && !searchBtn.disabled) {
              searchBtn.click();
              Step.updateAutomationState('journeyDetailsFilled', {});
            }
          }, 1000);
        }
        fillJourneyDetails();
      ''',
    };
  }

  /// Generate train selection automation (Java equivalent: trainSelectionScreen method)
  Map<String, dynamic> _generateTrainSelectionAutomation() {
    if (_automationFlags['trainSelected'] == true) {
      return {'action': 'wait'};
    }

    return {
      'action': 'train_selection',
      'trainNo': _formData['trainNo'] ?? '',
      'travelClass': _formData['travelClass'] ?? '',
      'script':
          '''
        function selectTrain() {
          var trainElements = document.querySelectorAll('.train-item, .train-row, [data-train-number]');
          var targetTrain = '${_formData['trainNo'] ?? ''}';
          var targetClass = '${_formData['travelClass'] ?? ''}';
          
          if (trainElements.length > 0 && targetTrain !== '') {
            for (var trainEl of trainElements) {
              var trainText = trainEl.innerText || '';
              if (trainText.includes(targetTrain)) {
                var classButtons = trainEl.querySelectorAll('.class-btn, .fare-btn, [data-class]');
                for (var classBtn of classButtons) {
                  if (classBtn.innerText.includes(targetClass)) {
                    classBtn.click();
                    Step.updateAutomationState('trainSelected', {});
                    Step.printJS('Train and class selected: ' + targetTrain + ' - ' + targetClass);
                    return true;
                  }
                }
                break;
              }
            }
          }
          return false;
        }
        selectTrain();
      ''',
    };
  }

  /// Generate passenger details automation (Java equivalent: passengerScreen method)
  Map<String, dynamic> _generatePassengerAutomation() {
    if (_automationFlags['passengerDetailsFilled'] == true) {
      return {'action': 'wait'};
    }

    List<Map<String, dynamic>> passengers = List<Map<String, dynamic>>.from(
      _formData['passengers'] ?? [],
    );
    if (_currentPassengerIndex >= passengers.length) {
      _automationFlags['passengerDetailsFilled'] = true;
      return {'action': 'complete'};
    }

    Map<String, dynamic> currentPassenger = passengers[_currentPassengerIndex];

    return {
      'action': 'passenger',
      'passengerIndex': _currentPassengerIndex,
      'passenger': currentPassenger,
      'script':
          '''
        function fillPassengerDetails() {
          var passenger = ${_encodeJson(currentPassenger)};
          var nameField = document.querySelector("input[formControlName='passengerName'], #passengerName");
          var ageField = document.querySelector("input[formControlName='passengerAge'], #passengerAge");
          var genderSelect = document.querySelector("select[formControlName='passengerGender'], #passengerGender");
          var berthSelect = document.querySelector("select[formControlName='passengerBerthChoice'], #berthPreference");
          var addBtn = document.querySelector(".add-passenger, .save-passenger, .done-passenger");
          
          var filled = 0;
          
          if (nameField && nameField.value === '' && passenger.name) {
            nameField.focus();
            nameField.value = passenger.name;
            nameField.dispatchEvent(new Event('input', { bubbles: true }));
            filled++;
          }
          
          if (ageField && ageField.value === '' && passenger.age) {
            setTimeout(function() {
              ageField.focus();
              ageField.value = passenger.age;
              ageField.dispatchEvent(new Event('input', { bubbles: true }));
              filled++;
            }, 200);
          }
          
          if (genderSelect && genderSelect.value === '' && passenger.gender) {
            setTimeout(function() {
              genderSelect.value = passenger.gender;
              genderSelect.dispatchEvent(new Event('change', { bubbles: true }));
              filled++;
            }, 400);
          }
          
          if (berthSelect && passenger.berthPref) {
            setTimeout(function() {
              berthSelect.value = passenger.berthPref;
              berthSelect.dispatchEvent(new Event('change', { bubbles: true }));
            }, 600);
          }
          
          setTimeout(function() {
            if (filled >= 3 && addBtn) {
              addBtn.click();
              Step.updateAutomationState('incrementPassenger', {});
              Step.printJS('Passenger ${_currentPassengerIndex + 1} details filled');
            }
          }, 800);
        }
        fillPassengerDetails();
      ''',
    };
  }

  /// Generate payment automation (Java equivalent: paymentSelectionScreen method)
  Map<String, dynamic> _generatePaymentAutomation() {
    if (_automationFlags['paymentMethodSelected'] == true) {
      return {'action': 'wait'};
    }

    return {
      'action': 'payment',
      'paymentMethod': _formData['paymentChoice'] ?? '',
      'bankChoice': _formData['bankChoice'] ?? '',
      'script':
          '''
        function selectPaymentMethod() {
          var paymentOptions = document.querySelectorAll('.payment-option, .bank-option, .payment-method');
          var targetPayment = '${_formData['paymentChoice'] ?? ''}';
          var targetBank = '${_formData['bankChoice'] ?? ''}';
          
          if (paymentOptions.length > 0) {
            for (var option of paymentOptions) {
              var optionText = option.innerText || '';
              if (optionText.includes(targetPayment) || optionText.includes(targetBank)) {
                option.click();
                Step.updateAutomationState('paymentMethodSelected', {});
                Step.printJS('Payment method selected: ' + optionText);
                return true;
              }
            }
          }
          return false;
        }
        selectPaymentMethod();
      ''',
    };
  }

  /// Generate review screen automation (Java equivalent: reviewScreen method)
  Map<String, dynamic> _generateReviewAutomation() {
    return {
      'action': 'review',
      'script': '''
        function proceedToPayment() {
          var makePaymentBtn = document.querySelector('#make_payment, .make-payment-btn, .final-payment');
          var captchaInput = document.querySelector('#captcha, .captcha-input, #nlpAnswer');
          
          if (makePaymentBtn) {
            if (!captchaInput || captchaInput.value !== '') {
              makePaymentBtn.click();
              Step.updateAutomationState('reviewCompleted', {});
              Step.printJS('Proceeding to payment');
              return true;
            }
          }
          return false;
        }
        proceedToPayment();
      ''',
    };
  }

  /// Generate captcha automation (Java equivalent: captcha handling)
  Map<String, dynamic> _generateCaptchaAutomation() {
    return {
      'action': 'captcha',
      'script':
          '''
        function handleCaptcha() {
          var captchaImg = document.querySelector('#captcha, .captcha-img, #nlpCaptchaContainer img');
          var captchaInput = document.querySelector('#captcha_input, .captcha-input, #nlpAnswer');
          
          if (captchaImg && captchaInput && captchaInput.value === '') {
            var imgSrc = captchaImg.src;
            if (imgSrc && ${_formData['autofillCaptcha'] ?? false}) {
              var type = imgSrc.startsWith('data:') ? 0 : 1;
              var location = window.location.href.includes('login') ? 0 : 1;
              Step.solveCaptcha(imgSrc, type, location);
              Step.printJS('Captcha solving initiated');
              return true;
            }
          }
          return false;
        }
        handleCaptcha();
      ''',
    };
  }

  /// Update automation flag
  void updateAutomationFlag(String flag, bool value) {
    _automationFlags[flag] = value;
    _broadcastStatus('Automation flag updated: $flag = $value', 'flag_update', {
      'flag': flag,
      'value': value,
    });

    // Handle special flag updates
    switch (flag) {
      case 'incrementPassenger':
        _currentPassengerIndex++;
        break;
      case 'incrementChild':
        _currentChildIndex++;
        break;
    }
  }

  /// Reset automation state
  void resetAutomation() {
    _automationFlags.updateAll((key, value) => false);
    _currentPassengerIndex = 0;
    _currentChildIndex = 0;
    _retryCount = 0;
    _currentScreen = 'unknown';
    _automationStep = 0;

    _broadcastStatus('Automation state reset', 'reset');
  }

  /// Get current automation status
  Map<String, dynamic> getStatus() {
    return {
      'enabled': _isEnabled,
      'currentScreen': _currentScreen,
      'automationStep': _automationStep,
      'currentPassengerIndex': _currentPassengerIndex,
      'currentChildIndex': _currentChildIndex,
      'retryCount': _retryCount,
      'flags': Map.from(_automationFlags),
    };
  }

  /// Broadcast status updates
  void _broadcastStatus(
    String message,
    String type, [
    Map<String, dynamic>? data,
  ]) {
    if (_statusController?.isClosed != false) return;

    _statusController!.add({
      'timestamp': DateTime.now().toIso8601String(),
      'message': message,
      'type': type,
      'data': data ?? {},
      'status': getStatus(),
    });
  }

  /// Encode JSON safely for JavaScript
  String _encodeJson(Map<String, dynamic> data) {
    return data.toString().replaceAll("'", "\\'");
  }

  /// Dispose resources
  void dispose() {
    stopAutomation();
    _statusController?.close();
    _statusController = null;
    _instance = null;

    developer.log('AccessibilityAutomationService disposed');
  }
}

/// Automation status data class
class AutomationStatus {
  final DateTime timestamp;
  final String message;
  final String type;
  final Map<String, dynamic> data;
  final bool isEnabled;
  final String currentScreen;
  final int automationStep;

  AutomationStatus({
    required this.timestamp,
    required this.message,
    required this.type,
    required this.data,
    required this.isEnabled,
    required this.currentScreen,
    required this.automationStep,
  });

  factory AutomationStatus.fromMap(Map<String, dynamic> map) {
    return AutomationStatus(
      timestamp: DateTime.parse(map['timestamp']),
      message: map['message'] ?? '',
      type: map['type'] ?? '',
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      isEnabled: map['status']?['enabled'] ?? false,
      currentScreen: map['status']?['currentScreen'] ?? 'unknown',
      automationStep: map['status']?['automationStep'] ?? 0,
    );
  }
}

class AccessibilityService {
  static const MethodChannel _channel = MethodChannel('accessibility_service');
  static const EventChannel _eventChannel = EventChannel(
    'accessibility_events',
  );

  // Static instance
  static AccessibilityService? _instance;

  static AccessibilityService get instance =>
      _instance ??= AccessibilityService._();

  AccessibilityService._();

  // Stream controllers for events
  final StreamController<Map<String, dynamic>> _statusController =
      StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _ticketInfoController =
      StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _captchaController =
      StreamController.broadcast();

  // Streams
  Stream<Map<String, dynamic>> get statusStream => _statusController.stream;

  Stream<Map<String, dynamic>> get ticketInfoStream =>
      _ticketInfoController.stream;

  Stream<Map<String, dynamic>> get captchaStream => _captchaController.stream;

  // Variables from Java implementation - keeping same names
  bool mBound = true;
  bool journeyDateOneTimeFlag = false;
  bool dateDialogOpened = false;
  bool dateSelected = false;
  bool concessionAlertDismissed = false;
  bool sameMonth = false;
  bool paymentModeSelected = false;
  bool paymentEntitySelected = false;
  bool proceedToPaymentClicked = false;
  int childAgeSelectionFlag = 0;
  int optBerthSelectionFlag = 0;
  int foodSelectionFlag = 0;
  int monthDifference = 99;
  int currentPassengerIndex = 0;
  int currentChildIndex = 0;

  bool loginClicked = false;
  bool pinEntered = false;
  bool quotaDropDownClicked = false;
  bool searchTrainClicked = false;

  int stationSelectionFlag = 0;
  bool isFillingPassDetails = false;
  int berthPrefSelectionFlag = 0;
  int concessionSelectionFlag = 0;
  bool concessionSelected = false;
  bool travelClassSelected = false;
  bool reviewCaptchaFocused = false;
  bool isFillingChildDetails = false;

  // Date grid index array
  List<String> dateGridIndex = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  String journeyDate = "";
  Map<String, String> quotaMap = {};

  // Payment arrays
  List<String> walletIds = [
    "click_ewallet",
    "click_mobikwik",
    "click_paytm_wallet",
    "click_ola_money",
    "click_jio_money",
    "click_airtel",
  ];

  List<String> otherPgIds = [
    "click_ipay",
    "click_paytm",
    "click_mobimpp",
    "click_payu_mpp",
    "click_razor_pay",
    "click_phone_pe",
    "click_icici_mpp",
    "click_hdfc_mpp",
    "click_airpay",
  ];

  String bhimId = "click_paytm_upi";

  // Form data variables - same as Java
  String? formName;
  String? rcPin;
  String? fromStation;
  String? toStation;
  String? quota;
  int fareLimit = 0;
  String? trainNo;
  String? travelClass;
  String? boardingStation;
  List<Passenger>? passenger;
  List<Child>? child;
  String? mobileNo;
  String? addrLine1;
  String? addrLine2;
  String? addrLine3;
  String? addrPin;
  String? addrCity;
  String? addrPo;
  int insurance = 0;
  bool autoUpgrade = false;
  bool onlyConfirm = false;
  bool miscDataFilled = false;
  int paymentType = 0;
  int bookingOption = 0;
  bool coachPreferred = false;
  String coachId = "";
  int pCount = 0;
  int cCount = 0;

  Map<String, String> travelClassMap = {};
  String? paymentMode;
  String? paymentEntity;

  bool movedToPayment = false;
  static int paymentFailed = 0;

  int timeStart = 0;
  bool ticketBookFlag = false;
  int dvSelectionFlag = 0;
  bool ticketSavedToServer = false;
  bool planMyJourneyClicked = false;

  static int windowWidth = 0;
  static int windowHeight = 0;

  String captchaResId = "";
  String submitResId = "";

  static int captchaSolving = 0;
  static bool retryCaptchaManual = false;

  bool autofillCaptcha = false;
  bool passengerDetailsProceedClicked = false;
  bool addressEntered = false;
  bool cityClicked = false;
  int postOfficeFlag = 0;

  bool login1Clicked = false;
  bool login2Clicked = false;
  bool journeyDateClicked = false;

  int clickOption = 0;

  bool passWait = false;
  int otpWait = 0;
  int otpTimerCount = 0;

  bool tatkalWait = false;
  bool tatkalTimerStarted = false;
  int tatkalLongWait = 0;
  int trainIndex = 0;
  int classIndex = 0;
  String availability = "";

  int currentScreen = 0;
  bool flexibleClicked = false;
  List<bool> screenTaskComplete = List.filled(15, false);
  String traversalString = "";

  String? vpa;
  bool autoOpenUpi = false;
  bool automateUpi = false;
  String? walletNo;
  int paytmWalletStage = 0;
  int mobikwikWalletStage = 0;
  int irctcWalletStage = 0;
  int otherPaymentStage = 0;
  bool upiButtonClicked = false;
  String? regPhone;

  bool paytmUpiClicked = false;
  bool paytmVpaFilled = false;
  bool paytmVpaVerified = false;

  static int timeDifference = 0;
  int irctcSeconds = -1;

  int boardingStnFlag = 0;
  int tatkalTime = 0;
  bool refreshRequired = false;

  bool continueButtonClicked = false;
  bool reCaptchaShowing = false;

  int normalReviewCaptcha = 0;
  bool passengerPageLogoutWait = false;
  bool passengerWaitFlag = false;

  bool iAgreeClicked = false;
  bool reviewCaptchaClicked = false;

  // GPay variables
  bool gPayButtonSkipped = false;
  bool selectAutoBank = false;
  int gPayButtonsClicked = 0;

  // PhonePe variables
  bool phonepePayClicked = false;
  bool phonepeProceedToPayClicked = false;
  bool phonepeDefaultBankClicked = false;

  // Paytm variables
  bool paytmPayClicked = false;
  bool paytmDefaultBankClicked = false;

  bool upiPinSubmitted = false;

  int invalidCaptchaFlag = 0;
  int loginCaptchaRetries = 0;
  int bookingCaptchaRetries = 0;

  bool captchaTryDone = false;

  int loginCaptchaCount = 1;
  int bookingCaptchaCount = 1;

  Uint8List? lastCaptchaBmp;
  static bool canProceedForCapture = true;

  bool firstLoginCaptcha = false;
  bool firstBookingCaptcha = false;

  String previousTraversalString = "NA";
  int sameScreenCount = 0;

  bool rcServiceEventTracked = false;

  int waitForPTResponse = 0;
  bool paymentAutofill = false;

  // Constants
  static const String upiPin = "1234"; // This should be securely stored
  static const String thirdPartyAppPackage = "cris.org.npci.upiapp";

  // Initialize the service
  Future<void> initialize() async {
    try {
      // Set up method call handler
      _channel.setMethodCallHandler(_handleMethodCall);

      // Start listening to events
      _eventChannel.receiveBroadcastStream().listen(
        (dynamic event) => _handleEvent(event),
        onError: (dynamic error) => print('Event channel error: $error'),
      );

      // Initialize variables
      _initializeVariables();

      print('AccessibilityService initialized successfully');
    } catch (e) {
      print('Failed to initialize AccessibilityService: $e');
      rethrow;
    }
  }

  // Handle method calls from native side
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onCaptchaSolved':
        final String result = call.arguments['result'];
        _handleCaptchaSolved(result);
        break;
      case 'onTicketBooked':
        final Map<String, dynamic> ticketInfo = Map<String, dynamic>.from(
          call.arguments,
        );
        _handleTicketBooked(ticketInfo);
        break;
      case 'onStatusUpdate':
        final String status = call.arguments['status'];
        _handleStatusUpdate(status);
        break;
      case 'onError':
        final String error = call.arguments['error'];
        _handleError(error);
        break;
      default:
        print('Unknown method call: ${call.method}');
    }
  }

  // Handle events from native side
  void _handleEvent(dynamic event) {
    if (event is Map) {
      final Map<String, dynamic> eventMap = Map<String, dynamic>.from(event);
      final String? eventType = eventMap['type'];
      switch (eventType) {
        case 'accessibility_event':
          _handleAccessibilityEvent(eventMap);
          break;
        case 'captcha_event':
          _captchaController.add(eventMap);
          break;
        case 'status_event':
          _statusController.add(eventMap);
          break;
        default:
          print('Unknown event type: $eventType');
      }
    }
  }

  // Initialize variables - same as Java implementation
  void _initializeVariables() {
    quotaMap = {
      "GN": "tv_general",
      "LD": "tv_ladies",
      "TQ": "tv_tatkal",
      "SS": "tv_senior_citizen",
      "PT": "tv_premium_tatkal",
      "HP": "tv_ph_handicap",
      "DP": "tv_general",
    };

    travelClassMap = {
      "SL": "Sleeper",
      "3A": "AC 3 Tier",
      "2A": "AC 2 Tier",
      "1A": "AC First Class",
      "EC": "Exec. Chair Car",
      "FC": "First Class",
      "3E": "AC 3 Economy",
      "2S": "Second Sitting",
      "CC": "AC Chair car",
      "EA": "Anubhuti Class",
      "VS": "Vistadome Non AC",
      "VC": "Vistadome Chair car",
      "EV": "Vistadome AC",
    };
  }

  // Public methods to interact with the service

  Future<bool> isAccessibilityServiceEnabled() async {
    try {
      final bool result = await _channel.invokeMethod(
        'isAccessibilityServiceEnabled',
      );
      return result;
    } catch (e) {
      print('Error checking accessibility service status: $e');
      return false;
    }
  }

  Future<void> openAccessibilitySettings() async {
    try {
      await _channel.invokeMethod('openAccessibilitySettings');
    } catch (e) {
      print('Error opening accessibility settings: $e');
    }
  }

  Future<void> startService(Map<String, dynamic> formData) async {
    try {
      await _channel.invokeMethod('startService', formData);
    } catch (e) {
      print('Error starting accessibility service: $e');
      rethrow;
    }
  }

  Future<void> stopService() async {
    try {
      await _channel.invokeMethod('stopService');
    } catch (e) {
      print('Error stopping accessibility service: $e');
    }
  }

  Future<void> solveCaptcha(Uint8List captchaImage) async {
    try {
      await _channel.invokeMethod('solveCaptcha', {
        'captchaImage': captchaImage,
      });
    } catch (e) {
      print('Error solving captcha: $e');
    }
  }

  Future<void> fillPassengerDetails(
    List<Map<String, dynamic>> passengers,
  ) async {
    try {
      await _channel.invokeMethod('fillPassengerDetails', {
        'passengers': passengers,
      });
    } catch (e) {
      print('Error filling passenger details: $e');
    }
  }

  Future<void> processPayment(Map<String, dynamic> paymentInfo) async {
    try {
      await _channel.invokeMethod('processPayment', paymentInfo);
    } catch (e) {
      print('Error processing payment: $e');
    }
  }

  Future<void> updateFormData(Map<String, dynamic> formData) async {
    try {
      await _channel.invokeMethod('updateFormData', formData);
    } catch (e) {
      print('Error updating form data: $e');
    }
  }

  Future<Map<String, dynamic>> getServiceStatus() async {
    try {
      final dynamic rawResult = await _channel.invokeMethod('getServiceStatus');
      if (rawResult is Map) {
        // Convert each key to String if possible
        return Map<String, dynamic>.fromEntries(
          rawResult.entries.map(
            (entry) => MapEntry(entry.key.toString(), entry.value),
          ),
        );
      }
      return {};
    } catch (e) {
      print('Error getting service status: $e');
      return {};
    }
  }

  // Event handlers
  void _handleAccessibilityEvent(Map<String, dynamic> event) {
    // Process accessibility events
    print('Accessibility event: $event');
  }

  void _handleCaptchaSolved(String result) {
    print('Captcha solved: $result');
    _captchaController.add({'type': 'captcha_solved', 'result': result});
  }

  void _handleTicketBooked(Map<String, dynamic> ticketInfo) {
    print('Ticket booked: $ticketInfo');
    _ticketInfoController.add(ticketInfo);
    ticketBookFlag = true;
    paymentFailed = 2;
  }

  void _handleStatusUpdate(String status) {
    print('Status update: $status');
    _statusController.add({'type': 'status_update', 'status': status});
  }

  void _handleError(String error) {
    print('Service error: $error');
    _statusController.add({'type': 'error', 'error': error});
  }

  void resetVariables() {
    pinEntered = false;
    planMyJourneyClicked = false;
    quotaDropDownClicked = false;
    dateDialogOpened = false;
    dateSelected = false;
    sameMonth = false;
    journeyDateOneTimeFlag = false;
    isFillingPassDetails = false;
    isFillingChildDetails = false;
    concessionAlertDismissed = false;
    concessionSelected = false;
    proceedToPaymentClicked = false;
    travelClassSelected = false;
    reviewCaptchaFocused = false;
    paymentModeSelected = false;
    paymentEntitySelected = false;
    ticketSavedToServer = false;
    ticketBookFlag = false;
    passengerDetailsProceedClicked = false;
    addressEntered = false;
    cityClicked = false;
    login1Clicked = false;
    login2Clicked = false;

    dvSelectionFlag = 0;
    currentChildIndex = 0;
    stationSelectionFlag = 0;
    monthDifference = 99;
    currentPassengerIndex = 0;
    berthPrefSelectionFlag = 0;
    concessionSelectionFlag = 0;
    optBerthSelectionFlag = 0;
    foodSelectionFlag = 0;
    childAgeSelectionFlag = 0;
    timeStart = 0;
    postOfficeFlag = 0;
  }

  void dispose() {
    _statusController.close();
    _ticketInfoController.close();
    _captchaController.close();
  }
}

class Passenger {
  String? name;
  int age = 0;
  String? gender;
  String? berthPref;
  String? meal;
  int seniorCitizen = 0;
  bool bedRoll = false;
  String? dob;
  String? concession;
  String? nationality;
  String? cardNo;
  String? validity;
  String? idType;
  String? idNo;
  bool optBerth = false;

  Passenger();

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'age': age,
      'gender': gender,
      'berthPref': berthPref,
      'meal': meal,
      'seniorCitizen': seniorCitizen,
      'bedRoll': bedRoll,
      'dob': dob,
      'concession': concession,
      'nationality': nationality,
      'cardNo': cardNo,
      'validity': validity,
      'idType': idType,
      'idNo': idNo,
      'optBerth': optBerth,
    };
  }

  factory Passenger.fromMap(Map<String, dynamic> map) {
    final passenger = Passenger();
    passenger.name = map['name'];
    passenger.age = map['age'] ?? 0;
    passenger.gender = map['gender'];
    passenger.berthPref = map['berthPref'];
    passenger.meal = map['meal'];
    passenger.seniorCitizen = map['seniorCitizen'] ?? 0;
    passenger.bedRoll = map['bedRoll'] ?? false;
    passenger.dob = map['dob'];
    passenger.concession = map['concession'];
    passenger.nationality = map['nationality'];
    passenger.cardNo = map['cardNo'];
    passenger.validity = map['validity'];
    passenger.idType = map['idType'];
    passenger.idNo = map['idNo'];
    passenger.optBerth = map['optBerth'] ?? false;
    return passenger;
  }
}

class Child {
  String? name;
  String? age;
  String? gender;

  Child();

  Map<String, dynamic> toMap() {
    return {'name': name, 'age': age, 'gender': gender};
  }

  factory Child.fromMap(Map<String, dynamic> map) {
    final child = Child();
    child.name = map['name'];
    child.age = map['age'];
    child.gender = map['gender'];
    return child;
  }
}
