import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import '../utils/CaptchaSolver.dart';
import 'webview_manager.dart';

class CaptchaService {
  final WebViewManager _webViewManager;
  
  CaptchaService(this._webViewManager);
  
  Future<void> solveCaptcha(String url, int type, int location, BuildContext context) async {
    try {
      developer.log("🔍 Solving captcha - Type: $type, Location: $location");

      String result = await CaptchaSolver.solve(url: url, context: context);
      
      if (result.isNotEmpty) {
        await _submitCaptcha(type, result, location);
        developer.log("✅ Captcha solved: $result");
      } else {
        await _submitCaptcha(type, "Error", location);
        developer.log("❌ Captcha solving failed");
      }
    } catch (e) {
      developer.log("💥 Captcha error: $e");
      await _submitCaptcha(type, "Error", location);
    }
  }
  
  Future<void> _submitCaptcha(int type, String output, int location) async {
    String script;
    
    if (type == 0) {
      // Login captcha
      script = '''
        document.getElementById('nlpAnswer').value = '$output';
        document.getElementById('nlpAnswer').focus();
        setTimeout(function() {
          var submitBtn = document.querySelector('.search_btn, .loginButton');
          if (submitBtn && !submitBtn.disabled) {
            submitBtn.click();
          }
        }, 500);
      ''';
    } else {
      // Booking captcha
      script = '''
        document.getElementById('captcha').value = '$output';
        document.getElementById('captcha').focus();
        setTimeout(function() {
          var submitBtn = document.querySelector('.search_btn');
          if (submitBtn && !submitBtn.disabled) {
            submitBtn.click();
          }
        }, 500);
      ''';
    }
    
    _webViewManager.runJavaScript(script);
  }
}