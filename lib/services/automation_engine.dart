import 'dart:async';
import 'dart:developer' as developer;
import '../core/state/booking_state.dart';
import 'webview_manager.dart';
import 'script_generator.dart';

class AutomationEngine {
  final BookingState _state;
  final WebViewManager _webViewManager;
  final ScriptGenerator _scriptGenerator;
  
  Timer? _automationTimer;
  bool _isEnabled = false;
  
  AutomationEngine(this._state, this._webViewManager, this._scriptGenerator);
  
  void startAutomation() {
    if (_isEnabled) return;
    
    _isEnabled = true;
    _automationTimer = Timer.periodic(
      Duration(milliseconds: 1500),
      (_) => _performAutomationCycle(),
    );
    developer.log("🚀 Automation engine started");
  }
  
  void stopAutomation() {
    _isEnabled = false;
    _automationTimer?.cancel();
    _automationTimer = null;
    developer.log("⏹️ Automation engine stopped");
  }
  
  void _performAutomationCycle() {
    if (!_isEnabled) return;
    
    try {
      final script = _scriptGenerator.generateAutomationScript(_state);
      _webViewManager.runJavaScript(script);
    } catch (e) {
      developer.log("Automation cycle error: $e");
    }
  }
  
  void handleScreenChange(String screenType) {
    switch (screenType) {
      case 'login':
        _state.updateAutomationStep(1);
        _triggerLoginAutomation();
        break;
      case 'journey':
        _state.updateAutomationStep(2);
        _triggerJourneyAutomation();
        break;
      case 'trains':
        _state.updateAutomationStep(3);
        break;
      case 'passenger':
        _state.updateAutomationStep(4);
        break;
      case 'payment':
        _state.updateAutomationStep(5);
        break;
      case 'review':
        _state.updateAutomationStep(6);
        break;
    }
  }
  
  void _triggerLoginAutomation() {
    final script = _scriptGenerator.generateLoginScript(
      _state.username,
      _state.password,
    );
    _webViewManager.runJavaScript(script);
  }
  
  void _triggerJourneyAutomation() {
    final script = _scriptGenerator.generateJourneyScript(
      _state.fromStation,
      _state.toStation,
      _state.journeyDate,
    );
    _webViewManager.runJavaScript(script);
  }
}