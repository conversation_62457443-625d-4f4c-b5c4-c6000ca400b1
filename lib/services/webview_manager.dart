import 'package:flutter/material.dart';
import '../core/network/advanced_webview.dart';
import 'dart:developer' as developer;

class WebViewManager {
  final GlobalKey<AdvancedWebViewState> _webViewKey;
  
  WebViewManager(this._webViewKey);
  
  void loadUrl(String url) {
    try {
      final state = _webViewKey.currentState;
      if (state != null) {
        if (url.startsWith('javascript:')) {
          String cleanJs = url.substring('javascript:'.length);
          state.runJavaScript(cleanJs);
        } else if (url.startsWith('http')) {
          state.loadUrl(url);
        } else {
          state.runJavaScript(url);
        }
        developer.log("WebView executed: ${url.length} chars");
      }
    } catch (e) {
      developer.log("WebView error: $e");
    }
  }
  
  void runJavaScript(String script) {
    try {
      _webViewKey.currentState?.runJavaScript(script);
    } catch (e) {
      developer.log("JavaScript error: $e");
    }
  }
}