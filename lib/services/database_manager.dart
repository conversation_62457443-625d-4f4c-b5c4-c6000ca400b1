import 'package:sqflite/sqflite.dart';
import '../database/MainDB.dart';
import '../core/state/booking_state.dart';
import '../utils/Cryptography.dart';
import 'dart:developer' as developer;

class DatabaseManager {
  
  Future<void> loadBookingData(String formName, BookingState state) async {
    try {
      final db = await MainDB.instance.database;
      final results = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [formName],
        limit: 1,
      );
      
      if (results.isNotEmpty) {
        final data = results.first;
        
        state.username = data['USERNAME']?.toString() ?? '';
        
        // Decrypt password
        final encrypted = data['PASSWORD'];
        if (encrypted != null && encrypted is String && encrypted.isNotEmpty) {
          state.password = Cryptography.decryptPassword(encrypted);
        }
        
        state.fromStation = data['FROM_STN']?.toString() ?? '';
        state.toStation = data['TO_STN']?.toString() ?? '';
        state.journeyDate = data['TRVL_DATE']?.toString() ?? '';
        state.quota = data['QUOTA']?.toString() ?? '';
        state.trainNo = data['TRAIN_NO']?.toString() ?? '';
        state.travelClass = data['TRAVEL_CLASS']?.toString() ?? '';
        state.boardingStation = data['BOARDING_STATION']?.toString() ?? '';
        state.paymentChoice = data['PAYMENT_CHOICE']?.toString() ?? '';
        
        developer.log("✅ Booking data loaded for form: $formName");
      }
    } catch (e) {
      developer.log("❌ Error loading booking data: $e");
    }
  }
  
  Future<void> saveBookingData(String formName, BookingState state) async {
    try {
      final db = await MainDB.instance.database;
      
      await db.update(
        MainDB.tableName,
        {
          'FROM_STN': state.fromStation,
          'TO_STN': state.toStation,
          'TRVL_DATE': state.journeyDate,
          'QUOTA': state.quota,
          'TRAIN_NO': state.trainNo,
          'TRAVEL_CLASS': state.travelClass,
        },
        where: 'FORM_NAME = ?',
        whereArgs: [formName],
      );
      
      developer.log("✅ Booking data saved for form: $formName");
    } catch (e) {
      developer.log("❌ Error saving booking data: $e");
    }
  }
}