import 'package:flutter/material.dart';

class PassengerModel {
  TextEditingController nameCtrl = TextEditingController();
  TextEditingController ageCtrl = TextEditingController();
  String gender;
  String nationality;
  String berth;
  String meal;
  bool senior;
  bool optBerth;
  bool bedroll;
  String concession;
  String cardNo;
  String cardValidity;
  String idType;
  String idNo;
  String dob;

  String error = '';

  PassengerModel({
    String name = '',
    String age = '',
    this.gender = 'Male',
    this.nationality = 'India',
    this.berth = 'No Preference',
    this.meal = 'No Meal',
    this.senior = false,
    this.optBerth = false,
    this.bedroll = false,
    this.concession = '',
    this.cardNo = '',
    this.cardValidity = '',
    this.idType = '',
    this.idNo = '',
    this.dob = '',
  }) {
    nameCtrl.text = name;
    ageCtrl.text = age;
  }

  factory PassengerModel.fromMap(Map row) {
    return PassengerModel(
      name: row['NAME']?.toString() ?? '',
      age: row['AGE']?.toString() ?? '',
      gender: row['GENDER']?.toString() ?? 'Male',
      nationality: row['NATIONALITY']?.toString() ?? 'India',
      berth: row['BERTH']?.toString() ?? 'No Preference',
      meal: row['MEAL']?.toString() ?? 'No Meal',
      senior: row['SENIOR']?.toString() == '1',
      optBerth: row['OPT_BERTH']?.toString() == '1',
      bedroll: row['BEDROLL'] == '1' || row['BEDROLL'] == true,
      concession: row['CONCESSION']?.toString() ?? '',
      cardNo: row['CARD_NO']?.toString() ?? '',
      cardValidity: row['CARD_VALIDITY']?.toString() ?? '',
      idType: row['ID_TYPE']?.toString() ?? '',
      idNo: row['ID_NO']?.toString() ?? '',
      dob: row['DOB']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toMap({required String formName}) {
    return {
      'FORM_NAME': formName,
      'NAME': nameCtrl.text,
      'AGE': ageCtrl.text,
      'GENDER': gender,
      'NATIONALITY': nationality,
      'BERTH': berth,
      'MEAL': meal,
      'SENIOR': senior ? '1' : '0',
      'OPT_BERTH': optBerth ? '1' : '0',
      'BEDROLL': bedroll ? '1' : '0',
      'CONCESSION': concession,
      'CARD_NO': cardNo,
      'CARD_VALIDITY': cardValidity,
      'ID_TYPE': idType,
      'ID_NO': idNo,
      'DOB': dob,
    };
  }

  void dispose() {
    nameCtrl.dispose();
    ageCtrl.dispose();
  }
}