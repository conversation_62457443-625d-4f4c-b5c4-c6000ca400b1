import 'package:flutter/material.dart';

class ChildModel {
  TextEditingController nameCtrl = TextEditingController();
  TextEditingController ageCtrl = TextEditingController();
  String name;
  String age;
  String gender;
  String error;

  ChildModel({
    this.name = '',
    this.age = 'Below one year',
    this.gender = 'Male',
    this.error = '',
  }) {
    nameCtrl.text = name;
    ageCtrl.text = age;
  }

  Map<String, dynamic> toMap({required String formName}) {
    return {
      'FORM_NAME': formName,
      'NAME': nameCtrl.text,
      'AGE': ageCtrl.text,
      'GENDER': gender,
    };
  }

  factory ChildModel.fromMap(Map<String, dynamic> row) {
    ChildModel model = ChildModel(
      name: row['NAME']?.toString() ?? '',
      age: row['AGE']?.toString() ?? 'Below one year',
      gender: row['GENDER']?.toString() ?? 'Male',
    );
    model.nameCtrl.text = model.name;
    model.ageCtrl.text = model.age;
    return model;
  }

  void dispose() {
    nameCtrl.dispose();
    ageCtrl.dispose();
  }
}
