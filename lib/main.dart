import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'core/state/booking_state.dart';
import 'screens/MainActivity.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => BookingState()),
      ],
      child: MaterialApp(
        title: 'Quick Tatkal',
        theme: ThemeData(
          primarySwatch: Colors.blue,
        ),
        home: MainActivity(),
      ),
    );
  }
}
