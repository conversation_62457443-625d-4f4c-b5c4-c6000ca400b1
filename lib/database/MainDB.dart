import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

class MainDB {
  static const _databaseName = "Main.db";
  static const _databaseVersion = 16;
  static const tableName = "BOOKING_INFO";

  static Database? _database;

  // Singleton
  MainDB._privateConstructor();
  static final MainDB instance = MainDB._privateConstructor();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final directory = await getApplicationDocumentsDirectory();
    final path = join(directory.path, _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $tableName (
        FORM_NAME TEXT PRIMARY KEY,
        USERNAME TEXT,
        PASSWORD TEXT,
        FROM_STN TEXT,
        TO_STN TEXT,
        TRVL_DATE TEXT,
        QUOTA TEXT,
        TRVL_CLASS TEXT,
        TRAIN TEXT,
        BOARDING TEXT,
        COACH_ID TEXT,
        COACH_PREF TEXT,
        VIKALP TEXT,
        AUTO_UPG TEXT,
        ONLY_CONFIRM TEXT,
        BOOKING_OPT INTEGER,
        MOBILE_NO TEXT,
        PYMT_CHOICE TEXT,
        PYMT_AUTO TEXT,
        CARD_NO TEXT,
        EXP_YR TEXT,
        EXP_MON TEXT,
        CARD_HOLDER TEXT,
        PIN TEXT,
        CVV TEXT,
        WALLET TEXT,
        W_USER TEXT,
        W_PASS TEXT,
        TXN_PWD TEXT,
        NB_NAME TEXT,
        NB_TYPE TEXT,
        SBI_NAME INTEGER,
        CORP_ID TEXT,
        NB_USER TEXT,
        NB_PWD TEXT,
        IC_MOB TEXT,
        IC_EMAIL TEXT,
        IC_ADDR1 TEXT,
        IC_ADDR2 TEXT,
        IC_CITY TEXT,
        IC_STATE TEXT,
        IC_COUNTRY TEXT,
        IC_ZIP TEXT,
        IC_BANK TEXT,
        BANK_NAME TEXT,
        AE_MOBILE TEXT,
        AE_EMAIL TEXT,
        CARD_TYPE TEXT,
        P_COUNT INTEGER,
        C_COUNT INTEGER,
        VPA TEXT,
        POD TEXT,
        RC_PIN TEXT DEFAULT '',
        MULTIPLE_PYMT_CHOICE TEXT DEFAULT '',
        UPI_BANK TEXT,
        CAPTCHA_AUTOFILL INTEGER,
        WB_PYMT_MODE INTEGER,
        OTP_AUTOFILL INTEGER,
        AUTO_OPEN INTEGER,
        UPI_AUTOMATE INTEGER,
        UPI_DEFAULT_BANK INTEGER,
        UPI_PIN TEXT,
        STATIC_PASS TEXT,
        HDFC_AUTOFILL INTEGER,
        FARE_LIMIT INTEGER,
        PYMT_AUTO_RC INTEGER,
        DELAY_SEC INTEGER,
        INSURANCE TEXT DEFAULT '1',
        BOOKING_OPTION TEXT
      )
    ''');

    // Create auxiliary tables to match Java DBs
    await db.execute('''
      CREATE TABLE IF NOT EXISTS GST_TABLE (
        FORM_NAME TEXT PRIMARY KEY,
        GST_IN TEXT,
        GST_NAME TEXT,
        GST_FLAT TEXT,
        GST_STREET TEXT,
        GST_PIN TEXT,
        GST_AREA TEXT,
        GST_CITY TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS ADDRESS_TABLE (
        FORM_NAME TEXT PRIMARY KEY,
        ADDR1 TEXT,
        ADDR2 TEXT,
        ADDR3 TEXT,
        PIN TEXT,
        CITY TEXT,
        PO TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS PASSENGER_TABLE (
        ID INTEGER PRIMARY KEY AUTOINCREMENT,
        FORM_NAME TEXT,
        CONCESSION TEXT,
        NATIONALITY TEXT,
        CARD_NO TEXT,
        VALIDITY TEXT,
        ID_TYPE TEXT,
        ID_NO TEXT,
        NAME TEXT,
        AGE INTEGER,
        GENDER TEXT,
        BERTH_PREF TEXT,
        MEAL TEXT,
        SENIOR_CITIZEN INTEGER,
        BED_ROLL TEXT,
        DOB TEXT,
        OPT_BERTH INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS CHILD_TABLE (
        ID INTEGER PRIMARY KEY AUTOINCREMENT,
        FORM_NAME TEXT,
        NAME TEXT,
        AGE TEXT,
        GENDER TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS POD_TABLE (
        FORM_NAME TEXT PRIMARY KEY,
        MOBILE TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS INSURANCE_TABLE (
        FORM_NAME TEXT PRIMARY KEY,
        INSURANCE INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS SECURITY_QUESTIONS_TABLE (
        FORM_NAME TEXT PRIMARY KEY,
        DOB TEXT,
        EMAIL TEXT,
        MOBILE TEXT,
        EWALLET INTEGER
      )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (newVersion > oldVersion) {
      if (oldVersion < 2 && newVersion >= 2) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN RC_PIN TEXT DEFAULT ''",
        );
      }
      if (oldVersion < 3 && newVersion >= 3) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN MULTIPLE_PYMT_CHOICE TEXT DEFAULT ''",
        );
        await db.execute("ALTER TABLE $tableName ADD COLUMN UPI_BANK TEXT");
      }
      if (oldVersion < 4 && newVersion >= 4) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN CAPTCHA_AUTOFILL INTEGER",
        );
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN WB_PYMT_MODE INTEGER",
        );
      }
      if (oldVersion < 6 && newVersion >= 6) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN OTP_AUTOFILL INTEGER",
        );
      }
      if (oldVersion < 7 && newVersion >= 7) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN AUTO_OPEN INTEGER DEFAULT 0",
        );
      }
      if (oldVersion < 8 && newVersion >= 8) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN UPI_AUTOMATE INTEGER DEFAULT 0",
        );
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN UPI_DEFAULT_BANK INTEGER DEFAULT 0",
        );
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN UPI_PIN TEXT DEFAULT ''",
        );
      }
      if (oldVersion < 9 && newVersion >= 9) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN STATIC_PASS TEXT DEFAULT ''",
        );
      }
      if (oldVersion < 10 && newVersion >= 10) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN HDFC_AUTOFILL INTEGER DEFAULT 0",
        );
      }
      if (oldVersion < 11 && newVersion >= 11) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN FARE_LIMIT INTEGER DEFAULT 0",
        );
      }
      if (oldVersion < 12 && newVersion >= 12) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN PYMT_AUTO_RC INTEGER DEFAULT 0",
        );
      }
      if (oldVersion < 13 && newVersion >= 13) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN DELAY_SEC INTEGER DEFAULT 52",
        );
      }
      if (oldVersion < 14 && newVersion >= 14) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN INSURANCE TEXT DEFAULT '1'",
        );
      }
      if (oldVersion < 15 && newVersion >= 15) {
        await db.execute(
          "ALTER TABLE $tableName ADD COLUMN BOOKING_OPTION TEXT",
        );
      }
      // New: ensure auxiliary tables exist when upgrading to v16+
      if (oldVersion < 16 && newVersion >= 16) {
        await db.execute('''
          CREATE TABLE IF NOT EXISTS GST_TABLE (
            FORM_NAME TEXT PRIMARY KEY,
            GST_IN TEXT,
            GST_NAME TEXT,
            GST_FLAT TEXT,
            GST_STREET TEXT,
            GST_PIN TEXT,
            GST_AREA TEXT,
            GST_CITY TEXT
          )
        ''');
        await db.execute('''
          CREATE TABLE IF NOT EXISTS ADDRESS_TABLE (
            FORM_NAME TEXT PRIMARY KEY,
            ADDR1 TEXT,
            ADDR2 TEXT,
            ADDR3 TEXT,
            PIN TEXT,
            CITY TEXT,
            PO TEXT
          )
        ''');
        await db.execute('''
          CREATE TABLE IF NOT EXISTS PASSENGER_TABLE (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            FORM_NAME TEXT,
            CONCESSION TEXT,
            NATIONALITY TEXT,
            CARD_NO TEXT,
            VALIDITY TEXT,
            ID_TYPE TEXT,
            ID_NO TEXT,
            NAME TEXT,
            AGE INTEGER,
            GENDER TEXT,
            BERTH_PREF TEXT,
            MEAL TEXT,
            SENIOR_CITIZEN INTEGER,
            BED_ROLL TEXT,
            DOB TEXT,
            OPT_BERTH INTEGER
          )
        ''');
        await db.execute('''
          CREATE TABLE IF NOT EXISTS CHILD_TABLE (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            FORM_NAME TEXT,
            NAME TEXT,
            AGE TEXT,
            GENDER TEXT
          )
        ''');
        await db.execute('''
          CREATE TABLE IF NOT EXISTS POD_TABLE (
            FORM_NAME TEXT PRIMARY KEY,
            MOBILE TEXT
          )
        ''');
        await db.execute('''
          CREATE TABLE IF NOT EXISTS INSURANCE_TABLE (
            FORM_NAME TEXT PRIMARY KEY,
            INSURANCE INTEGER
          )
        ''');
        await db.execute('''
          CREATE TABLE IF NOT EXISTS SECURITY_QUESTIONS_TABLE (
            FORM_NAME TEXT PRIMARY KEY,
            DOB TEXT,
            EMAIL TEXT,
            MOBILE TEXT,
            EWALLET INTEGER
          )
        ''');
      }
    }
  }
}
