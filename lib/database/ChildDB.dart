import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class ChildDB {
  static const String tableName = 'CHILD_INFO';
  static const String dbName = 'Child.db';
  static const int dbVersion = 1;

  static Database? _database;
  static final ChildDB instance = ChildDB._internal();

  ChildDB._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await initDB();
    return _database!;
  }

  /// CREATE TABLE CHILD_INFO (FORM_NAME TEXT, NAME TEXT, AGE TEXT, GENDER TEXT)
  static const String createTable =
      '''
    CREATE TABLE $tableName (
      FORM_NAME TEXT,
      NAME TEXT,
      AGE TEXT,
      GENDER TEXT
    )
  ''';

  Future<Database> initDB() async {
    Directory documentsDir = await getApplicationDocumentsDirectory();
    String dbPath = join(documentsDir.path, dbName);

    bool dbExists = await File(dbPath).exists();

    if (!dbExists) {
      await copyDatabaseFromAssets(dbPath);
    }

    return await openDatabase(
      dbPath,
      version: dbVersion,
      onCreate: (db, version) async {
        await db.execute(createTable);
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        if (newVersion > oldVersion) {
          await copyDatabaseFromAssets(dbPath);
        }
      },
    );
  }

  Future<void> copyDatabaseFromAssets(String dbPath) async {
    try {
      ByteData data = await rootBundle.load('assets/$dbName');
      List<int> bytes = data.buffer.asUint8List(
        data.offsetInBytes,
        data.lengthInBytes,
      );

      await File(dbPath).writeAsBytes(bytes, flush: true);
    } catch (e) {
      print("Error copying database from assets: $e");
    }
  }
}
