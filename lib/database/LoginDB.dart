import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class LoginDB {
  static const String _dbName = "login.db";
  static const String _tableName = "MOBILE";

  static Database? _database;

  static String get tableName => _tableName; // Optional public access

  static Future<Database> get database async {
    if (_database != null) return _database!;
    return await _initDB();
  }

  static Future<Database> _initDB() async {
    final dir = await getApplicationDocumentsDirectory();
    final path = join(dir.path, _dbName);
    return await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) {
        db.execute('CREATE TABLE $_tableName (MOBILE_NO TEXT)');
      },
    );
  }

  // Save mobile login - mirrors Java saveLogin method
  static Future<void> saveLogin(String mobileNo) async {
    final db = await database;
    await db.delete(_tableName); // Clear previous entries
    await db.insert(_tableName, {'MOBILE_NO': mobileNo});
  }

  // Get saved login - mirrors Java getLogin method
  static Future<String?> getLogin() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> records = await db.query(_tableName);
      if (records.isNotEmpty) {
        return records.last['MOBILE_NO'] as String?;
      }
    } catch (e) {
      print("LoginDB getLogin error: $e");
    }
    return null;
  }

  // Check if login exists - mirrors Java hasLogin method
  static Future<bool> hasLogin() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> records = await db.query(_tableName);
      return records.isNotEmpty;
    } catch (e) {
      print("LoginDB hasLogin error: $e");
      return false;
    }
  }

  static Future<void> deleteAll() async {
    final db = await database;
    await db.delete(_tableName);
  }

  static Future<void> closeDB() async {
    final db = await database;
    await db.close();
    _database = null;
  }
}
