import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class InsuranceDB {
  static const String tableName = 'INSURANCE';
  static const String dbName = 'Ins.db';
  static const int dbVersion = 1;

  static Database? _database;
  static final InsuranceDB instance = InsuranceDB._internal();

  InsuranceDB._internal();

  static const String createTable =
      '''
    CREATE TABLE $tableName (
      FORM_NAME TEXT,
      INSURANCE INTEGER
    )
  ''';

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB();
    return _database!;
  }

  Future<Database> _initDB() async {
    Directory documentsDir = await getApplicationDocumentsDirectory();
    String dbPath = join(documentsDir.path, dbName);

    bool dbExists = await File(dbPath).exists();

    if (!dbExists) {
      await _copyDatabaseFromAssets(dbPath);
    }

    return await openDatabase(
      dbPath,
      version: dbVersion,
      onCreate: (db, version) async {
        await db.execute(createTable);
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        if (newVersion > oldVersion) {
          await _copyDatabaseFromAssets(dbPath);
        }
      },
    );
  }

  Future<void> _copyDatabaseFromAssets(String dbPath) async {
    try {
      ByteData data = await rootBundle.load('assets/$dbName');
      List<int> bytes = data.buffer.asUint8List(
        data.offsetInBytes,
        data.lengthInBytes,
      );
      await File(dbPath).writeAsBytes(bytes, flush: true);
    } catch (e) {
      print("InsuranceDB copy error: $e");
    }
  }
}
