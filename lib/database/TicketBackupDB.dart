import 'dart:io';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class TicketBackupDB {
  static const String dbName = 'tb_new.db';
  static const int dbVersion = 1;
  static const String tableName = 'TICKET_BACKUP_NEW';

  static Database? _database;
  static final TicketBackupDB instance = TicketBackupDB._internal();

  TicketBackupDB._internal();

  static const String createTable =
      '''
    CREATE TABLE $tableName (
      TICKETS INTEGER,
      USER_TYPE TEXT,
      EXPIRY TEXT
    )
  ''';

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB();
    return _database!;
  }

  Future<Database> _initDB() async {
    Directory dir = await getApplicationDocumentsDirectory();
    String path = join(dir.path, dbName);

    return await openDatabase(
      path,
      version: dbVersion,
      onCreate: (db, version) async {
        await db.execute(createTable);
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        // No upgrade logic as per Java code
      },
    );
  }
}
