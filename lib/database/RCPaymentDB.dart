import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class RCPaymentDB {
  static const String tableName = 'RC_PYMT_INFO';
  static const String dbName = 'rc_pymt.db';
  static const int dbVersion = 2;

  static Database? _database;
  static final RCPaymentDB instance = RCPaymentDB._internal();

  RCPaymentDB._internal();

  static const String createTable =
      '''
    CREATE TABLE $tableName (
      FORM_NAME TEXT,
      PYMT_METHOD TEXT,
      PYMT_ENTITY TEXT,
      BANK_NAME TEXT,
      CARD_TYPE TEXT,
      CARD_NO TEXT,
      EXP_YR TEXT,
      EXP_MON TEXT,
      CARD_HOLDER TEXT,
      PIN TEXT,
      CVV TEXT,
      WALLET TEXT,
      W_USER TEXT,
      TXN_PWD TEXT,
      NB_NAME TEXT,
      NB_TYPE TEXT,
      CORP_ID TEXT,
      NB_USER TEXT,
      NB_PWD TEXT,
      IMOBILE_MOB TEXT,
      UPI_ID TEXT
    )
  ''';

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB();
    return _database!;
  }

  Future<Database> _initDB() async {
    Directory docDir = await getApplicationDocumentsDirectory();
    String dbPath = join(docDir.path, dbName);

    bool exists = await File(dbPath).exists();
    if (!exists) {
      await _copyDatabaseFromAssets(dbPath);
    }

    return await openDatabase(
      dbPath,
      version: dbVersion,
      onCreate: (db, version) async {
        await db.execute(createTable);
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        if (newVersion > oldVersion) {
          await _copyDatabaseFromAssets(dbPath);

          if (newVersion == 2) {
            await db.execute(createTable);
          }
        }
      },
    );
  }

  Future<void> _copyDatabaseFromAssets(String dbPath) async {
    try {
      ByteData data = await rootBundle.load('assets/$dbName');
      List<int> bytes = data.buffer.asUint8List(
        data.offsetInBytes,
        data.lengthInBytes,
      );
      await File(dbPath).writeAsBytes(bytes, flush: true);
    } catch (e) {
      print("RCPaymentDB copy error: $e");
    }
  }
}
