import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class PassengerDB {
  static const String dbName = 'Passenger.db';
  static const int dbVersion = 2;
  static const String tableName = 'PASSENGER_INFO';

  static Database? _database;
  static final PassengerDB instance = PassengerDB._internal();

  PassengerDB._internal();

  static const String createTable =
      '''
    CREATE TABLE $tableName (
      FORM_NAME TEXT,
      CONCESSION TEXT,
      NATIONALITY TEXT,
      CARD_NO TEXT,
      CARD_VALIDITY TEXT,
      ID_TYPE TEXT,
      ID_NO TEXT,
      NAME TEXT,
      AGE INTEGER,
      GENDER TEXT,
      BERTH TEXT,
      MEAL TEXT,
      SENIOR TEXT,
      BEDROLL TEXT,
      DOB TEXT,
      OPT_BERTH INTEGER
    )
  ''';

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB();
    return _database!;
  }

  Future<Database> _initDB() async {
    Directory docDir = await getApplicationDocumentsDirectory();
    String dbPath = join(docDir.path, dbName);

    bool exists = await File(dbPath).exists();

    if (!exists) {
      await _copyDatabaseFromAssets(dbPath);
    }

    return await openDatabase(
      dbPath,
      version: dbVersion,
      onCreate: (db, version) async {
        await db.execute(createTable);
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        if (newVersion > oldVersion) {
          await _copyDatabaseFromAssets(dbPath);
          if (newVersion == 2 && oldVersion == 1) {
            await db.execute(
              'ALTER TABLE $tableName ADD COLUMN OPT_BERTH INTEGER DEFAULT 0',
            );
          }
        }
      },
    );
  }

  Future<void> _copyDatabaseFromAssets(String dbPath) async {
    try {
      ByteData data = await rootBundle.load('assets/$dbName');
      List<int> bytes = data.buffer.asUint8List(
        data.offsetInBytes,
        data.lengthInBytes,
      );
      await File(dbPath).writeAsBytes(bytes, flush: true);
    } catch (e) {
      print("PassengerDB Error copying DB: $e");
    }
  }
}
