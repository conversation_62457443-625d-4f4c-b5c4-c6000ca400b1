import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class AddressDB {
  static const String dbName = 'address.db';
  static const int dbVersion = 2;
  static const String tableName = 'ADDRESS_TBL';

  static const String createTable =
      '''
    CREATE TABLE $tableName (
      FORM_NAME TEXT,
      ADDR1 TEXT,
      ADDR2 TEXT,
      ADDR3 TEXT,
      PIN TEXT,
      CITY TEXT,
      PO TEXT
    )
  ''';

  Database? _database;
  static final AddressDB instance = AddressDB._internal();

  AddressDB._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB();
    return _database!;
  }

  Future<Database> _initDB() async {
    final Directory documentsDirectory =
        await getApplicationDocumentsDirectory();
    final dbPath = join(documentsDirectory.path, dbName);

    final dbExists = await File(dbPath).exists();

    if (!dbExists) {
      await _copyDatabaseFromAssets(dbPath);
    }

    return await openDatabase(
      dbPath,
      version: dbVersion,
      onCreate: (db, version) async {
        await db.execute(createTable);
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        if (newVersion > oldVersion) {
          await _copyDatabaseFromAssets(dbPath);
        }
      },
    );
  }

  Future<void> _copyDatabaseFromAssets(String dbPath) async {
    try {
      final ByteData data = await rootBundle.load('assets/$dbName');
      final List<int> bytes = data.buffer.asUint8List(
        data.offsetInBytes,
        data.lengthInBytes,
      );
      await File(dbPath).writeAsBytes(bytes, flush: true);
    } catch (e) {
      print('AddressDB Error copying DB: $e');
    }
  }
}
