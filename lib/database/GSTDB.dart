import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class GSTDB {
  static const String tableName = 'GST_INFO_TBL';
  static const String dbName = 'GstInfo.db';
  static const int dbVersion = 2;

  static Database? _database;
  static final GSTDB instance = GSTDB._internal();

  GSTDB._internal();

  static const String createTable =
      '''
    CREATE TABLE $tableName (
      FORM_NAME TEXT,
      GSTIN TEXT,
      GST_NAME TEXT,
      GST_FLAT TEXT,
      GST_STREET TEXT,
      GST_PIN TEXT,
      GST_AREA TEXT,
      GST_CITY TEXT
    )
  ''';

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB();
    return _database!;
  }

  Future<Database> _initDB() async {
    Directory documentsDir = await getApplicationDocumentsDirectory();
    String dbPath = join(documentsDir.path, dbName);

    bool dbExists = await File(dbPath).exists();

    if (!dbExists) {
      await _copyDatabaseFromAssets(dbPath);
    }

    return await openDatabase(
      dbPath,
      version: dbVersion,
      onCreate: (db, version) async {
        await db.execute(createTable);
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        if (newVersion > oldVersion) {
          await _copyDatabaseFromAssets(dbPath);
        }
      },
    );
  }

  Future<void> _copyDatabaseFromAssets(String dbPath) async {
    try {
      ByteData data = await rootBundle.load('assets/$dbName');
      List<int> bytes = data.buffer.asUint8List(
        data.offsetInBytes,
        data.lengthInBytes,
      );
      await File(dbPath).writeAsBytes(bytes, flush: true);
    } catch (e) {
      print("GstDB Error copying DB: $e");
    }
  }
}
