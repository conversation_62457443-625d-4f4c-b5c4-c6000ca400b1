import 'package:flutter/material.dart';

// API Constants
class APIConsts {
  static String UPI_TRIAL_OPTED = 'Y';
}

// App Constants
class SplashActivity {
  static int isGoldUser = 0;
}

// Color Constants
const Color kPrimaryColor = Color(0xFF232334);
const Color kDialogColor = Color(0xFF272728);
const Color kGrayBgEnabled = Color(0xFF424259);
const Color kGrayBgDisabled = Color(0xFF21212A);
const double kBigRadius = 30.0;

// UPI App IDs
class UPIAppIds {
  static const String APP_ID_GPAY = 'com.google.android.apps.nbu.paisa.user';
  static const String APP_ID_PAYTM = 'net.one97.paytm';
  static const String APP_ID_PHONEPE = 'com.phonepe.app';
  static const String APP_ID_BHIM = 'in.org.npci.upiapp';
}

// Dropdown Options
class DropdownOptions {
  static const List<String> quotaOptions = [
    'General',
    'Tatkal',
    'Premium Tatkal',
    'Physically Handicapped',
    'Ladies',
    'Duty Pass',
    'Lower Berth',
  ];

  static const List<String> classOptions = [
    'SL',
    '3A',
    '2A',
    '1A',
    '2S',
    'CC',
    'FC',
    'EC',
    'EA',
  ];

  static const List<String> genderOptions = ['Male', 'Female', 'Transgender'];

  static const List<String> childAgeOptions = ['0', '1', '2', '3', '4'];

  static const List<String> berthOptions = [
    'No Preference',
    'Lower',
    'Middle',
    'Upper',
    'Side Lower',
    'Side Upper',
  ];

  static const List<String> mealOptions = ['No Meal', 'Veg', 'Non-Veg', 'Jain'];

  static const List<String> concessionOptions = [
    '',
    'Handicapped',
    'Escort',
    'Student',
    'Senior',
  ];

  static const List<String> nationalityOptions = [
    'India',
    'USA',
    'UK',
    'Canada',
    'Australia',
    'Other',
  ];

  static const List<String> idTypeOptions = [
    '',
    'Aadhaar Card',
    'PAN Card',
    'Voter Card',
    'Driving License',
    'Passport',
  ];

  static const List<String> upiBankOptions = [
    'Select Bank',
    'State Bank of India',
    'HDFC Bank',
    'ICICI Bank',
    'Axis Bank',
    'Punjab National Bank',
    'Bank of Baroda',
    'Canara Bank',
    'Union Bank of India',
    'Bank of India',
    'Indian Bank',
  ];

  static const List<String> delayOptions = [
    '5 seconds',
    '10 seconds',
    '15 seconds',
    '20 seconds',
    '30 seconds',
  ];
}

// Static Data
class StaticData {
  static const List<String> stationList = [
    'NEW DELHI - NDLS',
    'MUMBAI CENTRAL - BCT',
    'CHENNAI CENTRAL - MAS',
    'KOLKATA - KOAA',
    'BANGALORE - SBC',
    'HYDERABAD - HYB',
    'PUNE - PUNE',
    'AHMEDABAD - ADI',
    'KANPUR CENTRAL - CNB',
    'NAGPUR - NGP',
  ];

  static const List<String> trainList = [
    '12601 - MANGALORE MAIL',
    '12951 - MUMBAI RAJDHANI',
    '12301 - HOWRAH RAJDHANI',
    '12009 - SHATABDI EXP',
    '12434 - CHENNAI RAJDHANI',
    '22691 - RAJDHANI EXP',
    '12002 - BHOPAL SHTBDI',
    '12840 - HOWRAH MAIL',
    '16031 - ANDAMAN EXPRESS',
    '12626 - KERALA EXPRESS',
  ];
}