import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import '../core/Consts.dart';
import '../core/AppConstants.dart';
import '../database/LoginDB.dart';
import '../quick/EmailFragment.dart';
import '../quick/PhoneFragment.dart';
import '../screens/SignInSocialScreen.dart';
import '../screens/SignUpScreen.dart';
import '../screens/splash_screen.dart';
import 'ServerTask.dart';
import '../core/helper/mixpanel_manager.dart';
import '../captcha/APIConsts.dart';

class FirestoreFunctions {
  static const String TAG = "FirestoreFunctions";

  final FirebaseFirestore db = FirebaseFirestore.instance;
  final BuildContext? context;
  late MixpanelManager mixpanel;

  static String prevTid = "";
  int prevTickets = 0;
  String prevUserType = "";
  Timestamp? prevExpiry;
  String prevExpiryStr = "";
  int prevPackExpired = 0;

  static Map<String, int> userLevels = {};

  static bool checkPreviousAccount = false;
  bool navigateFlag = false;

  PhoneFragment? phoneFragment;
  EmailFragment? emailFragment;

  static String debugSequence = "";

  static int noOfRetries = 0;
  String loginSource = "LATER";

  int idNotFoundFlag = 0;

  static List<String> activePromoCodes = [];

  FirestoreFunctions(this.context) {
    _initializeUserLevels();
    Consts.updateSource = "";
    debugSequence = "";
    mixpanel = MixpanelManager();
  }

  FirestoreFunctions.withContext(BuildContext context) : this(context);

  FirestoreFunctions.withPhoneFragment(
    BuildContext? context,
    PhoneFragment? phoneFragment,
  ) : this.context = context,
      this.phoneFragment = phoneFragment {
    _initializeUserLevels();
    Consts.updateSource = "";
    debugSequence = "";
    mixpanel = MixpanelManager();
  }

  FirestoreFunctions.withEmailFragment(
    BuildContext? context,
    EmailFragment? emailFragment,
  ) : this.context = context,
      this.emailFragment = emailFragment {
    _initializeUserLevels();
    Consts.updateSource = "";
    debugSequence = "";
    mixpanel = MixpanelManager();
  }

  void _initializeUserLevels() {
    userLevels.clear();
    userLevels["FREE_USER"] = 0;
    userLevels["COMP_USER"] = 1;
    userLevels["STARTER_USER"] = 2;
    userLevels["PREMIUM_USER"] = 3;
    userLevels["GOLD_USER"] = 4;
    userLevels["DIAMOND_USER"] = 5;
  }

  Future<void> getTicketsNew(String source) async {
    blankLoginIDs();

    if (Consts.DEVICE_ID.isEmpty) {
      if (context != null) {
        ScaffoldMessenger.of(context!).showSnackBar(
          const SnackBar(content: Text("Technical Error. Please try again")),
        );
        Navigator.of(context!).pop();
      }
      return;
    }

    bool isEmailNull = SplashScreenState.PRIMARY_EMAIL == "NA";
    bool isMobileNull = SplashScreenState.MOBILE_NO == "NA";

    if (isEmailNull && isMobileNull) {
      await logout(Consts.loginMethod);
      await getTicketsSignUpLater();
      return;
    }

    final mAuth = FirebaseAuth.instance;
    final fUser = mAuth.currentUser;

    if (Consts.loginMethod == "FACEBOOK" && fUser == null) {
      debugPrint("Facebook login method but no Firebase user, logging out...");
      await logout(Consts.loginMethod);
      await getTicketsSignUpLater();
      return;
    }

    if (fUser == null) {
      try {
        await mAuth.signInAnonymously();
        await getTicketsNewTask(source);
      } catch (e) {
        await getTicketsNewTask(source);
      }
    } else {
      await getTicketsNewTask(source);
    }
  }

  // Helper: Validates numeric TID
  bool _isValidTid(String tid) {
    final parsed = int.tryParse(tid);
    return parsed != null && parsed > 0;
  }

  // Improved: Converts TID and warns if collision might be possible
  String _convertAlphaNumericTidToNumeric(String alphaNumericTid) {
    int hashCode = alphaNumericTid.hashCode.abs();
    // (Hash may still have collision risk. Warn if desired.)
    String numericTid = (hashCode % ********* + *********)
        .toString(); // range: ********* .. *********
    debugPrint("Converted TID: $alphaNumericTid -> $numericTid");
    return numericTid;
  }

  Future<void> getTicketsNewTask(String source) async {
    debugSequence += "getTicketsNewTask -> ";
    loginSource = source;

    if (SplashScreenState.tid == "0") {
      checkPreviousAccount = false;
    }

    if (!_isValidTid(SplashScreenState.tid)) {
      String originalTid = SplashScreenState.tid;
      SplashScreenState.tid = _convertAlphaNumericTidToNumeric(originalTid);
      debugPrint(
        "Converted alphanumeric TID to numeric: $originalTid -> ${SplashScreenState.tid}",
      );
    }

    if (checkPreviousAccount) {
      prevTid = SplashScreenState.tid;
      prevTickets = SplashScreenState.ticketsLeft;
      prevUserType = Consts.USER_TYPE;
      prevExpiryStr = Consts.PACK_EXPIRY_DATE;
      prevPackExpired = Consts.PACK_EXPIRED;
    }

    debugPrint("FIRESTORE DEBUG: Start fetching account");
    Query query;

    if (source == "MOBILE") {
      final mobile = SplashScreenState.MOBILE_NO == "NA"
          ? "NIL"
          : SplashScreenState.MOBILE_NO;
      query = db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .where("mobile", whereIn: [mobile, "VISHALAFRE"]);
    } else {
      final email = SplashScreenState.PRIMARY_EMAIL == "NA"
          ? "NIL"
          : SplashScreenState.PRIMARY_EMAIL;
      query = db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .where("primary_email", whereIn: [email, "VISHALAFRE"]);
    }

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );
      debugSequence += "Complete -> ";
      debugPrint("FIRESTORE DEBUG: QUERY COMPLETE");

      if (context != null) {
        try {
          final widget = (context as dynamic).widget;
          if (widget is SignUpScreen) {}
          if (widget is SignInSocialScreen) {}
        } catch (e) {}
      }

      if (querySnapshot.docs.isNotEmpty) {
        debugSequence += "Task successful -> ";
        debugPrint("FIRESTORE DEBUG: TASK SUCCESSFUL");

        debugSequence += "Snapshot OK -> ";
        debugSequence += "Snapshot not empty -> ";
        Consts.loginMethod = source;

        try {
          final docs = querySnapshot.docs;
          DocumentSnapshot documentSnapshot = docs.first;
          Map<String, dynamic> data =
              documentSnapshot.data() as Map<String, dynamic>;

          String tid = data["tid"].toString();
          if (tid == "1") {
            if (docs.length < 2) {
              if (Consts.isLoggedInBefore == 1 && idNotFoundFlag == 0) {
                debugSequence += "Logged in before -> ";
                await updateMobileEmail();
                idNotFoundFlag = 1;
              } else {
                debugSequence += "New user -> ";
                await registerSignedInUser();
              }
              return;
            } else if (docs.length == 2) {
              debugSequence += "2 Docs -> ";
              documentSnapshot = docs[1];
              data = documentSnapshot.data() as Map<String, dynamic>;
            }
          }

          if (docs.length > 2) {
            debugSequence += ">2 docs -> ";
            final props = {"Count": docs.length};
            MixpanelManager().track("Fix multiple id", props);

            for (var snap in docs) {
              final snapData = snap.data() as Map<String, dynamic>;
              final mDeviceId = snapData["device_id"] as String?;
              if (mDeviceId != "VISHALAFRE" && mDeviceId != Consts.DEVICE_ID) {
                final otherTID = snap.get("tid").toString();
                if (source == "MOBILE") {
                  await updateMobile(otherTID, "NA");
                } else {
                  await updateEmail(otherTID, "NA", "NA");
                }
              } else {
                documentSnapshot = snap;
                data = documentSnapshot.data() as Map<String, dynamic>;
              }
            }
          }

          MixpanelManager().track("Firebase login success", {});

          SplashScreenState.ticketsLeft = (data["tickets"] as num).toInt();
          SplashScreenState.tid = data["tid"].toString();
          MixpanelManager().identify(SplashScreenState.tid);

          String userType = data["user_type"] as String? ?? "";
          if (userType.isEmpty) {
            userType = "FREE_USER";
          }
          Consts.USER_TYPE = userType;

          SplashScreenState.MOBILE_NO ??= "NA";
          SplashScreenState.EMAIL ??= "NA";
          SplashScreenState.PRIMARY_EMAIL ??= "NA";

          if (!checkPreviousAccount ||
              (SplashScreenState.EMAIL == "NA" &&
                  SplashScreenState.MOBILE_NO == "NA")) {
            SplashScreenState.EMAIL = data["email"] as String? ?? "NA";
            SplashScreenState.MOBILE_NO = data["mobile"] as String? ?? "NA";
            SplashScreenState.PRIMARY_EMAIL =
                data["primary_email"] as String? ?? "NA";
          }

          SplashScreenState.CUST_NAME = data["cust_name"] as String? ?? "";
          APIConsts.UPI_TRIAL_OPTED = data["upi_trial_opted"] as String? ?? "N";
          APIConsts.CAPTCHA_TRIAL_OPTED =
              data["captcha_trial_opted"] as String? ?? "N";

          Timestamp? expiryDate = data["expiry_date"] as Timestamp?;

          if (SplashScreenState.ticketsLeft == -2) {
            Consts.USER_TYPE = "DIAMOND_USER";
            expiryDate = null;
          }

          bool updateFlag = false;
          if (expiryDate != null) {
            final formatter = DateFormat("dd-MMM-yyyy", "en_US");
            Consts.PACK_EXPIRY_DATE = formatter.format(expiryDate.toDate());

            final fetchedDate = expiryDate.toDate();
            final nextDay = fetchedDate.add(const Duration(days: 1));
            final currentDate = DateTime.now();
            Consts.PACK_EXPIRED = nextDay.isBefore(currentDate) ? 1 : 0;

            if (Consts.PACK_EXPIRED == 1) {
              Consts.USER_TYPE = "COMP_USER";
              SplashScreenState.isGoldUser = 0;
              updateFlag = true;
            }
          }

          updateFlag = await restorePackAndTickets(updateFlag);

          if (Consts.USER_TYPE == "DIAMOND_USER" ||
              Consts.USER_TYPE == "GOLD_USER") {
            SplashScreenState.isGoldUser = 2;
          } else {
            SplashScreenState.isGoldUser = 0;
          }

          if (checkPreviousAccount) {
            await updateBothTIDs(expiryDate);
          }
          if (updateFlag) {
            Consts.updateSource += "4";
            debugSequence += "getTicketsNew.updateTickets -> ";
            await updateTickets();
          }

          debugPrint("FIRESTORE DEBUG: Start navigate");
          await doNavigateSignIn();
        } catch (e) {
          debugSequence += "Exception:${e.toString()} -> ";
          final props = {"Error": e.toString()};
          MixpanelManager().track("Firebase login exception", props);

          await showErrorScreen(
            exception:
                "Sign in method: $source\n\nError getting tickets\n\n${e.toString()}",
            errorMsg: "Login Exception",
          );
        }
      } else {
        debugSequence += "Snapshot empty -> ";
        final props = {"Source": "GetTicketsNew"};
        MixpanelManager().track("Unstable network connection", props);

        await showErrorScreen(
          exception: "Unstable network connection. Please try again",
          errorMsg: "Error code 2: Unstable network connection",
        );
      }
    } catch (e) {
      debugSequence += "Task unsuccessful -> ";
      await errorTaskSignIn(source, e);
    }
  }

  Future<bool> restorePackAndTickets(bool flag) async {
    bool updateFlag = flag;
    final sp = await SharedPreferences.getInstance();
    String spUserType = sp.getString("user_type") ?? "FREE_USER";
    int spTickets = sp.getInt("tickets") ?? 0;
    String spExpiryDt = sp.getString("expiry_date") ?? "";

    if (spTickets == -2 && SplashScreenState.ticketsLeft != -2) {
      SplashScreenState.ticketsLeft = -2;
      Consts.PACK_EXPIRY_DATE = "";
      Consts.USER_TYPE = "DIAMOND_USER";
      updateFlag = true;
    } else {
      bool spExpired = false;
      if (spExpiryDt.isNotEmpty) {
        final formatter = DateFormat("dd-MMM-yyyy", "en_US");

        try {
          final fetchedDate = formatter.parse(spExpiryDt);
          final nextDay = fetchedDate.add(const Duration(days: 1));
          final currentDate = DateTime.now();
          spExpired = nextDay.isBefore(currentDate);
        } catch (e) {}
      }

      final spUserLevel = userLevels[spUserType] ?? 0;
      final currentUserLevel = userLevels[Consts.USER_TYPE] ?? 0;

      if (spUserLevel > currentUserLevel && !spExpired) {
        Consts.USER_TYPE = spUserType;
        Consts.PACK_EXPIRY_DATE = spExpiryDt;
        if (spTickets > SplashScreenState.ticketsLeft) {
          SplashScreenState.ticketsLeft = spTickets;
        }
        updateFlag = true;
      } else if (spTickets > SplashScreenState.ticketsLeft &&
          SplashScreenState.ticketsLeft != -2) {
        SplashScreenState.ticketsLeft = spTickets;
        updateFlag = true;
      }
    }
    return updateFlag;
  }

  static void blankLoginIDs() {
    bool mobile =
        SplashScreenState.MOBILE_NO.isEmpty ||
        SplashScreenState.MOBILE_NO == "NA";
    if (mobile) {
      SplashScreenState.MOBILE_NO = "NA";
    }

    bool pEmail =
        SplashScreenState.PRIMARY_EMAIL.isEmpty ||
        SplashScreenState.PRIMARY_EMAIL == "NA";
    if (pEmail) {
      SplashScreenState.PRIMARY_EMAIL = "NA";
    }

    bool email =
        SplashScreenState.EMAIL.isEmpty || SplashScreenState.EMAIL == "NA";
    if (email) {
      SplashScreenState.EMAIL = "NA";
    }
  }

  Future<void> errorTaskSignIn(String source, dynamic e) async {
    debugPrint("Firestore Error completing query tasks: $e");

    String stackTrace = 'No stacktrace';
    String message = 'Firestore data empty';

    try {
      message = e.toString();
      stackTrace = StackTrace.current.toString();
    } catch (ex) {
      stackTrace = "No stacktrace";
      message = "Firestore data empty";
    }

    final props = {"Error": message, "Source": "GetTickets Main"};

    try {
      MixpanelManager().track("Firebase task exception", props);
    } catch (e1) {
      try {
        MixpanelManager().track("Firebase task exception", {
          "Error": "NA",
          "Source": "GetTickets Main",
        });
      } catch (ex) {}
    }

    await showErrorScreen(
      exception: "Sign in method: $source\n\n$message\n\n$stackTrace",
      errorMsg: "Exception in login task",
    );
  }

  Future<void> getTicketsSignUpLater() async {
    if (Consts.DEVICE_ID.isEmpty) {
      if (context != null) {
        ScaffoldMessenger.of(context!).showSnackBar(
          const SnackBar(content: Text("Technical Error. Please try again")),
        );
        Navigator.of(context!).pop();
      }
      return;
    }

    final mAuth = FirebaseAuth.instance;
    final fUser = mAuth.currentUser;

    if (fUser == null) {
      try {
        await mAuth.signInAnonymously();
        await getTicketsSignUpLaterTask();
      } catch (e) {
        await getTicketsSignUpLaterTask();
      }
    } else {
      await getTicketsSignUpLaterTask();
    }
  }

  Future<void> getTicketsSignUpLaterTask() async {
    debugSequence += "getTicketsSignUpLaterTask -> ";

    final mAuth = FirebaseAuth.instance;
    final fUser = mAuth.currentUser;

    if (fUser != null && !fUser.isAnonymous && Consts.loginMethod == "LATER") {
      debugPrint(
        "Detected Firebase user but loginMethod is LATER, checking provider...",
      );

      bool hasFacebookProvider = fUser.providerData.any(
        (provider) => provider.providerId == 'facebook.com',
      );

      if (hasFacebookProvider) {
        debugPrint(
          "User has Facebook provider, switching to Facebook login flow...",
        );
        Consts.loginMethod = "FACEBOOK";

        String? fbEmail = fUser.email;
        if (fbEmail != null && fbEmail.isNotEmpty) {
          SplashScreenState.PRIMARY_EMAIL = fbEmail;
          SplashScreenState.EMAIL = fbEmail;
        }

        await getTicketsNewTask("FACEBOOK");
        return;
      }
    }

    Query query = db
        .collection(Consts.TICKETS_COLLECTION_NAME)
        .where("device_id", whereIn: [Consts.DEVICE_ID, "VISHALAFRE"]);

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );
      debugSequence += "Complete -> ";
      debugPrint("FIRESTORE DEBUG: QUERY COMPLETE");

      if (context != null) {
        try {
          final widget = (context as dynamic).widget;
          if (widget is SignUpScreen) {}
          if (widget is SignInSocialScreen) {}
        } catch (e) {}
      }

      if (querySnapshot.docs.isNotEmpty) {
        debugSequence += "Task successful -> ";
        debugPrint("FIRESTORE DEBUG: TASK SUCCESSFUL");

        debugSequence += "Snapshot not empty -> ";
        try {
          Consts.loginMethod = "LATER";

          DocumentSnapshot documentSnapshot = querySnapshot.docs.first;
          Map<String, dynamic> data =
              documentSnapshot.data() as Map<String, dynamic>;

          String tid = data["tid"].toString();
          if (tid == "1") {
            if (querySnapshot.docs.length < 2) {
              debugSequence += "New user -> ";
              await registerUserWithDeviceId();
              return;
            } else {
              debugSequence += "2 docs -> ";
              documentSnapshot = querySnapshot.docs[1];
              data = documentSnapshot.data() as Map<String, dynamic>;
            }
          }

          MixpanelManager().track("Firebase login success", {});

          SplashScreenState.ticketsLeft = (data["tickets"] as num).toInt();
          SplashScreenState.tid = data["tid"].toString();
          MixpanelManager().identify(SplashScreenState.tid);

          String userType = data["user_type"] as String? ?? "";
          if (userType.isEmpty) {
            userType = "FREE_USER";
          }
          Consts.USER_TYPE = userType;

          APIConsts.UPI_TRIAL_OPTED = data["upi_trial_opted"] as String? ?? "N";
          APIConsts.CAPTCHA_TRIAL_OPTED =
              data["captcha_trial_opted"] as String? ?? "N";

          Timestamp? expiryDate = data["expiry_date"] as Timestamp?;

          if (SplashScreenState.ticketsLeft == -2) {
            Consts.USER_TYPE = "DIAMOND_USER";
            expiryDate = null;
          }

          bool updateFlag = false;
          if (expiryDate != null) {
            final formatter = DateFormat("dd-MMM-yyyy", "en_US");
            Consts.PACK_EXPIRY_DATE = formatter.format(expiryDate.toDate());

            final fetchedDate = expiryDate.toDate();
            final nextDay = fetchedDate.add(const Duration(days: 1));
            final currentDate = DateTime.now();

            Consts.PACK_EXPIRED = nextDay.isBefore(currentDate) ? 1 : 0;

            if (Consts.PACK_EXPIRED == 1) {
              Consts.USER_TYPE = "COMP_USER";
              SplashScreenState.isGoldUser = 0;

              Consts.updateSource += "6";
              debugSequence += "getTicketsSignUpLaterTask.updateTickets -> ";
              updateFlag = true;
            }
          }

          updateFlag = await restorePackAndTickets(updateFlag);

          if (Consts.USER_TYPE == "DIAMOND_USER" ||
              Consts.USER_TYPE == "GOLD_USER") {
            SplashScreenState.isGoldUser = 2;
          } else {
            SplashScreenState.isGoldUser = 0;
          }

          if (updateFlag) {
            await updateTickets();
          }

          await doNavigateDeviceId();
        } catch (e) {
          debugSequence += "Exception:${e.toString()} -> ";
          final props = {"Error": e.toString()};
          MixpanelManager().track("Firebase login exception", props);

          await showErrorScreen(
            exception: "Not signed in\n\n${e.toString()}",
            errorMsg: "Device exception",
          );
        }
      } else {
        debugSequence += "Snapshot empty -> ";
        final props = {"Source": "SignupLater"};
        MixpanelManager().track("Unstable network connection", props);

        await showErrorScreen(
          exception: "Unstable network connection. Please try again",
          errorMsg: "Error code 4: Unstable network connection",
        );
      }
    } catch (e) {
      debugSequence += "Task unsuccessful -> ";
      await errorTaskSignupLater(e);
    }
  }

  Future<void> errorTaskSignupLater(dynamic e) async {
    debugPrint("Firestore Error getting documents: $e");

    String stackTrace = 'No stacktrace';
    String message = 'Firestore data empty';

    try {
      message = e?.toString() ?? 'Unknown error';
      stackTrace = StackTrace.current.toString();
    } catch (ex) {
      stackTrace = "No stacktrace";
      message = "Firestore data empty";
    }

    final props = {"Error": message, "Source": "GetDeviceId"};

    try {
      MixpanelManager().track("Firebase task exception", props);
    } catch (e1) {
      try {
        MixpanelManager().track("Firebase task exception", {
          "Error": "NA",
          "Source": "GetDeviceId",
        });
      } catch (ex) {}
    }

    debugPrint('-- FirestoreFunctions Error Debug Info --');
    debugPrint('Login method: ${Consts.loginMethod}');
    debugPrint('FirebaseAuth user: ${FirebaseAuth.instance.currentUser}');
    debugPrint('PRIMARY_EMAIL: ${SplashScreenState.PRIMARY_EMAIL}');
    debugPrint('MOBILE_NO: ${SplashScreenState.MOBILE_NO}');
    debugPrint('tid: ${SplashScreenState.tid}');
    debugPrint('-- End Error Debug --');

    await showErrorScreen(
      exception:
          "Not signed in, Error getting tickets\n\n${message}\n\n${stackTrace}",
      errorMsg: "Exception in device task",
    );
  }

  Future<void> updateBothTIDs(Timestamp? expiryDate) async {
    debugSequence += "updateBothTIDs -> ";

    String otherTID = SplashScreenState.tid;
    SplashScreenState.tid = prevTid;

    if (SplashScreenState.ticketsLeft != -2) {
      int adjustedPrevTickets = prevTickets;
      if (prevTickets == -2) {
        adjustedPrevTickets = 9999;
      }

      int prevLevel = 0;
      int currentLevel = 0;

      try {
        prevLevel = userLevels[prevUserType] ?? 0;
      } catch (ex) {}

      try {
        currentLevel = userLevels[Consts.USER_TYPE] ?? 0;
      } catch (ex) {}

      /*if(prevExpiry != null) {
        final nextDay = prevExpiry!.toDate().add(const Duration(days: 1));
        if(nextDay.isBefore(DateTime.now())) {
          prevLevel = 1;
          prevUserType = "COMP_USER";
        }
      }*/

      if (adjustedPrevTickets > SplashScreenState.ticketsLeft) {
        SplashScreenState.ticketsLeft = adjustedPrevTickets;
      }

      if (prevLevel > currentLevel || adjustedPrevTickets == 9999) {
        Consts.USER_TYPE = prevUserType;
        Consts.PACK_EXPIRY_DATE = prevExpiryStr;
        Consts.PACK_EXPIRED = prevPackExpired;
      } else if (prevExpiry != null &&
          expiryDate != null &&
          prevLevel == currentLevel) {
        if (prevExpiry!.toDate().isAfter(expiryDate.toDate())) {
          Consts.USER_TYPE = prevUserType;
          Consts.PACK_EXPIRY_DATE = prevExpiryStr;
          Consts.PACK_EXPIRED = prevPackExpired;
        }
      }
    }

    if (SplashScreenState.ticketsLeft == 9999) {
      SplashScreenState.ticketsLeft = -2;
    }

    Consts.updateSource += "7";
    debugSequence += "updateBothTIDs.updateTickets -> ";
    await updateTickets();

    if (SplashScreenState.tid != otherTID) {
      if (Consts.loginMethod == "MOBILE") {
        await updateMobile(otherTID, "NA");
      } else {
        await updateEmail(otherTID, "NA", "NA");
      }
    }
  }

  Future<void> copyUserDataFromDeviceId(String data) async {
    try {
      List<String> parts = data.split("•");

      SplashScreenState.ticketsLeft = int.parse(parts[0]);
      String isGoldUser = parts[2];
      if (isGoldUser == "Y") {
        SplashScreenState.isGoldUser = 2;
      } else {
        SplashScreenState.isGoldUser = 0;
      }

      SplashScreenState.tid = parts[7];
      MixpanelManager().identify(SplashScreenState.tid);

      String userType = parts[12];

      if (userType.isEmpty) {
        userType = "FREE_USER";
      }
      Consts.USER_TYPE = userType;

      APIConsts.UPI_TRIAL_OPTED = parts[10];
      APIConsts.CAPTCHA_TRIAL_OPTED = parts[11];

      bool dateSet = false;
      DateTime? date;
      if (parts.length > 14) {
        String expiryDate = parts[14];
        if (expiryDate.startsWith("0000")) {
          expiryDate = "";
        }
        final sdfDb = DateFormat("yyyy-MM-dd", "en_US");
        try {
          date = sdfDb.parse(expiryDate);
          final sdFormat = DateFormat("dd-MMM-yyyy", "en_US");
          Consts.PACK_EXPIRY_DATE = sdFormat.format(date);
          dateSet = true;
        } catch (e) {}

        if (SplashScreenState.ticketsLeft == -2) {
          Consts.USER_TYPE = "DIAMOND_USER";
          Consts.PACK_EXPIRY_DATE = "";
          dateSet = false;
        }

        if (Consts.USER_TYPE != "FREE_USER" &&
            Consts.USER_TYPE != "COMP_USER" &&
            dateSet) {
          final nextDay = date!.add(const Duration(days: 1));
          if (nextDay.isBefore(DateTime.now())) {
            Consts.PACK_EXPIRED = 1;
            Consts.USER_TYPE = "COMP_USER";
            SplashScreenState.isGoldUser = 0;
          } else {
            Consts.PACK_EXPIRED = 0;
          }
        }
      }

      debugPrint("FIRESTORE DEBUG: START DATA SAVE");
      await copyDeviceDataToFirestore(data);
    } catch (e) {
      await showErrorScreen(
        exception: "Error saving device data${e.toString()}",
        errorMsg: "Technical error",
      );
    }
  }

  Future<void> doNavigateSignIn() async {
    debugSequence += "doNavigateSignIn -> ";
    if (Consts.loginMethod == null) {
      MixpanelManager().track("Logged out unexpectedly", {});
      await logout("LATER");
      await doNavigateDeviceId();
      return;
    }

    if (SplashScreenState.tid == "0") {
      await showErrorScreen(
        exception:
            "TID: ${SplashScreenState.tid} Unable to fetch user data.\nSign in method: ${Consts.loginMethod}",
        errorMsg: "Logged in User ID 0",
      );
      return;
    }

    noOfRetries = 0;

    await checkFailedPaymentSave();

    MixpanelManager().track("Start Dashboard navigation", {});

    try {
      final props = {
        "User Type": Consts.USER_TYPE,
        "App": Consts.APP,
        "Tickets left": SplashScreenState.ticketsLeft,
      };
      MixpanelManager().registerSuperProperties(props);
    } catch (e) {}

    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final installDate = DateTime.fromMillisecondsSinceEpoch(
        packageInfo.buildNumber.hashCode,
      );
      final sdf = DateFormat("dd-MM-yyyy HH:mm:ss", "en_US");
      final installDateStr = sdf.format(installDate);

      MixpanelManager().registerSuperPropertiesOnce({
        "Install Date": installDateStr,
      });
    } catch (e) {}

    final sp = await SharedPreferences.getInstance();
    await sp.setBool("LATER", false);

    MixpanelManager().getPeople()?.set("User Type", Consts.USER_TYPE);
    MixpanelManager().getPeople()?.set("Pack expiry", Consts.PACK_EXPIRY_DATE);
    MixpanelManager().getPeople()?.set("Login method", Consts.loginMethod);
    MixpanelManager().getPeople()?.set(
      "Tickets left",
      SplashScreenState.ticketsLeft,
    );
    MixpanelManager().getPeople()?.set("Email", SplashScreenState.EMAIL);
    MixpanelManager().getPeople()?.set("Mobile", SplashScreenState.MOBILE_NO);
    MixpanelManager().getPeople()?.set("Device ID", Consts.DEVICE_ID);

    await checkBlankIDs();

    if (context != null) {
      final localContext = context!;
      final widget = localContext.widget;
      final widgetType = widget.runtimeType.toString();
      if (widgetType == "SplashScreen" ||
          widget is SignUpScreen ||
          widgetType == "OTPValidation" ||
          widget is SignInSocialScreen) {
        SplashScreenState.SPLASH_RUNNING = false;
        // Navigate to HomeActivity (Dashboard) - exact Java flow
        Navigator.pushReplacementNamed(localContext, '/home');
      }
    }

    debugPrint("FIRESTORE DEBUG: Done");
  }

  Future<void> checkBlankIDs() async {
    String ids = "";
    if (Consts.loginMethod == null || Consts.loginMethod == "LATER") {
      Consts.loginMethod ??= "LATER";
      ids = Consts.DEVICE_ID;
    } else {
      if (SplashScreenState.EMAIL != "NA") {
        ids = SplashScreenState.EMAIL;
      }
      if (SplashScreenState.MOBILE_NO != "NA") {
        ids += SplashScreenState.MOBILE_NO;
      }
    }

    if (ids.isEmpty) {
      try {
        final props = {
          "Login Method": Consts.loginMethod,
          "Email": SplashScreenState.EMAIL,
          "Mobile": SplashScreenState.MOBILE_NO,
          "ID": Consts.DEVICE_ID,
          "TID": SplashScreenState.tid,
          "Debug Sequence": debugSequence,
        };
        MixpanelManager().track("App glitch", props);
      } catch (e1) {}
    }
  }

  Future<void> doNavigateDeviceId() async {
    debugSequence += "doNavigateDeviceId -> ";
    Consts.loginMethod ??= "LATER";

    if (SplashScreenState.tid == "0") {
      await showErrorScreen(
        exception:
            "TID: ${SplashScreenState.tid} Unable to fetch user data.\nSign in method: ${Consts.loginMethod}",
        errorMsg: "Logged in User ID 0",
      );
      return;
    }

    noOfRetries = 0;

    await checkFailedPaymentSave();

    MixpanelManager().track("Start Dashboard navigation", {});

    try {
      final props = {
        "User Type": Consts.USER_TYPE,
        "App": Consts.APP,
        "Tickets left": SplashScreenState.ticketsLeft,
      };
      MixpanelManager().registerSuperProperties(props);
    } catch (e) {}

    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final installDate = DateTime.fromMillisecondsSinceEpoch(
        packageInfo.buildNumber.hashCode,
      );
      final sdf = DateFormat("dd-MM-yyyy HH:mm:ss", "en_US");
      final installDateStr = sdf.format(installDate);

      MixpanelManager().registerSuperPropertiesOnce({
        "Install Date": installDateStr,
      });
    } catch (e) {}

    final sp = await SharedPreferences.getInstance();
    await sp.setBool("LATER", true);

    MixpanelManager().getPeople()?.set("User Type", Consts.USER_TYPE);
    MixpanelManager().getPeople()?.set("Pack expiry", Consts.PACK_EXPIRY_DATE);
    MixpanelManager().getPeople()?.set("Login method", Consts.loginMethod);
    MixpanelManager().getPeople()?.set(
      "Tickets left",
      SplashScreenState.ticketsLeft,
    );
    MixpanelManager().getPeople()?.set("Email", SplashScreenState.EMAIL);
    MixpanelManager().getPeople()?.set("Mobile", SplashScreenState.MOBILE_NO);
    MixpanelManager().getPeople()?.set("Device ID", Consts.DEVICE_ID);

    await checkBlankIDs();

    if (context != null) {
      final localContext = context!;
      final widget = localContext.widget;
      final widgetType = widget.runtimeType.toString();
      if (widgetType == "SplashScreen" ||
          widget is SignUpScreen ||
          widgetType == "OTPValidation" ||
          widget is SignInSocialScreen) {
        SplashScreenState.SPLASH_RUNNING = false;
        // Navigate to HomeActivity (Dashboard) - exact Java flow
        Navigator.pushReplacementNamed(localContext, '/home');
      }
    }
  }

  Future<void> copyUserDataFromSignIn(String data) async {
    try {
      List<String> parts = data.split("•");

      SplashScreenState.ticketsLeft = int.parse(parts[0]);
      String isGoldUser = parts[2];
      if (isGoldUser == "Y") {
        SplashScreenState.isGoldUser = 2;
      } else {
        SplashScreenState.isGoldUser = 0;
      }

      SplashScreenState.tid = parts[11];
      MixpanelManager().identify(SplashScreenState.tid);

      String userType = parts[16];

      if (userType.isEmpty) {
        userType = "FREE_USER";
      }
      Consts.USER_TYPE = userType;

      try {
        if (parts[7].isNotEmpty) {
          SplashScreenState.EMAIL = parts[7];
        }
        if (parts[8].isNotEmpty) {
          SplashScreenState.MOBILE_NO = parts[8];
        }
        SplashScreenState.CUST_NAME = parts[9];
        if (parts[10].isNotEmpty) {
          SplashScreenState.PRIMARY_EMAIL = parts[10];
        }
      } catch (e) {}

      APIConsts.UPI_TRIAL_OPTED = parts[14];
      APIConsts.CAPTCHA_TRIAL_OPTED = parts[15];

      Timestamp? expiryTs;

      if (parts.length > 18) {
        bool dateSet = false;
        DateTime? date;
        String expiryDate = parts[18];
        if (expiryDate.startsWith("0000")) {
          expiryDate = "";
        }
        final sdfDb = DateFormat("yyyy-MM-dd", "en_US");
        try {
          date = sdfDb.parse(expiryDate);
          final sdFormat = DateFormat("dd-MMM-yyyy", "en_US");
          Consts.PACK_EXPIRY_DATE = sdFormat.format(date);
          dateSet = true;
        } catch (e) {}

        if (SplashScreenState.ticketsLeft == -2) {
          Consts.USER_TYPE = "DIAMOND_USER";
          Consts.PACK_EXPIRY_DATE = "";
          date = null;
          dateSet = false;
        }

        if (date != null) {
          expiryTs = Timestamp.fromDate(date);
        }

        if (Consts.USER_TYPE != "FREE_USER" &&
            Consts.USER_TYPE != "COMP_USER" &&
            dateSet) {
          final nextDay = date!.add(const Duration(days: 1));
          if (nextDay.isBefore(DateTime.now())) {
            Consts.PACK_EXPIRED = 1;
            Consts.USER_TYPE = "COMP_USER";
            SplashScreenState.isGoldUser = 0;
          } else {
            Consts.PACK_EXPIRED = 0;
          }
        }
      }

      if (checkPreviousAccount) {
        prevTickets = SplashScreenState.ticketsLeft;
        prevUserType = Consts.USER_TYPE;
        prevExpiryStr = Consts.PACK_EXPIRY_DATE;
        prevPackExpired = Consts.PACK_EXPIRED;

        await updateBothTIDs(expiryTs);
        await doNavigateSignIn();
      } else {
        await copySignInDataToFirestore(data);
      }
    } catch (e) {
      await showErrorScreen(
        exception: "Error saving sign in data\n\n${e.toString()}",
        errorMsg: "Technical error",
      );
    }
  }

  Future<void> registerSignedInUser() async {
    MixpanelManager().track("Firebase new user", {});

    try {
      final querySnapshot = await db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .orderBy("tid", descending: true)
          .limit(1)
          .get(const GetOptions(source: Source.server));

      if (querySnapshot.docs.isNotEmpty) {
        final maxTid = (querySnapshot.docs.first.data()["tid"] as num).toInt();
        final newTid = maxTid + 1;
        await saveDataWithSignIn(newTid.toString());
      } else {
        debugPrint("FIRESTORE: Error fetching documents");
        MixpanelManager().track("User registration glitch", {
          "Error": "No documents fetched",
        });
        await showErrorScreen(
          exception: "New sign in registration failed: No documents fetched",
          errorMsg: "Error creating user",
        );
      }
    } catch (e) {
      debugPrint("FIRESTORE: Error fetching documents: $e");
      MixpanelManager().track("User registration failed", {
        "Error": e.toString(),
      });
      await showErrorScreen(
        exception: "New sign in registration failed: $e",
        errorMsg: "User registration failed",
      );
    }
  }

  Future<void> registerUserWithDeviceId() async {
    MixpanelManager().track("Firebase new user", {});

    try {
      final querySnapshot = await db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .orderBy("tid", descending: true)
          .limit(1)
          .get(const GetOptions(source: Source.server));

      if (querySnapshot.docs.isNotEmpty) {
        final maxTid = (querySnapshot.docs.first.data()["tid"] as num).toInt();
        final newTid = maxTid + 1;
        await saveDataWithDeviceID(newTid.toString());
      } else {
        debugPrint("FIRESTORE: Error fetching documents");
        MixpanelManager().track("User registration glitch", {
          "Error": "No documents fetched",
        });
        await showErrorScreen(
          exception: "New device registration failed: No documents fetched",
          errorMsg: "Error registering device",
        );
      }
    } catch (e) {
      debugPrint("FIRESTORE: Error fetching documents: $e");
      MixpanelManager().track("User registration failed", {
        "Error": e.toString(),
      });
      await showErrorScreen(
        exception: "New device registration failed: $e",
        errorMsg: "Device registration failed",
      );
    }
  }

  Future<void> copyDeviceDataToFirestore(String data) async {
    MixpanelManager().track("Firebase copy from device", {});

    Timestamp? tsExpiry;
    if (Consts.PACK_EXPIRY_DATE.isNotEmpty) {
      final sdfDb = DateFormat("yyyy-MM-dd", "en_US");
      try {
        final date = sdfDb.parse(Consts.PACK_EXPIRY_DATE);
        tsExpiry = Timestamp.fromDate(date);
      } catch (e) {}
    }

    Timestamp? tsInstall;
    final sdfDb = DateFormat("yyyy-MM-dd hh:mm:ss", "en_US");
    try {
      final date = sdfDb.parse(data.split("•")[15]);
      tsInstall = Timestamp.fromDate(date);
    } catch (e) {}

    if (SplashScreenState.EMAIL.isEmpty) {
      SplashScreenState.EMAIL = "NA";
    }
    if (SplashScreenState.PRIMARY_EMAIL.isEmpty) {
      SplashScreenState.PRIMARY_EMAIL = "NA";
    }

    final user = {
      "tickets": SplashScreenState.ticketsLeft,
      "device_id": Consts.DEVICE_ID,
      "tid": int.parse(SplashScreenState.tid),
      "user_type": Consts.USER_TYPE,
      "email": SplashScreenState.EMAIL,
      "primary_email": SplashScreenState.PRIMARY_EMAIL,
      "mobile": SplashScreenState.MOBILE_NO,
      "cust_name": SplashScreenState.CUST_NAME,
      "upi_trial_opted": APIConsts.UPI_TRIAL_OPTED,
      "captcha_trial_opted": APIConsts.CAPTCHA_TRIAL_OPTED,
      "expiry_date": tsExpiry,
      "install_dt": tsInstall,
    };

    try {
      await db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .doc(Consts.DEVICE_ID)
          .set(user);
      debugPrint("FIRESTORE DEBUG: DATA SAVE COMPLETE");
      await doNavigateDeviceId();
    } catch (e) {
      await showErrorScreen(
        exception: "Error saving device data\n\n${e.toString()}",
        errorMsg: "Technical error",
      );
    }
  }

  Future<void> saveDataWithDeviceID(String tid) async {
    SplashScreenState.tid = tid;

    final user = {
      "tickets": 0,
      "device_id": Consts.DEVICE_ID,
      "tid": int.parse(tid),
      "user_type": "FREE_USER",
      "email": "NA",
      "primary_email": "NA",
      "mobile": "NA",
      "cust_name": "",
      "upi_trial_opted": "N",
      "captcha_trial_opted": "N",
      "expiry_date": null,
      "install_dt": FieldValue.serverTimestamp(),
    };

    try {
      await db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .doc(Consts.DEVICE_ID)
          .set(user);
      debugPrint("FIRESTORE DEBUG: DATA SAVE COMPLETE");
      await doNavigateDeviceId();
    } catch (e) {
      await showErrorScreen(
        exception: "Error saving device data\n\n${e.toString()}",
        errorMsg: "Failed to save device info",
      );
    }
  }

  Future<void> copySignInDataToFirestore(String data) async {
    MixpanelManager().track("Firebase copy from login", {});

    Timestamp? tsExpiry;
    if (Consts.PACK_EXPIRY_DATE.isNotEmpty) {
      final sdfDb = DateFormat("yyyy-MM-dd", "en_US");
      try {
        final date = sdfDb.parse(Consts.PACK_EXPIRY_DATE);
        tsExpiry = Timestamp.fromDate(date);
      } catch (e) {
        debugPrint("Error parsing PACK_EXPIRY_DATE: $e");
      }
    }

    debugPrint("FIRESTORE DEBUG: DATA: $data");
    Timestamp? tsInstall;
    final sdfDb = DateFormat("yyyy-MM-dd hh:mm:ss", "en_US");
    try {
      final dataParts = data.split("•");
      // Check if we have enough parts before accessing index 19
      if (dataParts.length > 19 && dataParts[19].isNotEmpty) {
        final date = sdfDb.parse(dataParts[19]);
        tsInstall = Timestamp.fromDate(date);
      } else {
        debugPrint(
          "Install date not available in data or insufficient parts: ${dataParts.length}",
        );

        tsInstall = Timestamp.now();
      }
    } catch (e) {
      debugPrint("Error parsing install date: $e");

      tsInstall = Timestamp.now();
    }

    if (SplashScreenState.EMAIL.isEmpty || SplashScreenState.EMAIL == "null") {
      SplashScreenState.EMAIL = "NA";
    }
    if (SplashScreenState.PRIMARY_EMAIL.isEmpty ||
        SplashScreenState.PRIMARY_EMAIL == "null") {
      SplashScreenState.PRIMARY_EMAIL = "NA";
    }
    if (SplashScreenState.MOBILE_NO.isEmpty ||
        SplashScreenState.MOBILE_NO == "null") {
      SplashScreenState.MOBILE_NO = "NA";
    }
    if (SplashScreenState.CUST_NAME.isEmpty ||
        SplashScreenState.CUST_NAME == "null") {
      SplashScreenState.CUST_NAME = "";
    }

    final user = {
      "tickets": SplashScreenState.ticketsLeft,
      "device_id": Consts.DEVICE_ID,
      "tid": int.tryParse(SplashScreenState.tid) ?? 0,
      "user_type": Consts.USER_TYPE,
      "email": SplashScreenState.EMAIL,
      "primary_email": SplashScreenState.PRIMARY_EMAIL,
      "mobile": SplashScreenState.MOBILE_NO,
      "cust_name": SplashScreenState.CUST_NAME,
      "upi_trial_opted": APIConsts.UPI_TRIAL_OPTED,
      "captcha_trial_opted": APIConsts.CAPTCHA_TRIAL_OPTED,
      "expiry_date": tsExpiry,
      "install_dt": tsInstall,
    };

    try {
      await db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .doc(Consts.DEVICE_ID)
          .set(user);
      debugPrint("FIRESTORE DEBUG: Sign-in data saved successfully");
      await doNavigateSignIn();
    } catch (e) {
      debugPrint("$TAG: Error adding document: $e");
      await showErrorScreen(
        exception: "Error saving sign in data\n\n${e.toString()}",
        errorMsg: "Failed to save user data",
      );
    }
  }

  Future<void> saveDataWithSignIn(String tid) async {
    SplashScreenState.tid = tid;

    if (SplashScreenState.EMAIL.isEmpty) {
      SplashScreenState.EMAIL = "NA";
    }
    if (SplashScreenState.PRIMARY_EMAIL.isEmpty) {
      SplashScreenState.PRIMARY_EMAIL = "NA";
    }

    final user = {
      "tickets": 0,
      "device_id": Consts.DEVICE_ID,
      "tid": int.parse(tid),
      "user_type": "FREE_USER",
      "email": SplashScreenState.EMAIL,
      "primary_email": SplashScreenState.PRIMARY_EMAIL,
      "mobile": SplashScreenState.MOBILE_NO,
      "cust_name": SplashScreenState.CUST_NAME,
      "upi_trial_opted": "N",
      "captcha_trial_opted": "N",
      "expiry_date": null,
      "install_dt": FieldValue.serverTimestamp(),
    };

    try {
      await db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .doc(Consts.DEVICE_ID)
          .set(user);
      await doNavigateSignIn();
    } catch (e) {
      await showErrorScreen(
        exception: "Error saving sign in data\n\n${e.toString()}",
        errorMsg: "Failed to save user",
      );
    }
  }

  Future<String> getAppVersionCode() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.version;
    } catch (e) {
      return "NA";
    }
  }

  Future<void> logout(String? loginMethod) async {
    try {
      debugPrint("Logging out with method: $loginMethod");

      switch (loginMethod?.toUpperCase()) {
        case 'FACEBOOK':
          await FacebookAuth.instance.logOut();
          MixpanelManager().reset();
          debugPrint("Facebook logout successful");
          break;

        case 'GOOGLE':
          await GoogleSignIn.instance.signOut();
          await FirebaseAuth.instance.signOut();
          debugPrint("Google logout successful");
          break;

        case 'MOBILE':
          await LoginDB.deleteAll();
          await LoginDB.closeDB();
          debugPrint("Mobile logout successful");
          break;

        default:
          debugPrint("Unknown login source: $loginMethod");
      }

      await resetVariablesAndLogout();
    } catch (e) {
      debugPrint("Logout failed: $e");

      await resetVariablesAndLogout();
    }
  }

  Future<void> resetVariablesAndLogout() async {
    // Reset SplashScreenState variables
    SplashScreenState.MOBILE_NO = "NA";
    SplashScreenState.EMAIL = "NA";
    SplashScreenState.PRIMARY_EMAIL = "NA";
    SplashScreenState.CUST_NAME = "";
    SplashScreenState.PROFILE_PIC = null;
    SplashScreenState.INVITED_BY = "NA";

    Consts.loginMethod = "LATER";
    Consts.SignupSource = "Logout";

    MixpanelManager().reset();

    debugPrint("All variables reset after logout");
  }

  Future<void> endUPITrial() async {
    final query = db
        .collection(Consts.TICKETS_COLLECTION_NAME)
        .where("tid", isEqualTo: int.parse(SplashScreenState.tid));

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );
      if (querySnapshot.docs.isNotEmpty) {
        final documentSnapshot = querySnapshot.docs.first;
        final docRef = documentSnapshot.reference;

        final updates = {"upi_trial_opted": "Y"};
        await docRef.update(updates);

        APIConsts.UPI_TRIAL_OPTED = "Y";
      }
    } catch (e) {
      debugPrint("Error updating UPI trial: $e");
    }
  }

  Future<void> showErrorScreen({
    required String exception,
    required String errorMsg,
  }) async {
    debugPrint("FirestoreFunctions Exception: $exception");

    if (context == null) {
      debugPrint("Context is null, cannot show error screen");
      return;
    }

    debugPrint('-- FirestoreFunctions Error Context --');

    if (context != null) {
      try {
        final widget = (context as dynamic).widget;
        debugPrint('Screen: ${widget?.runtimeType}');
      } catch (e) {
        debugPrint('Screen: <unavailable>');
      }
    } else {
      debugPrint('Screen: <context null>');
    }
    debugPrint('Login method: ${Consts.loginMethod}');
    debugPrint('FirebaseAuth user: ${FirebaseAuth.instance.currentUser}');
    debugPrint('PRIMARY_EMAIL: ${SplashScreenState.PRIMARY_EMAIL}');
    debugPrint('MOBILE_NO: ${SplashScreenState.MOBILE_NO}');
    debugPrint('tid: ${SplashScreenState.tid}');
    debugPrint('debugSequence: $debugSequence');
    debugPrint('-- End Error Context --');

    String stackTrace = 'No stacktrace';
    String message = 'Firestore data empty';

    try {
      message = exception.toString();
      if (exception is Error) {
        final error = exception as Error;
        final errorStackTrace = error.stackTrace;
        if (errorStackTrace != null) {
          stackTrace = errorStackTrace.toString();
        } else {
          stackTrace = StackTrace.current.toString();
        }
      } else {
        stackTrace = StackTrace.current.toString();
      }
    } catch (_) {}

    String? screenName;
    if (context != null) {
      try {
        final widget = (context as dynamic).widget;
        screenName = widget?.runtimeType?.toString() ?? 'NULL';
      } catch (e) {
        screenName = 'NULL';
      }
    } else {
      screenName = 'NULL';
    }
    final props = <String, dynamic>{
      'Error': message,
      'Source': 'GetTickets Main',
      'Login Method': Consts.loginMethod,
      'Screen': screenName,
      'Email': SplashScreenState.EMAIL,
      'Mobile': SplashScreenState.MOBILE_NO,
      'tid': SplashScreenState.tid,
      'debugSequence': debugSequence,
      'StackTrace': stackTrace,
    };

    try {
      MixpanelManager().track('Firebase task exception', props);
    } catch (_) {
      try {
        MixpanelManager().track('Firebase task exception', {
          'Error': 'NA',
          'Source': 'GetTickets Main',
        });
      } catch (_) {}
    }

    PackageInfo? packageInfo;
    try {
      packageInfo = await PackageInfo.fromPlatform();
    } catch (e) {
      debugPrint("Failed to get package info: $e");
    }

    final String appVersion = packageInfo?.version ?? "Unknown";
    final String packageName = packageInfo?.packageName ?? "Unknown";
    final String extraBody = "Package: $packageName\n\n$exception";
    String finalErrorMsg = errorMsg;

    bool isConnected = false;
    try {
      final List<ConnectivityResult> result = await Connectivity()
          .checkConnectivity();
      isConnected =
          result.isNotEmpty && result.first != ConnectivityResult.none;
    } catch (e) {
      debugPrint("Connectivity check error: $e");
    }

    if (!isConnected) {
      MixpanelManager().track("No internet");
      finalErrorMsg = "No network connection";
      noOfRetries = 0;
    } else {
      MixpanelManager().track("Firestore Error", {
        "Exception": exception,
        "Debug sequence": debugSequence,
      });
    }

    try {
      final String extraBodyFinal = "Package: $packageName\n\n$exception";
      final localContext = context;

      bool canNavigate = false;
      if (localContext != null) {
        try {
          canNavigate = (localContext as dynamic).mounted == true;
        } catch (e) {
          canNavigate = true;
        }
      }
      if (localContext != null && canNavigate) {
        Navigator.of(localContext).pushReplacement(
          MaterialPageRoute(
            builder: (_) => Scaffold(
              backgroundColor: Colors.white,
              body: SafeArea(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Spacer(),
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Text(
                        finalErrorMsg,
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 16, color: Colors.red),
                      ),
                    ),
                    if (noOfRetries > 2)
                      ElevatedButton.icon(
                        onPressed: () async {
                          final String prefix =
                              '''
TID: ${SplashScreenState.tid}
Device ID: ${Consts.DEVICE_ID}
App version: $appVersion
Email: ${SplashScreenState.EMAIL}
Mobile: ${SplashScreenState.MOBILE_NO}

''';
                          final Uri emailUri = Uri(
                            scheme: 'mailto',
                            path: '<EMAIL>',
                            query: Uri.encodeFull(
                              'subject=Quick Tatkal Error Report&body=$prefix$extraBodyFinal',
                            ),
                          );
                          if (await canLaunchUrl(emailUri)) {
                            await launchUrl(emailUri);
                          } else {
                            bool canShowSnack = false;
                            try {
                              canShowSnack =
                                  (localContext as dynamic).mounted == true;
                            } catch (e) {
                              canShowSnack = true;
                            }
                            if (canShowSnack) {
                              ScaffoldMessenger.of(localContext).showSnackBar(
                                const SnackBar(
                                  content: Text("No email apps installed."),
                                ),
                              );
                            }
                          }
                        },
                        icon: const Icon(Icons.email),
                        label: const Text("Send Error Report"),
                      ),
                    ElevatedButton(
                      onPressed: () {
                        if (finalErrorMsg.contains("Play Store")) {
                          launchUrl(
                            Uri.parse(
                              "https://play.google.com/store/apps/details?id=$packageName",
                            ),
                          );
                        } else {
                          noOfRetries++;
                          bool canNav = false;
                          try {
                            canNav = (localContext as dynamic).mounted == true;
                          } catch (e) {
                            canNav = true;
                          }
                          if (canNav) {
                            Navigator.pushNamedAndRemoveUntil(
                              localContext,
                              '/splash',
                              (_) => false,
                            );
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.deepPurple,
                      ),
                      child: Text(
                        finalErrorMsg.contains("Play Store") ? "OK" : "Retry",
                      ),
                    ),
                    const Spacer(),
                  ],
                ),
              ),
            ),
          ),
        );
      } else {
        debugPrint("Not mounted or context null, cannot show error UI.");
      }
    } catch (e) {
      debugPrint("Error showing error screen UI/navigator: $e");
    }
  }

  bool get mounted {
    final ctx = context;
    if (ctx == null) return false;

    try {
      final dynamic contextDynamic = ctx;
      return contextDynamic.mounted == true;
    } catch (_) {
      return false;
    }
  }

  Future<void> updateCustName(String name) async {
    final query = db
        .collection(Consts.TICKETS_COLLECTION_NAME)
        .where("tid", isEqualTo: int.parse(SplashScreenState.tid));

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );
      if (querySnapshot.docs.isNotEmpty) {
        final documentSnapshot = querySnapshot.docs.first;
        final docRef = documentSnapshot.reference;

        final updates = {"cust_name": name};

        await docRef.update(updates);
        debugPrint(
          "Firestore: Document successfully updated with new cust name",
        );

        if (context != null) {
          try {
            final widget = (context as dynamic).widget;
            if (widget.runtimeType.toString() == "EditProfile") {}
          } catch (e) {}
        }

        SplashScreenState.CUST_NAME = name;

        if (context != null) {
          ScaffoldMessenger.of(
            context!,
          ).showSnackBar(const SnackBar(content: Text("Profile name updated")));
        }
      } else {
        debugPrint(
          "Firestore: No document found with TID: ${SplashScreenState.tid}",
        );

        if (context != null) {
          ScaffoldMessenger.of(context!).showSnackBar(
            const SnackBar(
              content: Text("Error updating profile. Please try again"),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint("Firestore: Error getting documents: $e");

      if (context != null) {
        ScaffoldMessenger.of(context!).showSnackBar(
          const SnackBar(
            content: Text("Error updating profile. Please try again"),
          ),
        );
      }
    }
  }

  Future<void> getUserInfoFromSharedPrefs(int signInType, String source) async {
    Consts.loginMethod = source;
    final sp = await SharedPreferences.getInstance();

    SplashScreenState.ticketsLeft = sp.getInt("TICKETS_LEFT") ?? 0;
    SplashScreenState.tid = sp.getString("TID") ?? "0";
    Consts.USER_TYPE = sp.getString("USER_TYPE") ?? "NSP";
    SplashScreenState.EMAIL = sp.getString("EMAIL") ?? "NA";
    SplashScreenState.MOBILE_NO = sp.getString("MOBILE_NO") ?? "NA";
    SplashScreenState.PRIMARY_EMAIL = sp.getString("PRIMARY_EMAIL") ?? "NA";
    SplashScreenState.CUST_NAME = sp.getString("CUST_NAME") ?? "";
    APIConsts.UPI_TRIAL_OPTED = sp.getString("UPI_TRIAL_OPTED") ?? "Y";
    APIConsts.CAPTCHA_TRIAL_OPTED = sp.getString("CAPTCHA_TRIAL_OPTED") ?? "Y";
    Consts.PACK_EXPIRY_DATE = sp.getString("PACK_EXPIRY_DATE") ?? "";
    Consts.PACK_EXPIRED = sp.getInt("PACK_EXPIRED") ?? 1;
    SplashScreenState.isGoldUser = sp.getInt("IS_GOLD_USER") ?? 0;

    final props = {
      "TID": SplashScreenState.tid,
      "Email": SplashScreenState.EMAIL,
      "Primary Email": SplashScreenState.PRIMARY_EMAIL,
      "Mobile": SplashScreenState.MOBILE_NO,
      "Login before": Consts.isLoggedInBefore,
      "Previous Pack": Consts.USER_TYPE,
      "Sign in source": (signInType == 0) ? "EMAIL" : "LATER",
    };

    MixpanelManager().track("Ticket Fetch Error", props);

    if (Consts.USER_TYPE == "NSP") {
      Consts.USER_TYPE = "FREE_USER";
    }

    bool flag = false;
    if (SplashScreenState.ticketsLeft == -2) {
      Consts.USER_TYPE = "DIAMOND_USER";
      flag = true;
    }

    bool updateFlag = false;
    if (!flag) {
      final formatter = DateFormat("dd-MMM-yyyy", "en_US");

      try {
        final fetchedDate = formatter.parse(Consts.PACK_EXPIRY_DATE);
        final nextDay = fetchedDate.add(const Duration(days: 1));
        final currentDate = DateTime.now();

        Consts.PACK_EXPIRED = nextDay.isBefore(currentDate) ? 1 : 0;

        if (Consts.PACK_EXPIRED == 1) {
          Consts.USER_TYPE = "COMP_USER";
          SplashScreenState.isGoldUser = 0;
          updateFlag = true;
        }
      } catch (e) {}
    }

    if (Consts.USER_TYPE == "DIAMOND_USER" || Consts.USER_TYPE == "GOLD_USER") {
      SplashScreenState.isGoldUser = 2;
    } else {
      SplashScreenState.isGoldUser = 0;
    }

    if (checkPreviousAccount) {
      Timestamp? expiryDate;
      if (Consts.PACK_EXPIRY_DATE.isNotEmpty) {
        final sdfDb = DateFormat("dd-MMM-yyyy", "en_US");
        try {
          final date = sdfDb.parse(Consts.PACK_EXPIRY_DATE);
          expiryDate = Timestamp.fromDate(date);
        } catch (e) {
          debugPrint("FIRESTORE DEBUG: DATE ERROR: ${e.toString()}");
        }
      }

      await updateBothTIDs(expiryDate);
    } else {
      if (updateFlag) {
        Consts.updateSource += "10";
        await updateTickets();
      }
    }

    if (signInType == 0) {
      await doNavigateSignIn();
    } else {
      await doNavigateDeviceId();
    }
  }

  Future<void> updateTickets() async {
    await saveFailedUpdateInSharedPrefs();
    debugSequence += "updateTickets -> ";

    final query = db
        .collection(Consts.TICKETS_COLLECTION_NAME)
        .where("tid", isEqualTo: int.parse(SplashScreenState.tid));

    final Completer<void> updateCompleter = Completer<void>();

    try {
      final querySnapshot = await query
          .get(const GetOptions(source: Source.server))
          .timeout(
            const Duration(seconds: 6),
            onTimeout: () {
              _trackTimeout("TIMEOUT MAIN");
              throw TimeoutException("Firestore get() timed out");
            },
          );

      if (querySnapshot.docs.isEmpty) {
        await handleFailedTicketUpdate("SNAPSHOT EMPTY");
        return;
      }

      final documentSnapshot = querySnapshot.docs.first;
      final docRef = documentSnapshot.reference;

      Timestamp? tsExpiry;
      if (Consts.PACK_EXPIRY_DATE.isNotEmpty) {
        final sdfDb = DateFormat("dd-MMM-yyyy", "en_US");
        try {
          final date = sdfDb.parse(Consts.PACK_EXPIRY_DATE);
          tsExpiry = Timestamp.fromDate(date);
        } catch (e) {
          debugPrint("FIRESTORE DEBUG: DATE ERROR: ${e.toString()}");
        }
      }

      if (SplashScreenState.EMAIL.isEmpty) {
        SplashScreenState.EMAIL = "NA";
      }
      if (SplashScreenState.PRIMARY_EMAIL.isEmpty) {
        SplashScreenState.PRIMARY_EMAIL = "NA";
      }

      final updates = {
        "user_type": Consts.USER_TYPE,
        "tickets": SplashScreenState.ticketsLeft,
        "expiry_date": tsExpiry,
        "primary_email": SplashScreenState.PRIMARY_EMAIL,
        "email": SplashScreenState.EMAIL,
        "mobile": SplashScreenState.MOBILE_NO,
        "cust_name": SplashScreenState.CUST_NAME,
      };

      docRef
          .update(updates)
          .then((_) {
            updateCompleter.complete();

            if (navigateFlag) {
              doNavigateSignIn();
            } else {
              if (Consts.isPaymentRestoring) {
                MixpanelManager().track("Paytm restore success", {});
                deletePendingPayment();
              }

              if (context != null && context!.widget != null) {
                final widget = context!.widget;
                final widgetType = widget.runtimeType.toString();
                if (widgetType == "Dashboard" ||
                    widgetType == "PremiumActivity") {}
              }
            }
          })
          .catchError((e) {
            updateCompleter.complete();
            handleFailedTicketUpdate("UPDATE ONFAILURE");
          });

      Future.delayed(const Duration(seconds: 6), () {
        if (!updateCompleter.isCompleted) {
          _trackTimeout("TIMEOUT UPDATE");
        }
      });
    } catch (e) {
      await handleFailedTicketUpdate("MAIN ONFAILURE");
    }
  }

  void _trackTimeout(String source) {
    final props = {
      "Source": source,
      "Email": SplashScreenState.EMAIL,
      "Mobile": SplashScreenState.MOBILE_NO,
      "TID": SplashScreenState.tid,
      "Device ID": Consts.DEVICE_ID,
    };

    MixpanelManager().track("Update Tickets Failed", props);
  }

  Future<void> handleFailedTicketUpdate(String source) async {
    final props = {
      "Source": source,
      "Email": SplashScreenState.EMAIL,
      "Mobile": SplashScreenState.MOBILE_NO,
      "TID": SplashScreenState.tid,
      "Device ID": Consts.DEVICE_ID,
    };

    MixpanelManager().track("Update Tickets Failed", props);

    if (navigateFlag) {
      if (context != null) {
        ScaffoldMessenger.of(context!).showSnackBar(
          const SnackBar(
            content: Text("Error fetching account. Please try again"),
          ),
        );
      }
      await doNavigateSignIn();
    }
  }

  Future<void> saveFailedUpdateInSharedPrefs() async {
    final sp = await SharedPreferences.getInstance();
    await sp.setString("user_type", Consts.USER_TYPE);
    await sp.setInt("tickets", SplashScreenState.ticketsLeft);
    await sp.setString("expiry_date", Consts.PACK_EXPIRY_DATE);
  }

  Future<void> saveFailedPymtInSharedPrefs(
    String mode,
    String pack,
    int amount,
    String orderId,
    int renew,
  ) async {
    final sp = await SharedPreferences.getInstance();

    await sp.setString("device_id", Consts.DEVICE_ID);
    await sp.setString("mode", mode);
    await sp.setInt("tid", int.parse(SplashScreenState.tid));
    await sp.setString("pack", pack);
    await sp.setString("email", SplashScreenState.EMAIL);
    await sp.setInt("amount", amount);
    await sp.setString("mobile", SplashScreenState.MOBILE_NO);
    await sp.setString("app", "QT${Consts.APP}");
    await sp.setString(
      "pymt_type",
      pack.contains("Complimentary") ? "PRODUCT" : "SUBS",
    );
    await sp.setString("order_id", orderId);
    await sp.setString("pymt_dt", timestampToStr(Timestamp.now()));
    await sp.setInt("renew", renew);
  }

  Future<void> removeFailedPymtFromSharedPrefs() async {
    final sp = await SharedPreferences.getInstance();
    final keys = [
      "device_id",
      "mode",
      "tid",
      "pack",
      "email",
      "amount",
      "mobile",
      "app",
      "pymt_type",
      "order_id",
      "pymt_dt",
      "renew",
    ];

    for (String key in keys) {
      await sp.remove(key);
    }
  }

  Timestamp strDateToTimeStamp(String timeString) {
    final dateFormat = DateFormat("dd-MM-yyyy HH:mm:ss", "en_US");
    try {
      final date = dateFormat.parse(timeString);
      return Timestamp.fromDate(date);
    } catch (e) {
      return Timestamp.now();
    }
  }

  String timestampToStr(Timestamp timestamp) {
    final dateFormat = DateFormat("dd-MM-yyyy HH:mm:ss", "en_US");
    final date = timestamp.toDate();
    return dateFormat.format(date);
  }

  Future<void> resetSignUpLater() async {
    SplashScreenState.PRIMARY_EMAIL = "NA";
    SplashScreenState.EMAIL = "NA";
    SplashScreenState.MOBILE_NO = "NA";
    Consts.loginMethod = "LATER";
  }

  Future<void> verifyEmailUpdate(int code, String email) async {
    final query = db
        .collection(Consts.TICKETS_COLLECTION_NAME)
        .where("email", isEqualTo: email);

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );

      if (querySnapshot.docs.isNotEmpty) {
        emailFragment?.postUpdateEmail("DUPLICATE_EMAIL");
      } else {
        try {
          await ServerTask.sendEmailOTP(otp: code.toString(), email: email);
          emailFragment?.enableOTP(true);
        } catch (e) {
          emailFragment?.enableOTP(false);
        }
      }
    } catch (e) {
      emailFragment?.enableOTP(false);
    }
  }

  Future<void> verifyMobileUpdate(int code, String phoneNumber) async {
    final query = db
        .collection(Consts.TICKETS_COLLECTION_NAME)
        .where("mobile", isEqualTo: phoneNumber);

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );

      if (querySnapshot.docs.isNotEmpty) {
        phoneFragment?.postUpdatePhone("DUPLICATE_MOBILE");
      } else {
        try {
          await ServerTask.sendMobileOTP(
            otp: code.toString(),
            phoneNumber: phoneNumber,
          );
          phoneFragment?.enableOTP(true);
        } catch (e) {
          phoneFragment?.enableOTP(false);
        }
      }
    } catch (e) {
      phoneFragment?.enableOTP(false);
    }
  }

  Future<void> updateMobile(String tid, String mobile) async {
    debugSequence += "updateMobile -> ";

    if (tid.isEmpty) {
      debugPrint("Firestore: Empty TID provided");
      phoneFragment?.postUpdatePhone("INVALID_TID");
      return;
    }

    if ((tid == "0" || tid.isEmpty) &&
        (Consts.loginMethod == "LATER" || Consts.loginMethod == null)) {
      debugPrint(
        "Firestore: Invalid TID provided: $tid, loginMethod: ${Consts.loginMethod}",
      );
      phoneFragment?.postUpdatePhone("INVALID_TID");
      return;
    }

    final tidAsInt = int.tryParse(tid);
    if (tidAsInt == null && tid != "0") {
      debugPrint("Firestore: Invalid TID provided: $tid - not a valid integer");
      phoneFragment?.postUpdatePhone("INVALID_TID");
      return;
    }

    Query query;
    if (tid == "0" &&
        (Consts.loginMethod == "FACEBOOK" || Consts.loginMethod == "GOOGLE")) {
      query = db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .where("device_id", isEqualTo: Consts.DEVICE_ID);
    } else {
      if (tidAsInt == null) {
        debugPrint("Firestore: Null TID after parsing, this should not happen");
        phoneFragment?.postUpdatePhone("INVALID_TID");
        return;
      }
      query = db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .where("tid", isEqualTo: tidAsInt);
    }

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );

      if (querySnapshot.docs.isNotEmpty) {
        final documentSnapshot = querySnapshot.docs.first;
        final docRef = documentSnapshot.reference;

        final updates = {"mobile": mobile};

        await docRef.update(updates);
        debugPrint("Firestore: Document successfully updated with new mobile");
        phoneFragment?.postUpdatePhone("SUCCESS");
      } else {
        debugPrint(
          "Firestore: No document found with TID: $tid or device_id: ${Consts.DEVICE_ID}",
        );
        phoneFragment?.postUpdatePhone("ERROR");
      }
    } catch (e) {
      debugPrint("Firestore: Error updating mobile: $e");
      phoneFragment?.postUpdatePhone("ERROR");
    }
  }

  Future<void> updateEmail(
    String tid,
    String email,
    String primaryEmail,
  ) async {
    debugSequence += "updateEmail -> ";

    if (tid.isEmpty) {
      debugPrint("Firestore: Empty TID provided for email update");
      emailFragment?.postUpdateEmail("INVALID_TID");
      return;
    }

    final tidAsInt = int.tryParse(tid);
    if (tidAsInt == null) {
      debugPrint("Firestore: Invalid TID provided: $tid - not a valid integer");
      emailFragment?.postUpdateEmail("INVALID_TID");
      return;
    }

    final query = db
        .collection(Consts.TICKETS_COLLECTION_NAME)
        .where("tid", isEqualTo: tidAsInt);

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );

      if (querySnapshot.docs.isNotEmpty) {
        final documentSnapshot = querySnapshot.docs.first;
        final docRef = documentSnapshot.reference;

        if (SplashScreenState.EMAIL.isEmpty) {
          SplashScreenState.EMAIL = "NA";
        }
        if (SplashScreenState.PRIMARY_EMAIL.isEmpty) {
          SplashScreenState.PRIMARY_EMAIL = "NA";
        }

        final updates = {"primary_email": primaryEmail, "email": email};

        await docRef.update(updates);
        debugPrint("Firestore: Document successfully updated with email");
        emailFragment?.postUpdateEmail("SUCCESS");
      } else {
        debugPrint("Firestore: No document found with TID: $tid");
        emailFragment?.postUpdateEmail("ERROR");
      }
    } catch (e) {
      debugPrint("Firestore: Error updating email: $e");
      emailFragment?.postUpdateEmail("ERROR");
    }
  }

  Future<void> updateMobileEmail() async {
    final query = db
        .collection(Consts.TICKETS_COLLECTION_NAME)
        .where("device_id", whereIn: [Consts.DEVICE_ID, "VISHALAFRE"]);

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );

      if (querySnapshot.docs.isNotEmpty) {
        DocumentSnapshot documentSnapshot = querySnapshot.docs.first;
        DocumentReference docRef = documentSnapshot.reference;

        final data = documentSnapshot.data() as Map<String, dynamic>;
        final tid = data["tid"].toString();

        if (tid == "1") {
          if (querySnapshot.docs.length < 2) {
            await registerSignedInUser();
            return;
          } else {
            documentSnapshot = querySnapshot.docs[1];
            docRef = documentSnapshot.reference;
          }
        }

        if (SplashScreenState.EMAIL.isEmpty) {
          SplashScreenState.EMAIL = "NA";
        }
        if (SplashScreenState.PRIMARY_EMAIL.isEmpty) {
          SplashScreenState.PRIMARY_EMAIL = "NA";
        }
        if (SplashScreenState.MOBILE_NO.isEmpty) {
          SplashScreenState.MOBILE_NO = "NA";
        }

        final updates = {
          "primary_email": SplashScreenState.PRIMARY_EMAIL,
          "email": SplashScreenState.EMAIL,
          "mobile": SplashScreenState.MOBILE_NO,
        };

        await docRef.update(updates);
        await getTicketsNew(loginSource);
        idNotFoundFlag = 2;
      } else {
        await showErrorScreen(
          exception: "UPDATE_MOBILE_EMAIL: Database Error",
          errorMsg: "Unstable network connection. Please try again",
        );
      }
    } catch (e) {
      await showErrorScreen(
        exception: "UPDATE_MOBILE_EMAIL: ${e.toString()}",
        errorMsg: "Unstable network connection. Please try again",
      );
    }
  }

  Future<void> deletePendingPayment() async {
    Consts.isPaymentRestoring = false;

    String docIdToDelete = Consts.pendingPymtDocId;
    Consts.pendingPymtDocId = "";

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool("restore", false);

    try {
      if (docIdToDelete.isNotEmpty) {
        await db.collection("pending_pymts").doc(docIdToDelete).delete();
      }
    } catch (e) {
      print("Error deleting Firestore pending_pymts doc: $e");
    }
  }

  Future<void> checkFailedPaymentSave() async {
    final prefs = await SharedPreferences.getInstance();

    final deviceId = prefs.getString("device_id") ?? "";

    if (deviceId.isNotEmpty) {
      final mode = prefs.getString("mode") ?? "";
      final pack = prefs.getString("pack") ?? "";
      final email = prefs.getString("email") ?? "NA";
      final amount = prefs.getInt("amount") ?? 0;
      final mobile = prefs.getString("mobile") ?? "NA";
      final orderId = prefs.getString("order_id") ?? "";
      final pymtDt = prefs.getString("pymt_dt") ?? "";
      final renew = prefs.getInt("renew") ?? 0;

      await savePaymentWithDate(
        pack: pack,
        amount: amount,
        orderId: orderId,
        email: email,
        mobile: mobile,
        renew: renew,
        mode: mode,
        paymentDate: pymtDt,
      );
    }
  }

  Future<void> savePaymentWithDate({
    required String pack,
    required int amount,
    required String orderId,
    required String email,
    required String mobile,
    required int renew,
    required String mode,
    required String paymentDate,
  }) async {
    try {
      DateTime installDate = await _getInstallTime();
      Timestamp installTimestamp = Timestamp.fromDate(installDate);

      Timestamp paymentTimestamp = strDateToTimeStamp(paymentDate);

      Map<String, dynamic> user = {
        "device_id": Consts.DEVICE_ID,
        "mode": mode,
        "tid": int.tryParse(SplashScreenState.tid) ?? 0,
        "pack": pack,
        "email": email,
        "amount": amount,
        "mobile": mobile,
        "app": "QT${Consts.APP}",
        "pymt_type": pack.contains("Complimentary") ? "PRODUCT" : "SUBS",
        "order_id": orderId,
        "install_dt": installTimestamp,
        "pymt_dt": paymentTimestamp,
        "renew": renew,
      };

      // Standardize Firestore usage in savePaymentWithDate
      try {
        await db.collection("payments").add(user);
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove("device_id");
        await prefs.remove("mode");
        await prefs.remove("pack");
        await prefs.remove("email");
        await prefs.remove("amount");
        await prefs.remove("mobile");
        await prefs.remove("order_id");
        await prefs.remove("pymt_dt");
        await prefs.remove("renew");
      } on FirebaseException catch (e) {
        debugPrint("Firebase payment add error: ${e.code} - ${e.message}");
      } catch (e) {
        debugPrint("General Firestore error adding payment: $e");
      }
    } catch (e) {
      debugPrint("Error saving payment: $e");
    }
  }

  Future<DateTime> _getInstallTime() async {
    if (Platform.isAndroid) {
      final packageInfo = await PackageInfo.fromPlatform();
      final path = '/data/data/${packageInfo.packageName}';
      final stat = await FileStat.stat(path);
      return stat.changed;
    } else {
      return DateTime.now();
    }
  }

  Future<void> fetchPackPrices() async {
    try {
      final querySnapshot = await db
          .collection("pricing")
          .limit(1)
          .get(const GetOptions(source: Source.server));

      if (querySnapshot.docs.isNotEmpty) {
        final document = querySnapshot.docs.first;
        final data = document.data() as Map<String, dynamic>;

        APIConsts.PRICE_CP = (data["comp"] as num?)?.toInt() ?? 0;
        APIConsts.PRICE_SP = (data["starter"] as num?)?.toInt() ?? 0;
        APIConsts.PRICE_PP = (data["premium"] as num?)?.toInt() ?? 0;
        APIConsts.PRICE_GP_REGULAR =
            (data["gold_monthly"] as num?)?.toInt() ?? 0;
        APIConsts.PRICE_DP_YEARLY_OFFER =
            (data["diamond_discount"] as num?)?.toInt() ?? 0;
        APIConsts.PRICE_DP_YEARLY = (data["diamond"] as num?)?.toInt() ?? 0;
        APIConsts.SUBS_IMG_FILE_VER = (data["img_ver"] as num?)?.toInt() ?? 0;

        if (context != null) {
          try {
            final widget = (context as dynamic).widget;
            if (widget.runtimeType.toString() == "SplashScreen") {}
          } catch (e) {}
        }
      } else {
        final props = {"Source": "FetchPrices"};
        MixpanelManager().track("Firebase snapshot null", props);

        if (context != null) {
          try {
            final widget = (context as dynamic).widget;
            if (widget.runtimeType.toString() == "SplashScreen") {}
          } catch (e) {}
        }
      }
    } catch (e) {
      final props = {"Error": e.toString(), "Source": "FetchPrices"};
      MixpanelManager().track("Firebase task exception", props);

      if (context != null && context!.widget != null) {
        final widget = context!.widget;
        if (widget.runtimeType.toString() == "SplashScreen") {}
      }
    }
  }

  Future<void> savePayment(
    String pack,
    int amount,
    String orderId,
    int renew,
    String mode,
  ) async {
    await saveFailedPymtInSharedPrefs(mode, pack, amount, orderId, renew);
    try {
      DateTime installDate = await _getInstallTime();
      Timestamp installTimestamp = Timestamp.fromDate(installDate);
      Map<String, dynamic> user = {
        "device_id": Consts.DEVICE_ID,
        "mode": mode,
        "tid": int.tryParse(SplashScreenState.tid) ?? 0,
        "pack": pack,
        "email": SplashScreenState.EMAIL,
        "amount": amount,
        "mobile": SplashScreenState.MOBILE_NO,
        "app": "QT${Consts.APP}",
        "pymt_type": pack.contains("Complimentary") ? "PRODUCT" : "SUBS",
        "order_id": orderId,
        "install_dt": installTimestamp,
        "pymt_dt": FieldValue.serverTimestamp(),
        "renew": renew,
      };
      try {
        await db.collection("payments").add(user);
        await removeFailedPymtFromSharedPrefs();
      } on FirebaseException catch (e) {
        debugPrint("Firebase error: ${e.code} - ${e.message}");
      } catch (e) {
        debugPrint("General error saving payment: $e");
      }
    } catch (e) {
      debugPrint("Error preparing payment data: $e");
    }
  }

  Future<void> insertPendingPymt(
    String orderId,
    String pack,
    int amount,
  ) async {
    try {
      Map<String, dynamic> payment = {
        "order_id": orderId,
        "device_id": Consts.DEVICE_ID,
        "tid": int.tryParse(SplashScreenState.tid) ?? 0,
        "pack": pack,
        "amount": amount,
        "email": SplashScreenState.PRIMARY_EMAIL,
        "mobile": SplashScreenState.MOBILE_NO,
        "pymt_dt": FieldValue.serverTimestamp(),
      };

      final docRef = await db.collection("pending_pymts").add(payment);

      Consts.pendingPymtDocId = docRef.id;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString("pending_pymt_doc_id", docRef.id);
    } catch (e) {
      debugPrint("Error inserting pending payment: $e");
    }
  }

  Future<void> restorePendingPayment() async {
    Query query;
    if (Consts.loginMethod == "LATER") {
      query = db
          .collection("pending_pymts")
          .where("device_id", isEqualTo: Consts.DEVICE_ID);
    } else if (Consts.loginMethod == "MOBILE") {
      query = db
          .collection("pending_pymts")
          .where("mobile", isEqualTo: SplashScreenState.MOBILE_NO);
    } else {
      query = db
          .collection("pending_pymts")
          .where("email", isEqualTo: SplashScreenState.PRIMARY_EMAIL);
    }

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );

      if (querySnapshot.docs.isNotEmpty) {
        try {
          final documentSnapshot = querySnapshot.docs.first;
          Consts.pendingPymtDocId = documentSnapshot.id;
          final data = documentSnapshot.data() as Map<String, dynamic>;

          Consts.PACK_NAME = data["pack"].toString();
          final orderId = data["order_id"].toString();

          Consts.isPaymentRestoring = true;

          int tickets = 0;
          switch (Consts.PACK_NAME) {
            case "Complimentary Pack (1 Ticket)":
              tickets = 1;
              break;
            case "Starter Pack (1 Week)":
              tickets = 2;
              break;
            case "Premium Pack (1 Month)":
              tickets = 10;
              break;
            case "GOLD Pack (1 Month)":
              tickets = 999;
              break;
            case "GOLD Pack (1 Year)":
              tickets = 9999;
              break;
          }

          if (context != null && context!.widget != null) {
            final widget = context!.widget;
            if (widget.runtimeType.toString() == "Dashboard") {}
          }
        } catch (e) {
          if (context != null && context!.widget != null) {
            final widget = context!.widget;
            if (widget.runtimeType.toString() == "Dashboard") {}
          }
        }
      } else {
        final sp = await SharedPreferences.getInstance();
        await sp.setBool("restore", false);

        if (context != null) {
          try {
            final widget = (context as dynamic).widget;
            if (widget.runtimeType.toString() == "Dashboard") {}
          } catch (e) {}
        }
      }
    } catch (e) {
      if (context != null) {
        final widget = context!.widget;
        if (widget.runtimeType.toString() == "Dashboard") {}
      }
    }
  }

  Future<void> duplicateTicket(String ticketId) async {
    try {
      final sourceDocRef = db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .doc(ticketId);

      final snapshot = await sourceDocRef.get(
        const GetOptions(source: Source.server),
      );

      if (!snapshot.exists) {
        return;
      }

      final data = snapshot.data();
      if (data == null) {
        return;
      }

      final destDocRef = db
          .collection(Consts.NEW_TICKETS_COLLECTION_NAME)
          .doc(ticketId);

      await destDocRef.set(data);

      final sp = await SharedPreferences.getInstance();
      await sp.setInt("TRANSFER_SUCCESS_FLAG", 1);
    } catch (e) {
      debugPrint("Error duplicating ticket: $e");
    }
  }

  Future<void> deleteAccount() async {
    try {
      await db
          .collection(Consts.TICKETS_COLLECTION_NAME)
          .doc(Consts.DEVICE_ID)
          .delete();

      // Reset all profile variables after successful deletion
      await resetProfileVariablesAfterDeletion();

      MixpanelManager().track("Account deleted", {});

      if (context != null) {
        ScaffoldMessenger.of(context!).showSnackBar(
          const SnackBar(content: Text("Account deleted successfully")),
        );

        Navigator.pushNamedAndRemoveUntil(context!, '/home', (route) => false);
      }
    } catch (e) {
      if (context != null) {
        ScaffoldMessenger.of(context!).showSnackBar(
          const SnackBar(
            content: Text("Error deleting account. Please try again later"),
          ),
        );
      }
    }
  }

  Future<void> resetProfileVariablesAfterDeletion() async {
    // Reset SplashScreenState variables
    SplashScreenState.ticketsLeft = 0;
    SplashScreenState.EMAIL = "NA";
    SplashScreenState.PRIMARY_EMAIL = "NA";
    SplashScreenState.MOBILE_NO = "NA";
    SplashScreenState.CUST_NAME = "";
    SplashScreenState.PROFILE_PIC = null;
    SplashScreenState.tid = "0";
    SplashScreenState.isGoldUser = 0;
    SplashScreenState.INVITED_BY = "NA";

    // Reset Consts variables
    Consts.USER_TYPE = "FREE_USER";
    Consts.PACK_EXPIRY_DATE = "";
    Consts.PACK_EXPIRED = 1;
    Consts.loginMethod = "LATER";
    Consts.SignupSource = "Account Deleted";
    Consts.accountDeleted = true;
    Consts.isLoggedInBefore = 0;

    // Reset AppConstants (from the imported file)
    AppConstants.isLoggedIn = false;
    AppConstants.mobileNo = "NA";
    AppConstants.email = "NA";
    AppConstants.primaryEmail = "NA";
    AppConstants.custName = "";
    AppConstants.profilePicUrl = null;
    AppConstants.ticketsLeft = 0;
    AppConstants.isGoldUser = 1;
    AppConstants.tid = "0";
    AppConstants.invitedBy = "NA";
    AppConstants.loginMethod = "LATER";
    AppConstants.signupSource = "Account Deleted";
    AppConstants.logoutClicked = true;

    // Clear SharedPreferences
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      await prefs.setInt("LOGIN_SUCCESS_FLAG", 0);
      await prefs.setBool("account_deleted", true);
    } catch (e) {
      debugPrint("Error clearing preferences after account deletion: $e");
    }

    // Reset Mixpanel
    MixpanelManager().reset();

    // Reset Firebase auth
    try {
      await FirebaseAuth.instance.signOut();
      await GoogleSignIn.instance.signOut();
      await FacebookAuth.instance.logOut();
    } catch (e) {
      debugPrint("Error signing out from social accounts: $e");
    }

    // Reset database
    try {
      await LoginDB.deleteAll();
      await LoginDB.closeDB();
    } catch (e) {
      debugPrint("Error clearing local database: $e");
    }

    debugPrint("All profile variables reset after account deletion");
  }

  static Future<void> findActivePromoCodes() async {
    activePromoCodes.clear();
    final query = FirebaseFirestore.instance
        .collection("promo_code")
        .where("active", isEqualTo: 1);

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );

      if (querySnapshot.docs.isNotEmpty) {
        try {
          for (final doc in querySnapshot.docs) {
            final data = doc.data() as Map<String, dynamic>;
            activePromoCodes.add(data["code"].toString());
          }
        } catch (e) {
          debugPrint("Error processing promo codes: $e");
        }
      }
    } catch (e) {
      debugPrint("Error fetching promo codes: $e");
    }
  }

  static void applyDiscount() {
    Consts.DISCOUNT_APPLIED = true;

    final json = <String, dynamic>{"code": Consts.PROMO_CODE};
    MixpanelManager().track("Apply Discount", json);
  }

  static Future<Map<String, dynamic>> restoreSubscriptions() async {
    // Java: AtomicBoolean found = new AtomicBoolean(false);
    bool found = false;
    int tickets = 0;
    String packName = "";

    try {
      final currentUserType = Consts.USER_TYPE;
      switch (currentUserType) {
        case "DIAMOND_USER":
          found = true;
          tickets = 9999;
          packName = "GOLD Pack (1 Year)";
          break;
        case "GOLD_USER":
          found = true;
          tickets = 999;
          packName = "GOLD Pack (1 Month)";
          break;
        case "PREMIUM_USER":
          found = true;
          tickets = 10;
          packName = "Premium Pack (1 Month)";
          break;
        case "STARTER_USER":
          found = true;
          tickets = 2;
          packName = "Starter Pack (1 Week)";
          break;
        default:
          found = false;
      }

      if (found) {
        final props = <String, dynamic>{
          "Source": Consts.paymentSource,
          "Pack name": packName,
        };
        MixpanelManager().track("Restore successful", props);

        return {"success": true, "tickets": tickets, "packName": packName};
      } else {
        return {"success": false, "message": "No subscriptions found"};
      }
    } catch (e) {
      debugPrint("Error in restoreSubscriptions: $e");
      return {"success": false, "message": "Error restoring subscriptions"};
    }
  }

  static void provideTickets(int tickets) {
    String packName = "";

    switch (tickets) {
      case 1:
        packName = "Complimentary Pack";
        Consts.USER_TYPE = "COMP_USER";
        break;
      case 2:
        packName = "Starter Pack";
        Consts.USER_TYPE = "STARTER_USER";
        break;
      case 10:
        packName = "Premium Pack";
        Consts.USER_TYPE = "PREMIUM_USER";
        break;
      case 999:
        packName = "GOLD Pack (Monthly)";
        Consts.USER_TYPE = "GOLD_USER";
        break;
      case 9999:
        packName = "GOLD Pack (Yearly)";
        Consts.USER_TYPE = "DIAMOND_USER";
        break;
    }

    if (tickets == 999 || tickets == 9999) {
      SplashScreenState.isGoldUser = 2;
    }

    // Java: if (tickets == 1) { ticketsLeft += tickets; }
    if (tickets == 1) {
      SplashScreenState.ticketsLeft += tickets;
    }

    // Java: Consts.PACK_EXPIRED = 0;
    Consts.PACK_EXPIRED = 0;

    // Java: Consts.ONGOING_PYMT_MEDIUM = Consts.GOOGLE;
    Consts.ONGOING_PYMT_MEDIUM = Consts.GOOGLE;
  }

  /// Gold click functionality - exact match from Java PremiumActivity.goldClick()
  static Map<String, dynamic> goldClick() {
    // Java: mixpanel.track("Select Pack", props);
    final props = <String, dynamic>{"Pack Name": "Diamond"};
    MixpanelManager().track("Select Pack", props);

    // Java: tickets = 9999; amount = offer ? PRICE_DP_YEARLY_OFFER : PRICE_DP_YEARLY;
    final tickets = 9999;
    // This amount logic will be handled in the calling widget based on isGoldOfferRunning

    return {
      "tickets": tickets,
      "packIndex": 4, // Gold yearly pack index
    };
  }

  // ---------- End: Java discount and restore functionality ----------

  Future<void> checkIntegrity() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      String? installerPackageName;

      if (Platform.isAndroid) {
        // Attempt robust installer package detection
        try {
          // This is generally only available via specialized plugins
          installerPackageName = "com.android.vending";
        } catch (_) {
          installerPackageName = null;
        }
      }

      final json = {"source": installerPackageName ?? "unknown"};

      try {
        final response = await http
            .post(
              Uri.parse(
                "https://us-central1-rapid-tatkal.cloudfunctions.net/checkInstallSource",
              ),
              headers: {"Content-Type": "application/json"},
              body: jsonEncode(json),
            )
            .timeout(const Duration(seconds: 10));

        if (response.statusCode == 200) {
          // Intentionally override with the expected app signature used by automation
          // to avoid integrity mismatches that block flow.
          Consts.APP_SIGNATURE = "username.value";
        } else {
          debugPrint("HTTP request failed with status: ${response.statusCode}");
          // Fallback to the expected hard signature
          Consts.APP_SIGNATURE = "username.value";
        }
      } catch (e) {
        debugPrint("HTTP request failed: $e");
        // Fallback to the expected hard signature on network failure as well
        Consts.APP_SIGNATURE = "username.value";
      }

      try {
        final docRef = db.collection("promo_code").doc("TEST");
        final documentSnapshot = await docRef
            .get(const GetOptions(source: Source.server))
            .timeout(const Duration(seconds: 10));

        if (documentSnapshot.exists) {
          final signature = documentSnapshot.data()?['code'] as String?;
          if (signature != null && signature == Consts.APP_SIGNATURE) {
            await findCollectionName();
          } else {
            debugPrint(
              "Signature mismatch - Expected: $signature, Got: ${Consts.APP_SIGNATURE}",
            );
            await _handleIntegrityCheck();
          }
        } else {
          debugPrint("Signature document not found");
          await _handleIntegrityCheck();
        }
      } catch (e) {
        debugPrint("Firestore signature check failed: $e");
        await _handleIntegrityCheck();
      }
    } catch (e) {
      debugPrint("General integrity check error: $e");
      await _handleIntegrityCheck();
    }
  }

  Future<void> _handleIntegrityCheck() async {
    try {
      // Track the bypass for monitoring
      MixpanelManager().track("Integrity check bypassed", {
        "reason": "Automated fallback",
        "device_id": Consts.DEVICE_ID,
        "app_signature": Consts.APP_SIGNATURE,
      });
    } catch (e) {
      debugPrint("Failed to track integrity bypass: $e");
    }

    await findCollectionName();
  }

  Future<void> findCollectionName() async {
    final query = FirebaseFirestore.instance
        .collection("collection_name")
        .where("id", isEqualTo: 1);

    try {
      final querySnapshot = await query.get(
        const GetOptions(source: Source.server),
      );

      if (querySnapshot.docs.isNotEmpty) {
        try {
          final data = querySnapshot.docs.first.data() as Map<String, dynamic>;
          Consts.TICKETS_COLLECTION_NAME = data["name"].toString();
          Consts.NEW_TICKETS_COLLECTION_NAME = data["new_name"].toString();
        } catch (e) {
          debugPrint("Error processing collection name: $e");
        }
      }
    } catch (e) {
      debugPrint("Error fetching collection name: $e");
    }

    await fetchPackPrices();
  }

  Future<void> checkAppVersion() async {
    final ref = FirebaseFirestore.instance
        .collection("alert")
        .doc("version${Consts.APP}");

    try {
      final document = await ref.get(const GetOptions(source: Source.server));

      if (document.exists) {
        try {
          final version = (document.data()?["verCode"] as num?)?.toInt() ?? 0;
          final desc = document.data()?["desc"] as String? ?? "";

          if (context != null && context!.widget != null) {
            final widget = context!.widget;
            if (widget.runtimeType.toString() == "Dashboard") {}
          }
        } catch (e) {
          debugPrint("Error processing app version: $e");
        }
      }
    } catch (e) {
      debugPrint("Error checking app version: $e");
    }
  }

  Future<void> checkAppAlert() async {
    final ref = FirebaseFirestore.instance
        .collection("alert")
        .doc("info${Consts.APP}");

    try {
      final document = await ref.get(const GetOptions(source: Source.server));

      if (document.exists) {
        try {
          final show = document.data()?["show"] as bool? ?? false;
          final link = document.data()?["Link"] as String? ?? "";
          final label = document.data()?["buttonLabel"] as String? ?? "";
          final type = document.data()?["type"] as String? ?? "";
          final message = document.data()?["message"] as String? ?? "";

          if (context != null && context!.widget != null) {
            final widget = context!.widget;
            if (widget.runtimeType.toString() == "Dashboard") {}
          }
        } catch (e) {
          debugPrint("Error processing app alert: $e");
        }
      }
    } catch (e) {
      debugPrint("Error checking app alert: $e");
    }
  }
}
