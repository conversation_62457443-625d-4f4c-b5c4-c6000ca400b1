import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:http/http.dart' as http;

class FirebasePushService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
  FlutterLocalNotificationsPlugin();

  static Future<void> initialize(BuildContext context) async {
    // 🔹 Request permissions
    await _firebaseMessaging.requestPermission();

    // 🔹 Init local notifications
    const AndroidInitializationSettings androidInitSettings =
    AndroidInitializationSettings('qt_notif_3'); // your drawable icon name
    final InitializationSettings initSettings =
    InitializationSettings(android: androidInitSettings);
    await _flutterLocalNotificationsPlugin.initialize(initSettings);

    // 🔹 Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _showNotification(message);
    });

    // 🔹 Optional: background/terminated handler
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      // Handle navigation if needed
    });
  }

  static Future<void> _showNotification(RemoteMessage message) async {
    final data = message.data;
    final title = data['title'] ?? 'No Title';
    final body = data['body'] ?? 'No Body';
    final imageUrl = data['image']; // Optional image URL

    BigPictureStyleInformation? styleInfo;
    if (imageUrl != null && imageUrl.isNotEmpty) {
      try {
        final http.Response response = await http.get(Uri.parse(imageUrl));
        final ByteArrayAndroidBitmap bitmap =
        ByteArrayAndroidBitmap.fromBase64String(base64Encode(response.bodyBytes));
        styleInfo = BigPictureStyleInformation(bitmap);
      } catch (_) {}
    }

    final androidDetails = AndroidNotificationDetails(
      '10001', 'Alerts',
      importance: Importance.max,
      priority: Priority.high,
      styleInformation: styleInfo ?? const BigTextStyleInformation(''),
      largeIcon: styleInfo != null ? null : const DrawableResourceAndroidBitmap('qt_notif_3'),
      icon: 'qt_notif_3',
    );

    final notificationDetails = NotificationDetails(android: androidDetails);
    await _flutterLocalNotificationsPlugin.show(
      10001,
      title,
      body,
      notificationDetails,
    );
  }
}
