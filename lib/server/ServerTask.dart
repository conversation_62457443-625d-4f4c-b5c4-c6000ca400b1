import 'dart:async';
import 'dart:core';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:path_provider/path_provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../core/Consts.dart';
import '../utils/NotificationAlarm.dart';
import '../screens/splash_screen.dart';
import 'FirestoreFunctions.dart';
import 'package:google_sign_in/google_sign_in.dart';

class ServerTask {
  final BuildContext context;
  bool savePrefs = false;
  bool updateTicketFlag = false;
  final String baseUrl = 'https://www.afrestudios.com/quick-tatkal/msg91';
  final String logUrl = 'https://www.afrestudios.com/quick-tatkal/2.0';
  static const String _url =
      'https://www.afrestudios.com/quick-tatkal/4.0-firestore/get_tickets_firestore.php';

  ServerTask(this.context);

  //OkHttpClient getUnsafeOkHttpClient
  /*public static OkHttpClient getUnsafeOkHttpClient() {
        if (unsafeClient == null) {
            synchronized (ServerTask.class) {
                if (unsafeClient == null) {
                    try {
                        unsafeClient = createUnsafeOkHttpClient();
                    } catch (Exception e) {
                        unsafeClient = new OkHttpClient();
                    }
                }
            }
        }
        return unsafeClient;
    }*/

  HttpClient createUnsafeHttpClient() {
    final HttpClient client = HttpClient()
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
    return client;
  }

  IOClient getUnsafeClient() {
    final HttpClient httpClient = createUnsafeHttpClient();
    return IOClient(httpClient);
  }

  //initClient (java code)
  /*private void initClient() {
        Context ctx;
        if(activity != null) {
            ctx = activity;
        } else if(context != null) {
            ctx = context;
        } else {
            ctx = fragment.getActivity();
        }

       /* String hostname = "afrestudios.com";
        CertificatePinner certificatePinner = new CertificatePinner.Builder()
                .add(hostname, "sha256/o56M6QExK2rr6fVH9JZPrfAwji99B/812iIJc+VHs6Q=") // Example SHA-256 key pin
                .build();

        OkHttpClient client = new OkHttpClient.Builder()
                .certificatePinner(certificatePinner)
                .build();*/


        try {
            CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
// InputStream for the certificate
            InputStream inputStream = ctx.getResources().openRawResource(R.raw.certificate);

            Certificate certificate;
            try {
                certificate = certificateFactory.generateCertificate(inputStream);
            } finally {
                inputStream.close();
            }

// Create a KeyStore containing our trusted certificate
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null, null);
            keyStore.setCertificateEntry("ca", certificate);

// Create a TrustManager that trusts the certificate in our KeyStore
            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            trustManagerFactory.init(keyStore);

// Create an SSLContext that uses our TrustManager
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustManagerFactory.getTrustManagers(), null);

// Build the OkHttp client
            client = new OkHttpClient.Builder()
                    .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustManagerFactory.getTrustManagers()[0])
                    .build();
        } catch (Exception e) {
            client = getUnsafeOkHttpClient();
        }
    }*/

  static Future<String> sendOTP({
    required String phone,
    required String otp,
    bool voice = false,
  }) async {
    final url = voice
        ? "https://www.afrestudios.com/quick-tatkal/msg91/resend_otp.php"
        : "https://www.afrestudios.com/quick-tatkal/msg91/send_otp.php";

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'mobileno': phone,
          if (voice) 'voice': '1',
          if (!voice) 'otp': otp,
        },
      );

      return response.body;
    } catch (e) {
      return "ERROR: $e";
    }
  }

  static Future<String> verifyOtp({
    required String phone,
    required String otp,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(
          "https://www.afrestudios.com/quick-tatkal/msg91/verify_otp.php",
        ),
        body: {'mobileno': phone, 'otp': otp},
      );

      return response.body;
    } catch (e) {
      return "ERROR: $e";
    }
  }

  void showToast(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  Future<void> resendOTP(String mobileNo, String voice) async {
    final url = Uri.parse('$baseUrl/resend_otp.php');
    try {
      final response = await http.post(
        url,
        body: {'mobileno': mobileNo, 'voice': voice},
      );

      if (response.statusCode == 200 && response.body == 'success') {
        showToast(
          voice == '0' ? 'OTP sent again' : 'You will receive a call with OTP',
        );
      } else {
        showToast('Unable to resend OTP. Please try again');
      }
    } catch (e) {
      showToast('Error resending OTP: $e');
    }
  }

  void _handleOtpError(String error, String mobileNo) {
    if (error.contains('LIMIT_REACHED')) {
      showToast('OTP limit reached. Try again tomorrow.');
    } else {
      showToast('Unable to send OTP. Please try again.');
    }
  }

  /*Future<void> getTicketsSignupLater() async {
    const String url =
        'https://www.afrestudios.com/quick-tatkal/4.0-firestore/log_device_firestore.php';
    final String deviceId = Consts.DEVICE_ID;
    final String invitedBy = SplashActivityConsts.INVITED_BY.isNotEmpty
        ? SplashActivityConsts.INVITED_BY
        : '';

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {'deviceid': deviceId, 'invitedby': invitedBy},
      );

      final responseBody = response.body;
      print(" Server Response: $responseBody");

      // Check for internet connectivity
      // final hasInternet = await isConnectedToInternet();
      // if (!hasInternet) {
      //   showErrorScreen(); // Same as your Android fallback screen
      //  return;
      // }

      print(" FIRESTORE DEBUG: SERVER CHECK COMPLETE");

      if (responseBody == "NEW_USER") {
        await FirestoreFunctions.registerUserWithDeviceId();
        return;
      }

      // Check if response is valid user data
      if (responseBody.contains("•") &&
          responseBody.split("•").length > 7 &&
          !responseBody.contains("ERROR")) {
        await FirestoreFunctions.copyUserDataFromDeviceId(responseBody);
      } else {
        final localData = await getDataFromLocal("log_device_id");

        if (localData.contains("•") &&
            localData.split("•").length > 7 &&
            !localData.contains("ERROR")) {
          await FirestoreFunctions.copyUserDataFromDeviceId(localData);
        } else {
          await FirestoreFunctions.registerUserWithDeviceId();
        }
      }
    } catch (e) {
      print(" Exception: ${e.toString()}");
    }
  }*/

  Future<bool> deleteFileFromFtp({
    required String username,
    required String password,
  }) async {
    // Call your custom HTTP backend API endpoint for FTP deletion
    final url = Uri.parse(
      'https://your-backend-host/api/delete_ftp_file',
    ); // <-- Replace with your backend URL
    final fileName = Consts.DEVICE_ID;

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body:
            '{"username": "$username", "password": "$password", "filename": "$fileName"}',
      );
      if (response.statusCode == 200) {
        // Backend should return a JSON with success: true/false
        final responseBody = response.body;
        print('Backend FTP delete response: ' + responseBody);
        // You may further check by decoding JSON result
        return responseBody.contains('success') &&
            responseBody.contains('true');
      } else {
        print('API error: ${response.statusCode} ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error calling FTP backend API: $e');
      return false;
    }
  }

  /*Future<void> endUPITrial(FloatingWidgetService? floatingService) async {
    const String url =
        'https://www.afrestudios.com/quick-tatkal/2.0/end_upi_trial.php';
    final String tid = SplashActivityConsts.tid;

    String result = "ERROR";

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {'tid': tid},
      );

      if (response.statusCode == 200) {
        result = response.body;
        print('✅ endUPITrial response: $result');
      } else {
        print(' endUPITrial failed with status: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Exception in endUPITrial: $e');
    }

    // Always stop the floating service like onPostExecute()
    if (floatingService != null) {
      floatingService.stopEverything();
    }
  } //floating wedgets tril*/

  /// This mimics the `onPostExecute` logic in Flutter
  void handlePostUpdate({
    required bool updateSuccess,
    required bool isFromDashboard,
    required bool isFromPremium,
    required Function onPaymentComplete,
  }) {
    if (!updateTicketFlag) {
      if (isFromDashboard || isFromPremium) {
        onPaymentComplete();
      }
    }
    updateTicketFlag = false;
  }

  Future<void> execute() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      showErrorScreen();
      return;
    }

    final deviceId = Consts.DEVICE_ID;
    final invitedBy = SplashScreenState.INVITED_BY;

    final url = Uri.parse(
      'https://www.afrestudios.com/quick-tatkal/4.0-firestore/log_device_firestore.php',
    );
    final client = getUnsafeHttpClient();

    try {
      final response = await client.post(
        url,
        body: {'deviceid': deviceId, 'invitedby': invitedBy},
      );

      String result;
      if (response.statusCode == 200) {
        result = response.body;
      } else {
        result = "ERROR: UNSUCCESSFUL";
      }

      debugPrint("FIRESTORE DEBUG: SERVER CHECK COMPLETE");

      if (result == "NEW_USER") {
        FirestoreFunctions(context).registerUserWithDeviceId();
        return;
      }

      if (!(result.contains("•") &&
          result.split("•").length > 7 &&
          !result.contains("ERROR"))) {
        result = await getDataFromLocal("log_device_id");
      }

      if (result.contains("•") &&
          result.split("•").length > 7 &&
          !result.contains("ERROR")) {
        FirestoreFunctions(context).copyUserDataFromDeviceId(result);
      } else {
        FirestoreFunctions(context).registerUserWithDeviceId();
      }
    } catch (e) {
      debugPrint("ERROR: ${e.toString()}");
      showErrorScreen();
    }
  }

  http.Client getUnsafeHttpClient() {
    return http.Client();
  }

  Future<void> savePymt({
    required String installDate,
    required String mode,
    required String pack,
    required int amount,
    required int renew,
    required String deviceId,
    required String tid,
    required String appVersion,
    required String userType,
  }) async {
    final url = Uri.parse(
      "https://www.afrestudios.com/quick-tatkal/3.0/save_pymt_2.php",
    );

    final String pymtType = pack.contains("Complimentary") ? "PRODUCT" : "SUBS";

    try {
      final response = await http.post(
        url,
        body: {
          'insatllDate': installDate,
          'mode': mode,
          'pack': pack,
          'deviceId': deviceId,
          'amount': amount.toString(),
          'renew': renew.toString(),
          'tid': tid,
          'app': 'QT$appVersion',
          'userType': userType,
          'pymtType': pymtType,
        },
      );

      if (response.statusCode == 200) {
        print("Save pymt response : ${response.body}");
      } else {
        print("Save pymt failed: ${response.statusCode}");
      }
    } catch (e) {
      print("Save pymt error: $e");
    }
  }

  Future<String> deleteServerRecord({
    required String deviceId,
    required String mobile,
    required String email,
  }) async {
    final url = Uri.parse(
      "https://www.afrestudios.com/quick-tatkal/3.0/delete_record.php",
    );

    try {
      final response = await http.post(
        url,
        body: {'deviceid': deviceId, 'mobile': mobile, 'email': email},
      );

      if (response.statusCode == 200) {
        final result = response.body;
        print("Delete Record Response: $result");
        return result;
      } else {
        print("Delete Record Failed: ${response.statusCode}");
        return "ERROR";
      }
    } catch (e) {
      print("Delete Record Exception: $e");
      return "ERROR";
    }
  }

  Future<void> logPymtError({
    required String source,
    required String error,
  }) async {
    final url = Uri.parse(
      "https://www.afrestudios.com/quick-tatkal/razorpay/log_error.php",
    );

    try {
      final response = await http.post(
        url,
        body: {'source': source, 'error': error},
      );

      if (response.statusCode == 200) {
        print("Save pymt response : ${response.body}");
      } else {
        print("Error logging payment issue: ${response.statusCode}");
      }
    } catch (e) {
      print("Payment error logging failed: $e");
    }
  }

  Future<void> saveGoogleToken({
    required String token,
    required String tid,
  }) async {
    final url = Uri.parse(
      "https://www.afrestudios.com/quick-tatkal/2.0/save_google_token.php",
    );

    try {
      final response = await http.post(url, body: {'token': token, 'tid': tid});

      if (response.statusCode == 200) {
        print("Save pymt response : ${response.body}");
      } else {
        print("Save token failed: ${response.statusCode}");
      }
    } catch (e) {
      print("Save token error: $e");
    }
  }

  Future<void> insertBookingInfo({
    required String medium,
    required String from,
    required String to,
    required String trvlClass,
    required String trainNo,
    required String quota,
    required String pymtMode,
    required String pymtEntity,
    required String mobile,
    required String status,
    required String time,
    required String deviceId,
    required String email,
    required String globalMobile,
  }) async {
    final url = Uri.parse(
      "https://www.afrestudios.com/quick-tatkal/insert_booking_new.php",
    );

    try {
      final response = await http.post(
        url,
        body: {
          'medium': medium,
          'from': from,
          'to': to,
          'class': trvlClass,
          'train': trainNo,
          'quota': quota,
          'pm': pymtMode,
          'ps': pymtEntity,
          'mobile': mobile,
          'status': status,
          'time': time,
          'deviceid': deviceId,
          'email': email,
          'mobile': globalMobile,
        },
      );

      if (response.statusCode == 200) {
        print("BOOKING RESPONSE : ${response.body}");
      } else {
        print("BOOKING FAILED : ${response.statusCode}");
      }
    } catch (e) {
      print("BOOKING ERROR: $e");
    }
  }

  Future<void> endCaptchaTrial(String tid) async {
    final url = Uri.parse(
      "https://www.afrestudios.com/quick-tatkal/2.0/end_captcha_trial.php",
    );

    try {
      final response = await http.post(url, body: {'tid': tid});

      if (response.statusCode == 200) {
        String result = response.body;
        print('Captcha trial ended: $result');
      } else {
        print('Server error: ${response.statusCode}');
      }
    } catch (e) {
      print('Request failed: $e');
    }
  }

  Future<void> readTask({
    required BuildContext context,
    required String fileName,
    required int readType,
    required int appVersion,
  }) async {
    String data = '';
    try {
      final url = Uri.parse(
        'https://www.afrestudios.com/quick-tatkal/$fileName',
      );
      final response = await http.get(url);

      if (response.statusCode == 200) {
        data = response.body;
      }

      if (data.isEmpty && fileName != 'version') {
        data = '0';
      }
    } catch (e) {
      if (fileName != 'version') {
        data = '-1';
      } else {
        debugPrint('IO Error: $fileName');
      }
    }

    switch (readType) {
      case 0:
        if (data.isNotEmpty) {
          try {
            List<String> parts = data.split('|');
            int minVersion = int.parse(parts[0]);

            if (appVersion != 0 && appVersion < minVersion) {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Update Quick Tatkal'),
                  content: Text(parts[1]),
                  icon: const Icon(Icons.info_outline),
                  actions: [
                    TextButton(
                      onPressed: () async {
                        final url = Uri.parse(
                          'https://play.google.com/store/apps/details?id=com.quicktatkal.app',
                        );
                        if (await canLaunchUrl(url)) {
                          await launchUrl(url);
                        }

                        await FirebaseAnalytics.instance.logEvent(
                          name: 'update_from_alert',
                          parameters: {'task': 'update'},
                        );
                      },
                      child: const Text('Update'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('No'),
                    ),
                  ],
                ),
              );
            }
          } catch (e) {
            debugPrint("Version check error: $e");
          }
        }
        break;

      case 3:
        try {
          List<String> parts = data.split('`');
          if (parts[0] == 'TRUE') {
            if (parts[1] == '1') {
              // Display info
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Info'),
                  content: Text(parts[2]),
                  icon: const Icon(Icons.info_outline),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(parts[3]),
                    ),
                  ],
                ),
              );
            } else if (parts[1] == '2') {
              final String message = parts[2];
              final String buttonLabel = parts[3];
              final String link = parts[4];

              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Info'),
                  content: Text(message),
                  icon: const Icon(Icons.info_outline),
                  actions: [
                    TextButton(
                      onPressed: () async {
                        final uri = Uri.parse(link);
                        if (await canLaunchUrl(uri)) {
                          await launchUrl(uri);
                        }

                        await FirebaseAnalytics.instance.logEvent(
                          name: 'alert_link_visited',
                          parameters: {'task': 'update'},
                        );
                      },
                      child: Text(buttonLabel),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('CANCEL'),
                    ),
                  ],
                ),
              );
            }
          }
        } catch (e) {
          debugPrint("Alert check error: $e");
        }
        break;
    }
  }

  Future<void> URLTask() async {
    try {
      final response = await http.get(
        Uri.parse("https://www.temp.com/quick-tatkal/fetch_irapi_urls.php"),
      );

      if (response.statusCode == 200) {
        final data = response.body;

        if (!data.startsWith("http")) {
          return;
        }

        final parts = data.split("`");
        if (parts.length >= 3) {
          Consts.SEAT_AVL_URL = parts[0];
          Consts.RUNNING_STATUS_URL = parts[1];
          Consts.TRAIN_AUTOCOMPLETE_URL = parts[2];
        }
      } else {
        print("ERROR: ${response.statusCode}");
      }
    } catch (e) {
      print("ERROR: $e");
    }
  }

  Future<void> FetchTrainTimeTask({
    required BuildContext context,
    required String train,
    required String stn,
    required String date,
    required String source,
    required String pnrStatus,
    required String pnrNo,
  }) async {
    try {
      final response = await http.post(
        Uri.parse("http://temp.com/quick-tatkal/irapi/fetch_train_time.php"),
        body: {"train": train, "stn": stn},
      );

      if (response.statusCode == 200) {
        final depTime = response.body.trim();
        if (depTime.contains(":")) {
          //  new NotificationAlarm
          NotificationAlarm(
            context: context,
            date: date,
            source: source,
            pnrStatus: pnrStatus,
            pnrNo: pnrNo,
          ).setNotificationTask(depTime);
        }
      } else {
        debugPrint("Server error while fetching train time");
      }
    } catch (e) {
      debugPrint("Error fetching train time: $e");
    }
  }

  Future<String> getTicketsInfoFromDB(String deviceId) async {
    try {
      final url = Uri.parse(
        'https://www.afrestudios.com/quick-tatkal/get_tickets_2.php',
      );
      final response = await http.post(url, body: {'deviceid': deviceId});

      if (response.statusCode == 200) {
        return response.body;
      } else {
        return 'ERROR';
      }
    } catch (e) {
      return 'ERROR';
    }
  }

  Future<String> getTicketsInfoFromDBNew({
    required String deviceId,
    required String email,
    required String phone,
    required String name,
    required String invitedBy,
    required String userType,
    required int tickets,
    required String packExpiry,
  }) async {
    try {
      final Map<String, String> body = {
        'deviceid': deviceId,
        'email': email,
        'mobile': phone,
        'name': name,
        'invitedby': invitedBy.isNotEmpty ? invitedBy : '',
        'userType': userType,
        'tickets': tickets.toString(),
        'packExpiry': packExpiry,
      };

      final response = await http.post(
        Uri.parse(_url),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: body,
      );

      if (response.statusCode == 200) {
        return response.body;
      } else {
        return 'ERROR: UNSUCCESSFUL (${response.statusCode})';
      }
    } catch (e) {
      return 'ERROR: ${e.toString()}';
    }
  }

  //FetchStationList (java code)
  /*public class FetchStationList extends AsyncTask<String, Void, String> {

        @Override
        protected String doInBackground(String... values) {

            try{
                client = getUnsafeOkHttpClient();
                try {
                    Request request = new Request.Builder()
                            .url("https://www.irctc.co.in/eticketing/StationLinguisticNames")
                            .build();

                    try (Response response = client.newCall(request).execute()) {
                        if (!response.isSuccessful()) return "ERROR";

                        return response.body().string();
                    }
                } catch(Exception e) {
                    return "ERROR";
                }
            } catch(Exception e){
                return "ERROR";
            }
        }

        @Override
        protected void onPostExecute(String s) {
            if(s.toUpperCase().contains("CANT")) {
                Consts.STATION_LIST = s.split("\\[")[1].split("]")[0].
                        replaceAll("\"", "");
            }
        }
    }*/

  //FetchTrainList (java code)
  /*public class FetchTrainList extends AsyncTask<String, Void, String> {

        @Override
        protected String doInBackground(String... values) {

            try{
                client = getUnsafeOkHttpClient();
                try {
                    Request request = new Request.Builder()
                            .url("https://www.irctc.co.in/eticketing/trainList")
                            .build();

                    try (Response response = client.newCall(request).execute()) {
                        if (!response.isSuccessful()) return "ERROR";

                        return response.body().string();
                    }
                } catch(Exception e) {
                    return "ERROR";
                }
            } catch(Exception e){
                return "ERROR";
            }
        }

        @Override
        protected void onPostExecute(String s) {
            if(s.toUpperCase().contains("EXP")) {
                Consts.TRAIN_LIST = s.replaceAll("\"", "");
            }
        }
    }*/

  //GetTicketsNew (java code)
  /*public class GetTicketsNew extends AsyncTask<String, Void, String> {

        public GetTicketsNew(String source) {
            Consts.loginMethod = source;
        }
        @Override
        protected String doInBackground(String... values) {
            return getTicketsInfoFromDBNew(Consts.DEVICE_ID, SplashActivity.PRIMARY_EMAIL, SplashActivity.MOBILE_NO, SplashActivity.CUST_NAME);
        }

        @Override
        protected void onPostExecute(String s) {
            if(!new ConnectionDetector(activity).isConnectingToInternet()) {
                showErrorScreen();
                return;
            }

            if(s.equals("NEW_USER")) {
                new FirestoreFunctions(activity).registerSignedInUser();
                return;
            }

            if(!(s.contains("•") && s.split("•").length > 7 && !s.contains("ERROR"))) {
                s = getDataFromLocal("log_device_id");
            }

            if(s.contains("•") && s.split("•").length > 7 && !s.contains("ERROR")) {
                new FirestoreFunctions(activity).copyUserDataFromSignIn(s);
            } else {
                new FirestoreFunctions(activity).registerSignedInUser();
            }
        }
    }*/

  void showErrorScreen() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text("No internet connection or server error.")),
    );
  }

  Future<void> saveDataInLocal(String fileName, String data) async {
    try {
      final directory = await getExternalStorageDirectory();

      if (directory != null) {
        final file = File('${directory.path}/$fileName');

        await file.writeAsString(data);
        print("Data saved in: ${file.path}");
      } else {
        print("External storage directory is null.");
      }
    } catch (e) {
      print("Error writing file: $e");
    }
  }

  Future<String> getDataFromLocal(String fileName) async {
    try {
      final directory = await getExternalStorageDirectory();
      if (directory == null) return "ERROR";

      final file = File('${directory.path}/$fileName');

      if (await file.exists()) {
        final lines = await file.readAsLines();
        if (lines.isNotEmpty) {
          return lines.first; // Return first line
        }
      }
    } catch (e) {}
    return "ERROR";
  }

  //GetTicketsSignupLater (java code)
  /*public class GetTicketsSignupLater extends AsyncTask<String, Void, String> {

        @Override
        protected String doInBackground(String... values) {

            client = getUnsafeOkHttpClient();
            try {
                RequestBody formBody = new FormBody.Builder()
                        .add("deviceid", Consts.DEVICE_ID)
                        .add("invitedby", SplashActivity.INVITED_BY)
                        .build();
                Request request = new Request.Builder()
                        .url("https://www.afrestudios.com/quick-tatkal/4.0-firestore/log_device_firestore.php")
                        .post(formBody)
                        .build();

                try (Response response = client.newCall(request).execute()) {
                    if (!response.isSuccessful()) return "ERROR: UNSUCCESSFUL";

                    return response.body().string();
                }
            } catch(Exception e) {
                return "ERROR: " + e.getMessage();
            }
        }

        @Override
        protected void onPostExecute(String s) {
            if(!new ConnectionDetector(activity).isConnectingToInternet()) {
                showErrorScreen();
                return;
            }

            Log.d("FIRESTORE DEBUG", "SERVER CHECK COMPLETE");
            if(s.equals("NEW_USER")) {
                new FirestoreFunctions(activity).registerUserWithDeviceId();
                return;
            }

            if(!(s.contains("•") && s.split("•").length > 7 && !s.contains("ERROR"))) {
                s = getDataFromLocal("log_device_id");
            }

            if(s.contains("•") && s.split("•").length > 7 && !s.contains("ERROR")) {
                new FirestoreFunctions(activity).copyUserDataFromDeviceId(s);
            } else {
                new FirestoreFunctions(activity).registerUserWithDeviceId();
            }
        }
    }*/

  Future<bool> deleteFileFromFTP({
    required String deviceId,
    required String context, // username
    required String quickTatkal, // password
  }) async {
    // Call your custom HTTP backend API endpoint for FTP deletion
    final url = Uri.parse(
      'https://your-backend-host/api/delete_ftp_file',
    ); // <-- Replace with your backend URL
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body:
            '{"username": "$context", "password": "$quickTatkal", "filename": "$deviceId"}',
      );
      if (response.statusCode == 200) {
        final responseBody = response.body;
        print('Backend FTP delete response: ' + responseBody);
        return responseBody.contains('success') &&
            responseBody.contains('true');
      } else {
        print('API error: ${response.statusCode} ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error calling FTP backend API: $e');
      return false;
    }
  }

  //endUPITrial
  /*public void endUPITrial(FloatingWidgetService floatingService) {
        class SendPostReqAsyncTask extends AsyncTask<String, Void, String> {
            @Override
            protected String doInBackground(String... params) {
                client = getUnsafeOkHttpClient();
                try {
                    RequestBody formBody = new FormBody.Builder()
                            .add("tid", SplashActivity.TID)
                            .build();
                    Request request = new Request.Builder()
                            .url("https://www.afrestudios.com/quick-tatkal/2.0/end_upi_trial.php")
                            .post(formBody)
                            .build();

                    try (Response response = client.newCall(request).execute()) {
                        if (!response.isSuccessful()) return "ERROR";

                        return Objects.requireNonNull(response.body()).string();
                    }
                } catch(Exception e) {
                    return "ERROR";
                }
            }
            //18567 sgst

            @Override
            protected void onPostExecute(String result) {
                super.onPostExecute(result);
                if(floatingService != null) {
                    floatingService.stopEverything();
                }
            }
        }

        SendPostReqAsyncTask sendPostReqAsyncTask = new SendPostReqAsyncTask();
        sendPostReqAsyncTask.execute();
    }*/

  Future<bool> updateTicketsWithTID({
    required bool isFromDashboard,
    required bool isFromPremium,
    required Function onPaymentComplete,
  }) async {
    const String url =
        'https://www.afrestudios.com/quick-tatkal/3.0/update_tickets_2.php';

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'tid': SplashScreenState.tid,
          'tickets': SplashScreenState.ticketsLeft.toString(),
          'userType': Consts.USER_TYPE,
          'packageName': 'com.tatkal.train.quick',
          'purchaseToken': Consts.PURCHASE_TOKEN,
        },
      );

      if (!response.statusCode.toString().startsWith("2")) {
        savePrefs = true;
        handlePostUpdate(
          updateSuccess: false,
          isFromDashboard: isFromDashboard,
          isFromPremium: isFromPremium,
          onPaymentComplete: onPaymentComplete,
        );
        return true;
      }

      final result = response.body;
      print("✅ Update Response: $result");

      if (!result.contains("SUCCESS")) {
        savePrefs = true;
      } else {
        if (SplashScreenState.ticketsLeft == -2 && !result.contains("*")) {
          savePrefs = true;
        }
      }

      handlePostUpdate(
        updateSuccess: true,
        isFromDashboard: isFromDashboard,
        isFromPremium: isFromPremium,
        onPaymentComplete: onPaymentComplete,
      );

      return true;
    } catch (e) {
      print(" Error in updateTicketsWithTID: $e");
      savePrefs = true;
      handlePostUpdate(
        updateSuccess: false,
        isFromDashboard: isFromDashboard,
        isFromPremium: isFromPremium,
        onPaymentComplete: onPaymentComplete,
      );
      return false;
    }
  }

  Future<String> logError(
    String phone,
    String s,
    String result, {
    required String mobile,
    required String medium,
    required String error,
  }) async {
    const String url =
        'https://www.afrestudios.com/quick-tatkal/2.0/log_error.php';
    String output = 'ERROR';

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'deviceid': Consts.DEVICE_ID,
          'mobile': mobile,
          'medium': medium,
          'error': error,
        },
      );

      if (response.statusCode == 200) {
        output = response.body;
        print(" LogError response: $output");
      } else {
        print(" LogError failed with status: ${response.statusCode}");
      }
    } catch (e) {
      print(" Exception while logging error: $e");
    }

    return output;
  }

  static Future<bool> sendEmailOTP({
    required String otp,
    required String email,
  }) async {
    const String url = 'https://www.afrestudios.com/send_otp_mail.php';
    bool success = false;

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {'otp': otp, 'email': email},
      );

      if (response.statusCode == 200) {
        final result = response.body;
        print('📧 OTP Response: $result');
        success = result.contains('SUCCESS');
      } else {
        print(' Failed to send OTP. Code: ${response.statusCode}');
      }
    } catch (e) {
      print(' Exception sending OTP: $e');
    }

    return success;
  }

  static Future<bool> sendMobileOTP({
    required String otp,
    required String phoneNumber,
  }) async {
    const String url =
        'https://www.afrestudios.com/quick-tatkal/msg91/send_otp.php';
    bool success = false;

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {'otp': otp, 'mobileno': phoneNumber},
      );

      if (response.statusCode == 200) {
        final result = response.body;
        print('📱 Mobile OTP Response: $result');
        success = result.contains('success') || result.contains('SUCCESS');
      } else {
        print(' Failed to send Mobile OTP. Code: ${response.statusCode}');
      }
    } catch (e) {
      print(' Exception sending Mobile OTP: $e');
    }

    return success;
  }
}

class LoginHandler {
  String manipulate(List<int> list, int r, int factor) {
    int m = r - factor;
    int s = r * r;
    int v = s * s + r + 2;
    String vc = String.fromCharCode(v);

    List<int> values = [
      s * 7 + 2,
      (s + 1) * 11,
      (s + 1) * 10,
      s * 13 - 3,
      s * 12 + 3,
      s * 12 - 3,
      s * 11 + 1,
      s * 5 + 2,
      s * 11 - 2,
    ];
    String comp = "";

    for (int i = 0; i < 15; i++) {
      String c;
      if (i < 9) {
        c = String.fromCharCode(values[i]);
      } else {
        c = String.fromCharCode(values[(i + 1) % 9]);
      }
      comp += c;
    }

    String str = "";
    for (int i in list) {
      str += String.fromCharCode(i % 128);
    }
    int l = str.length ~/ 2;

    String t;
    if (factor == -1) {
      t =
          str.substring(l) +
          str.substring(0, l).toUpperCase().trim() +
          comp +
          vc;
    } else {
      t =
          str.substring(l) +
          str.substring(0, l).toUpperCase().trim() +
          comp +
          m.toString();
    }
    return t;
  }
}
