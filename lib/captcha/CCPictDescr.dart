import 'dart:typed_data';
import 'APIConsts.dart';
import 'CCProtoPacket.dart';

class CCPictDescr {
  int _timeout = APIConsts.ptoDEFAULT;
  int _type = APIConsts.ptUNSPECIFIED;
  int _size = 0;
  int _major_id = 0;
  int _minor_id = 0;
  Uint8List? _data;

  Uint8List pack() {
    int data_length = _data == null ? 0 : _data!.length;
    Uint8List res = Uint8List(4 * 5 + data_length);
    int i = 0;
    int j = 0;
    int value = 0;

    value = _timeout;
    res[i++] = (value >> 0) & 0xff;
    res[i++] = (value >> 8) & 0xff;
    res[i++] = (value >> 16) & 0xff;
    res[i++] = (value >> 24) & 0xff;

    value = _type;
    res[i++] = (value >> 0) & 0xff;
    res[i++] = (value >> 8) & 0xff;
    res[i++] = (value >> 16) & 0xff;
    res[i++] = (value >> 24) & 0xff;

    value = _size;
    res[i++] = (value >> 0) & 0xff;
    res[i++] = (value >> 8) & 0xff;
    res[i++] = (value >> 16) & 0xff;
    res[i++] = (value >> 24) & 0xff;

    value = _major_id;
    res[i++] = (value >> 0) & 0xff;
    res[i++] = (value >> 8) & 0xff;
    res[i++] = (value >> 16) & 0xff;
    res[i++] = (value >> 24) & 0xff;

    value = _minor_id;
    res[i++] = (value >> 0) & 0xff;
    res[i++] = (value >> 8) & 0xff;
    res[i++] = (value >> 16) & 0xff;
    res[i++] = (value >> 24) & 0xff;

    if (_data != null) {
      for (j = 0; j < _data!.length; j++) {
        res[i++] = _data![j];
      }
    }

    return res;
  }

  void unpack(Uint8List bin) {
    int i = 0;
    int j = 0;

    if (bin.length < CCProtoPacket.SIZEOF_CC_PICT_DESCR) {
      return;
    }
    _timeout = (bin[0] << 0) | (bin[1] << 8) | (bin[2] << 16) | (bin[3] << 24);
    _type = (bin[4] << 0) | (bin[5] << 8) | (bin[6] << 16) | (bin[7] << 24);
    _size = (bin[8] << 0) | (bin[9] << 8) | (bin[10] << 16) | (bin[11] << 24);
    _major_id =
        (bin[12] << 0) | (bin[13] << 8) | (bin[14] << 16) | (bin[15] << 24);
    _minor_id =
        (bin[16] << 0) | (bin[17] << 8) | (bin[18] << 16) | (bin[19] << 24);

    _data = null;

    if (bin.length > CCProtoPacket.SIZEOF_CC_PICT_DESCR) {
      _data = Uint8List(bin.length - CCProtoPacket.SIZEOF_CC_PICT_DESCR);
      for (
        int i = CCProtoPacket.SIZEOF_CC_PICT_DESCR, j = 0;
        i < bin.length;
        i++, j++
      ) {
        _data![j] = bin[i];
      }
    }
  }

  void setTimeout(int to) {
    _timeout = to;
  }

  int getTimeout() {
    return _timeout;
  }

  void setType(int type) {
    _type = type;
  }

  int getType() {
    return _type;
  }

  void setSize(int size) {
    _size = size;
  }

  int getSize() {
    return _size;
  }

  int calcSize() {
    if (_data == null) {
      _size = 0;
    } else {
      _size = _data!.length;
    }
    return _size;
  }

  int getFullSize() {
    return CCProtoPacket.SIZEOF_CC_PICT_DESCR + _size;
  }

  void setMajorID(int major_id) {
    _major_id = major_id;
  }

  int getMajorID() {
    return _major_id;
  }

  void setMinorID(int minor_id) {
    _minor_id = minor_id;
  }

  int getMinorID() {
    return _minor_id;
  }

  void setData(Uint8List? data) {
    _data = data;
  }

  Uint8List? getData() {
    return _data;
  }
}
