class CaptchaScriptHelper {
  static String fillNlpAnswer(String output) {
    return """javascript:function fill() {
      document.getElementById('nlpAnswer').value = '$output';
      document.getElementById('nlpAnswer').focus();
      Step.spaceAndBackspace();

      setTimeout(function() {
        if(document.getElementsByClassName('train_Search').length > 1) {
          document.getElementsByClassName('search_btn train_Search')[2].click();
        } else {
          document.getElementsByClassName('train_Search btnDefault')[0].click();
        }
      }, 200);
    }
    fill()""";
  }

  static String fillCaptcha(String output) {
    return """javascript:function fill() {
      document.getElementById('captcha').value = '$output';
      document.getElementById('captcha').focus();
      Step.spaceAndBackspace();

      setTimeout(function() {
        if(document.getElementsByClassName('train_Search').length > 1) {
          document.getElementsByClassName('search_btn train_Search')[2].click();
        } else {
          document.getElementsByClassName('train_Search btnDefault')[0].click();
        }
      }, 200);
    }
    fill()""";
  }

  static String fillHdfcCaptcha(String output) {
    return """javascript:function aish() {
      document.getElementsByName('passline')[0].value = '$output';
      document.getElementById('submit_btn').click();
    }
    aish()""";
  }
}
