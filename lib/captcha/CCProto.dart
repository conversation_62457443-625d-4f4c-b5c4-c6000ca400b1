import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'APIConsts.dart';
import 'CCProtoPacket.dart';
import 'CCPictDescr.dart';

/// Top-level class for the result (must be outside)
class PictureResult {
  final int timeReallyUsed;
  final int typeReallyUsed;
  final String text;
  final int majorId;
  final int minorId;
  final int returnCode;

  PictureResult(
    this.timeReallyUsed,
    this.typeReallyUsed,
    this.text,
    int? major,
    int? minor,
    this.returnCode,
  ) : this.majorId = major ?? 0,
      this.minorId = minor ?? 0;
}

/// CC protocol class
class CCProto {
  static const int sCCC_INIT =
      1; // initial status, ready to issue LOGIN on client
  static const int sCCC_LOGIN =
      2; // LOGIN is sent, waiting for RAND (login accepted) or CLOSE CONNECTION (login is unknown)
  static const int sCCC_HASH =
      3; // HASH is sent, server may CLOSE CONNECTION (hash is not recognized)
  static const int sCCC_PICTURE = 4;

  int _status = sCCC_INIT;
  Socket? _sock;
  IOSink? _dos;
  Stream<Uint8List>? _dis;

  /// Login method
  Future<int> login(String hostname, int port, String login, String pwd) async {
    CCProtoPacket? pack;
    int i = 0;
    int j = 0;

    _status = sCCC_INIT;

    try {
      _sock = await Socket.connect(hostname, port);
      _dos = _sock;
      _dis = _sock!.asBroadcastStream();
    } catch (e) {
      return APIConsts.ccERR_NET_ERROR;
    }

    pack = CCProtoPacket();
    pack.setVer(CCProtoPacket.CC_PROTO_VER);

    pack.setCmd(CCProtoPacket.cmdCC_LOGIN);
    pack.setSize(login.length);
    pack.setData(Uint8List.fromList(utf8.encode(login)));

    if (pack.packTo(_dos!) == false) {
      return APIConsts.ccERR_NET_ERROR;
    }

    if (await pack.unpackFrom(
          _dis!,
          CCProtoPacket.cmdCC_RAND,
          CCProtoPacket.CC_RAND_SIZE,
        ) ==
        false) {
      return APIConsts.ccERR_NET_ERROR;
    }

    var md5Hash = md5.convert(utf8.encode(pwd));
    String md5str = md5Hash.toString();

    List<int> shabuf = [];
    shabuf.addAll(pack.getData()!);
    shabuf.addAll(utf8.encode(md5str));
    shabuf.addAll(utf8.encode(login));

    var shaHash = sha256.convert(shabuf);

    pack = CCProtoPacket();
    pack.setVer(CCProtoPacket.CC_PROTO_VER);
    pack.setCmd(CCProtoPacket.cmdCC_HASH);
    pack.setSize(CCProtoPacket.CC_HASH_SIZE);
    pack.setData(Uint8List.fromList(shaHash.bytes));

    if (pack.packTo(_dos!) == false) {
      return APIConsts.ccERR_NET_ERROR;
    }

    if (await pack.unpackFrom(_dis!, CCProtoPacket.cmdCC_OK, 0) == false) {
      return APIConsts.ccERR_NET_ERROR;
    }

    _status = sCCC_PICTURE;

    return APIConsts.ccERR_OK;
  }

  /// Receive back a result object that includes all the details in a format trivial to use
  /// in other Dart code, while passing only and exactly what's needed.
  ///
  /// This is a simple wrapper to picture2 and does no logic of its own.
  Future<PictureResult> picture2Simple(
    Uint8List pict,
    int timeout,
    int type,
  ) async {
    List<int> to_wrapper = [timeout];
    List<int> type_wrapper = [type];
    List<int> major_wrapper = [0];
    List<int> minor_wrapper = [0];
    List<String> text_wrapper = [""];

    int result = await picture2(
      pict,
      to_wrapper,
      type_wrapper,
      text_wrapper,
      major_wrapper,
      minor_wrapper,
    );
    return PictureResult(
      to_wrapper[0],
      type_wrapper[0],
      text_wrapper[0],
      major_wrapper[0],
      minor_wrapper[0],
      result,
    );
  }

  /// say "thanks" to Java incapability to pass values by reference in order to use them as multiple returns
  /// all arrays[] are used as workaround to get values out of the function, really
  Future<int> picture2(
    Uint8List pict, // IN      picture binary data
    List<int> pict_to,
    // IN/OUT  timeout specifier to be used, on return - really used specifier, see ptoXXX constants, ptoDEFAULT in case of unrecognizable
    List<int> pict_type,
    // IN/OUT  type specifier to be used, on return - really used specifier, see ptXXX constants, ptUNSPECIFIED in case of unrecognizable
    List<String> text, // OUT text
    List<int> major_id, // OUT OPTIONAL major part of the picture ID
    List<int> minor_id, // OUT OPTIONAL minor part of the picture ID
  ) async {
    if (_status != sCCC_PICTURE) return APIConsts.ccERR_STATUS;

    CCProtoPacket pack = CCProtoPacket();
    pack.setVer(CCProtoPacket.CC_PROTO_VER);
    pack.setCmd(CCProtoPacket.cmdCC_PICTURE2);

    CCPictDescr desc = CCPictDescr();
    desc.setTimeout(pict_to[0]);
    desc.setType(pict_type[0]);
    desc.setMajorID(0);
    desc.setMinorID(0);
    desc.setData(pict);
    desc.calcSize();

    pack.setData(desc.pack());
    pack.calcSize();

    if (pack.packTo(_dos!) == false) {
      return APIConsts.ccERR_NET_ERROR;
    }

    if (await pack.unpackFrom(_dis!, -1, -1) == false) {
      return APIConsts.ccERR_NET_ERROR;
    }

    switch (pack.getCmd()) {
      case CCProtoPacket.cmdCC_TEXT2:
        desc.unpack(pack.getData()!);
        pict_to[0] = desc.getTimeout();
        pict_type[0] = desc.getType();
        text[0] = desc.getData() == null ? "" : utf8.decode(desc.getData()!);

        if (major_id.isNotEmpty) major_id[0] = desc.getMajorID();
        if (minor_id.isNotEmpty) minor_id[0] = desc.getMinorID();

        return APIConsts.ccERR_OK;

      case CCProtoPacket.cmdCC_BALANCE:
        // balance depleted
        return APIConsts.ccERR_BALANCE;

      case CCProtoPacket.cmdCC_OVERLOAD:
        // server's busy
        return APIConsts.ccERR_OVERLOAD;

      case CCProtoPacket.cmdCC_TIMEOUT:
        // picture timed out
        return APIConsts.ccERR_TIMEOUT;

      case CCProtoPacket.cmdCC_FAILED:
        // server's error
        return APIConsts.ccERR_GENERAL;

      default:
        // unknown error
        return APIConsts.ccERR_UNKNOWN;
    }
  }

  int picture_bad2(int major_id, int minor_id) {
    CCProtoPacket pack = CCProtoPacket();

    pack.setVer(CCProtoPacket.CC_PROTO_VER);
    pack.setCmd(CCProtoPacket.cmdCC_PICTUREFL);

    CCPictDescr desc = CCPictDescr();
    desc.setTimeout(APIConsts.ptoDEFAULT);
    desc.setType(APIConsts.ptUNSPECIFIED);
    desc.setMajorID(major_id);
    desc.setMinorID(minor_id);
    desc.calcSize();

    pack.setData(desc.pack());
    pack.calcSize();

    if (pack.packTo(_dos!) == false) {
      return APIConsts.ccERR_NET_ERROR;
    }

    return APIConsts.ccERR_NET_ERROR;
  }

  Future<int> balance(List<String> balance) async {
    CCProtoPacket? pack;

    if (_status != sCCC_PICTURE) return APIConsts.ccERR_STATUS;

    pack = CCProtoPacket();
    pack.setVer(CCProtoPacket.CC_PROTO_VER);
    pack.setCmd(CCProtoPacket.cmdCC_BALANCE);
    pack.setSize(0);

    if (pack.packTo(_dos!) == false) {
      return APIConsts.ccERR_NET_ERROR;
    }

    if (await pack.unpackFrom(_dis!, -1, -1) == false) {
      return APIConsts.ccERR_NET_ERROR;
    }

    switch (pack.getCmd()) {
      case CCProtoPacket.cmdCC_BALANCE:
        balance[0] = utf8.decode(pack.getData()!);
        return APIConsts.ccERR_OK;

      default:
        // unknown error
        return APIConsts.ccERR_UNKNOWN;
    }
  }

  int close() {
    CCProtoPacket pack = CCProtoPacket();
    pack.setVer(CCProtoPacket.CC_PROTO_VER);

    pack.setCmd(CCProtoPacket.cmdCC_BYE);
    pack.setSize(0);

    if (pack.packTo(_dos!) == false) {
      return APIConsts.ccERR_NET_ERROR;
    }

    try {
      _sock?.close();
    } catch (e) {}
    _status = sCCC_INIT;

    return APIConsts.ccERR_NET_ERROR;
  }
}
