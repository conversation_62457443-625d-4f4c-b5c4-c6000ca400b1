import 'dart:io';
import 'dart:typed_data';

class CCProtoPacket {
  static const int CC_PROTO_VER = 1; // protocol version
  static const int CC_RAND_SIZE =
      256; // size of the random sequence for authentication procedure
  static const int CC_MAX_TEXT_SIZE =
      100; // maximum characters in returned text for picture
  static const int CC_MAX_LOGIN_SIZE =
      100; // maximum characters in login string
  static const int CC_MAX_PICTURE_SIZE =
      200000; // 200 K bytes for picture seems sufficient for all purposes
  static const int CC_HASH_SIZE = 32; //

  static const int cmdCC_UNUSED = 0;
  static const int cmdCC_LOGIN = 1; // login
  static const int cmdCC_BYE = 2; // end of session
  static const int cmdCC_RAND =
      3; // random data for making hash with login+password
  static const int cmdCC_HASH = 4; // hash data
  static const int cmdCC_PICTURE = 5; // picture data, deprecated
  static const int cmdCC_TEXT = 6; // text data, deprecated
  static const int cmdCC_OK = 7; //
  static const int cmdCC_FAILED = 8; //
  static const int cmdCC_OVERLOAD = 9; //
  static const int cmdCC_BALANCE = 10; // zero balance
  static const int cmdCC_TIMEOUT = 11; // time out occured
  static const int cmdCC_PICTURE2 = 12; // picture data
  static const int cmdCC_PICTUREFL = 13; // picture failure
  static const int cmdCC_TEXT2 = 14; // text data

  static const int SIZEOF_CC_PACKET = 6;
  static const int SIZEOF_CC_PICT_DESCR = 20;

  int _ver = CC_PROTO_VER; // version of the protocol
  int _cmd = cmdCC_BYE; // command, see cc_cmd_t
  int _size = 0; // data size in consequent bytes
  Uint8List? _data; // packet payload

  bool _checkPackHdr(int cmd, int size) {
    if (_ver != CC_PROTO_VER) return false;
    if ((cmd != -1) && (_cmd != cmd)) return false;
    if ((size != -1) && (_size != size)) return false;

    return true;
  }

  static int swapInt(int value) {
    int b1 = (value >> 0) & 0xff;
    int b2 = (value >> 8) & 0xff;
    int b3 = (value >> 16) & 0xff;
    int b4 = (value >> 24) & 0xff;

    return b1 << 24 | b2 << 16 | b3 << 8 | b4 << 0;
  }

  bool packTo(IOSink oos) {
    try {
      var buffer = BytesBuilder();
      buffer.addByte(_ver);
      buffer.addByte(_cmd);

      var sizeBytes = ByteData(4);
      sizeBytes.setInt32(0, swapInt(_size), Endian.big);
      buffer.add(sizeBytes.buffer.asUint8List());

      if (_data != null) {
        if (_data!.length > 0) {
          buffer.add(_data!);
        }
      }

      oos.add(buffer.toBytes());
      return true;
    } catch (e) {
      return false;
    }
  }

  bool _unpackHeader(Stream<List<int>> ios) {
    try {
      // This would need proper async implementation
      // For now, keeping the structure similar
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> unpackFrom(Stream<List<int>> dis, int cmd, int size) async {
    _unpackHeader(dis);

    if (_checkPackHdr(cmd, size) == false) return false;

    try {
      if (_size > 0) {
        _data = Uint8List(_size);
        // Would need proper stream reading implementation
      } else {
        _data = null;
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  void setVer(int ver) {
    _ver = ver;
  }

  int getVer() {
    return _ver;
  }

  void setCmd(int cmd) {
    _cmd = cmd;
  }

  int getCmd() {
    return _cmd;
  }

  void setSize(int size) {
    _size = size;
  }

  int getSize() {
    return _size;
  }

  int calcSize() {
    if (_data != null) {
      _size = _data!.length;
    } else {
      _size = 0;
    }
    return _size;
  }

  int getFullSize() {
    return SIZEOF_CC_PACKET + _size;
  }

  void setData(Uint8List? data) {
    _data = data;
  }

  Uint8List? getData() {
    return _data;
  }
}
