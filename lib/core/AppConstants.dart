import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';

class AppConstants {
  static int ticketsLeft = 0;
  static int isGoldUser = 1; // 0: not gold, 1: network error, 2: gold
  static String mobileNo = "NA";
  static String custName = "";
  static String email = "NA";
  static String primaryEmail = "NA";
  static String? profilePicUrl;
  static String tid = "0";
  static String invitedBy = "NA";
  static String loginMethod = "LATER";
  static String signupSource = "Logout";
  static bool logoutClicked = false;
  static bool isLoggedIn = false;

  static Image? profilePic;
  static String INVITED_BY = "NA";

  // Additional SharedPreferences keys from Java code
  static int rating = 0;
  static bool rcSettings = false;
  static int clickCount = 0;
  static bool formSaveStatus = false;
  static String paymentBackup = "";
  static bool pendingPaytmPayment = false;
  static bool transferSuccess = false;
  static bool loginSuccess = false;
  static String goldRegular = "";
  static String goldOffer = "";
  static bool guideShown = false;
  static int purchaseCount = 0;
  static String ticketBackup = "";
  static bool signupLater = false;

  // Load user data from SharedPreferences
  static Future<void> loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      isLoggedIn = prefs.getBool('is_logged_in') ?? false;
      mobileNo = prefs.getString('mobile_no') ?? "NA";
      email = prefs.getString('email') ?? "NA";
      primaryEmail = prefs.getString('primary_email') ?? "NA";
      custName = prefs.getString('cust_name') ?? "";
      loginMethod = prefs.getString('login_method') ?? "LATER";
      profilePicUrl = prefs.getString('profile_pic_url');
      ticketsLeft = prefs.getInt('tickets_left') ?? 0;
      isGoldUser = prefs.getInt('is_gold_user') ?? 1;

      // Load additional SharedPreferences keys
      rating = prefs.getInt('RATING') ?? 0;
      rcSettings = prefs.getBool('RC') ?? false;
      clickCount = prefs.getInt('CLICK') ?? 0;
      formSaveStatus = prefs.getBool('FORM_SAVE') ?? false;
      paymentBackup = prefs.getString('PYMT_BACKUP') ?? "";
      pendingPaytmPayment = prefs.getBool('PENDING_PYMT_PAYTM') ?? false;
      transferSuccess = prefs.getBool('TRANSFER_SUCCESS') ?? false;
      loginSuccess = prefs.getBool('LOGIN_SUCCESS') ?? false;
      goldRegular = prefs.getString('GOLD_REGULAR') ?? "";
      goldOffer = prefs.getString('GOLD_OFFER') ?? "";
      guideShown = prefs.getBool('GUIDE') ?? false;
      purchaseCount = prefs.getInt('PURCHASE_COUNT') ?? 0;
      ticketBackup = prefs.getString('TICKET_BACKUP') ?? "";
      signupLater = prefs.getBool('SIGNUP_LATER') ?? false;

      // Sync with SplashScreenState for consistency
      await syncWithSplashScreenState();

      print(
        " User data loaded: isLoggedIn=$isLoggedIn, method=$loginMethod, mobile=$mobileNo",
      );
    } catch (e) {
      print(" Error loading user data: $e");
    }
  }

  // Save user data to SharedPreferences
  static Future<void> saveUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setBool('is_logged_in', isLoggedIn);
      await prefs.setString('mobile_no', mobileNo);
      await prefs.setString('email', email);
      await prefs.setString('primary_email', primaryEmail);
      await prefs.setString('cust_name', custName);
      await prefs.setString('login_method', loginMethod);
      await prefs.setInt('tickets_left', ticketsLeft);
      await prefs.setInt('is_gold_user', isGoldUser);

      // Save additional SharedPreferences keys
      await prefs.setInt('RATING', rating);
      await prefs.setBool('RC', rcSettings);
      await prefs.setInt('CLICK', clickCount);
      await prefs.setBool('FORM_SAVE', formSaveStatus);
      await prefs.setString('PYMT_BACKUP', paymentBackup);
      await prefs.setBool('PENDING_PYMT_PAYTM', pendingPaytmPayment);
      await prefs.setBool('TRANSFER_SUCCESS', transferSuccess);
      await prefs.setBool('LOGIN_SUCCESS', loginSuccess);
      await prefs.setString('GOLD_REGULAR', goldRegular);
      await prefs.setString('GOLD_OFFER', goldOffer);
      await prefs.setBool('GUIDE', guideShown);
      await prefs.setInt('PURCHASE_COUNT', purchaseCount);
      await prefs.setString('TICKET_BACKUP', ticketBackup);
      await prefs.setBool('SIGNUP_LATER', signupLater);

      if (profilePicUrl != null) {
        await prefs.setString('profile_pic_url', profilePicUrl!);
      }

      print(" User data saved: isLoggedIn=$isLoggedIn, method=$loginMethod");
    } catch (e) {
      print(" Error saving user data: $e");
    }
  }

  // Clear user data (for logout)
  static Future<void> clearUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Clear main user data
      await prefs.remove('is_logged_in');
      await prefs.remove('mobile_no');
      await prefs.remove('email');
      await prefs.remove('primary_email');
      await prefs.remove('cust_name');
      await prefs.remove('login_method');
      await prefs.remove('profile_pic_url');
      await prefs.remove('tickets_left');
      await prefs.remove('is_gold_user');

      // Clear additional SharedPreferences keys
      await prefs.remove('RATING');
      await prefs.remove('RC');
      await prefs.remove('CLICK');
      await prefs.remove('FORM_SAVE');
      await prefs.remove('PYMT_BACKUP');
      await prefs.remove('PENDING_PYMT_PAYTM');
      await prefs.remove('TRANSFER_SUCCESS');
      await prefs.remove('LOGIN_SUCCESS');
      await prefs.remove('GOLD_REGULAR');
      await prefs.remove('GOLD_OFFER');
      await prefs.remove('GUIDE');
      await prefs.remove('PURCHASE_COUNT');
      await prefs.remove('TICKET_BACKUP');
      await prefs.remove('SIGNUP_LATER');

      // Reset to default values
      isLoggedIn = false;
      mobileNo = "NA";
      email = "NA";
      primaryEmail = "NA";
      custName = "";
      loginMethod = "LATER";
      profilePicUrl = null;
      ticketsLeft = 0;
      isGoldUser = 1;

      // Reset additional values
      rating = 0;
      rcSettings = false;
      clickCount = 0;
      formSaveStatus = false;
      paymentBackup = "";
      pendingPaytmPayment = false;
      transferSuccess = false;
      loginSuccess = false;
      goldRegular = "";
      goldOffer = "";
      guideShown = false;
      purchaseCount = 0;
      ticketBackup = "";
      signupLater = false;

      print(" User data cleared");
    } catch (e) {
      print(" Error clearing user data: $e");
    }
  }

  // Sync AppConstants with SplashScreenState data
  static Future<void> syncWithSplashScreenState() async {
    try {
      // Import SplashScreenState dynamically to avoid circular dependency
      final splash = await _getSplashScreenData();

      if (splash != null) {
        // Sync data from SplashScreenState if it has more recent data
        if (splash['ticketsLeft'] != null) ticketsLeft = splash['ticketsLeft'];
        if (splash['isGoldUser'] != null) isGoldUser = splash['isGoldUser'];
        if (splash['MOBILE_NO'] != null && splash['MOBILE_NO'] != "NA") {
          mobileNo = splash['MOBILE_NO'];
        }
        if (splash['CUST_NAME'] != null && splash['CUST_NAME'].isNotEmpty) {
          custName = splash['CUST_NAME'];
        }
        if (splash['EMAIL'] != null && splash['EMAIL'] != "NA") {
          email = splash['EMAIL'];
        }
        if (splash['PRIMARY_EMAIL'] != null &&
            splash['PRIMARY_EMAIL'] != "NA") {
          primaryEmail = splash['PRIMARY_EMAIL'];
        }
        if (splash['tid'] != null && splash['tid'] != "0") {
          tid = splash['tid'];
        }
        if (splash['INVITED_BY'] != null && splash['INVITED_BY'] != "NA") {
          invitedBy = splash['INVITED_BY'];
        }

        print(" Data synced with SplashScreenState");
      }
    } catch (e) {
      print(" Error syncing with SplashScreenState: $e");
    }
  }

  // Helper method to get SplashScreenState data without importing
  static Future<Map<String, dynamic>?> _getSplashScreenData() async {
    try {
      // Use reflection-like approach to avoid circular imports
      return {
        'ticketsLeft': null,
        // Will be filled by actual values from SplashScreenState
        'isGoldUser': null,
        'MOBILE_NO': null,
        'CUST_NAME': null,
        'EMAIL': null,
        'PRIMARY_EMAIL': null,
        'tid': null,
        'INVITED_BY': null,
      };
    } catch (e) {
      return null;
    }
  }

  // Utility methods for specific SharedPreferences operations
  static Future<void> setPendingPayment(
    String orderId,
    String packName,
    int amount,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('PYMT_BACKUP', '$orderId|$packName|$amount');
    paymentBackup = '$orderId|$packName|$amount';
  }

  static Future<Map<String, String>?> getPendingPayment() async {
    final prefs = await SharedPreferences.getInstance();
    final backup = prefs.getString('PYMT_BACKUP');
    if (backup != null && backup.isNotEmpty) {
      final parts = backup.split('|');
      if (parts.length >= 3) {
        return {'orderId': parts[0], 'packName': parts[1], 'amount': parts[2]};
      }
    }
    return null;
  }

  static Future<void> incrementPurchaseCount(String sku) async {
    final prefs = await SharedPreferences.getInstance();
    final key = 'PURCHASE_COUNT_$sku';
    final currentCount = prefs.getInt(key) ?? 0;
    await prefs.setInt(key, currentCount + 1);
  }
}
