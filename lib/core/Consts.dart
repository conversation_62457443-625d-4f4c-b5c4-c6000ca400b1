/**
 * AUTHOR : LOKESH
 * DATE : 03-07-25.
 */

class Consts {
  static const String SBI = "State Bank of India";
  static const String IND_OVS_BK = "Indian Overseas Bank";
  static const String PNB = "Punjab National Bank";
  static const String INDIAN_BK = "Indian Bank";
  static const String UNION_BK = "Union Bank of India";
  static const String BOI = "Bank of India";
  static const String ANDHRA_BK = "Andhra Bank";
  static const String CANARA_BK = "Canara Bank";
  static const String CITI_BK = "CITI Bank";
  static const String ICICI = "ICICI Bank";
  static const String HDFC = "HDFC Bank";
  static const String CBI = "Central Bank of India";
  static const String AXIS = "AXIS Bank";
  static const String UNITED_BK = "United Bank of India";
  static const String C1 = "emulated";
  static const String C2 = "file001.log";
  static const String C3 = "settings.info";
  static const List<int> PERMISSIONS = [97, 107, 97, 110, 107, 115, 104, 97];

  // yyyymmdd
  static String SEAT_AVL_URL =
      "http://server1.irapi.online/api/V2/SeatAvailability/apikey/f6d9695c613bbd6ba0593b43996beaa3/From/{FROM}/To/{TO}/Date/{DATE}/server/1/route/1";
  static String RUNNING_STATUS_URL =
      "http://server1.irapi.online/api/V2/livetrainstatus/apikey/f6d9695c613bbd6ba0593b43996beaa3/trainnumber/{TRAIN_NO}/date/{START_DT}";
  static String TRAIN_AUTOCOMPLETE_URL =
      "http://server1.irapi.online/api/V2/AutoCompleteTrainInformation/apikey/f6d9695c613bbd6ba0593b43996beaa3/TrainNumberOrName/{TRAIN}/CovidSpecial";

  static int AD_SHOWN_TIME_IN_MILLIS = 0;
  static String loginMethod = "LATER";
  static bool NO_WEBVIEW = false;

  // Railofy Notifications
  static const int BOOK_RETURN_TICKET = 1;
  static const int FOOD_IN_TRAIN = 2;
  static const int TRAVEL_GUARANTEE = 3;
  static const int BOOK_ALTERNATE_TRAIN = 4;

  static const String SOURCE_PNR = "SOURCE_PNR";
  static const String SOURCE_BOOKING = "SOURCE_BOOKING";

  static String ONGOING_PYMT_MEDIUM = "";
  static const String RAZORPAY = "RAZORPAY";
  static const String GOOGLE = "GOOGLE";
  static String PACK_NAME = "";

  static bool TICKET_RESTORE = false;
  static String STATION_LIST = "";
  static String TRAIN_LIST = "";

  static bool ACCESSIBILITY_ACCEPTED = false;

  static const String APP_ID_PHONEPE = "com.phonepe.app";
  static const String APP_ID_GPAY = "com.google.android.apps.nbu.paisa.user";
  static const String APP_ID_PAYTM = "net.one97.paytm";
  static const String APP_ID_BHIM = "in.org.npci.upiapp";

  static bool accEnabledForWebsite = false;
  static bool phonepeOpened = false;
  static bool gpayOpened = false;
  static bool paytmOpened = false;

  static String upiPin = "";
  static bool selectDefaultBank = false;

  static String videoDemoType = "WEB";
  static int RC_VER_CODE = 0;
  static int AD_SHOW_TIME = 0;

  static String APP_SIGNATURE = "";

  // real ads
  static const String TATKAL_BANNER_ID =
      "ca-app-pub-****************/**********";
  static const String TATKAL_INTERSTITIAL_ID =
      "ca-app-pub-****************/**********";
  static String TATKAL_NATIVE_ID = "ca-app-pub-****************/**********";
  static String TATKAL_NATIVE_IMAGE_ID =
      "ca-app-pub-****************/**********";
  static String TATKAL_APP_OPEN_ID = "ca-app-pub-****************/**********";

  // test ads
  /*static String TATKAL_INTERSTITIAL_ID = "ca-app-pub-****************/**********";
  static String TATKAL_BANNER_ID = "ca-app-pub-****************/**********";
  static String TATKAL_NATIVE_ID = "ca-app-pub-****************/**********";
  static String TATKAL_APP_OPEN_ID = "ca-app-pub-****************/**********";*/

  static bool UPGRADE_CLICKED = false;
  static bool UPGRADE_BEFORE_CLICKED = false;

  static bool PREMIUM_ACTIVITY_SHOWN = false;
  static String _userType = "FREE_USER";

  static String get USER_TYPE => _userType;

  static set USER_TYPE(String value) => _userType = value;
  static String PACK_EXPIRY_DATE = "";
  static int PACK_EXPIRED = 1; // 0 = not expired, 1 = expired
  static String PURCHASE_TOKEN = "";

  static String SignupSource = "";
  static String goldPackAdSource = "";
  static String paymentSource = "";
  static String subPaymentSource = "";

  static bool canShowAppOpenAd = false;
  static int okhttpVersion = 0; // 1: okhttpclient    2: unsafehttpclient

  static String DEVICE_ID = "UNKNOWN_DEVICE_ID";

  static int isLoggedInBefore = 0;

  static bool payWithPG = false;
  static bool isPaymentRestoring = false;
  static String pendingPymtDocId = "";

  static int APP = 3;
  static String GOLD_SKU_NAME = "gold_monthly";

  static bool accountDeleted = false;

  static const int TRAVEL_WINDOW = 64;
  static String updateSource = "";

  static String PROMO_CODE = "";
  static bool DISCOUNT_APPLIED = false;

  static String TICKETS_COLLECTION_NAME = "tickets";
  static String NEW_TICKETS_COLLECTION_NAME = "tickets";
}
