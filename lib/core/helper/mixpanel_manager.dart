import 'package:mixpanel_flutter/mixpanel_flutter.dart';

class MixpanelManager {
  static final MixpanelManager _instance = MixpanelManager._internal();
  late Mixpanel _mixpanel;
  bool _initialized = false;

  bool askAiViewLogged = false;

  factory MixpanelManager() {
    return _instance;
  }

  MixpanelManager._internal();

  Future<void> init() async {
    if (_initialized) return;

    try {
      _mixpanel = await Mixpanel.init(
        "90e5df6cbcdf26ceb51933100e8bab3b",
        optOutTrackingDefault: false,
        trackAutomaticEvents: true,
      );
      _initialized = true;
    } catch (e) {
      print('Failed to initialize Mixpanel: $e');
    }
  }

  Mixpanel get mixpanel => _mixpanel;

  void trackEventOnce(String eventName) {
    if (!_initialized) {
      print('MixpanelManager not initialized');
      return;
    }

    if (!askAiViewLogged && eventName == "ask_view") {
      askAiViewLogged = true;
      _mixpanel.track(eventName);
    }
  }

  void track(String event, [Map<String, dynamic>? props]) {
    if (!_initialized) {
      print('MixpanelManager not initialized');
      return;
    }

    try {
      _mixpanel.track(event, properties: props);
    } catch (e) {
      print('Failed to track event $event: $e');
    }
  }

  void identify(String userId) {
    if (!_initialized) {
      print('MixpanelManager not initialized');
      return;
    }

    try {
      _mixpanel.identify(userId);
    } catch (e) {
      print('Failed to identify user $userId: $e');
    }
  }

  void reset() {
    if (!_initialized) {
      print('MixpanelManager not initialized');
      return;
    }

    try {
      _mixpanel.reset();
      askAiViewLogged = false; // Reset our internal flag too
    } catch (e) {
      print('Failed to reset Mixpanel: $e');
    }
  }

  void registerSuperProperties(Map<String, dynamic> props) {
    if (!_initialized) {
      print('MixpanelManager not initialized');
      return;
    }

    try {
      _mixpanel.registerSuperProperties(props);
    } catch (e) {
      print('Failed to register super properties: $e');
    }
  }

  void registerSuperPropertiesOnce(Map<String, dynamic> props) {
    if (!_initialized) {
      print('MixpanelManager not initialized');
      return;
    }

    try {
      _mixpanel.registerSuperPropertiesOnce(props);
    } catch (e) {
      print('Failed to register super properties once: $e');
    }
  }

  dynamic getPeople() {
    if (!_initialized) {
      print('MixpanelManager not initialized');
      return null;
    }

    try {
      return _mixpanel.getPeople();
    } catch (e) {
      print('Failed to get people: $e');
      return null;
    }
  }

  // Additional helper methods
  void trackWithoutProperties(String event) {
    track(event, null);
  }

  void incrementPeopleProperty(String property, [double value = 1.0]) {
    if (!_initialized) {
      print('MixpanelManager not initialized');
      return;
    }

    try {
      _mixpanel.getPeople().increment(property, value);
    } catch (e) {
      print('Failed to increment people property $property: $e');
    }
  }

  void setPeopleProperty(String property, dynamic value) {
    if (!_initialized) {
      print('MixpanelManager not initialized');
      return;
    }

    try {
      _mixpanel.getPeople().set(property, value);
    } catch (e) {
      print('Failed to set people property $property: $e');
    }
  }

  bool get isInitialized => _initialized;
}
