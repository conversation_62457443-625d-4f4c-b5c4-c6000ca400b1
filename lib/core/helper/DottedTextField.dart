import 'package:flutter/material.dart';

class DottedTextField extends StatelessWidget {
  final TextEditingController controller;

  const DottedTextField({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: controller,
          style: const TextStyle(color: Colors.white),
          decoration: const InputDecoration(
            hintText: "Enter Form Name",
            hintStyle: TextStyle(color: Colors.white54),
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(horizontal: 10),
          ),
        ),
        // Dotted line
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: LayoutBuilder(
            builder: (context, constraints) {
              const dashWidth = 4.0;
              const dashSpace = 3.0;
              final boxWidth = constraints.constrainWidth();
              final dashCount = (boxWidth / (dashWidth + dashSpace)).floor();

              return Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: List.generate(dashCount, (_) {
                  return Container(
                    width: dashWidth,
                    height: 1,
                    color: Colors.white54,
                    margin: const EdgeInsets.only(right: dashSpace),
                  );
                }),
              );
            },
          ),
        ),
      ],
    );
  }
}
