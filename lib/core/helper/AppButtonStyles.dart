import 'package:flutter/material.dart';

import '../utils/AppColors.dart';

class AppButtonStyles {
  static ButtonStyle primaryButton = ButtonStyle(
    backgroundColor: MaterialStateProperty.resolveWith<Color>((states) {
      if (states.contains(MaterialState.disabled)) {
        return AppColors.primaryButtonDisabled;
      } else if (states.contains(MaterialState.pressed)) {
        return AppColors.primaryButtonPressed;
      }
      return AppColors.primaryButtonNormal;
    }),
    foregroundColor: MaterialStateProperty.all<Color>(Colors.white),
    textStyle: MaterialStateProperty.all(
      const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
    ),
    minimumSize: MaterialStateProperty.all(const Size(200, 40)),
  );
}
