class Passenger {
  String name;
  int age;
  String gender;
  String berthPref;
  String nationality;
  String idType;
  String idNo;
  String meal;
  bool bedRoll;
  bool optBerth;
  
  Passenger({
    this.name = '',
    this.age = 0,
    this.gender = '',
    this.berthPref = '',
    this.nationality = 'IN',
    this.idType = '',
    this.idNo = '',
    this.meal = 'V',
    this.bedRoll = false,
    this.optBerth = false,
  });
  
  Map<String, dynamic> toJson() => {
    'name': name,
    'age': age,
    'gender': gender,
    'berthPref': berthPref,
    'nationality': nationality,
    'idType': idType,
    'idNo': idNo,
    'meal': meal,
    'bedRoll': bedRoll,
    'optBerth': optBerth,
  };
  
  factory Passenger.fromJson(Map<String, dynamic> json) => Passenger(
    name: json['name'] ?? '',
    age: json['age'] ?? 0,
    gender: json['gender'] ?? '',
    berthPref: json['berthPref'] ?? '',
    nationality: json['nationality'] ?? 'IN',
    idType: json['idType'] ?? '',
    idNo: json['idNo'] ?? '',
    meal: json['meal'] ?? 'V',
    bedRoll: json['bedRoll'] ?? false,
    optBerth: json['optBerth'] ?? false,
  );
}