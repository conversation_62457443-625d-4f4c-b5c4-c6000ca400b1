import 'package:flutter/foundation.dart';

class BookingState extends ChangeNotifier {
  // Core booking data
  String username = '';
  String password = '';
  String fromStation = '';
  String toStation = '';
  String journeyDate = '';
  String quota = '';
  String trainNo = '';
  String travelClass = '';
  String boardingStation = '';
  
  // Payment data
  String paymentChoice = '';
  String bankChoice = '';
  bool paymentAutofill = false;
  
  // Automation flags
  int automationStep = 0;
  bool isLoginClicked = false;
  bool isJourneyFilled = false;
  bool isTrainSelected = false;
  
  // UI state
  String availability = '';
  String currentTime = '';
  
  void updateJourneyDetails({
    required String from,
    required String to,
    required String date,
  }) {
    fromStation = from;
    toStation = to;
    journeyDate = date;
    notifyListeners();
  }
  
  void updateAutomationStep(int step) {
    automationStep = step;
    notifyListeners();
  }
  
  void updateAvailability(String avl) {
    availability = avl;
    notifyListeners();
  }
}