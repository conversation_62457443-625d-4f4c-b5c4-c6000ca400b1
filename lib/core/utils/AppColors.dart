import 'package:flutter/material.dart';

class AppColors {
  static const Color primaryColor = Color(0xFF000000); // #000000
  static const Color secondaryColor = Color(0xFF9C5DF7); // #9C5DF7
  static const Color dialogColor = Color(0xFF1E1E29);

  // Button state colors (from drawable XMLs)
  static Color get primaryButtonNormal {
    return Color(0xFF3B3B3B);
  } // example from button_normal

  static const Color primaryButtonPressed = Color(
    0xFF2A2A2A,
  ); // example from button_pressed
  static const Color primaryButtonDisabled = Color(
    0xFF555555,
  ); // example from button_disabled
}
