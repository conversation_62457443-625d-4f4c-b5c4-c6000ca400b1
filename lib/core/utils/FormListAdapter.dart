import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../pojo/FormDetail.dart';
import '../../database/MainDB.dart';
import '../../database/PassengerDB.dart';
import '../../database/AddressDB.dart';
import '../../database/ChildDB.dart';
import '../../database/GSTDB.dart';
import '../../database/InsuranceDB.dart';
import '../../database/PodDB.dart';
import '../../database/RCPaymentDB.dart';
import '../../database/SecurityQuestionsDB.dart';

class FormListAdapter extends StatefulWidget {
  final List<FormDetail> forms;
  final Function(FormDetail) onItemClick;
  final Function(FormDetail) onDeleteItem;
  final Function(FormDetail, String) onEditItem;
  final Function(FormDetail, String) onCopyForm;
  final Function(String) isDuplicateForm;
  final Function(String) getDuplicateFormName;

  const FormListAdapter({
    Key? key,
    required this.forms,
    required this.onItemClick,
    required this.onDeleteItem,
    required this.onEditItem,
    required this.onCopyForm,
    required this.isDuplicateForm,
    required this.getDuplicateFormName,
  }) : super(key: key);

  @override
  State<FormListAdapter> createState() => _FormListAdapterState();
}

class _FormListAdapterState extends State<FormListAdapter> {
  static bool isFormNameEditing = false;
  Map<int, bool> editingStates = {};

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: widget.forms.length,
      itemBuilder: (context, index) => _buildFormItem(index),
    );
  }

  Widget _buildFormItem(int index) {
    final FormDetail fd = widget.forms[index];
    final bool isEditing = editingStates[index] ?? false;
    final TextEditingController formNameController = TextEditingController(
      text: fd.getFormName(),
    );

    return Card(
      color: Colors.grey[850],
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
      child: InkWell(
        onTap: () => _onItemClick(fd),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (!isEditing) ...[
                          Text(
                            fd.getFormName(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          if (fd.getFormContent() != null &&
                              fd.getFormContent().isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Text(
                                fd.getFormContent(),
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                        ] else ...[
                          TextField(
                            controller: formNameController,
                            autofocus: true,
                            style: const TextStyle(color: Colors.white),
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                            onSubmitted: (_) => _onDoneEditing(
                              index,
                              fd,
                              formNameController.text,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (!isEditing) ...[
                        IconButton(
                          icon: const Icon(Icons.edit, color: Colors.white54),
                          onPressed: () => _onEditPressed(index),
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: () => _onDeletePressed(fd),
                        ),
                        IconButton(
                          icon: const Icon(Icons.copy, color: Colors.orange),
                          onPressed: () => _onCopyPressed(fd),
                        ),
                      ] else ...[
                        IconButton(
                          icon: const Icon(Icons.done, color: Colors.green),
                          onPressed: () => _onDoneEditing(
                            index,
                            fd,
                            formNameController.text,
                          ),
                        ),
                      ],
                      if (fd.getPassword() != null &&
                          fd.getPassword().isNotEmpty)
                        const Padding(
                          padding: EdgeInsets.only(left: 8),
                          child: Icon(
                            Icons.lock,
                            color: Colors.amber,
                            size: 20,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onItemClick(FormDetail fd) {
    if (!isFormNameEditing) {
      widget.onItemClick(fd);
    }
  }

  void _onEditPressed(int index) {
    setState(() {
      editingStates[index] = true;
      isFormNameEditing = true;
    });
  }

  void _onDoneEditing(int index, FormDetail fd, String newName) {
    _hideKeyboard();

    final trimmedName = newName.trim();

    if (trimmedName != fd.getFormName()) {
      if (widget.isDuplicateForm(trimmedName)) {
        _showSnackBar('Form "$trimmedName" already exists!');
        return;
      } else if (trimmedName.isNotEmpty) {
        widget.onEditItem(fd, trimmedName);
      } else {
        _showSnackBar('Form name cannot be blank!');
        return;
      }
    }

    setState(() {
      editingStates[index] = false;
      isFormNameEditing = false;
    });
  }

  void _onDeletePressed(FormDetail fd) {
    _showDeleteConfirmationDialog(fd);
  }

  void _onCopyPressed(FormDetail fd) {
    _showCopyConfirmationDialog(fd);
  }

  void _showDeleteConfirmationDialog(FormDetail fd) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF121212),
          title: const Text(
            'Delete Form',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Do you want to delete this form?',
            style: const TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white54),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _deleteFormFromAllDatabases(fd);
                widget.onDeleteItem(fd);
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text(
                'Delete',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showCopyConfirmationDialog(FormDetail fd) {
    final String duplicateName = widget.getDuplicateFormName(fd.getFormName());

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF121212),
          title: const Text(
            'Duplicate Form',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Create duplicate form of "${fd.getFormName()}"?',
            style: const TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('No', style: TextStyle(color: Colors.white54)),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _duplicateFormInAllDatabases(fd, duplicateName);
                widget.onCopyForm(fd, duplicateName);
              },
              child: const Text('Yes', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteFormFromAllDatabases(FormDetail fd) async {
    try {
      // Delete from MainDB
      final mainDb = await MainDB.instance.database;
      await mainDb.delete(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );

      // Delete from PassengerDB
      final passengerDb = await PassengerDB.instance.database;
      await passengerDb.delete(
        PassengerDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );

      // Delete from ChildDB
      final childDb = await ChildDB.instance.database;
      await childDb.delete(
        ChildDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );

      // Delete from AddressDB
      final addressDb = await AddressDB.instance.database;
      await addressDb.delete(
        AddressDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );

      // Delete from GSTDB
      final gstDb = await GSTDB.instance.database;
      await gstDb.delete(
        GSTDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );

      // Delete from InsuranceDB
      final insuranceDb = await InsuranceDB.instance.database;
      await insuranceDb.delete(
        InsuranceDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );

      // Delete from PodDB
      final podDb = await PodDB.instance.database;
      await podDb.delete(
        PodDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );

      // Delete from RCPaymentDB
      final rcPaymentDb = await RCPaymentDB.instance.database;
      await rcPaymentDb.delete(
        RCPaymentDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );

      // Delete from SecurityQuestionsDB
      final securityDb = await SecurityQuestionsDB.instance.database;
      await securityDb.delete(
        SecurityQuestionsDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );
    } catch (e) {
      _showSnackBar('Error deleting form: $e');
    }
  }

  Future<void> _duplicateFormInAllDatabases(
    FormDetail fd,
    String duplicateName,
  ) async {
    try {
      // Duplicate in MainDB
      final mainDb = await MainDB.instance.database;
      final mainRows = await mainDb.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );
      for (final row in mainRows) {
        final newRow = Map<String, Object?>.from(row);
        newRow['FORM_NAME'] = duplicateName;
        await mainDb.insert(MainDB.tableName, newRow);
      }

      // Duplicate in PassengerDB
      final passengerDb = await PassengerDB.instance.database;
      final passengerRows = await passengerDb.query(
        PassengerDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );
      for (final row in passengerRows) {
        final newRow = Map<String, Object?>.from(row);
        newRow['FORM_NAME'] = duplicateName;
        await passengerDb.insert(PassengerDB.tableName, newRow);
      }

      // Duplicate in ChildDB
      final childDb = await ChildDB.instance.database;
      final childRows = await childDb.query(
        ChildDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );
      for (final row in childRows) {
        final newRow = Map<String, Object?>.from(row);
        newRow['FORM_NAME'] = duplicateName;
        await childDb.insert(ChildDB.tableName, newRow);
      }

      // Duplicate in AddressDB
      final addressDb = await AddressDB.instance.database;
      final addressRows = await addressDb.query(
        AddressDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );
      for (final row in addressRows) {
        final newRow = Map<String, Object?>.from(row);
        newRow['FORM_NAME'] = duplicateName;
        await addressDb.insert(AddressDB.tableName, newRow);
      }

      // Duplicate in GSTDB
      final gstDb = await GSTDB.instance.database;
      final gstRows = await gstDb.query(
        GSTDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );
      for (final row in gstRows) {
        final newRow = Map<String, Object?>.from(row);
        newRow['FORM_NAME'] = duplicateName;
        await gstDb.insert(GSTDB.tableName, newRow);
      }

      // Duplicate in InsuranceDB
      final insuranceDb = await InsuranceDB.instance.database;
      final insuranceRows = await insuranceDb.query(
        InsuranceDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );
      for (final row in insuranceRows) {
        final newRow = Map<String, Object?>.from(row);
        newRow['FORM_NAME'] = duplicateName;
        await insuranceDb.insert(InsuranceDB.tableName, newRow);
      }

      // Duplicate in PodDB
      final podDb = await PodDB.instance.database;
      final podRows = await podDb.query(
        PodDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );
      for (final row in podRows) {
        final newRow = Map<String, Object?>.from(row);
        newRow['FORM_NAME'] = duplicateName;
        await podDb.insert(PodDB.tableName, newRow);
      }

      // Duplicate in RCPaymentDB
      final rcPaymentDb = await RCPaymentDB.instance.database;
      final rcPaymentRows = await rcPaymentDb.query(
        RCPaymentDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );
      for (final row in rcPaymentRows) {
        final newRow = Map<String, Object?>.from(row);
        newRow['FORM_NAME'] = duplicateName;
        await rcPaymentDb.insert(RCPaymentDB.tableName, newRow);
      }

      // Duplicate in SecurityQuestionsDB
      final securityDb = await SecurityQuestionsDB.instance.database;
      final securityRows = await securityDb.query(
        SecurityQuestionsDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.getFormName()],
      );
      for (final row in securityRows) {
        final newRow = Map<String, Object?>.from(row);
        newRow['FORM_NAME'] = duplicateName;
        await securityDb.insert(SecurityQuestionsDB.tableName, newRow);
      }
    } catch (e) {
      _showSnackBar('Error duplicating form: $e');
    }
  }

  void _hideKeyboard() {
    FocusScope.of(context).unfocus();
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
