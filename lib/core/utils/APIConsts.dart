class APIConsts {
  // ERROR CODES
  static const int ccERR_OK = 0; // everything went OK
  static const int ccERR_GENERAL = -1; // general internal error
  static const int ccERR_STATUS = -2; // status is not correct
  static const int ccERR_NET_ERROR = -3; // network data transfer error
  static const int ccERR_TEXT_SIZE = -4; // text is not of an appropriate size
  static const int ccERR_OVERLOAD = -5; // server's overloaded
  static const int ccERR_BALANCE = -6; // not enough funds to complete the request
  static const int ccERR_TIMEOUT = -7; // request timed out
  static const int ccERR_UNKNOWN = -200; // unknown error

  // picture processing TIMEOUTS
  static const int ptoDEFAULT = 0; // default timeout, server-specific
  static const int ptoLONG = 1; // long timeout for picture, server-specific
  static const int pto30SEC = 2; // 30 seconds timeout for picture
  static const int pto60SEC = 3; // 60 seconds timeout for picture
  static const int pto90SEC = 4; // 90 seconds timeout for picture

  // picture processing TYPES
  static const int ptUNSPECIFIED = 0; // picture type unspecified

  static String HOST = ""; // YOUR ADDRESS
  static int PORT = 0; // YOUR PORT
  static String USERNAME = "";
  static String PASSWORD = "";

  static int PRICE_CP = 49;
  static int PRICE_SP = 99;
  static int PRICE_PP = 149;
  static int PRICE_GP_REGULAR = 199;
  static int PRICE_DP_YEARLY_OFFER = 299;
  static int PRICE_DP_YEARLY = 399;

  static String PRICE_CP_STR = "";
  static String PRICE_SP_STR = "";
  static String PRICE_PP_STR = "";
  static String PRICE_GP_STR = "";
  static String PRICE_DP_OFFER_STR = "";
  static String PRICE_DP_REG_STR = "";

  static int GOOGLE_IAP_CODE = 6;
  static int GOOGLE_IMG_FILE_VER = 5;
  static int SUBS_IMG_FILE_VER = 4;
  static String UPI_TRIAL_OPTED = "Y";

  // flag for duplicate purchaseUpdated call IAP
  static int updateTimeDifference = 0;
  static bool goldPackAlreadyPurchased = false;
  static bool allowCaptchaAutofill = true;
  static String CAPTCHA_TRIAL_OPTED = "Y";
}
