import 'package:get/get.dart';
import 'package:quick_tatkal_v2/screens/EditProfile.dart';
import 'package:quick_tatkal_v2/screens/splash_screen.dart';
import '../screens/ExitScreen.dart';
import '../screens/FormActivity2.dart';
import '../screens/FreeTickets.dart';
import '../screens/OTPValidation.dart';
import '../screens/PNRResult.dart';
import '../screens/PNRScreen.dart';
import '../screens/PremiumScreen.dart';
import '../screens/SignInSocialScreen.dart';
import '../screens/SignUpScreen.dart';
import '../screens/TabActivity2.dart';
import '../screens/QuickTatkalScreen.dart';
import '../screens/profile_screen.dart';
import 'package:quick_tatkal_v2/screens/booking_screen.dart';
import '../screens/MainActivity.dart';
// QuickTatkalScreen is the main dashboard - mirrors Java Dashboard.java

class AppRoutes {
  // Main navigation routes - mirrors Java flow
  static const splash = '/splash';
  static const home = '/home'; // Dashboard - mirrors Java Dashboard.java
  static const dashboard = '/dashboard'; // Alias for home
  static const formActivity2 =
      '/forms'; // Form list - mirrors Java FormActivity2.java
  static const tabActivity2 =
      '/form-details'; // Form editing - mirrors Java TabActivity2.java

  // Authentication routes
  static const signInSocialScreen = '/sign-in-social';
  static const signUp = "/sign-up";
  static const otp = "/otp";

  // Feature routes
  static const pnrScreen = '/pnr-screen';
  static const premiumScreen = '/premium-screen';
  static const exitScreen = '/exit-screen';
  static const quickBooking = '/quick-booking';
  static const booking = '/booking';

  // Profile routes
  static const profile = '/profile';
  static const editProfile = '/editProfile';

  // Misc routes
  static const freeTickets = '/free-tickets';
  static const pnrResult = '/pnrResult';
  static const PnrResultScreen = '/PnrResultScreen';
  static const logout = "/logout";

  static final routes = [
    // Splash - entry point like Java SplashActivity
    GetPage(name: splash, page: () => SplashScreen()),

    // Home/Dashboard - mirrors Java Dashboard.java
    GetPage(name: home, page: () => QuickTatkalScreen()),
    GetPage(name: dashboard, page: () => QuickTatkalScreen()),

    // Form screens - mirrors Java FormActivity2 -> TabActivity2 flow
    GetPage(name: formActivity2, page: () => FormScreen()),
    GetPage(
      name: tabActivity2,
      page: () {
        final args = Get.arguments as Map<String, dynamic>? ?? {};
        final formName = args['formName'] as String? ?? 'Untitled';
        final lang = args['lang'] as String? ?? 'en';
        return TabActivity2(formName: formName, lang: lang);
      },
    ),

    // Authentication screens
    GetPage(name: signInSocialScreen, page: () => SignInSocialScreen()),
    GetPage(name: signUp, page: () => const SignUpScreen()),
    GetPage(
      name: otp,
      page: () {
        final args = Get.arguments as Map<String, String>;
        final mobile = args['mobile'] ?? '';
        final display = args['display'] ?? '';
        return OTPValidationScreen(phone: mobile, displayPhone: display);
      },
    ),

    // Feature screens
    GetPage(name: pnrScreen, page: () => PNRScreen()),
    GetPage(name: premiumScreen, page: () => const PremiumScreen()),
    GetPage(name: exitScreen, page: () => const ExitScreen()),
    GetPage(name: quickBooking, page: () => const FormScreen()),

    // Booking engine
    GetPage(
      name: booking,
      page: () {
        final args = Get.arguments as Map<String, dynamic>? ?? {};
        final url = args['url'] as String?;
        // If a direct URL is provided (manual booking), open lightweight BookingScreen.
        // Otherwise, open MainActivity (automation engine) with form context.
        if ((url != null && url.isNotEmpty) &&
            ((args['formName'] as String?) == null ||
                (args['formName'] as String?)!.isEmpty)) {
          return BookingScreen(url: url);
        }

        final formName = args['formName'] as String? ?? '';
        final language =
            args['language'] as String? ?? args['lang'] as String? ?? 'ENG';
        final autoLogin =
            args['autoLoginEnabled'] == true || args['autoLogin'] == true;
        return MainActivity(
          formName: formName,
          language: language,
          autoLogin: autoLogin,
        );
      },
    ),

    // Profile screens
    GetPage(name: profile, page: () => const ProfileScreen()),
    GetPage(
      name: editProfile,
      page: () =>
          EditProfile(initialName: '', initialEmail: '', initialMobile: ''),
    ),

    // Misc screens
    GetPage(name: freeTickets, page: () => const FreeTicketsScreen()),
    GetPage(name: PnrResultScreen, page: () => const Pnrresult()),
    GetPage(name: logout, page: () => const SignUpScreen()),

    // Legacy/fallback routes
    GetPage(name: '/splashscreen', page: () => SplashScreen()),
    GetPage(name: '/', page: () => QuickTatkalScreen()), // Default to dashboard
    GetPage(name: '/FormActivity2', page: () => FormScreen()),
  ];
}
