import 'dart:typed_data';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'package:quick_tatkal_v2/core/utils/AppColors.dart';
import 'package:quick_tatkal_v2/screens/OTPValidation.dart';
import 'package:quick_tatkal_v2/screens/profile_screen.dart';
import 'package:quick_tatkal_v2/screens/splash_screen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../core/AppConstants.dart';
import '../core/Consts.dart';
import '../core/helper/mixpanel_manager.dart';
import '../server/FirestoreFunctions.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final TextEditingController _phoneController = TextEditingController();
  String _countryCode = "+91";

  // Separate loading states for different operations
  bool _isMobileLoading = false;
  bool _isGoogleLoading = false;
  bool _isFacebookLoading = false;
  bool _mobileNumberHintShown = false;

  // Profile picture
  Uint8List? _profilePicture;
  String? _profilePicUrl;

  // Focus node for phone number hint
  final FocusNode _phoneFocusNode = FocusNode();

  final GoogleSignIn _googleSignIn = GoogleSignIn.instance;
  bool _isGoogleSignInInitialized = false;

  // Helper getter to check if any loading is active
  bool get _isAnyLoading =>
      _isMobileLoading || _isGoogleLoading || _isFacebookLoading;

  String _getLoadingMessage() {
    if (_isMobileLoading) {
      return "Signing in...";
    } else if (_isGoogleLoading) {
      return "Signing in with Google...";
    } else if (_isFacebookLoading) {
      return "Signing in with Facebook...";
    } else {
      return "Please wait...";
    }
  }

  @override
  void initState() {
    super.initState();
    _initializeScreen();
    _phoneFocusNode.addListener(_onPhoneFocusChange);
    _initializeGoogleSignIn();
  }

  @override
  void dispose() {
    _phoneFocusNode.removeListener(_onPhoneFocusChange);
    _phoneFocusNode.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _initializeScreen() {
    // Log analytics event
    FirebaseAnalytics.instance.logEvent(
      name: 'login_started',
      parameters: {'value': 'true'},
    );

    // Track signup source - you can set this based on your app flow
    MixpanelManager().track('Sign Up opened', {'Source': 'DEFAULT'});
  }

  void _onPhoneFocusChange() {
    if (_phoneFocusNode.hasFocus && !_mobileNumberHintShown) {
      _mobileNumberHintShown = true;
      _requestPhoneNumberHint();
    }
  }

  void _requestPhoneNumberHint() {
    // On Android, you can implement phone number hint using platform channels
    // For now, we'll skip this implementation as it requires platform-specific code
    // This would need to be implemented via a plugin or platform channel
    print("Phone number hint requested");
  }

  void _setMobileLoading(bool loading) {
    if (mounted) {
      setState(() {
        _isMobileLoading = loading;
      });
    }
  }

  void _setGoogleLoading(bool loading) {
    if (mounted) {
      setState(() {
        _isGoogleLoading = loading;
      });
    }
  }

  void _setFacebookLoading(bool loading) {
    if (mounted) {
      setState(() {
        _isFacebookLoading = loading;
      });
    }
  }

  void _trackEvent(String event, Map<String, Object> parameters) {
    try {
      FirebaseAnalytics.instance.logEvent(
        name: event.toLowerCase().replaceAll(' ', '_'),
        parameters: parameters,
      );
      MixpanelManager().track(event, parameters);
    } catch (e) {
      print('Analytics error: $e');
    }
  }

  void _logError(String medium, String errorMsg) {
    print('Error - $medium: $errorMsg');
    // You can implement server-side error logging here
  }

  void _onContinue() async {
    MixpanelManager().track('Mobile login clicked', {});
    FirebaseAnalytics.instance.logEvent(
      name: 'signup_mobile',
      parameters: {'value': 'true'},
    );

    final rawPhone = _phoneController.text.trim();

    if (rawPhone.length < 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid mobile number')),
      );
      return;
    }

    _hideKeyboard();
    final phone = _countryCode + rawPhone;
    final displayPhone = "$_countryCode $rawPhone";

    _setMobileLoading(true);

    try {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) =>
              OTPValidationScreen(phone: phone, displayPhone: displayPhone),
        ),
      );

      if (result == true) {
        // Set user state
        AppConstants.mobileNo = phone;
        AppConstants.email = "NA";
        AppConstants.primaryEmail = "NA";
        AppConstants.custName = '';
        AppConstants.isLoggedIn = true;
        AppConstants.loginMethod = "MOBILE";

        // Save to local storage
        await _saveUserData();

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (_) => const ProfileScreen()),
        );
      }
    } finally {
      _setMobileLoading(false);
    }
  }

  Future<void> _saveUserData() async {
    await AppConstants.saveUserData();
  }

  void _hideKeyboard() {
    FocusScope.of(context).unfocus();
  }

  Future<void> _initializeGoogleSignIn() async {
    try {
      // Initialize GoogleSignIn instance with required parameters
      await _googleSignIn.initialize(
        serverClientId:
            "835763879870-j9vc6m3lkgemk4dk9a4r2h8ptdhpsioi.apps.googleusercontent.com",
      );
      _isGoogleSignInInitialized = true;
    } catch (e) {
      debugPrint('Failed to initialize Google Sign-In: $e');
    }
  }

  Future<void> _ensureGoogleSignInInitialized() async {
    if (!_isGoogleSignInInitialized) {
      await _initializeGoogleSignIn();
    }
  }

  @override
  Widget build(BuildContext context) {
    final gradientColors = [const Color(0xFFA34BFF), const Color(0xFF6100FF)];

    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title: Quick Tatkal
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'Quick TatKal',

                      textAlign: TextAlign.left,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),

                  Center(
                    child: Image.asset(
                      'assets/images/sign_up_train.jpg',
                      width: double.infinity,
                    ),
                  ),

                  const SizedBox(height: 20),

                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Theme(
                      data: Theme.of(
                        context,
                      ).copyWith(canvasColor: AppColors.primaryColor),
                      child: Stack(
                        children: [
                          Container(
                            margin: const EdgeInsets.only(top: 12),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [Color(0xFF5B09E0), Color(0xFFCC4BFF)],
                              ),
                              borderRadius: BorderRadius.circular(25),
                            ),
                            padding: const EdgeInsets.all(2),
                            child: Container(
                              decoration: BoxDecoration(
                                color: AppColors.primaryColor,
                                borderRadius: BorderRadius.circular(25),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              child: Row(
                                children: [
                                  CountryCodePicker(
                                    onChanged: (code) {
                                      setState(() {
                                        _countryCode = code.dialCode ?? "+91";
                                      });
                                    },
                                    initialSelection: 'IN',
                                    favorite: ['+91', 'IN'],
                                    textStyle: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                    ),
                                    showFlag: true,
                                    showFlagMain: true,
                                    flagWidth: 24,
                                    showDropDownButton: true,
                                    dialogBackgroundColor: Colors.black,
                                    dialogTextStyle: const TextStyle(
                                      color: Colors.white,
                                    ),
                                    searchStyle: const TextStyle(
                                      color: Colors.white,
                                    ),
                                    searchDecoration: const InputDecoration(
                                      hintText: "Search country",
                                      hintStyle: TextStyle(color: Colors.grey),
                                      border: UnderlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Colors.grey,
                                        ),
                                      ),
                                      focusedBorder: UnderlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Colors.purpleAccent,
                                        ),
                                      ),
                                    ),
                                  ),
                                  const Text(
                                    "| ",
                                    style: TextStyle(
                                      color: Colors.white54,
                                      fontSize: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: TextField(
                                      controller: _phoneController,
                                      focusNode: _phoneFocusNode,
                                      keyboardType: TextInputType.phone,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                      ),
                                      decoration: const InputDecoration(
                                        hintText: "Mobile No",
                                        hintStyle: TextStyle(
                                          color: Colors.grey,
                                        ),
                                        border: InputBorder.none,
                                      ),
                                      onSubmitted: (_) => _onContinue(),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Positioned(
                            left: 20,
                            top: 0,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                              ),
                              color: Colors.black,
                              child: const Text(
                                "Mobile No",
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Sign In Button
                  GestureDetector(
                    onTap: _isMobileLoading ? null : _onContinue,
                    child: Center(
                      child: Container(
                        width: 250,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(25),
                          gradient: _isMobileLoading
                              ? LinearGradient(
                                  colors: [
                                    Colors.grey.shade600,
                                    Colors.grey.shade700,
                                  ],
                                )
                              : const LinearGradient(
                                  colors: [
                                    Color(0xFFA34BFF),
                                    Color(0xFF6100FF),
                                  ],
                                ),
                        ),
                        child: Center(
                          child: _isMobileLoading
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const SizedBox(
                                      height: 16,
                                      width: 16,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      "Signing in...",
                                      style: GoogleFonts.poppins(
                                        fontSize: 14,
                                        color: Colors.white70,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                )
                              : Text(
                                  "Sign in",
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 25),

                  // OR Divider
                  Center(
                    child: Wrap(
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        Text(
                          "Or Sign in with ",
                          style: GoogleFonts.poppins(
                            color: Colors.grey,
                            fontSize: 14,
                          ),
                        ),
                        GestureDetector(
                          onTap: () => _onGoogleSignIn(),
                          child: Text(
                            "Google",
                            style: GoogleFonts.poppins(
                              color: Colors.lightBlueAccent,
                              fontSize: 14,
                              decoration: TextDecoration.underline,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        Text(
                          " or ",
                          style: GoogleFonts.poppins(
                            color: Colors.grey,
                            fontSize: 14,
                          ),
                        ),
                        GestureDetector(
                          onTap: () => _onFacebookSignIn(),
                          child: Text(
                            "Facebook",
                            style: GoogleFonts.poppins(
                              color: Colors.lightBlueAccent,
                              fontSize: 14,
                              decoration: TextDecoration.underline,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(width: 6),
                        const Icon(
                          Icons.arrow_drop_down,
                          color: Colors.grey,
                          size: 30,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Social Buttons Row (Keep XML style)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 40),
                    child: Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: _onGoogleSignIn,
                            child: Container(
                              height: 45,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Image.asset(
                                'assets/images/google_signin.png',
                                height: 45,
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: GestureDetector(
                            onTap: _onFacebookSignIn,
                            child: Container(
                              height: 45,
                              decoration: BoxDecoration(
                                color: const Color(0xFFF2F2F4), // Facebook blue
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset(
                                    'assets/images/fb.png',
                                    height: 24,
                                    width: 24,
                                    fit: BoxFit.contain,
                                  ),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'Facebook',
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 45),

                  // Continue Without Login
                  Center(
                    child: GestureDetector(
                      onTap: () => _showSignUpLaterDialog(context),
                      child: Text(
                        "Sign up later >",
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // Terms and Privacy
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                          children: [
                            const TextSpan(
                              text:
                                  "By continuing, you confirm that you have read and agree to our ",
                            ),
                            TextSpan(
                              text: "Privacy Policy",
                              style: const TextStyle(
                                color: Colors.lightBlueAccent,
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  _trackEvent('privacy_policy_view', {
                                    'value': 'true',
                                  });
                                  _launchURL(
                                    "https://www.afrestudios.com/index.php/quick-tatkal-privacy-policy/",
                                  );
                                },
                            ),
                            const TextSpan(text: " and "),
                            TextSpan(
                              text: "Terms & Conditions",
                              style: const TextStyle(
                                color: Colors.lightBlueAccent,
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  _trackEvent('tnc_view', {'value': 'true'});
                                  _launchURL(
                                    "https://www.afrestudios.com/index.php/quick-tatkal-train-ticket-terms-conditions/",
                                  );
                                },
                            ),
                            const TextSpan(text: "."),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 40),
                ],
              ),
            ),

            // Loading overlay
            if (_isAnyLoading)
              Container(
                color: Colors.black54,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 3,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _getLoadingMessage(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _onGoogleSignIn() async {
    if (_isGoogleLoading) return;

    MixpanelManager().track('Google login clicked', {});
    FirebaseAnalytics.instance.logEvent(
      name: 'signup_google',
      parameters: {'value': 'true'},
    );

    _hideKeyboard();
    await _firebaseAuthWithGoogle(context);
  }

  void _onFacebookSignIn() async {
    if (_isFacebookLoading) return;

    MixpanelManager().track('FB login clicked', {});
    FirebaseAnalytics.instance.logEvent(
      name: 'signup_fb',
      parameters: {'value': 'true'},
    );

    _hideKeyboard();
    await _facebookAuthWithFacebook(context);
  }

  void _continueWithoutLogin() {
    MixpanelManager().track('Sign up alert later', {'Source': 'DEFAULT'});
    // You can implement the logic to continue without login here
    // For example, navigate to a different screen or set guest mode
    Navigator.pop(context);
  }

  void _launchURL(String url) async {
    if (!await launchUrl(
      Uri.parse(url),
      mode: LaunchMode.externalApplication,
    )) {
      throw 'Could not launch $url';
    }
  }

  Future<void> _firebaseAuthWithGoogle(BuildContext context) async {
    _setGoogleLoading(true);

    try {
      await _ensureGoogleSignInInitialized();

      // Use authenticate method for v7.1.1 - this is equivalent to the Java signInIntent
      final GoogleSignInAccount googleUser = await _googleSignIn.authenticate();

      // In v7.1.1, authentication is synchronous (no await needed)
      final GoogleSignInAuthentication googleAuth = googleUser.authentication;

      if (googleAuth.idToken == null) {
        debugPrint("GoogleAuth returned null idToken.");
        _showErrorSnackBar("Google authentication failed. Try again.");
        return;
      }

      // Create Firebase credential with idToken and accessToken (similar to Java implementation)
      final authorization = await googleUser.authorizationClient
          .authorizationForScopes(['email']);

      final OAuthCredential credential = GoogleAuthProvider.credential(
        idToken: googleAuth.idToken,
        accessToken: authorization?.accessToken,
      );

      // Sign in with Firebase (similar to Java firebaseAuthWithGoogle)
      final UserCredential userCredential = await FirebaseAuth.instance
          .signInWithCredential(credential);
      final User? user = userCredential.user;

      if (user != null) {
        _trackEvent('Google signin success', {
          'user_email': user.email ?? '',
          'user_id': user.uid,
        });
        FirebaseAnalytics.instance.logEvent(
          name: 'signin_google_success',
          parameters: {'user_email': user.email ?? '', 'user_id': user.uid},
        );

        // Set user data (similar to Java implementation)
        AppConstants.isLoggedIn = true;
        AppConstants.custName = user.displayName ?? 'Guest';
        AppConstants.email = user.email ?? 'NA';
        AppConstants.primaryEmail = user.email ?? 'NA';
        AppConstants.mobileNo = user.phoneNumber ?? 'NA';
        AppConstants.loginMethod = "GOOGLE";
        Consts.loginMethod = "GOOGLE";

        // Update SplashScreenState for consistency (similar to Java SplashActivity)
        SplashScreenState.CUST_NAME = user.displayName ?? 'Guest';
        SplashScreenState.EMAIL = user.email ?? 'NA';
        SplashScreenState.PRIMARY_EMAIL = user.email ?? 'NA';
        SplashScreenState.MOBILE_NO = user.phoneNumber ?? 'NA';

        // Download and set profile picture (similar to Java SetImageTask)
        if (user.photoURL != null) {
          await _downloadProfilePicture(user.photoURL!);
        }

        await _saveUserData();

        // Use FirestoreFunctions to get proper TID (similar to Java implementation)
        FirestoreFunctions(context).getTicketsNew("GOOGLE");

        debugPrint("✅ Google Login success: ${user.email}");
        Get.offAll(() => const ProfileScreen());
      } else {
        debugPrint("Firebase returned null user.");
        _showErrorSnackBar("Sign-in failed. Try again.");
      }
    } on GoogleSignInException catch (e) {
      debugPrint("GoogleSignInException: ${e.code} - ${e.description}");
      _logError('GOOGLE', '${e.code}: ${e.description}');

      String errorMessage;
      switch (e.code) {
        case GoogleSignInExceptionCode.canceled:
          // User cancelled - don't show error message (similar to Java onCancel)
          debugPrint("Google Sign-In cancelled by user");
          return;
        case GoogleSignInExceptionCode.interrupted:
          errorMessage = "Google Sign-In was interrupted. Please try again.";
          break;
        case GoogleSignInExceptionCode.clientConfigurationError:
          errorMessage = "Configuration error. Please contact support.";
          break;
        case GoogleSignInExceptionCode.providerConfigurationError:
          errorMessage =
              "Google Sign-In is currently unavailable. Please try again later.";
          break;
        case GoogleSignInExceptionCode.uiUnavailable:
          errorMessage =
              "Google Sign-In is currently unavailable. Please try again later.";
          break;
        case GoogleSignInExceptionCode.userMismatch:
          errorMessage =
              "There was an issue with your account. Please sign out and try again.";
          break;
        case GoogleSignInExceptionCode.unknownError:
        default:
          errorMessage =
              "Google Sign-In error: ${e.description ?? 'Unknown error'}";
          break;
      }
      _showErrorSnackBar(errorMessage);
    } on PlatformException catch (e) {
      debugPrint("PlatformException: ${e.code} - ${e.message}");
      _logError('GOOGLE', '${e.code}: ${e.message}');

      String errorMessage;
      switch (e.code) {
        case 'sign_in_canceled':
        case 'cancelled':
        case 'canceled':
          // User cancelled - don't show error message
          debugPrint("Google Sign-In cancelled by user");
          return;
        case 'sign_in_failed':
        case 'sign_in_failure':
          errorMessage = "Google Sign-In failed. Please try again.";
          break;
        case 'network_error':
          errorMessage =
              "Network error. Please check your connection and try again.";
          break;
        case 'sign_in_required':
          errorMessage = "Please try signing in again.";
          break;
        default:
          // Check if it's a reauth failure (error code 16)
          if (e.message?.contains('[16]') == true ||
              e.message?.contains('Account reauth failed') == true) {
            debugPrint(
              "Google Sign-In reauth failed - treating as cancellation",
            );
            return;
          }
          errorMessage =
              "Google Sign-In error: ${e.message ?? 'Unknown error'}";
          break;
      }
      _showErrorSnackBar(errorMessage);
    } on FirebaseAuthException catch (e) {
      debugPrint("FirebaseAuthException: ${e.code} - ${e.message}");
      _logError('GOOGLE', '${e.code}: ${e.message}');

      String errorMessage;
      switch (e.code) {
        case 'account-exists-with-different-credential':
          errorMessage =
              "Account exists with different credential. Try signing in with email/password.";
          break;
        case 'invalid-credential':
          errorMessage = "Invalid credentials. Please try again.";
          break;
        case 'operation-not-allowed':
          errorMessage =
              "Google Sign-In is not enabled. Please contact support.";
          break;
        case 'user-disabled':
          errorMessage =
              "Your account has been disabled. Please contact support.";
          break;
        case 'user-not-found':
          errorMessage = "No account found. Please sign up first.";
          break;
        case 'wrong-password':
          errorMessage = "Incorrect password. Please try again.";
          break;
        default:
          errorMessage = "Auth error: ${e.message ?? 'Unknown error'}";
          break;
      }
      _showErrorSnackBar(errorMessage);
    } catch (e) {
      debugPrint("Unexpected error during Google Sign-In: $e");
      _logError('GOOGLE', e.toString());

      // Check if it's a cancellation error that didn't get caught above
      String errorString = e.toString().toLowerCase();
      if (errorString.contains('cancel') ||
          errorString.contains('16') ||
          errorString.contains('reauth failed')) {
        debugPrint("Google Sign-In cancelled by user (fallback detection)");
        return;
      }

      _showErrorSnackBar("Google Sign-In failed. Please try again.");
    } finally {
      _setGoogleLoading(false);
    }
  }

  Future<void> _facebookAuthWithFacebook(BuildContext context) async {
    _setFacebookLoading(true);

    try {
      final LoginResult result = await FacebookAuth.instance.login(
        permissions: ['email', 'public_profile'],
      );

      if (result.status == LoginStatus.cancelled) {
        _logError('FACEBOOK', 'CANCELLED');
        return;
      }

      if (result.status != LoginStatus.success) {
        _logError('FACEBOOK', 'Login failed: ${result.status}');
        _showErrorSnackBar("Facebook login failed or cancelled");
        return;
      }

      // Get user data from Facebook
      final userData = await FacebookAuth.instance.getUserData(
        fields: "id,name,email,picture.width(200)",
      );

      debugPrint("🔍 Facebook userData: $userData");

      // Create Firebase credential
      final OAuthCredential credential = FacebookAuthProvider.credential(
        result.accessToken!.tokenString,
      );

      // Sign in with Firebase
      final UserCredential userCredential = await FirebaseAuth.instance
          .signInWithCredential(credential);
      final User? user = userCredential.user;

      if (user != null) {
        debugPrint("🔍 Firebase user email: ${user.email}");
        debugPrint("🔍 Firebase user displayName: ${user.displayName}");
        debugPrint("🔍 Facebook userData email: ${userData['email']}");
        debugPrint("🔍 Facebook userData name: ${userData['name']}");

        _trackEvent('Facebook signin success', {
          'user_email': user.email ?? userData['email'] ?? '',
          'user_id': user.uid,
        });
        FirebaseAnalytics.instance.logEvent(
          name: 'signin_facebook_success',
          parameters: {
            'user_email': user.email ?? userData['email'] ?? '',
            'user_id': user.uid,
          },
        );

        // Set user data - prioritize Facebook data over Firebase user data
        final String finalName =
            userData['name'] ?? user.displayName ?? 'Facebook User';
        final String finalEmail = userData['email'] ?? user.email ?? 'NA';

        debugPrint("🔍 Setting user data:");
        debugPrint("  - finalName: '$finalName'");
        debugPrint("  - finalEmail: '$finalEmail'");
        debugPrint("  - userData['email']: '${userData['email']}'");
        debugPrint("  - user.email: '${user.email}'");

        AppConstants.isLoggedIn = true;
        AppConstants.custName = finalName;
        AppConstants.email = finalEmail;
        AppConstants.primaryEmail = finalEmail;
        AppConstants.mobileNo = user.phoneNumber ?? 'NA';
        AppConstants.loginMethod = "FACEBOOK";

        // IMPORTANT: Also update Consts.loginMethod for ProfileScreen logout functionality
        Consts.loginMethod = "FACEBOOK";

        // Update SplashScreenState for consistency
        SplashScreenState.CUST_NAME = finalName;
        SplashScreenState.EMAIL = finalEmail;
        SplashScreenState.PRIMARY_EMAIL = finalEmail;
        SplashScreenState.MOBILE_NO = user.phoneNumber ?? 'NA';

        // For Facebook login without proper TID, use Firebase UID temporarily
        // This will be updated by FirestoreFunctions later
        if (SplashScreenState.tid == "0" || SplashScreenState.tid.isEmpty) {
          SplashScreenState.tid = user.uid.substring(
            0,
            8,
          ); // Use first 8 chars of UID
          AppConstants.tid = SplashScreenState.tid;
        }

        // Set profile picture URL in AppConstants
        if (userData['picture'] != null &&
            userData['picture']['data'] != null) {
          final picUrl = userData['picture']['data']['url'];
          if (picUrl != null) {
            AppConstants.profilePicUrl = picUrl;
            await _downloadProfilePicture(picUrl);
          }
        }

        await _saveUserData();

        debugPrint(
          "💾 User data saved: isLoggedIn=${AppConstants.isLoggedIn}, method=${AppConstants.loginMethod}",
        );
        debugPrint("📧 Final email set: ${AppConstants.email}");
        debugPrint("👤 Final name set: ${AppConstants.custName}");
        debugPrint("🔧 Consts.loginMethod set: ${Consts.loginMethod}");
        debugPrint("🆔 TID set: ${SplashScreenState.tid}");
        debugPrint("✅ Facebook Login success: ${finalEmail}");

        // Use FirestoreFunctions to get proper TID instead of Firebase UID
        FirestoreFunctions(context).getTicketsNew("FACEBOOK");

        Get.offAll(() => const ProfileScreen());
      } else {
        _showErrorSnackBar("Facebook sign-in failed. Try again.");
      }
    } on FirebaseAuthException catch (e) {
      _logError('FACEBOOK', '${e.code}: ${e.message}');
      _showErrorSnackBar("Facebook Auth error: ${e.message}");
    } catch (e) {
      _logError('FACEBOOK', e.toString());
      _showErrorSnackBar("Facebook Sign-In failed. Please try again.");
    } finally {
      _setFacebookLoading(false);
    }
  }

  Future<void> _downloadProfilePicture(String url) async {
    try {
      final response = await http.get(Uri.parse(url));
      print('OTP send response: ' + response.toString());
      if (response.statusCode == 200) {
        setState(() {
          _profilePicture = response.bodyBytes;
          _profilePicUrl = url;
        });

        // Save profile picture locally if needed
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('profile_pic_url', url);
      }
    } catch (e) {
      debugPrint("Error downloading profile picture: $e");
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  void _showSignUpLaterDialog(BuildContext context) {
    MixpanelManager().track('Sign up later clicked', {});
    FirebaseAnalytics.instance.logEvent(
      name: 'signup_later',
      parameters: {'value': 'true'},
    );

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A1A),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Text(
            "Sign up later?",
            style: GoogleFonts.poppins(color: Colors.white, fontSize: 18),
          ),
          content: Text(
            "Your account and ticket information will be lost when you change mobile, "
            "and support option will be disabled until you sign in.",
            style: GoogleFonts.poppins(color: Colors.white70, fontSize: 14),
          ),
          actionsPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          actionsAlignment: MainAxisAlignment.spaceBetween,
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                MixpanelManager().track('Sign up alert later', {
                  'Source': 'DEFAULT',
                });
                _continueWithoutLogin();
              },
              child: Text(
                "Later",
                style: GoogleFonts.poppins(
                  color: Colors.lightBlueAccent,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                MixpanelManager().track('Sign up alert sign in', {});
                FirebaseAnalytics.instance.logEvent(
                  name: 'signup_now',
                  parameters: {'value': 'true'},
                );
                _mobileNumberHintShown = true;
                _requestPhoneNumberHint();
              },
              child: Text(
                "Sign in",
                style: GoogleFonts.poppins(
                  color: Colors.lightBlueAccent,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
