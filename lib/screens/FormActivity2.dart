import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:flutter/services.dart';
import '../core/AppConstants.dart';
import '../core/helper/mixpanel_manager.dart';
import '../pojo/FormDetail.dart';
import '../database/MainDB.dart';
import '../database/PassengerDB.dart';
import '../database/AddressDB.dart';
import '../database/ChildDB.dart';
import '../database/GSTDB.dart';
import '../database/InsuranceDB.dart';
import '../database/PodDB.dart';
import '../database/RCPaymentDB.dart';
import '../database/SecurityQuestionsDB.dart';
import 'package:sqflite/sqflite.dart';
import '../quick/my_context_wrapper.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import '../core/Consts.dart';
import '../pg/TicketInfoDialog.dart';
import 'TabActivity2.dart';
import 'package:video_player/video_player.dart';
import '../utils/Cryptography.dart';

class FormScreen extends StatefulWidget {
  const FormScreen({Key? key}) : super(key: key);

  @override
  State<FormScreen> createState() => _FormScreenState();
}

class DemoVideoDialog extends StatefulWidget {
  final String assetPath;
  const DemoVideoDialog({Key? key, required this.assetPath}) : super(key: key);

  @override
  State<DemoVideoDialog> createState() => _DemoVideoDialogState();
}

class _DemoVideoDialogState extends State<DemoVideoDialog> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.asset(widget.assetPath)
      ..initialize().then((_) {
        setState(() {
          _isInitialized = true;
        });
        _controller.play();
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Responsive: Use MediaQuery to size the video.
    return Dialog(
      backgroundColor: Colors.black87,
      insetPadding: EdgeInsets.symmetric(horizontal: 15, vertical: 30),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.45,
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18),
          color: Colors.black,
        ),
        child: _isInitialized
            ? Stack(
                children: [
                  Center(
                    child: AspectRatio(
                      aspectRatio: _controller.value.aspectRatio,
                      child: VideoPlayer(_controller),
                    ),
                  ),
                  Positioned(
                    bottom: 10,
                    left: 0,
                    right: 0,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          icon: Icon(
                            _controller.value.isPlaying
                                ? Icons.pause_circle_filled
                                : Icons.play_circle_filled,
                            color: Colors.white,
                            size: 32,
                          ),
                          onPressed: () {
                            setState(() {
                              if (_controller.value.isPlaying) {
                                _controller.pause();
                              } else {
                                _controller.play();
                              }
                            });
                          },
                        ),
                        SizedBox(width: 12),
                        Text(
                          '${_formatDuration(_controller.value.position)} / ${_formatDuration(_controller.value.duration)}',
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    top: 6,
                    right: 6,
                    child: IconButton(
                      icon: Icon(Icons.close, color: Colors.white, size: 28),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ],
              )
            : Center(child: CircularProgressIndicator(color: Colors.white)),
      ),
    );
  }

  String _formatDuration(Duration d) {
    final minutes = d.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = d.inSeconds.remainder(60).toString().padLeft(2, '0');
    return "$minutes:$seconds";
  }
}

class _FormScreenState extends State<FormScreen> {
  static const List<String> languageCodes = ["en", "hi", "mr", "bn", "gu"];

  bool isGoldUser = false;
  bool isFormNameEditing = false;
  int ticketsLeft = 3;
  List<FormDetail> forms = [];
  bool askAiViewLogged = false;
  String? selectedFormName;
  bool isLoading = false;

  late BannerAd _bannerAd;
  late InterstitialAd? _interstitialAd;
  bool _isBannerAdLoaded = false;
  bool _isReversed = false;

  String? currentLanguageCode;

  final ScrollController _scrollController = ScrollController();
  bool shouldShowRating = false;

  @override
  void initState() {
    super.initState();
    _loadUserPackAndTickets();
    _loadBannerAd();
    _loadInterstitialAd();
    _loadForms();
    _loadCurrentLanguage();
    _scrollController.addListener(_onScrollHideKeyboard);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowRatingDialog();
    });
    _checkPermissions();
  }

  Future<void> _loadUserPackAndTickets() async {
    setState(() {
      try {
        isGoldUser =
            AppConstants.isGoldUser == 2 ||
            Consts.USER_TYPE == "GOLD_USER" ||
            Consts.USER_TYPE == "DIAMOND_USER";
        ticketsLeft = AppConstants.ticketsLeft;
      } catch (e) {
        isGoldUser = false;
        ticketsLeft = 3;
      }
    });
  }

  void _loadBannerAd() {
    _bannerAd = BannerAd(
      adUnitId: Consts.TATKAL_BANNER_ID,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (_) {
          setState(() {
            _isBannerAdLoaded = true;
          });
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
        },
      ),
    );
    _bannerAd.load();
  }

  void _loadInterstitialAd() {
    if (!isGoldUser) {
      InterstitialAd.load(
        adUnitId: Consts.TATKAL_INTERSTITIAL_ID,
        request: const AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (InterstitialAd ad) {
            _interstitialAd = ad;
          },
          onAdFailedToLoad: (LoadAdError error) {
            _interstitialAd = null;
          },
        ),
      );
    }
  }

  void _showInterstitialIfEligible() {
    if (!isGoldUser && _interstitialAd != null) {
      _interstitialAd?.show();
      _interstitialAd = null;
      _loadInterstitialAd();
    }
  }

  Future<String?> _extractFormContent(Map<String, Object?> row) async {
    String fromStn = (row['FROM_STN'] ?? '').toString();
    String toStn = (row['TO_STN'] ?? '').toString();
    String travelClass = (row['TRVL_CLASS'] ?? '').toString();
    String quota = (row['QUOTA'] ?? '').toString();
    String date = (row['TRVL_DATE'] ?? '').toString();

    fromStn = (fromStn == "null") ? "" : fromStn;
    toStn = (toStn == "null") ? "" : toStn;
    travelClass = (travelClass == "null") ? "" : travelClass;
    quota = (quota == "null") ? "" : quota;
    date = (date == "null") ? "" : date;

    String formattedDate = date;
    if (date.isNotEmpty) {
      try {
        formattedDate = DateFormat(
          'dd MMM',
        ).format(DateFormat('dd-MM-yyyy').parse(date));
      } catch (e) {
        formattedDate = date;
      }
    }
    return '$fromStn ➝ $toStn • $travelClass • $quota • $formattedDate';
  }

  void _showSnack(String msg) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(msg)));
  }

  void _showVideoGuide() async {
    MixpanelManager().track("Click video guide");
    // Show a dialog to choose which demo video to play (Web or RC), then play it
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text('Demo Captcha'),
        content: Text('Watch a demo for Web or RC ticket captcha filling.'),
        actions: [
          TextButton(
            child: Text('Web Demo'),
            onPressed: () {
              Navigator.pop(ctx);
              _playDemoVideo(context, 'assets/raw/captcha_web.mp4');
            },
          ),
          TextButton(
            child: Text('RC Demo'),
            onPressed: () {
              Navigator.pop(ctx);
              _playDemoVideo(context, 'assets/raw/captcha_rc.mp4');
            },
          ),
          TextButton(
            child: Text('Cancel'),
            onPressed: () => Navigator.pop(ctx),
          ),
        ],
      ),
    );
  }

  void _playDemoVideo(BuildContext context, String assetPath) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (ctx) {
        return DemoVideoDialog(assetPath: assetPath);
      },
    );
  }

  void _showNewFormDialog({String source = "New"}) {
    final name = TextEditingController();
    String errorText = '';
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (ctx) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFF1B1A26),
              // close to @drawable/theme_round_dialog
              borderRadius: BorderRadius.circular(18),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 22),
            child: StatefulBuilder(
              builder: (dialogCtx, setDialog) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Center(
                      child: Text(
                        'Form Name',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: name,
                      autofocus: true,
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                      textCapitalization: TextCapitalization.words,
                      decoration: InputDecoration(
                        isDense: true,
                        hintText: '',
                        hintStyle: TextStyle(color: Colors.white54),
                        enabledBorder: UnderlineInputBorder(
                          borderSide: BorderSide(
                            color: Color(0xFF474457),
                            width: 1.25,
                          ),
                        ),
                        focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(
                            color: Color(0xFFA34BFF),
                            width: 1.55,
                          ),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          vertical: 13,
                          horizontal: 10,
                        ),
                        fillColor: Colors.transparent,
                        filled: true,
                      ),
                      onSubmitted: (_) => FocusScope.of(dialogCtx).unfocus(),
                    ),
                    if (errorText.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 5, left: 4),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            errorText,
                            style: const TextStyle(
                              color: Color(0xFFD83237),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    const SizedBox(height: 22),
                    Center(
                      child: SizedBox(
                        width: 200,
                        height: 40,
                        child: ElevatedButton(
                          onPressed: () async {
                            final userName = name.text.trim();
                            if (userName.isEmpty) {
                              setDialog(
                                () => errorText = 'Please enter form name',
                              );
                              return;
                            }
                            if (isDuplicateForm(userName)) {
                              setDialog(
                                () => errorText =
                                    'Form "$userName" already created',
                              );
                              return;
                            }
                            setDialog(() => errorText = '');
                            // Immediately insert MINIMAL row into MainDB, like Java
                            try {
                              final db = await MainDB.instance.database;
                              await db.insert(
                                MainDB.tableName,
                                {
                                  'FORM_NAME': userName,
                                  'CAPTCHA_AUTOFILL': 1,
                                  // Enable captcha autofill by default
                                  // Only minimal info, rest default/null.
                                },
                                conflictAlgorithm: ConflictAlgorithm.ignore,
                              );
                            } catch (e) {
                              // handle/log error optionally
                            }
                            // Now continue as before
                            await _createForm(name: userName, source: source);
                            Navigator.pop(ctx);
                          },
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            padding: EdgeInsets.zero,
                            backgroundColor: Colors.transparent,
                            shadowColor: Colors.transparent,
                          ),
                          child: Ink(
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Container(
                              alignment: Alignment.center,
                              width: double.infinity,
                              height: 40,
                              child: const Text(
                                'Submit',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.white,
                                  fontFamily: 'Poppins-Bold.ttf',
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }

  bool isDuplicateForm(String formName) {
    return forms.any(
      (fd) => fd.formName.toLowerCase() == formName.toLowerCase(),
    );
  }

  Future<void> _createForm({
    required String name,
    required String source,
  }) async {
    setState(() {
      isLoading = true;
    });
    try {
      // Encrypt password for true parity
      final encryptedPassword = Cryptography.encryptPassword(
        "",
      ); // Empty password by default
      final db = await MainDB.instance.database;
      // Update the FORM_NAME row with password instead of inserting again
      await db.update(
        MainDB.tableName,
        {'PASSWORD': encryptedPassword},
        where: 'FORM_NAME = ?',
        whereArgs: [name],
      );
      MixpanelManager().track("Click new form", {"Source": source});
      _showSnack("Created form: $name");
      await _loadForms();
    } catch (e, stack) {
      print("Create Form Error: $e");
      print(stack);
      _showSnack('DB Error: Unable to create form. $e');
    }
    setState(() {
      isLoading = false;
    });
  }

  Future<void> deleteItem(FormDetail fd) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Delete Form'),
        content: Text('Do you want to delete the form "${fd.formName}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(ctx, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(ctx, true),
            child: const Text('Delete'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          ),
        ],
      ),
    );
    if (confirm != true) return;
    setState(() {
      isLoading = true;
    });
    try {
      final db = await MainDB.instance.database;
      await db.delete(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.formName],
      );

      await _deleteFormInAllRelatedDBs(fd.formName);
      _showSnack('Deleted form ${fd.formName}');
      MixpanelManager().track("Delete form");
      await _loadForms();
    } catch (e, stack) {
      print("Delete Form Error: $e");
      print(stack);
      _showSnack('DB Error: Unable to delete form. $e');
    }
    setState(() {
      isLoading = false;
    });
  }

  Future<void> _deleteFormInAllRelatedDBs(String formName) async {
    try {
      final pDb = await PassengerDB.instance.database;
      await pDb.delete(
        PassengerDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [formName],
      );
      final aDb = await AddressDB.instance.database;
      await aDb.delete(
        AddressDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [formName],
      );
      final cDb = await ChildDB.instance.database;
      await cDb.delete(
        ChildDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [formName],
      );
      final gDb = await GSTDB.instance.database;
      await gDb.delete(
        GSTDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [formName],
      );
      final iDb = await InsuranceDB.instance.database;
      await iDb.delete(
        InsuranceDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [formName],
      );
      final podDb = await PodDB.instance.database;
      await podDb.delete(
        PodDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [formName],
      );
      final rcDb = await RCPaymentDB.instance.database;
      await rcDb.delete(
        RCPaymentDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [formName],
      );
      final sqDb = await SecurityQuestionsDB.instance.database;
      await sqDb.delete(
        SecurityQuestionsDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [formName],
      );
    } catch (e) {}
  }

  String getDuplicateFormName(String name) {
    int index = 1;
    String baseName = name;
    final regex = RegExp(r'-(\d+)');
    final match = regex.firstMatch(name);
    if (match != null) {
      baseName = name.substring(0, match.start);
    }
    final matches = forms.where((fd) => fd.formName.startsWith(baseName + '-'));
    for (final fd in matches) {
      final suffix = fd.formName.replaceFirst(baseName + '-', '');
      if (int.tryParse(suffix) != null && int.parse(suffix) >= index) {
        index = int.parse(suffix) + 1;
      }
    }
    return baseName + '-' + index.toString();
  }

  Future<void> onEdit(FormDetail fd, String newName) async {
    if (newName.isEmpty) {
      _showSnack('Form name cannot be blank.');
      return;
    }
    if (isDuplicateForm(newName)) {
      _showSnack('Form "$newName" already exists!');
      return;
    }
    setState(() {
      isLoading = true;
    });
    try {
      final db = await MainDB.instance.database;
      await db.update(
        MainDB.tableName,
        {'FORM_NAME': newName},
        where: 'FORM_NAME = ?',
        whereArgs: [fd.formName],
      );
      await _editFormNameInAllRelatedDBs(fd.formName, newName);
      MixpanelManager().track("Edit form name");
      _showSnack('Renamed form to $newName');
      await _loadForms();
    } catch (e, stack) {
      print("Edit Form Error: $e");
      print(stack);
      _showSnack('DB Error: Failed to rename form. $e');
    }
    setState(() {
      isLoading = false;
    });
  }

  Future<void> _editFormNameInAllRelatedDBs(
    String oldName,
    String newName,
  ) async {
    try {
      final pDb = await PassengerDB.instance.database;
      await pDb.update(
        PassengerDB.tableName,
        {'FORM_NAME': newName},
        where: 'FORM_NAME = ?',
        whereArgs: [oldName],
      );
      final aDb = await AddressDB.instance.database;
      await aDb.update(
        AddressDB.tableName,
        {'FORM_NAME': newName},
        where: 'FORM_NAME = ?',
        whereArgs: [oldName],
      );
      final cDb = await ChildDB.instance.database;
      await cDb.update(
        ChildDB.tableName,
        {'FORM_NAME': newName},
        where: 'FORM_NAME = ?',
        whereArgs: [oldName],
      );
      final gDb = await GSTDB.instance.database;
      await gDb.update(
        GSTDB.tableName,
        {'FORM_NAME': newName},
        where: 'FORM_NAME = ?',
        whereArgs: [oldName],
      );
      final iDb = await InsuranceDB.instance.database;
      await iDb.update(
        InsuranceDB.tableName,
        {'FORM_NAME': newName},
        where: 'FORM_NAME = ?',
        whereArgs: [oldName],
      );
      final podDb = await PodDB.instance.database;
      await podDb.update(
        PodDB.tableName,
        {'FORM_NAME': newName},
        where: 'FORM_NAME = ?',
        whereArgs: [oldName],
      );
      final rcDb = await RCPaymentDB.instance.database;
      await rcDb.update(
        RCPaymentDB.tableName,
        {'FORM_NAME': newName},
        where: 'FORM_NAME = ?',
        whereArgs: [oldName],
      );
      final sqDb = await SecurityQuestionsDB.instance.database;
      await sqDb.update(
        SecurityQuestionsDB.tableName,
        {'FORM_NAME': newName},
        where: 'FORM_NAME = ?',
        whereArgs: [oldName],
      );
    } catch (e) {}
  }

  Future<void> postCopyForm(FormDetail fd, String formName) async {
    MixpanelManager().track('copy_form', {'value': 'true'});
    MixpanelManager().incrementPeopleProperty('Forms created', 1);
    setState(() {
      isLoading = true;
    });
    try {
      final db = await MainDB.instance.database;
      final origRowList = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [fd.formName],
      );
      if (origRowList.isEmpty) throw Exception('Form not found!');
      final origRow = Map<String, Object?>.from(origRowList[0]);
      origRow['FORM_NAME'] = formName;
      await db.insert(MainDB.tableName, origRow);
      await _duplicateFormInAllRelatedDBs(fd.formName, formName);
      forms.add(FormDetail(formName, fd.password, fd.formContent));
      _showSnack('Duplicated form "$formName"');
      await _loadForms();
    } catch (e, stack) {
      print("Copy Form Error: $e");
      print(stack);
      _showSnack('Failed to duplicate form. $e');
    }
    setState(() {
      isLoading = false;
    });
  }

  Future<void> _duplicateFormInAllRelatedDBs(
    String oldName,
    String newName,
  ) async {
    Future<void> _copyTable(Database db, String table) async {
      final rows = await db.query(
        table,
        where: 'FORM_NAME = ?',
        whereArgs: [oldName],
      );
      for (final row in rows) {
        final newRow = Map<String, Object?>.from(row);
        newRow['FORM_NAME'] = newName;
        await db.insert(table, newRow);
      }
    }

    try {
      await _copyTable(
        await PassengerDB.instance.database,
        PassengerDB.tableName,
      );
      await _copyTable(await AddressDB.instance.database, AddressDB.tableName);
      await _copyTable(await ChildDB.instance.database, ChildDB.tableName);
      await _copyTable(await GSTDB.instance.database, GSTDB.tableName);
      await _copyTable(
        await InsuranceDB.instance.database,
        InsuranceDB.tableName,
      );
      await _copyTable(await PodDB.instance.database, PodDB.tableName);
      await _copyTable(
        await RCPaymentDB.instance.database,
        RCPaymentDB.tableName,
      );
      await _copyTable(
        await SecurityQuestionsDB.instance.database,
        SecurityQuestionsDB.tableName,
      );
    } catch (e) {}
  }

  Widget _formListItem(FormDetail fd) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Color(0xFF232332),
        borderRadius: BorderRadius.circular(22),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 10),
            child: Container(
              width: 38,
              height: 38,
              decoration: BoxDecoration(
                color: Color(0xFF222039),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Image.asset(
                  "assets/images/train_white.png",
                  width: 28,
                  height: 28,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Flexible(
                      child: Text(
                        fd.formName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          fontFamily: 'Poppins-Bold.ttf',
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Image.asset(
                      'assets/images/edit.png',
                      width: 16,
                      height: 16,
                    ),
                  ],
                ),
                if (fd.formContent != null && fd.formContent.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 5.0),
                    child: Text(
                      fd.formContent,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        fontFamily: 'Poppins-Medium.ttf',
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
              ],
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Image.asset(
                  'assets/images/copy.png',
                  width: 22,
                  height: 22,
                ),
                tooltip: 'Copy',
                onPressed: () {
                  final dupFormName = getDuplicateFormName(fd.formName);
                  postCopyForm(fd, dupFormName);
                },
              ),
              IconButton(
                icon: Image.asset(
                  'assets/images/delete.png',
                  width: 22,
                  height: 22,
                ),
                tooltip: 'Delete',
                onPressed: () => deleteItem(fd),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _viewForm(FormDetail fd) async {
    setState(() {
      isLoading = true;
    });
    await Future.delayed(const Duration(milliseconds: 600));

    // Skip password validation - directly navigate to TabActivity2
    setState(() {
      isLoading = false;
    });
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (ctx) =>
            TabActivity2(formName: fd.formName, lang: getCurrentLanguageCode()),
      ),
    );
  }

  Future<void> _loadCurrentLanguage() async {
    try {
      final langCode = await MyContextWrapper.getCurrentLanguageCode();
      setState(() {
        currentLanguageCode = langCode;
      });
    } catch (e) {
      setState(() {
        currentLanguageCode = "en";
      });
    }
  }

  String getCurrentLanguageCode() {
    return currentLanguageCode ?? "en";
  }

  Future<void> _loadForms() async {
    setState(() {
      isLoading = true;
    });
    try {
      final db = await MainDB.instance.database;
      final result = await db.query(MainDB.tableName);
      final List<FormDetail> loadedForms = [];
      for (final m in result) {
        final formName = (m["FORM_NAME"] ?? "").toString();
        if (formName.trim().isEmpty) continue;
        final password = (m["PASSWORD"] ?? "").toString();
        final content = await _extractFormContent(m) ?? "";
        loadedForms.add(FormDetail(formName, password, content));
      }
      forms = loadedForms;
    } catch (e) {
      debugPrint('Error loading forms: $e');
      forms = [];
    }
    setState(() {
      isLoading = false;
    });
  }

  void _onScrollHideKeyboard() {
    if (_scrollController.position.isScrollingNotifier.value) {
      FocusScope.of(context).unfocus();
    }
  }

  void _checkPermissions() async {
    // To enable permissions, add permission_handler to pubspec.yaml and uncomment below
    // For notifications (Android 13+), and storage if needed
    // if (await Permission.notification.isDenied) {
    //   await Permission.notification.request();
    // }
    // if (await Permission.storage.isDenied) {
    //   await Permission.storage.request();
    // }
  }

  void _checkAndShowRatingDialog() async {
    final prefs = await SharedPreferences.getInstance();
    int rated = prefs.getInt('DONE') ?? 0;
    int viewed = prefs.getInt('VIEW') ?? 0;
    if (rated == 0 && viewed == 1) {
      shouldShowRating = true;
      // If you want rating prompt, add 'in_app_review' pub package and uncomment next block
      /*
      final inAppReview = InAppReview.instance;
      if (await inAppReview.isAvailable()) {
        await inAppReview.requestReview();
        await prefs.setInt('DONE', 1);
      }
      */
    }
  }

  void _onMenuUpdatePressed() async {
    // To use version display, require package_info_plus. Otherwise, just show generic dialog:
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text('Update App'),
        content: Text(
          'For enhanced performance, update the app for latest features.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(ctx),
            child: const Text('Later'),
          ),
          ElevatedButton(
            onPressed: () {
              // open Play Store
              launchUrl(
                Uri.parse(
                  'https://play.google.com/store/apps/details?id=com.your.package',
                ),
              );
              Navigator.pop(ctx);
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (isFormNameEditing) {
          setState(() {
            isFormNameEditing = false;
          });
          return false;
        } else {
          _showInterstitialIfEligible();
          return true;
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          titleSpacing: 0,
          backgroundColor: Colors.black,
          elevation: 2,
          toolbarHeight: 60,
          title: const Text(
            "Ticket Booking",
            style: TextStyle(
              fontFamily: 'Poppins-Bold.ttf',
              fontSize: 19,
              fontWeight: FontWeight.w700,
              color: Colors.white,
            ),
          ),
          actions: [
            Center(
              child: GestureDetector(
                onTap: _showTicketsDialog,
                child: Container(
                  margin: const EdgeInsets.only(right: 16),
                  width: 34,
                  height: 33,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    color: isGoldUser ? Colors.orange : null,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      _buildBadgeText(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Poppins-Bold.ttf',
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        body: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Color(0xFF424259),
                borderRadius: BorderRadius.circular(30),
              ),
            ),
            Column(
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                    top: 20,
                    left: 18,
                    right: 18,
                    bottom: 10,
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Color(0xFF23223A),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    padding: const EdgeInsets.symmetric(
                      vertical: 10,
                      horizontal: 16,
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/dashboard_icon.png',
                          width: 22,
                          height: 22,
                          fit: BoxFit.contain,
                        ),
                        const SizedBox(width: 10),
                        const Text(
                          'Forms',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 19,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Poppins-Bold.ttf',
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: _showVideoGuide,
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                      borderRadius: BorderRadius.circular(14),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0x66000000),
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white12,
                          ),
                          child: Image.asset(
                            'assets/images/play_white.png',
                            width: 40,
                            height: 40,
                          ),
                        ),
                        const SizedBox(width: 15),
                        const Text(
                          "How to book Tatkal ticket?",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Poppins-Bold.ttf',
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: forms.isEmpty
                      ? Container(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 10,
                          ),
                          child: Center(
                            child: SizedBox(
                              width: 140,
                              height: 40,
                              child: ElevatedButton(
                                onPressed: () =>
                                    _showNewFormDialog(source: "New"),
                                style: ElevatedButton.styleFrom(
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  backgroundColor: Colors.transparent,
                                  shadowColor: Colors.transparent,
                                  padding: EdgeInsets.zero,
                                ),
                                child: Ink(
                                  width: 140,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Color(0xFFA34BFF),
                                        Color(0xFF224FF9),
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Container(
                                    alignment: Alignment.center,
                                    width: double.infinity,
                                    height: 40,
                                    child: const Text(
                                      'New Form',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontFamily: 'Poppins-Bold.ttf',
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        )
                      : ListView.builder(
                          controller: _scrollController,
                          itemCount: forms.length,
                          itemBuilder: (context, index) => GestureDetector(
                            onTap: () async {
                              await _viewForm(forms[index]);
                              await _loadForms();
                            },
                            child: _formListItem(forms[index]),
                          ),
                        ),
                ),
                if (_isBannerAdLoaded)
                  SizedBox(
                    height: _bannerAd.size.height.toDouble(),
                    width: _bannerAd.size.width.toDouble(),
                    child: AdWidget(ad: _bannerAd),
                  ),
              ],
            ),
            if (isLoading)
              Container(
                color: Colors.black26,
                child: const Center(child: CircularProgressIndicator()),
              ),
          ],
        ),
        bottomNavigationBar: forms.isEmpty
            ? null
            : BottomAppBar(
                color: Colors.transparent,
                elevation: 0,
                child: Container(
                  height: 80,
                  color: Colors.transparent,
                  padding: const EdgeInsets.only(bottom: 20),
                  child: Center(
                    child: SizedBox(
                      width: 140,
                      height: 40,
                      child: ElevatedButton(
                        onPressed: () => _showNewFormDialog(source: 'Main'),
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          padding: EdgeInsets.zero,
                        ),
                        child: Ink(
                          width: 140,
                          height: 40,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Container(
                            alignment: Alignment.center,
                            width: double.infinity,
                            height: 40,
                            child: const Text(
                              'New Form',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Poppins-Bold.ttf',
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
      ),
    );
  }

  String _buildBadgeText() {
    switch (Consts.USER_TYPE) {
      case "DIAMOND_USER":
        return "D";
      case "GOLD_USER":
        return "G";
      case "PREMIUM_USER":
        return "P";
      case "STARTER_USER":
        return "S";
      default:
        return '$ticketsLeft';
    }
  }

  void _showTicketsDialog() {
    showDialog(context: context, builder: (ctx) => TicketInfoDialog(type: 0));
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _bannerAd.dispose();
    _interstitialAd?.dispose();
    super.dispose();
  }
}
