import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:url_launcher/url_launcher.dart';

import 'legal_screen.dart';

class LegalActivityList extends StatefulWidget {
  const LegalActivityList({Key? key}) : super(key: key);

  @override
  State<LegalActivityList> createState() => _LegalActivityListState();
}

class _LegalActivityListState extends State<LegalActivityList> {
  final FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  void _navigateToLegal(String title, String url, String event) async {
    await analytics.logEvent(name: event, parameters: {"value": "true"});
    Get.to(() => LegalScreen(type: title, url: url));
  }

  TextSpan _buildLinkText(String text, String url) {
    return TextSpan(
      text: text,
      style: const TextStyle(
        color: Colors.blueAccent,
        decoration: TextDecoration.underline,
      ),
      recognizer: TapGestureRecognizer()
        ..onTap = () async {
          final uri = Uri.parse(url);
          if (await canLaunchUrl(uri)) {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
          }
        },
    );
  }

  @override
  Widget build(BuildContext context) {
    const textColor = Colors.white;
    const divider = Divider(height: 1, thickness: 1, color: Colors.grey);

    return Scaffold(
      backgroundColor: const Color(0xFF000000),
      appBar: AppBar(
        backgroundColor: const Color(0xFF000000),
        title: const Text('Legal'),
        elevation: 4,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () => _navigateToLegal(
              'Privacy Policy',
              'https://afrestudios.com/quick-tatkal/privacy_policy.html',
              'show_privacy_policy',
            ),
            child: const Padding(
              padding: EdgeInsets.all(15),
              child: Text(
                'Privacy Policy',
                style: TextStyle(fontSize: 16, color: textColor),
              ),
            ),
          ),
          divider,
          InkWell(
            onTap: () => _navigateToLegal(
              'Terms & Conditions',
              'https://afrestudios.com/quick-tatkal/terms_and_conditions.html',
              'show_tnc',
            ),
            child: const Padding(
              padding: EdgeInsets.all(15),
              child: Text(
                'Terms & Conditions',
                style: TextStyle(fontSize: 16, color: textColor),
              ),
            ),
          ),
          divider,
          InkWell(
            onTap: () => _navigateToLegal(
              'Use of Accessibility Service',
              'https://afrestudios.com/quick-tatkal/2.0/accessibility_service.html',
              'show_accessibility',
            ),
            child: const Padding(
              padding: EdgeInsets.all(15),
              child: Text(
                'Use of Accessibility Service',
                style: TextStyle(fontSize: 16, color: textColor),
              ),
            ),
          ),
          divider,
          const Spacer(),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 10),
            child: Text(
              'DISCLAIMER',
              style: TextStyle(fontWeight: FontWeight.bold, color: Colors.grey),
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(10, 0, 10, 10),
            child: RichText(
              text: TextSpan(
                style: const TextStyle(color: Colors.grey),
                children: [
                  const TextSpan(
                    text:
                        "Quick Tatkal does not represent any government entity (in this case, IRCTC - Indian Railway Catering and Tourism Corporation) and train ticket bookings are carried out on the official IRCTC website via an in-app browser by user's own IRCTC account only. The original content is available on official ",
                  ),
                  _buildLinkText("IRCTC website", "https://irctc.co.in/"),
                  const TextSpan(
                    text:
                        ".\n\nFor checking PNR status, the app uses official Indian Railway website ",
                  ),
                  _buildLinkText(
                    "indianrail.gov.in",
                    "https://www.indianrail.gov.in/enquiry/PNR/PnrEnquiry.html",
                  ),
                  const TextSpan(text: "."),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
