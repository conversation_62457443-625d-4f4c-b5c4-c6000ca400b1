import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class TrainStatus {
  String stationName;
  String stnCode;
  String scheduleArrival;
  String actualArrival;
  String scheduleDeparture;
  String actualDeparture;
  String delayInDeparture;
  String passed;
  int day;
  bool isIntermediate;
  int intmStations;
  String distance;
  bool isExpanded;

  TrainStatus({
    required this.stationName,
    required this.stnCode,
    required this.scheduleArrival,
    required this.actualArrival,
    required this.scheduleDeparture,
    required this.actualDeparture,
    required this.delayInDeparture,
    this.passed = '',
    this.day = 0,
    this.isIntermediate = false,
    this.intmStations = 0,
    this.distance = '',
    this.isExpanded = false,
  });

  // Getter methods to match Java POJO pattern
  String getStnName() => stationName;

  String getStnCode() => stnCode;

  String getSchArrival() => scheduleArrival;

  String getActArrival() => actualArrival;

  String getSchDep() => scheduleDeparture;

  String getActDep() => actualDeparture;

  String getDelayDep() => delayInDeparture;

  String getPassed() => passed;

  int getDay() => day;

  int getIntmStations() => intmStations;

  String getDistance() => distance;

  // Setter methods
  void setPassed(String passed) => this.passed = passed;

  void setDay(int day) => this.day = day;

  void setIntermediate(bool isIntermediate) =>
      this.isIntermediate = isIntermediate;
  void setIntmStations(int intmStations) => this.intmStations = intmStations;
  void setDistance(String distance) => this.distance = distance;
  void setExpanded(bool isExpanded) => this.isExpanded = isExpanded;
}

class RunningStatus extends StatefulWidget {
  final Map<String, dynamic> arguments;

  const RunningStatus({Key? key, required this.arguments}) : super(key: key);

  @override
  _RunningStatusState createState() => _RunningStatusState();
}

class _RunningStatusState extends State<RunningStatus> {
  // Variables converted from Java
  List<TrainStatus> trainRoute = [];
  List<TrainStatus> trainRouteAll = [];
  TrainStatus? currentStnObj;
  late RunningStatusAdapter adapter;

  late WebViewController webViewController;
  bool fetched = false;

  String startDate = '';
  String trainNo = '';
  static DateTime? selectedDate;
  static DateTime? currentStatus;
  static String currentStation = '';

  int currentDay = 0;
  static int liveIndexToScroll = -1;
  static int departedOrReached = 0; // 1: dep, 2: reach

  bool trainDateAfterCurrentStn = false;
  int dayChangeCount = 0;
  bool previousPM = false;

  bool notStarted = true;
  bool reachedDestination = false;
  String locationMsg = '';

  static String trainDates = '';
  bool statusEventTracked = false;

  static String liveStation = '';

  // UI Components - matching provided design
  bool isLoading = true;
  String dateText = 'Start Date';
  String trainName = "01039 KOP GONDIA SPL"; // Default from design
  bool isListVisible = false;
  bool isLastLocationVisible = false;
  BannerAd? bannerAd;

  // Date filter options
  final List<String> dateOptions = ["Today", "Yesterday", "Custom Date"];

  @override
  void initState() {
    super.initState();
    onCreate();
  }

  void onCreate() {
    // Extract arguments (equivalent to Bundle in Android)
    final args = widget.arguments;
    int dateIndex = args['date'] ?? 0;
    String dateLong = args['dateStr'] ?? '';
    trainNo = args['train'] ?? '';
    String tInfo = args['trainInfo'] ?? '';

    if (RegExp(r'^[0-9]+$').hasMatch(tInfo)) {
      tInfo = "Train No : $tInfo";
    }

    selectedDate = DateTime.now();
    DateFormat sdf = DateFormat('yyyyMMdd');
    startDate = sdf.format(DateTime.now());

    dateText = 'Start Date';

    selectedDate = selectedDate!.subtract(Duration(days: dateIndex));
    startDate = sdf.format(selectedDate!);

    currentStatus = DateTime.now();
    trainRoute.add(
      TrainStatus(
        stationName: '',
        stnCode: '',
        scheduleArrival: '',
        actualArrival: '',
        scheduleDeparture: '',
        actualDeparture: '',
        delayInDeparture: '',
      ),
    );
    trainRouteAll.add(
      TrainStatus(
        stationName: '',
        stnCode: '',
        scheduleArrival: '',
        actualArrival: '',
        scheduleDeparture: '',
        actualDeparture: '',
        delayInDeparture: '',
      ),
    );

    initializeWebView();
    loadBanner();
    adapter = RunningStatusAdapter(this, trainRoute, trainRouteAll);
  }

  void initializeWebView() {
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel(
        'Step',
        onMessageReceived: (JavaScriptMessage message) {
          // Handle different types of messages
          if (message.message.contains('|||')) {
            // Handle setData callback
            final parts = message.message.split('|||');
            if (parts.length >= 2) {
              setData(parts[0], int.parse(parts[1]));
            }
          } else if (message.message.contains('|')) {
            // Handle setDates callback
            setDates(message.message);
          }
        },
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            onPageFinished(url);
          },
        ),
      )
      ..loadRequest(
        Uri.parse('https://www.trainman.in/running-status/$trainNo'),
      );
  }

  void onPageFinished(String url) {
    if (!fetched) {
      fetched = true;
      String jsCode = '''
        var rows = 0;
        var c1 = 0;
        var liveIndex = -1;
        var prevLiveIndex = -1;

        function don(index) {
            var rows = document.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            var divCount = 0;
            try {
                divCount = rows[index].getElementsByTagName('td')[1].getElementsByTagName('div').length;
            } catch {
                createJson();
                return;
            }
            
            if(divCount == 3) {
                rows[index].click();
                var checkInt = setInterval(function() {
                    var c2 = document.getElementsByTagName('tbody')[0].getElementsByTagName('tr').length;
                    if(c2 > c1) {
                        c1 = c2;
                        index++;
                        clearInterval(checkInt);
                        don(index);
                    }
                }, 50);
            } else {
                index++;
                don(index);
            }
        }

        function createJson() {
            var json = '{\\n"ResponseCode": 200,\\n"TrainRoute": [';
            var rows = document.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            for(i=0; i<rows.length; i++) {
                if(rows[i].getAttribute('class').indexOf('indermediate') == -1) {
                    prevLiveIndex = i;
                    var cStn = rows[i].getElementsByClassName('st-name')[0].innerHTML.split(' (')[0];
                    var cCode = rows[i].getElementsByClassName('st-name')[0].innerHTML.split(' (')[1].split(')')[0];
                    var cSA = rows[i].getElementsByTagName('td')[2].getElementsByTagName('div')[0].innerText;
                    var cAA = rows[i].getElementsByTagName('td')[2].getElementsByTagName('div')[1].innerText;
                    var cSD = rows[i].getElementsByTagName('td')[3].getElementsByTagName('div')[0].innerText;
                    var cAD = rows[i].getElementsByTagName('td')[3].getElementsByTagName('div')[2].innerText;
                    var cDelayDep = rows[i].getElementsByTagName('td')[4].getElementsByTagName('div')[0].innerText;
                    var day = rows[i].getElementsByTagName('td')[1].getElementsByTagName('div')[1].innerText.split(' (')[1].split('day ')[1].split(')')[0];
                    var km = rows[i].getElementsByTagName('td')[1].getElementsByTagName('div')[1].innerText.split(' (')[0];
                    var intmStn = '0';
                    if(rows[i].getElementsByTagName('td')[1].getElementsByTagName('div').length == 3) {
                        intmStn = rows[i].getElementsByTagName('td')[1].getElementsByTagName('div')[2].innerText.split(' ')[0]
                    }

                    var passed = 'COMING';
                    if(rows[i].getElementsByTagName('img').length > 0) {
                        if(rows[i].getElementsByTagName('td')[0].getElementsByTagName('div').length == 3) {
                            passed = 'HALT';
                        } else {
                            passed = 'BETWEEN';
                        }
                    } else {
                        if(rows[i].getElementsByTagName('td')[0].getElementsByTagName('div')[0].getAttribute('class').indexOf('trainArrived') != -1) {
                            passed = 'PASSED';
                        }
                    }
                    json += '\\n{\\n"StationName": "'+ cStn +'",\\n"StationCode": "'+ cCode +'",\\n"Passed": "'+ passed +'",\\n"ScheduleArrival": "'+ cSA +'",\\n"ActualArrival": "'+ cAA +'",\\n"InterStn": "'+ intmStn +'",\\n"ScheduleDeparture": "'+ cSD +'",\\n"ActualDeparture": "'+ cAD +'",\\n"Day": "'+ day +'",\\n"KM": "'+ km +'",\\n"DelayInDeparture": "'+ cDelayDep +'",\\n"Main" : "Y"}';

                } else {
                    var cStn = rows[i].getElementsByClassName('st-name')[0].innerHTML.split(' (')[0];
                    var cCode = '';
                    var cSA = rows[i].getElementsByTagName('td')[2].getElementsByTagName('div')[0].innerText;
                    var cAA = '-';
                    var cSD = '-';
                    var cAD = '-';
                    var cDelayDep = '-';
                    var day = rows[i].getElementsByTagName('td')[1].getElementsByTagName('div')[1].innerText.split(' (')[1].split('day ')[1].split(')')[0];
                    var km = rows[i].getElementsByTagName('td')[1].getElementsByTagName('div')[1].innerText.split(' (')[0];
                    try {
                        cDelayDep = rows[i].getElementsByTagName('td')[4].getElementsByTagName('div')[0].innerText;
                    } catch {
                        
                    }
                    var intmStn = '0';

                    var passed = 'COMING';
                    if(rows[i].getElementsByTagName('img').length > 0) {
                        if(liveIndex == -1) {
                            liveIndex = prevLiveIndex;
                        }
                        if(rows[i].getElementsByTagName('td')[0].getElementsByTagName('div').length == 3) {
                            passed = 'HALT';
                        } else {
                            passed = 'BETWEEN';
                        }
                    } else {
                        if(rows[i].getElementsByTagName('td')[0].getElementsByTagName('div')[0].getAttribute('class').indexOf('trainArrived') != -1) {
                            passed = 'PASSED';
                        }
                    }
                    json += '\\n{\\n"StationName": "'+ cStn +'",\\n"StationCode": "'+ cCode +'",\\n"Passed": "'+ passed +'",\\n"ScheduleArrival": "'+ cSA +'",\\n"ActualArrival": "'+ cAA +'",\\n"InterStn": "'+ intmStn +'",\\n"ScheduleDeparture": "'+ cSD +'",\\n"ActualDeparture": "'+ cAD +'",\\n"Day": "'+ day +'",\\n"KM": "'+ km +'",\\n"DelayInDeparture": "'+ cDelayDep +'",\\n"Main" : "N"}';
                }
                if(i != rows.length - 1) {
                    json += ',';
                }
            }
            json += '\\n],\\n"LiveIndex" : "'+ liveIndex +'"\\n}';

            var blocks = document.getElementsByTagName('ul')[1].getElementsByTagName('li');
            var dates1 = '';
            for(i=0;i<blocks.length;i++) {
                dates1 += blocks[i].innerText + '|';
            }
            Step.postMessage(dates1);
            Step.postMessage(json + '|||0');
        }

        var loaderFlag = 0;
        var loadInterval = setInterval(function() {
            if(document.getElementsByTagName('snack-bar-container').length > 0) {
                if(document.getElementsByTagName('snack-bar-container')[0].innerHTML.indexOf('Unable to get train details') >= 0) {
                    clearInterval(loadInterval);
                    Step.postMessage('|||0');
                    return;
                }
            }
            if(loaderFlag == 0) {
                if(document.getElementsByTagName('loader').length > 0) {
                    loaderFlag = 1;
                }
            } else if(loaderFlag == 1) {
                if(document.getElementsByTagName('loader').length == 0) {
                    clearInterval(loadInterval);
                    rows = document.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
                    c1 = rows.length;
                    don(0);
                }
            }
        }, 10);
      ''';
      webViewController.runJavaScript(jsCode);
    }
  }

  void setData(String data, int method) {
    setState(() {
      currentDay = 0;
      liveIndexToScroll = -1;
      trainDateAfterCurrentStn = false;
      dayChangeCount = 0;
      previousPM = false;
      locationMsg = '';
      notStarted = true;
      reachedDestination = true;

      bool failed = false;
      try {
        if (data.isEmpty) {
          failed = true;
        } else {
          Map<String, dynamic> obj = json.decode(data);
          String response = obj['ResponseCode'].toString();
          int liveIndex = int.parse(obj['LiveIndex'].toString());

          if (response != '200') {
            failed = true;
          } else {
            trainRoute.clear();
            trainRoute.add(
              TrainStatus(
                stationName: '',
                stnCode: '',
                scheduleArrival: '',
                actualArrival: '',
                scheduleDeparture: '',
                actualDeparture: '',
                delayInDeparture: '',
              ),
            );
            trainRouteAll.add(
              TrainStatus(
                stationName: '',
                stnCode: '',
                scheduleArrival: '',
                actualArrival: '',
                scheduleDeparture: '',
                actualDeparture: '',
                delayInDeparture: '',
              ),
            );

            List<dynamic> trainsArr = obj['TrainRoute'];
            for (int i = 0; i < trainsArr.length; i++) {
              Map<String, dynamic> trainData = trainsArr[i];

              String stnName = trainData['StationName'] ?? '';
              String stnCode = trainData['StationCode'] ?? '';
              String schArrival = trainData['ScheduleArrival'] ?? '';
              String actArrival = trainData['ActualArrival'] ?? '';
              String schDep = trainData['ScheduleDeparture'] ?? '';
              String actDep = trainData['ActualDeparture'] ?? '';
              String delayDep = trainData['DelayInDeparture'] ?? '';
              String passed = trainData['Passed'] ?? '';
              String intmStations = trainData['InterStn'] ?? '0';
              String isIntm = trainData['Main'] ?? 'Y';
              String distance = trainData['KM'] ?? '';

              if (!statusEventTracked) {
                statusEventTracked = true;
              }

              if (i == trainsArr.length - 1 && passed == 'BETWEEN') {
                passed = 'PASSED';
                liveIndexToScroll = i;
              }

              if (passed != 'COMING') {
                notStarted = false;
              }
              if (passed != 'PASSED') {
                reachedDestination = false;
              }

              // Handle delay calculation
              try {
                if (delayDep.toLowerCase().contains('no delay')) {
                  actDep = schDep;
                  actArrival = schArrival;
                } else {
                  // Complex delay calculation logic would go here
                  // For now, keeping it simple
                }
              } catch (e) {
                // Handle error
              }

              if (i == liveIndex) {
                passed = 'BETWEEN';
                liveStation = stnCode;
                liveIndexToScroll = trainRoute.length - 1;
              }

              if (passed == 'BETWEEN') {
                locationMsg = "Departed from $stnName at $actDep";
                if (delayDep.toLowerCase().contains('no delay')) {
                  locationMsg += ". Train running on time";
                } else {
                  locationMsg += ". Delayed by $delayDep";
                }
                if (liveIndexToScroll == -1) {
                  liveIndexToScroll = trainRoute.length - 1;
                }
              } else if (passed == 'HALT' && locationMsg.isEmpty) {
                locationMsg = "Reached $stnName at $actArrival";
                if (delayDep.toLowerCase().contains('no delay')) {
                  locationMsg += ". Train running on time";
                } else {
                  locationMsg += ". Delayed by $delayDep";
                }
                if (liveIndexToScroll == -1) {
                  liveIndexToScroll = trainRoute.length - 1;
                }
              }

              TrainStatus route = TrainStatus(
                stationName: stnName,
                stnCode: stnCode,
                scheduleArrival: schArrival,
                actualArrival: actArrival,
                scheduleDeparture: schDep,
                actualDeparture: actDep,
                delayInDeparture: delayDep,
                passed: passed,
              );

              route.setDay(int.tryParse(trainData['Day'] ?? '0') ?? 0);
              route.setIntermediate(isIntm == 'N');
              route.setIntmStations(int.tryParse(intmStations) ?? 0);
              route.setDistance(distance);
              route.setExpanded(false);
              trainRouteAll.add(route);

              if (isIntm == 'Y') {
                trainRoute.add(route);
              }

              if (i == trainsArr.length - 1) {
                if (notStarted) {
                  locationMsg = "Not started from source";
                } else if (reachedDestination) {
                  locationMsg = "Reached destination $stnName";
                }
              }
            }

            // Show the UI elements after data is loaded
            hideLoader();
            showList();
            if (locationMsg.isNotEmpty) {
              showLastLocation(locationMsg);
            }

            // Schedule scroll after build
            Future.delayed(const Duration(milliseconds: 750), () {
              if (liveIndexToScroll != -1) {
                scrollToPosition(liveIndexToScroll);
              }
            });
          }
        }
      } catch (e) {
        failed = true;
      }

      if (failed) {
        // Handle error
        hideLoader();
        Navigator.pop(context);
      }
    });
  }

  void setDates(String dates) {
    trainDates = dates;
    List<String> parts = dates.split('|');
    if (parts.isNotEmpty) {
      setState(() {
        dateText = parts[parts.length - 1];
      });
    }
  }

  void scrollToPosition(int position) {
    // Implement scroll logic if using ScrollController
  }

  void setDate(String date, int index) {
    setState(() {
      dateText = date;
      isLoading = true;
    });

    departedOrReached = 0;
    liveIndexToScroll = -1;

    String script =
        '''
      var liveIndex = -1;
      function createJson() {
        // Same JSON creation logic as above
        Step.postMessage(json + '|||1');
      }
      function aish() {
        var dates = document.getElementsByTagName('ul')[1].getElementsByTagName('li');
        dates[$index].click();
        var changeInterval = setInterval(function() {
            var dc = dates[$index].getElementsByTagName('div')[0].getAttribute('class');
            if(dc.indexOf('active') != -1) {
                createJson();
                clearInterval(changeInterval);
            }
        }, 100);
      }
      aish();
    ''';
    webViewController.runJavaScript(script);
  }

  void loadBanner() {
    bannerAd = BannerAd(
      adUnitId: 'your-ad-unit-id', // Replace with actual ad unit ID
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (Ad ad) {
          setState(() {});
        },
        onAdFailedToLoad: (Ad ad, LoadAdError error) {
          ad.dispose();
        },
      ),
    );
    bannerAd?.load();
  }

  void scrollListView(int index) {
    if (liveIndexToScroll == -1) {
      // Implement scroll logic
    }
  }

  void setLiveIndex(int index, String currStatus) {
    if (liveIndexToScroll == -1) {
      liveIndexToScroll = index;
      setState(() {
        locationMsg = currStatus;
      });
      scrollToPosition(liveIndexToScroll);
    }
  }

  bool isStationPresent(String stnCode) {
    for (TrainStatus s in trainRoute) {
      if (s.stnCode == stnCode) {
        return true;
      }
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final args = widget.arguments;
    String tInfo = args['trainInfo'] ?? '';
    if (RegExp(r'^[0-9]+$').hasMatch(tInfo)) {
      tInfo = "Train No : $tInfo";
    }
    trainName = tInfo; // Update train name from arguments

    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A), // primary_color
      body: SafeArea(
        child: Column(
          children: [
            // Toolbar - matches toolbar in XML
            Container(
              width: double.infinity,
              height: kToolbarHeight,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                boxShadow: const [
                  BoxShadow(
                    color: Colors.black26,
                    offset: Offset(0, 4),
                    blurRadius: 4,
                  ),
                ],
              ),
              child: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                title: const Text(
                  'Running Status',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),

            // Header with gradient background - matches header LinearLayout
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(10),
              decoration: const BoxDecoration(
                // Matches @drawable/theme_gradient - purple gradient from image
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF8A2BE2), // Purple from the image
                    Color(0xFF6A1B9A), // Darker purple
                    Color(0xFF4A148C), // Even darker purple
                  ],
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Train name TextView - matches train_name
                  Container(
                    margin: const EdgeInsets.all(5),
                    child: Text(
                      trainName,
                      style: const TextStyle(
                        color: Colors.white, // textColor="#fff"
                        fontSize: 16, // textSize="16sp"
                        fontWeight: FontWeight.bold, // textStyle="bold"
                      ),
                    ),
                  ),

                  // Date filter dropdown - matches dateFilter TextView with spinner_theme
                  Container(
                    width: 220,
                    // layout_width="220dp"
                    height: 40,
                    // layout_height="40dp"
                    margin: const EdgeInsets.only(bottom: 5),
                    // layout_marginBottom="5dp"
                    decoration: BoxDecoration(
                      // Matches @drawable/spinner_theme - dark background with border
                      color: const Color(0xFF2D2D2D),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.white24, width: 1),
                    ),
                    child: InkWell(
                      onTap: () => _showDatePickerDialog(),
                      child: Container(
                        padding: const EdgeInsets.only(
                          left: 15, // paddingStart="15dp"
                          right: 20, // paddingEnd="20dp"
                          top: 5, // paddingTop="5dp"
                          bottom: 5, // paddingBottom="5dp"
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                dateText,
                                style: const TextStyle(
                                  color: Colors.white, // textColor="#fff"
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            const Icon(
                              Icons.arrow_drop_down,
                              color: Colors.white,
                              size: 20,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Main content RelativeLayout equivalent
            Expanded(
              child: Stack(
                children: [
                  // ListView - matches android:id/list
                  if (isListVisible)
                    Positioned.fill(
                      bottom: 60,
                      // Space for ad_view_container and lastLocation
                      child: Container(
                        color: const Color(0xFF1A1A1A),
                        // background="@color/primary_color"
                        child: ListView.builder(
                          itemCount: trainRoute.length,
                          itemBuilder: (context, index) {
                            return adapter.getView(index, context);
                          },
                        ),
                      ),
                    ),

                  // ProgressBar loader - matches loader
                  if (isLoading)
                    Center(
                      child: CircularProgressIndicator(
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          Color(
                            0xFF8A2BE2,
                          ), // indeterminateTint="@color/secondary_color"
                        ),
                        strokeWidth: 3,
                      ),
                    ),

                  // Last location TextView - matches lastLocation
                  if (isLastLocationVisible && locationMsg.isNotEmpty)
                    Positioned(
                      bottom: 50, // Above ad_view_container
                      left: 0,
                      right: 0,
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(10),
                        // padding="10dp"
                        color: const Color(0xFF8A2BE2),
                        // background="@color/secondary_color"
                        child: Text(
                          locationMsg,
                          style: const TextStyle(
                            color: Colors.white, // textColor="#fff"
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),

                  // Ad view container - matches ad_view_container
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      width: double.infinity,
                      height: 50, // Standard banner ad height
                      color: const Color(0xFF2D2D2D),
                      child: bannerAd != null
                          ? AdWidget(ad: bannerAd!)
                          : const Center(
                              child: Text(
                                'Ad Space',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Date picker dialog - matches spinner behavior
  void _showDatePickerDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2D2D2D),
          title: const Text(
            'Select Date',
            style: TextStyle(color: Colors.white),
          ),
          content: Container(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: dateOptions.length,
              itemBuilder: (context, index) {
                return ListTile(
                  title: Text(
                    dateOptions[index],
                    style: const TextStyle(color: Colors.white),
                  ),
                  selected: dateText == dateOptions[index],
                  selectedTileColor: const Color(0xFF8A2BE2).withOpacity(0.3),
                  onTap: () {
                    setState(() {
                      dateText = dateOptions[index];
                    });
                    Navigator.of(context).pop();
                    // Backend logic will be added to refresh data based on date
                    _onDateFilterChanged();
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  // Methods to control UI state - will be called from backend logic
  void showLoader() {
    setState(() {
      isLoading = true;
      isListVisible = false;
      isLastLocationVisible = false;
    });
  }

  void hideLoader() {
    setState(() {
      isLoading = false;
    });
  }

  void showList() {
    setState(() {
      isLoading = false;
      isListVisible = true;
    });
  }

  void hideList() {
    setState(() {
      isListVisible = false;
    });
  }

  void showLastLocation(String location) {
    setState(() {
      locationMsg = location;
      isLastLocationVisible = true;
    });
  }

  void hideLastLocation() {
    setState(() {
      isLastLocationVisible = false;
    });
  }

  void updateTrainName(String name) {
    setState(() {
      trainName = name;
    });
  }

  // Date filter change handler - will trigger backend refresh
  void _onDateFilterChanged() {
    // Show loading and refresh data based on selected date
    showLoader();
    // Backend logic will be implemented later
  }

  @override
  void dispose() {
    bannerAd?.dispose();
    super.dispose();
  }
}

// RunningStatusAdapter equivalent - converted from Java
class RunningStatusAdapter {
  final _RunningStatusState activity;
  final List<TrainStatus> trainRoute;
  final List<TrainStatus> trainRouteAll;
  int isLive = 0;
  int maxDay = 1;

  RunningStatusAdapter(this.activity, this.trainRoute, this.trainRouteAll);

  // Equivalent of getView method
  Widget getView(int i, BuildContext context) {
    if (i == 0) {
      // First item - equivalent to item_imageview layout
      return Container(
        padding: const EdgeInsets.all(16.0),
        child: GestureDetector(
          onTap: () {
            // Equivalent of railofy ad click
            // Navigate to food ordering page
          },
          child: Image.asset(
            'assets/images/cleanest_food.png', // Replace with actual asset
            height: 120,
            fit: BoxFit.contain,
          ),
        ),
      );
    }

    TrainStatus status = trainRoute[i];
    return _buildStationItem(status, i, context);
  }

  Widget _buildStationItem(
    TrainStatus status,
    int position,
    BuildContext context,
  ) {
    String delayedy = status.getDelayDep();
    Color textColor = Colors.white;

    // Color logic from Java
    if (delayedy.contains("-")) {
      textColor = Colors.white;
    } else if (!delayedy.toLowerCase().contains("no delay")) {
      textColor = Colors.red;
      delayedy += " delay";
    } else {
      textColor = Colors.green;
    }

    String passed = status.getPassed();
    bool showLiveTrain = passed == "BETWEEN" && position != trainRoute.length;
    bool showHalt = passed == "HALT";

    // Set live index if not set
    if (showLiveTrain && _RunningStatusState.liveIndexToScroll == -1) {
      _RunningStatusState.liveIndexToScroll = position;
    }

    return Container(
      color: status.isIntermediate
          ? const Color(0xFF333333)
          : const Color(0xFF111111),
      child: Column(
        children: [
          // Main station row
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 12.0,
            ),
            child: Row(
              children: [
                // Train line indicator
                Container(
                  width: 4,
                  height: 60,
                  margin: const EdgeInsets.only(right: 16),
                  decoration: BoxDecoration(
                    color: _getTrainLineColor(passed, position),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Stack(
                    children: [
                      // Live train indicator
                      if (showLiveTrain)
                        Positioned(
                          top: 20,
                          left: -6,
                          child: Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                          ),
                        ),
                      // Halt indicator
                      if (showHalt)
                        Positioned(
                          top: 20,
                          left: -4,
                          child: Container(
                            width: 12,
                            height: 12,
                            decoration: const BoxDecoration(
                              color: Colors.orange,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Station details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Station name and code
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                status.getStnName(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                status.getStnCode(),
                                style: const TextStyle(
                                  color: Colors.grey,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          // Day and distance
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                "Day ${status.getDay()}",
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                              Text(
                                status.getDistance(),
                                style: const TextStyle(
                                  color: Colors.grey,
                                  fontSize: 10,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Timings row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Arrival times
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                "Arrival",
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 10,
                                ),
                              ),
                              Text(
                                status.getSchArrival(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                              Text(
                                status.getActArrival(),
                                style: TextStyle(
                                  color: textColor,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),

                          // Departure times
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                "Departure",
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 10,
                                ),
                              ),
                              Text(
                                status.getSchDep(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                              Text(
                                status.getActDep(),
                                style: TextStyle(
                                  color: textColor,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),

                          // Delay info
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              const Text(
                                "Status",
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 10,
                                ),
                              ),
                              Text(
                                delayedy.toLowerCase(),
                                style: TextStyle(
                                  color: textColor,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Intermediate stations button
          if (!status.isIntermediate && status.getIntmStations() > 0)
            GestureDetector(
              onTap: () => expandList(position, status.isExpanded),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "${status.getIntmStations()} stations ▼",
                      style: const TextStyle(color: Colors.blue, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Color _getTrainLineColor(String passed, int position) {
    // Equivalent of train line image resource logic
    if (position == 1) {
      switch (passed) {
        case "HALT":
          return Colors.orange;
        case "BETWEEN":
          return Colors.green;
        case "PASSED":
          return Colors.blue;
        case "COMING":
          return Colors.grey;
        default:
          return Colors.grey;
      }
    } else if (position == trainRoute.length - 1) {
      if (passed == "COMING") {
        return Colors.grey;
      } else {
        return Colors.blue;
      }
    } else {
      switch (passed) {
        case "BETWEEN":
          return Colors.green;
        case "PASSED":
          return Colors.blue;
        case "HALT":
          return Colors.orange;
        default:
          return Colors.grey;
      }
    }
  }

  void expandList(int position, bool isExpanded) {
    int findFlag = 0;
    int j = 1;
    String stnCode = trainRoute[position].getStnCode();

    for (TrainStatus train in trainRouteAll) {
      if (findFlag == 0) {
        if (train.getStnCode() == stnCode) {
          findFlag = 1;
        }
      } else {
        if (!isExpanded) {
          if (train.isIntermediate) {
            trainRoute.insert(position + j, train);
            j++;
          } else {
            trainRoute[position].setExpanded(true);
            if (trainRoute[position].getPassed() == "BETWEEN") {
              trainRoute[position].setPassed("PASSED");
            }
            activity.setState(() {});
            break;
          }
        } else {
          if (train.isIntermediate) {
            trainRoute.removeAt(position + 1);
          } else {
            trainRoute[position].setExpanded(false);
            if (trainRoute[position].getStnCode() ==
                _RunningStatusState.liveStation) {
              trainRoute[position].setPassed("BETWEEN");
            }
            activity.setState(() {});
            break;
          }
        }
      }
    }
  }

  bool isActNextDay(String prevTime, String nextArr) {
    DateTime prev = DateTime.now();
    DateTime next = DateTime.now().add(const Duration(days: 1));

    List<String> nextParts = nextArr.split(":");
    int hour = int.parse(nextParts[0]);
    int min = int.parse(nextParts[1]);
    next = DateTime(next.year, next.month, next.day, hour, min);

    // For prev time
    List<String> prevParts = prevTime.split(":");
    hour = int.parse(prevParts[0]);
    min = int.parse(prevParts[1]);
    prev = DateTime(prev.year, prev.month, prev.day, hour, min);
    prev = prev.add(const Duration(hours: 9));

    return prev.isAfter(next);
  }
}
