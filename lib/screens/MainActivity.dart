/*
 * CRITICAL FIXES APPLIED - MainActivity Flutter vs Java Comparison
 * 
 * ✅ FIX 1: Static Step Controller - Added StepController class for static workflow state
 * ✅ FIX 2: Variable Initialization - Fixed nullable variables causing JS injection errors  
 * ✅ FIX 3: Collection Initialization - Fixed Map/List initialization to prevent null errors
 * ✅ FIX 4: JavaScript Interface - Updated methods to use StepController for static state
 * ⚠️  FIX 5: WebView Setup - TODO: Adapt JS interface to actual WebView implementation
 * 
 * These fixes resolve workflow issues after auto login by ensuring proper state management.
 */

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:path/path.dart';
import 'package:provider/provider.dart';
import 'package:quick_tatkal_v2/core/AppConstants.dart';
import 'package:quick_tatkal_v2/core/utils/APIConsts.dart';
import 'package:quick_tatkal_v2/utils/CaptchaSolver.dart';
import '../core/state/booking_state.dart';
import '../services/webview_manager.dart';
import '../services/automation_engine.dart';
import '../services/script_generator.dart';
import '../services/database_manager.dart';
import '../services/captcha_service.dart';
import '../core/network/advanced_webview.dart';
import 'dart:developer' as developer;

class MainActivity extends StatefulWidget {
  final String? formName;
  final String language;
  final bool autoLogin;

  const MainActivity({
    Key? key,
    this.formName,
    this.language = 'ENG',
    this.autoLogin = false,
  }) : super(key: key);

  @override
  _MainActivityState createState() => _MainActivityState();
}

class _MainActivityState extends State<MainActivity> {
  final GlobalKey<AdvancedWebViewState> _webViewKey = GlobalKey();
  
  late WebViewManager _webViewManager;
  late AutomationEngine _automationEngine;
  late DatabaseManager _databaseManager;
  late CaptchaService _captchaService;
  late ScriptGenerator _scriptGenerator;
  
  String? _currentUrl;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _loadInitialData();
  }

  void _initializeServices() {
    _webViewManager = WebViewManager(_webViewKey);
    _scriptGenerator = ScriptGenerator();
    _databaseManager = DatabaseManager();
    _captchaService = CaptchaService(_webViewManager);
    
    final bookingState = context.read<BookingState>();
    _automationEngine = AutomationEngine(
      bookingState,
      _webViewManager,
      _scriptGenerator,
    );
  }

  Future<void> _loadInitialData() async {
    if (widget.formName != null) {
      final bookingState = context.read<BookingState>();
      await _databaseManager.loadBookingData(widget.formName!, bookingState);
      
      if (widget.autoLogin) {
        _automationEngine.startAutomation();
      }
    }
  }

  void _onPageFinished(String url) {
    setState(() => _currentUrl = url);
    
    // Detect screen type and trigger appropriate automation
    if (url.contains('login')) {
      _automationEngine.handleScreenChange('login');
    } else if (url.contains('train-search')) {
      _automationEngine.handleScreenChange('journey');
    } else if (url.contains('train-list')) {
      _automationEngine.handleScreenChange('trains');
    }
  }

  void _onStepMessage(String method, List<dynamic> args) {
    switch (method) {
      case 'solveCaptcha':
        if (args.length >= 3) {
          _captchaService.solveCaptcha(
            args[0]?.toString() ?? '',
            int.tryParse(args[1]?.toString() ?? '0') ?? 0,
            int.tryParse(args[2]?.toString() ?? '0') ?? 0,
          );
        }
        break;
      case 'fillJourneyDetails':
        if (args.length >= 3) {
          final bookingState = context.read<BookingState>();
          bookingState.updateJourneyDetails(
            from: args[0]?.toString() ?? '',
            to: args[1]?.toString() ?? '',
            date: args[2]?.toString() ?? '',
          );
        }
        break;
      case 'updateAvailability':
        if (args.isNotEmpty) {
          final bookingState = context.read<BookingState>();
          bookingState.updateAvailability(args[0]?.toString() ?? '');
        }
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.black,
        title: Consumer<BookingState>(
          builder: (context, state, child) {
            return Text(
              state.currentTime.isNotEmpty ? state.currentTime : 'Quick Tatkal',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            );
          },
        ),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'restart', child: Text('Restart')),
              const PopupMenuItem(value: 'stop_automation', child: Text('Stop Automation')),
              const PopupMenuItem(value: 'start_automation', child: Text('Start Automation')),
            ],
          ),
        ],
      ),
      body: AdvancedWebView(
        key: _webViewKey,
        url: 'https://www.irctc.co.in/nget/train-search',
        title: "IRCTC",
        appBarColor: Colors.blueAccent,
        onPageFinished: _onPageFinished,
        onProgress: (_) {},
        onPageStarted: (url) => setState(() => _currentUrl = url),
        onStepMessage: _onStepMessage,
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'restart':
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => MainActivity(
              formName: widget.formName,
              language: widget.language,
              autoLogin: widget.autoLogin,
            ),
          ),
        );
        break;
      case 'stop_automation':
        _automationEngine.stopAutomation();
        break;
      case 'start_automation':
        _automationEngine.startAutomation();
        break;
    }
  }

  @override
  void dispose() {
    _automationEngine.stopAutomation();
    super.dispose();
  }
}
        onStepMessage: _onStepMessage,
      ),
    );
  }

  void _handleScreenDetected(String screenType) {
    developer.log('Screen detected: $screenType');

    switch (screenType) {
      case 'login':
        _automationStep = 1;
        break;
      case 'journey':
        _automationStep = 2;
        break;
      case 'trains':
        _automationStep = 3;
        break;
      case 'passenger':
        _automationStep = 4;
        break;
      case 'payment':
        _automationStep = 5;
        break;
      case 'review':
        _automationStep = 6;
        break;
    }
    setState(() {});
  }

  void _handleAutomationCompleted(String action) {
    developer.log('Automation completed: $action');
    printCenter('✅ $action completed successfully');
  }

  void startClock() {
    if (clock != null) {
      clock!.cancel();
      clock = null;
    }

    initializeClockTask();

    clock = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted) {
      } else {
        timer.cancel();
        clock = null;
      }
    });
  }

  void initializeWaitTimerTask() {
    developer.log(
      "initializeWaitTimerTask - passenger wait timer task initialized",
    );
  }

  void initializeClockTask() {
    if (clockTask != null) {
      clockTask!.cancel();
    }
    clockTask = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted) {
        DateTime calendar = DateTime.now();

        /*if(timeDifferenceValue != 0) {
          calendar = calendar.add(Duration(milliseconds: timeDifferenceValue));
        }*/

        String hour = ((calendar.hour % 12 == 0) ? 12 : calendar.hour % 12)
            .toString()
            .padLeft(2, '0');
        String minute = calendar.minute.toString().padLeft(2, '0');
        String second = calendar.second.toString().padLeft(2, '0');
        String ampm = calendar.hour >= 12 ? 'PM' : 'AM';
        String formattedTime = "$hour:$minute:$second $ampm";

        String avlText = "";
        if (availability != "") {
          avlText = " | $availability";
        }

        setState(() {
          _currentTime = formattedTime + avlText;
        });
      } else {
        timer.cancel();
        clockTask = null;
      }
    });
  }

  void _updateClock() {
    final now = DateTime.now();
    final hour = ((now.hour % 12 == 0) ? 12 : now.hour % 12).toString().padLeft(
      2,
      '0',
    );
    final minute = now.minute.toString().padLeft(2, '0');
    final second = now.second.toString().padLeft(2, '0');
    final ampm = now.hour >= 12 ? 'PM' : 'AM';
    final formattedTime = "$hour:$minute:$second $ampm";
    final avlText = availability.isNotEmpty ? " | $availability" : "";
    setState(() {
      _currentTime = "$formattedTime$avlText";
    });
  }

  static const String THIRD_PARTY_APP_PACKAGE = "cris.org.in.prs.ima";

  String username = '';
  String password = '';
  String fromStation = '';
  String toStation = '';
  String journeyDate = '';
  String quota = '';
  int fareLimit = 0;
  String trainNo = '';
  String travelClass = '';
  String boardingStation = '';
  String clickOption = '';

  Map<String, int> quotaMap = {
    'GN': 0,
    'LD': 1,
    'SS': 2,
    'HP': 3,
    'DP': 4,
    'TQ': 5,
    'PT': 6,
  };
  List<PojoPassenger.Passenger> passenger = [];
  List<PojoChild.Child> child = [];

  String mobileNo = '';
  String addrLine1 = '';
  String addrLine2 = '';
  String addrLine3 = '';
  String addrPin = '';
  String addrCity = '';
  String addrPo = '';

  int paymentMode = 0;
  bool autofillCaptcha = false;
  int insurance = 0;
  bool autoUpgrade = true;
  bool onlyConfirm = true;
  int bookingOption = 0;
  bool coachPreferred = false;
  String coachId = "";
  bool paymentAutofill = false;
  String paymentChoice = '';
  static String bankChoice = '';
  String cardNo = '';
  String expMon = '';
  String expYr = '';
  String cvv = '';
  String pin = '';
  String cardHolder = '';
  String cardType = 'MC';
  String staticPassword = '';
  int delaySec = 52;

  int sbiOpt = 0;
  String bankingType = 'R';
  String corpId = "";
  String bUserName = "";
  String bPassword = "";

  String mMobile = "";
  String email = "";
  String addr1 = "";
  String addr2 = "";
  String city = "";
  String state = "";
  String country = "";
  String zip = "";
  String mBank = "";

  late String gstIN;
  late String gstName;
  late String gstFlat;
  late String gstPin;
  late String gstCity;
  late String gstStreet;
  late String gstArea;

  late String vpa;
  late bool autoOpenUpi;
  late String podMobile;

  late bool sqAuto;
  late String sqEmail;
  late String sqMobile;
  late String sqDob;
  late int sqEwallet;

  String wUserName = "";
  String wPassword = "";

  String eWalletPass = "";

  static int step = 0;
  late bool incrementFlag;

  late Widget progressBar;

  late dynamic webView;
  late int pCount;
  late int cCount;

  Timer? waitTimerTask;
  Timer? passWaitTimerTask;
  Timer? countTimerTask;
  Timer? exitTimerTask;

  late bool replanClicked = false;
  late bool languageChanged = false;
  late bool atLeastOneSenior = false;
  late bool oneTimeCaptchaFlag = false;
  late bool eventLogged = false;
  late bool ticketDone = false;
  late bool sFinished = false;
  late bool passFillDone = false;
  late bool shouldStartWaitTimer = false;
  late bool hideKeyboard = false;
  late bool captchaTryDone = false;

  late double roundTime = 0.0;
  late DateTime passPageRefreshTime;
  late dynamic waitDialog;
  late dynamic passTatkalWaitDialog;
  late dynamic pymtWaitDialog;

  late int timeStart = 0;
  late bool ticketSavedToServer = false;
  late bool lastCaptcha = false;
  late int autofillsDone = 0;
  late int loginCaptchaRetries = 0;
  late int bookingCaptchaRetries = 0;
  late int captchaSaveCount = 1;
  late int hdfcCardTries = 0;
  late int screenHeight = 0;
  late int otpTimerCount = 0;
  late String otpMethod = "";

  int waitForPTResponse = 0;
  late bool clickLoginOnce = false;

  Timer? timer;
  Timer? clock;
  Timer? clockTask;
  Timer? waitTimer;
  Timer? passWaitTimer;
  Timer? exitTimer;
  Timer? passTimer;
  Timer? otpTimer;
  Timer? pymtWaitTimer;

  Future<List<int>> generateKey(String keyString) async {
    return keyString.codeUnits;
  }

  Future<List<int>> decodeFile(List<int> key, dynamic blob) async {
    if (blob == null) return [];
    return blob is List<int> ? blob : [];
  }

  String? getCurrentWebViewUrl() {
    return _currentUrl;
  }

  void loadWebViewUrl(String jsCode) {
    try {
      if (mounted && _webViewKey.currentState != null) {
        if (jsCode.startsWith('javascript:')) {
          String cleanJs = jsCode.substring('javascript:'.length);
          _webViewKey.currentState!.runJavaScript(cleanJs);
        } else if (jsCode.startsWith('http')) {
          _webViewKey.currentState!.loadUrl(jsCode);
        } else {
          _webViewKey.currentState!.runJavaScript(jsCode);
        }
        developer.log("WebView JavaScript executed: ${jsCode.length} chars");
      } else {
        developer.log("WebView not ready or widget not mounted");
      }
    } catch (e) {
      developer.log("Error executing WebView JavaScript: $e");
    }
  }

  String _jsEscape(String? s) {
    if (s == null) return '';
    return s
        .replaceAll(r'\\', r'\\\\')
        .replaceAll("'", r"\\'")
        .replaceAll('\n', '')
        .replaceAll('\r', '');
  }

  Future<void> initializeVariables() async {
    ticketSavedToServer = false;

    pageFinishTime = 0;
    language = '';
    mFirebaseAnalytics = null;
    paymentFailed = 0;
    timeDifferenceValue = 0;
    irctcSeconds = -1;
    otpReceiver = null;
    htmlString = "";
    continueBtn = ElevatedButton(
      onPressed: () => _handleContinueClick(),
      child: Text('Continue'),
    );
    movedToHDFCPayment = false;
    extraHeaders = HashMap<String, String>();
    waitSecondsRemaining = 25;
    pymtWaitSecondsRemaining = 0;
    hdfcBitmap = null;
    mixpanel = null;
    bookingResponse = "NA";

    replanClicked = false;
    languageChanged = false;
    atLeastOneSenior = false;
    oneTimeCaptchaFlag = false;
    eventLogged = false;
    ticketDone = false;
    sFinished = false;
    passFillDone = false;
    shouldStartWaitTimer = false;
    hideKeyboard = false;
    captchaTryDone = false;
    timeStart = 0;
    ticketSavedToServer = false;
    lastCaptcha = false;
    autofillsDone = 0;
    loginCaptchaRetries = 0;
    bookingCaptchaRetries = 0;
    captchaSaveCount = 1;
    hdfcCardTries = 0;
    otpTimerCount = 0;
    otpMethod = "";
    waitForPTResponse = 0;
    clickLoginOnce = false;
    roundTime = 0.0;

    quotaMap["GN"] = 0;
    quotaMap["LD"] = 1;
    quotaMap["SS"] = 2;
    quotaMap["HP"] = 3;
    quotaMap["DP"] = 4;
    quotaMap["TQ"] = 5;
    quotaMap["PT"] = 6;

    paymentChoiceMap["DEBIT_CARD"] = "Debit Card with PIN";
    paymentChoiceMap["NETBANKING"] = "Netbanking";
    paymentChoiceMap["SCAN_AND_PAY"] = "Bharat QR / Scan & Pay";
    paymentChoiceMap["CASH_CARD"] = "Wallets / Cash Card";
    paymentChoiceMap["MULTIPLE_GATEWAY"] = "Multiple Payment Service";
    paymentChoiceMap["E_WALLET"] = "IRCTC eWallet";
    paymentChoiceMap["COD"] = "Pay-On-Delivery/Pay later";
    paymentChoiceMap["CREDIT_CARD"] =
        "Payment Gateway / Credit Card / Debit Card";
    paymentChoiceMap["IRCTC_IPAY"] = "IRCTC iPay (Credit Card/Debit Card/UPI)";

    paymentChoiceMapPG["IRCTC_IPAY"] = 0;
    paymentChoiceMapPG["MULTIPLE_GATEWAY"] = 1;
    paymentChoiceMapPG["NETBANKING"] = 2;
    paymentChoiceMapPG["SCAN_AND_PAY"] = 3;
    paymentChoiceMapPG["CASH_CARD"] = 4;
    paymentChoiceMapPG["E_WALLET"] = 5;
    paymentChoiceMapPG["COD"] = 4;
    paymentChoiceMapPG["CREDIT_CARD"] = 3;

    paymentChoiceMapUPI["UPI_VPA"] = 0;
    paymentChoiceMapUPI["MULTIPLE_GATEWAY"] = 1;

    try {
      Database db = await MainDB.instance.database;
      List<Map<String, dynamic>> result = await db.rawQuery(
        "select * from ${MainDB.tableName} where FORM_NAME = ?",
        [formName],
      );

      List<int> key = await generateKey("akanksha");

      if (result.isNotEmpty) {
        var cur = result[0];

        username = (cur['USERNAME'] as String? ?? '').replaceAll(" ", "");

        if (cur.containsKey('PASSWORD_BLOB') && cur['PASSWORD_BLOB'] != null) {
          List<int> passwordBytes = await decodeFile(key, cur['PASSWORD_BLOB']);
          password = String.fromCharCodes(passwordBytes);
        } else if (cur.containsKey('PASSWORD') && cur['PASSWORD'] != null) {
          try {
            password = Cryptography.decryptPassword(cur['PASSWORD'] as String);
          } catch (_) {
            String passwordValue = cur['PASSWORD']?.toString() ?? '';
            password = passwordValue;
          }
        }

        fromStation = cur['FROM_STN']?.toString() ?? '';
        toStation = cur['TO_STN']?.toString() ?? '';
        journeyDate = cur['TRVL_DATE']?.toString() ?? '';
        quota = cur['QUOTA']?.toString() ?? '';

        var fareLimitValue = cur['FARE_LIMIT'];
        if (fareLimitValue is int) {
          fareLimit = fareLimitValue;
        } else if (fareLimitValue is String && fareLimitValue.isNotEmpty) {
          fareLimit = int.tryParse(fareLimitValue) ?? 0;
        } else {
          fareLimit = 0;
        }

        SharedPreferences sp = await SharedPreferences.getInstance();
        String clickOption = (sp.getInt('CLICK_VALUE') ?? 0).toString();

        travelClass = cur['TRAVEL_CLASS']?.toString() ?? '';
        trainNo = cur['TRAIN_NO']?.toString() ?? '';

        if (cur['BOARDING_STATION'] != null &&
            cur['BOARDING_STATION'] is String &&
            (cur['BOARDING_STATION'] as String).trim().isNotEmpty &&
            (cur['BOARDING_STATION'] as String).trim().toLowerCase() !=
                "null") {
          boardingStation = cur['BOARDING_STATION'];
        } else {
          boardingStation = '';
        }

        coachId = cur['COACH_ID']?.toString() ?? '';
        coachPreferred =
            (cur['COACH_PREFERRED']?.toString() ?? 'false') == 'true';

        bool vikalp = (cur['VIKALP']?.toString() ?? 'false') == 'true';
        autoUpgrade = (cur['AUTO_UPGRADE']?.toString() ?? 'true') == 'true';

        onlyConfirm = (cur['ONLY_CONFIRM']?.toString() ?? 'true') == 'true';

        var bookingOptionValue = cur['BOOKING_OPTION'];
        if (bookingOptionValue is int) {
          bookingOption = bookingOptionValue;
        } else if (bookingOptionValue is String &&
            bookingOptionValue.isNotEmpty) {
          bookingOption = int.tryParse(bookingOptionValue) ?? 0;
        } else {
          bookingOption = 0;
        }

        mobileNo = cur['MOBILE_NO']?.toString() ?? '';
        paymentChoice = cur['PAYMENT_CHOICE']?.toString() ?? '';
        paymentAutofill =
            (cur['PAYMENT_AUTOFILL']?.toString() ?? 'false') == 'true';

        var delaySecValue = cur['DELAY_SEC'];
        if (delaySecValue is int) {
          delaySec = delaySecValue;
        } else if (delaySecValue is String && delaySecValue.isNotEmpty) {
          delaySec = int.tryParse(delaySecValue) ?? 0;
        } else {
          delaySec = 0;
        }

        {
          var captchaAutofillValue = cur['CAPTCHA_AUTOFILL'];
          if (captchaAutofillValue != null) {
            if (captchaAutofillValue.toString() == '1' ||
                captchaAutofillValue == 1 ||
                captchaAutofillValue == true) {
              autofillCaptcha = true;
              captchaTryDone = APIConsts.CAPTCHA_TRIAL_OPTED == "N";
              developer.log("✅ Captcha autofill ENABLED for form: $formName");
            } else {
              autofillCaptcha = false;
              developer.log("ℹ️ Captcha autofill DISABLED for form: $formName");
            }
          } else {
            autofillCaptcha = true;
            developer.log(
              "🔧 Captcha autofill set to DEFAULT (enabled) for form: $formName",
            );
          }

          if (!autofillCaptcha) {
            developer.log(
              "🔧 TEMP FIX: Force enabling captcha autofill for testing...",
            );
            autofillCaptcha = true;

            try {
              await db.rawUpdate(
                "UPDATE ${MainDB.tableName} SET CAPTCHA_AUTOFILL = ? WHERE FORM_NAME = ?",
                [1, formName],
              );
              developer.log(
                "📝 Database updated: CAPTCHA_AUTOFILL set to 1 for form: $formName",
              );
            } catch (e) {
              developer.log("⚠️ Failed to update database: $e");
            }
          }
        }

        {
          var autoOpenValue = cur['AUTO_OPEN'];
          if (autoOpenValue != null) {
            if (autoOpenValue.toString() == '1' || autoOpenValue == 1) {
              autoOpenUpi = true;
            }
          }
        }

        try {
          var pymtModeValue = cur['WB_PYMT_MODE'];
          if (pymtModeValue is int) {
            paymentMode = pymtModeValue;
          } else if (pymtModeValue is String) {
            paymentMode = int.tryParse(pymtModeValue) ?? 0;
          } else {
            paymentMode = 0;
          }
          if (paymentChoice.isNotEmpty && paymentChoice == "UPI_VPA") {
            paymentMode = 1;
          }
        } catch (e) {}

        List<Map<String, dynamic>> gstResult = await db.rawQuery(
          "select * from GST_TABLE where FORM_NAME = ?",
          [formName],
        );

        if (gstResult.isNotEmpty) {
          var curGst = gstResult[0];
          gstIN = curGst['GST_IN']?.toString() ?? '';
          gstName = curGst['GST_NAME']?.toString() ?? '';
          gstFlat = curGst['GST_FLAT']?.toString() ?? '';
          gstStreet = curGst['GST_STREET']?.toString() ?? '';
          gstPin = curGst['GST_PIN']?.toString() ?? '';
          gstArea = curGst['GST_AREA']?.toString() ?? '';
          gstCity = curGst['GST_CITY']?.toString() ?? '';
        } else {
          gstIN = "";
        }

        List<Map<String, dynamic>> addrResult = await db.rawQuery(
          "select * from ADDRESS_TABLE where FORM_NAME = ?",
          [formName],
        );

        if (addrResult.isNotEmpty) {
          var curAd = addrResult[0];
          addrLine1 = curAd['ADDR1']?.toString() ?? '';
          addrLine2 = curAd['ADDR2']?.toString() ?? '';
          addrLine3 = curAd['ADDR3']?.toString() ?? '';
          addrPin = curAd['PIN']?.toString() ?? '';
          addrCity = curAd['CITY']?.toString() ?? '';
          addrPo = curAd['PO']?.toString() ?? '';
        }

        if (paymentChoice.isNotEmpty) {
          if (paymentChoice == "SCAN_AND_PAY") {
            bankChoice = "Bharat QR(powered by Atom)";
          } else if (paymentChoice == "IRCTC_PREPAID") {
            bankChoice = "IRCTC Union Bank prepaid (RuPay)";
          }
        }

        List<int> cardTypeBytes = await decodeFile(key, cur['CARD_TYPE_BLOB']);
        List<int> bankNameBytes = await decodeFile(key, cur['BANK_NAME_BLOB']);
        List<int> cardNoBytes = await decodeFile(key, cur['CARD_NO_BLOB']);
        List<int> expiryYrBytes = await decodeFile(key, cur['EXPIRY_YR_BLOB']);
        List<int> expiryMonBytes = await decodeFile(
          key,
          cur['EXPIRY_MON_BLOB'],
        );
        List<int> cardHolderBytes = await decodeFile(
          key,
          cur['CARD_HOLDER_BLOB'],
        );

        List<int> corpIdBytes = await decodeFile(key, cur['CORP_ID_BLOB']);
        List<int> sbiBankNameBytes = await decodeFile(
          key,
          cur['SBI_BANK_NAME_BLOB'],
        );
        List<int> netBankNameBytes = await decodeFile(
          key,
          cur['NET_BANK_NAME_BLOB'],
        );
        List<int> netBankingTypeBytes = await decodeFile(
          key,
          cur['NET_BANKING_TYPE_BLOB'],
        );
        List<int> nbUsernameBytes = await decodeFile(
          key,
          cur['NB_USERNAME_BLOB'],
        );
        List<int> nbPasswordBytes = await decodeFile(
          key,
          cur['NB_PASSWORD_BLOB'],
        );

        List<int> walletBytes = await decodeFile(key, cur['WALLET_BLOB']);
        List<int> wUsernameBytes = await decodeFile(
          key,
          cur['W_USERNAME_BLOB'],
        );
        List<int> wPasswordBytes = await decodeFile(
          key,
          cur['W_PASSWORD_BLOB'],
        );

        List<int> txnPasswordBytes = await decodeFile(
          key,
          cur['TXN_PASSWORD_BLOB'],
        );

        List<int> aeMobileBytes = await decodeFile(key, cur['AE_MOBILE_BLOB']);
        List<int> aeEmailBytes = await decodeFile(key, cur['AE_EMAIL_BLOB']);

        List<int> icMobileBytes = await decodeFile(key, cur['IC_MOBILE_BLOB']);
        List<int> icEmailBytes = await decodeFile(key, cur['IC_EMAIL_BLOB']);
        List<int> icAddress1Bytes = await decodeFile(
          key,
          cur['IC_ADDRESS1_BLOB'],
        );
        List<int> icAddress2Bytes = await decodeFile(
          key,
          cur['IC_ADDRESS2_BLOB'],
        );
        List<int> icCityBytes = await decodeFile(key, cur['IC_CITY_BLOB']);
        List<int> icStateBytes = await decodeFile(key, cur['IC_STATE_BLOB']);
        List<int> icCountryBytes = await decodeFile(
          key,
          cur['IC_COUNTRY_BLOB'],
        );
        List<int> icZipBytes = await decodeFile(key, cur['IC_ZIP_BLOB']);
        List<int> icBankBytes = await decodeFile(key, cur['IC_BANK_BLOB']);

        List<int> upiBankBytes = await decodeFile(key, cur['UPI_BANK_BLOB']);
        List<int> vpaBytes = await decodeFile(key, cur['VPA_BLOB']);
        List<int> podBytes = await decodeFile(key, cur['POD_BLOB']);

        List<int> multipleOptBytes = await decodeFile(
          key,
          cur['MULTIPLE_PYMT_CHOICE_BLOB'],
        );

        cardNo = String.fromCharCodes(cardNoBytes);
        expYr = String.fromCharCodes(expiryYrBytes);
        expMon = String.fromCharCodes(expiryMonBytes);
        cardHolder = String.fromCharCodes(cardHolderBytes);

        if (cur['PIN_BLOB'] != null) {
          List<int> pinBytes = await decodeFile(key, cur['PIN_BLOB']);
          pin = String.fromCharCodes(pinBytes);
        }

        if (cur['CVV_BLOB'] != null) {
          List<int> cvvBytes = await decodeFile(key, cur['CVV_BLOB']);
          cvv = String.fromCharCodes(cvvBytes);
        }

        if (cur['STATIC_PASS_BLOB'] != null) {
          List<int> staticBytes = await decodeFile(
            key,
            cur['STATIC_PASS_BLOB'],
          );
          staticPassword = String.fromCharCodes(staticBytes);
        }

        wUserName = String.fromCharCodes(wUsernameBytes);
        if (wPasswordBytes.isNotEmpty) {
          wPassword = String.fromCharCodes(wPasswordBytes);
        }
        eWalletPass = String.fromCharCodes(txnPasswordBytes);

        if (cur['NET_BANKING_TYPE_BLOB'] != null) {
          try {
            String bankingTypeStr = String.fromCharCodes(netBankingTypeBytes);
            if (bankingTypeStr.isNotEmpty) {
              bankingType = bankingTypeStr[0];
              if (bankingType == 'P') {
                bankingType = 'R';
              }
            }
          } catch (ex) {
            bankingType = 'R';
          }
        }

        if (cur['SBI_BANK_NAME_BLOB'] != null) {
          try {
            sbiOpt = int.parse(String.fromCharCodes(sbiBankNameBytes));
          } catch (e) {
            sbiOpt = 0;
          }
        }

        corpId = String.fromCharCodes(corpIdBytes);
        bUserName = String.fromCharCodes(nbUsernameBytes);
        bPassword = String.fromCharCodes(nbPasswordBytes);

        addr1 = String.fromCharCodes(icAddress1Bytes);
        addr2 = String.fromCharCodes(icAddress2Bytes);
        city = String.fromCharCodes(icCityBytes);
        state = String.fromCharCodes(icStateBytes);
        country = String.fromCharCodes(icCountryBytes);
        zip = String.fromCharCodes(icZipBytes);
        mBank = String.fromCharCodes(icBankBytes);
        cardType = String.fromCharCodes(cardTypeBytes);

        // Java equivalent: //pCount = cur.getInt(48); //cCount = cur.getInt(49);
        // pCount = cur['PCOUNT'] ?? 0;
        // cCount = cur['CCOUNT'] ?? 0;

        if (!paymentAutofill) {
          if (paymentChoice == "CASH_CARD") {
            bankChoice = String.fromCharCodes(walletBytes);
          } else if (paymentChoice == "NETBANKING") {
            bankChoice = String.fromCharCodes(netBankNameBytes);
          } else if (paymentChoice == "DEBIT_CARD" ||
              paymentChoice == "CREDIT_CARD") {
            bankChoice = String.fromCharCodes(bankNameBytes);

            if (paymentChoice == "CREDIT_CARD") {
              if (bankChoice == "American Express") {
                mMobile = String.fromCharCodes(aeMobileBytes);
                email = String.fromCharCodes(aeEmailBytes);
              } else if (bankChoice ==
                  "International cards (Powered by ATOM)") {
                mMobile = String.fromCharCodes(icMobileBytes);
                email = String.fromCharCodes(icEmailBytes);
              }
            }
          } else if (paymentChoice == "UPI_VPA") {
            bankChoice = String.fromCharCodes(upiBankBytes);
            if (bankChoice ==
                "Credit & Debit cards / Wallet / UPI (Powered by PhonePe)") {
              paymentChoice = "MULTIPLE_GATEWAY";
            }
            vpa = String.fromCharCodes(vpaBytes);
          } else if (paymentChoice == "COD") {
            bankChoice = String.fromCharCodes(podBytes);
          } else if (paymentChoice == "E_WALLET" ||
              paymentChoice == "IRCTC_IPAY") {
            bankChoice = "IRCTC";
          } else if (paymentChoice == "MULTIPLE_GATEWAY") {
            bankChoice = String.fromCharCodes(multipleOptBytes);
          }

          if (bankChoice != null && bankChoice == "IRCTC iPay UPI") {
            paymentChoiceMap["UPI_VPA"] =
                "IRCTC iPay (Credit Card/Debit Card/UPI)";
          } else {
            paymentChoiceMap["UPI_VPA"] = "BHIM/ UPI/ USSD";
          }

          if (bankChoice != null && bankChoice == "IRCTC iPay UPI") {
            bankChoice =
                "Credit cards/Debit cards/Netbanking/UPI (Powered by IRCTC)";
          }
        }
      }

      List<PojoPassenger.Passenger> passengerTemp = [];

      List<Map<String, dynamic>> passengerResult;
      try {
        passengerResult = await db.rawQuery(
          "select * from PASSENGER_TABLE where FORM_NAME = ?",
          [formName],
        );
      } catch (e) {
        developer.log(
          'Missing table PASSENGER_TABLE or other DB error: ' + e.toString(),
        );
        passengerResult = [];
        passenger = [];
        pCount = 0;
      }

      pCount = 0;
      try {
        for (var pCur in passengerResult) {
          PojoPassenger.Passenger p = PojoPassenger.Passenger();
          p.setConcession(pCur['CONCESSION'] ?? '');
          p.setNationality(pCur['NATIONALITY'] ?? '');
          p.setCardNo(pCur['CARD_NO'] ?? '');
          p.setValidity(pCur['VALIDITY'] ?? '');
          p.setIdType(pCur['ID_TYPE'] ?? '');
          p.setIdNo(pCur['ID_NO'] ?? '');
          p.setName(pCur['NAME'] ?? '');
          p.setAge(pCur['AGE'] ?? 0);
          p.setGender(pCur['GENDER'] ?? '');
          p.setBerthPref(pCur['BERTH_PREF'] ?? '');

          if (pCur['MEAL'] != null && pCur['MEAL'] != "NA") {
            p.setMeal(pCur['MEAL']);
          } else {
            p.setMeal("V");
          }

          p.setSeniorCitizen(pCur['SENIOR_CITIZEN'] ?? 0);

          if ((p.getAge() ?? 0) >= 60) {
            atLeastOneSenior = true;
          }

          p.setBedRoll((pCur['BED_ROLL'] ?? 'false') == 'true');
          p.setDob(pCur['DOB'] ?? '');
          p.setOptBerth(pCur['OPT_BERTH'] ?? 0);

          passengerTemp.add(p);
          pCount++;
        }
      } catch (e) {
        // Handle error
      }

      passenger = passengerTemp;

      List<PojoChild.Child> childTemp = [];

      List<Map<String, dynamic>> childResult;
      try {
        childResult = await db.rawQuery(
          "select * from CHILD_TABLE where FORM_NAME = ?",
          [formName],
        );
      } catch (e) {
        developer.log(
          'Missing table CHILD_TABLE or other DB error: ' + e.toString(),
        );
        childResult = [];
        child = [];
        cCount = 0;
      }

      cCount = 0;
      for (var cCur in childResult) {
        PojoChild.Child c = PojoChild.Child();
        c.setName(cCur['NAME'] ?? '');
        c.setAge(cCur['AGE'] ?? '');
        c.setGender(cCur['GENDER'] ?? '');

        childTemp.add(c);
        cCount++;
      }

      child = childTemp;

      List<Map<String, dynamic>> podResult;
      try {
        podResult = await db.rawQuery(
          "select * from POD_TABLE where FORM_NAME = ?",
          [formName],
        );
      } catch (e) {
        developer.log(
          'Missing table POD_TABLE or other DB error: ' + e.toString(),
        );
        podResult = [];
        podMobile = '';
      }

      if (podResult.isNotEmpty) {
        podMobile = podResult[0]['MOBILE']?.toString() ?? '';
      }

      List<Map<String, dynamic>> insResult;
      try {
        insResult = await db.rawQuery(
          "select * from INSURANCE_TABLE where FORM_NAME = ?",
          [formName],
        );
      } catch (e) {
        developer.log(
          'Missing table INSURANCE_TABLE or other DB error: ' + e.toString(),
        );
        insResult = [];
        insurance = 0;
      }

      if (insResult.isNotEmpty) {
        insurance = insResult[0]['INSURANCE'] is int
            ? insResult[0]['INSURANCE'] as int
            : int.tryParse(insResult[0]['INSURANCE']?.toString() ?? '0') ?? 0;
      }

      List<Map<String, dynamic>> sqResult;
      try {
        sqResult = await db.rawQuery(
          "select * from SECURITY_QUESTIONS_TABLE where FORM_NAME = ?",
          [formName],
        );
      } catch (e) {
        developer.log(
          'Missing table SECURITY_QUESTIONS_TABLE or other DB error: ' +
              e.toString(),
        );
        sqResult = [];
        sqAuto = false;
        sqDob = '';
        sqEmail = '';
        sqMobile = '';
        sqEwallet = 0;
      }

      if (sqResult.isNotEmpty) {
        sqAuto = true;
        sqDob = sqResult[0]['DOB']?.toString() ?? '';
        sqEmail = sqResult[0]['EMAIL']?.toString() ?? '';
        sqMobile = sqResult[0]['MOBILE']?.toString() ?? '';
        sqEwallet = sqResult[0]['EWALLET'] is int
            ? sqResult[0]['EWALLET'] as int
            : int.tryParse(sqResult[0]['EWALLET']?.toString() ?? '0') ?? 0;
      }
    } catch (e) {
      developer.log("Error in initializeVariables: $e");
    }
  }

  static int clearCacheFolder(Directory? dir, int numDays) {
    int deletedFiles = 0;

    if (dir != null && dir.existsSync()) {
      try {
        List<FileSystemEntity> files = dir.listSync();

        for (FileSystemEntity child in files) {
          if (child is Directory) {
            deletedFiles += clearCacheFolder(child, numDays);
          }

          DateTime cutoffTime = DateTime.now().subtract(
            Duration(days: numDays),
          );

          if (child.statSync().modified.isBefore(cutoffTime)) {
            try {
              child.deleteSync();
              deletedFiles++;
            } catch (e) {}
          }
        }
      } catch (e) {
        developer.log("Failed to clean the cache, error: $e");
      }
    }
    return deletedFiles;
  }

  static void clearCache(int numDays) async {
    developer.log(
      "Starting cache prune, deleting files older than $numDays days",
    );

    try {
      Directory cacheDir =
          await getApplicationDocumentsDirectory(); // Flutter equivalent of getCacheDir()
      int numDeletedFiles = clearCacheFolder(cacheDir, numDays);
      developer.log("Cache pruning completed, $numDeletedFiles files deleted");
    } catch (e) {
      developer.log("Error clearing cache: $e");
    }
  }

  Future<void> deleteOTP() async {
    try {
      SharedPreferences sp = await SharedPreferences.getInstance();
      await sp.remove('OTP_RECEIVED');
      await sp.setString('OTP_RECEIVED', '');
    } catch (e) {}
  }

  String getCaptchaSolveJS(String clearIntervalParam, int location) {
    bool captchaCondition =
        (autofillCaptcha ||
        AppConstants.isGoldUser == 2 ||
        APIConsts.allowCaptchaAutofill ||
        (AppConstants.ticketsLeft == 0 &&
            (Consts.USER_TYPE == "FREE_USER" ||
                Consts.USER_TYPE == "COMP_USER")));

    String captchaCheck = captchaCondition ? "if(true) {" : "if(false) {";
    String solveCaptchaCall = _guardStepCall(
      "Step.solveCaptcha(url, type, $location);",
    );

    return "var url = '';" +
        "var type = 0;" +
        "var imgElem = document.getElementById('nlpCaptchaContainer');" +
        "if(imgElem != null && imgElem.getElementsByTagName('img').length > 0) {" +
        "url = imgElem.getElementsByTagName('img')[imgElem.getElementsByTagName('img').length - 1].src;" +
        "} else if(document.getElementsByClassName('captcha-img').length > 0) {" +
        "url = document.getElementsByClassName('captcha-img')[0].src;" +
        "type = 1;" +
        "}" +
        "" +
        "if(url != '') {" +
        "if(url.startsWith('data')) {" +
        "if(document.getElementById('nlpAnswer') == null || document.getElementById('nlpAnswer').type != 'hidden') {" +
        captchaCheck +
        solveCaptchaCall +
        clearIntervalParam +
        "}" +
        "}" +
        "} " +
        "else if(url.indexOf('nget') > 0) {" +
        "document.getElementsByClassName('glyphicon glyphicon-repeat')[0].parentElement.click();" +
        "}" +
        "}";
  }

  Future<void> onCreate() async {
    paymentFailed = 0;

    movedToHDFCPayment = false;

    screenHeight = MediaQuery.of(context).size.height.toInt();

    Map<String, dynamic> props = {};
    try {
      props["Quota"] = quota;
      props["Payment method"] = paymentChoice;
      props["Bank"] = bankChoice;
    } catch (e) {}

    startClock();

    formName = widget.formName ?? '';
    language = widget.language;

    if (!(formName != null &&
        formName is String &&
        (formName as String).isNotEmpty)) {
      SharedPreferences sp = await SharedPreferences.getInstance();
      formName = sp.getString('FORM_NAME') ?? '';
    }

    await initializeVariables();

    progressBar = CircularProgressIndicator();
    continueBtn = ElevatedButton(
      onPressed: () => _handleContinueClick(),
      child: Text('Continue'),
    );

    await clearWebViewSession();

    extraHeaders['X-Requested-With'] = 'com.android.chrome';
    developer.log(
      "MainActivity onCreate completed - WebView initialized with all settings",
    );
  }

  String waitDialogMsg = "";

  void initializeWaiting(String jsCode) {
    waitTimerTask = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          roundTime--;
          if (roundTime < 0) {
            roundTime = 0;
          }
          String msg =
              "Automatic OTP in ${roundTime.round()} seconds"; // String.format equivalent
          waitDialogMsg = msg;

          if (waitDialog != null) {
            (context as Element).markNeedsBuild();
          }

          if (roundTime == 0) {
            stopWaiting(jsCode);
          }
        });
      } else {
        timer.cancel();
        waitTimerTask = null;
      }
    });
  }

  void startWaiting(String jsCode, int delay) {
    waitTimer = Timer(Duration(milliseconds: delay), () {
      initializeWaiting(jsCode);
      if (waitTimerTask != null) {
        waitTimerTask!.cancel();
        waitTimerTask = null;
      }
      waitTimerTask = Timer.periodic(Duration(milliseconds: 1000), (timer) {
        if (!mounted || waitDialog == null) {
          timer.cancel();
          waitTimerTask = null;
        }
      });
    });
  }

  void stopWaiting(String jsCode) {
    if (mounted && waitDialog != null) {
      Navigator.of(context).pop();
      waitDialog = null;
    }

    loadWebViewUrl(jsCode);

    if (waitTimer != null) {
      waitTimer!.cancel();
      waitTimer = null;
    }

    if (waitTimerTask != null) {
      waitTimerTask!.cancel();
      waitTimerTask = null;
    }
  }

  int daysBetween(DateTime startDate, DateTime endDate) {
    int end = endDate.millisecondsSinceEpoch;
    int start = startDate.millisecondsSinceEpoch;
    return Duration(milliseconds: start - end).inSeconds;
  }

  void _initializeWebView() {
    // TODO: This needs to be adapted to your actual WebView implementation
    // The Java code uses: webView.addJavascriptInterface(new MyJavaScriptInterface(), "Step");
    // You need to set up JavaScript channels/interface for "Step" object with your WebView

    /*
    // This matches the Java webView.addJavascriptInterface(new MyJavaScriptInterface(), "Step");

    try {
      // Set up JavaScript channels for Step interface
      webViewController?.addJavaScriptChannel(
        'Step',
        onMessageReceived: (JavaScriptMessage message) {
          _handleStepMessage(message);
        },
      );

      // Load IRCTC with proper configuration
      webViewController?.loadRequest(
          Uri.parse('https://www.irctc.co.in/nget/train-search'));

      developer.log("✅ WebView initialized with Step JavaScript interface");
    } catch (e) {
      developer.log("Error during _initializeWebView: $e");
    }
    */

    developer.log(
      "⚠️ WebView JavaScript interface setup needed for Step object",
    );
  }

  void _handleStepMessage(/*JavaScriptMessage message*/) {
    // TODO: Implement JavaScript message handling when WebView interface is set up
    /*
    try {
      final data = json.decode(message.message);
      final method = data['method'] as String?;

      switch (method) {
        case 'incrementStep':
          incrementStepJS();
          break;
        case 'setStep':
          setStepJS(data['value'].toString());
          break;
        case 'print':
          printJS(data['message'].toString());
          break;
        case 'clickLoginOnce':
          final result = clickLoginOnceJS();
          // Send result back to JavaScript if needed
          break;
        case 'manualClickEwallet':
          manualClickEwallet();
          break;
        case 'continueClick':
          continueClick();
          break;
      // Add other JavaScript interface methods as needed
        default:
          developer.log("Unknown Step method: $method");
      }
    } catch (e) {
      developer.log("Error handling Step message: $e");
    }
    */
  }

  Future<void> _setupWebViewSettings() async {}

  void _handleContinueClick() {
    String? url = getCurrentWebViewUrl();

    if (url != null) {
      if (url.contains("psgninput") || url.contains("reviewBooking")) {
        loadWebViewUrl(
          "javascript:function aish() {document.getElementsByClassName('train_Search btnDefault')[0].click();} aish()",
        );
      } else if (url.contains("ewallet")) {
        loadWebViewUrl(
          "javascript:function aish() {document.getElementsByClassName('mob-bot-btn search_btn')[0].click();} aish()",
        );
      } else {
        loadWebViewUrl(
          "javascript:function aish() {" +
              "if(document.getElementsByClassName('bank-top-header')[0].getBoundingClientRect().x < 0) {" +
              "var len = document.getElementsByClassName('mob-bot-btn search_btn').length;" +
              "document.getElementsByClassName('mob-bot-btn search_btn')[len - 1].click();" +
              "} else {" +
              "document.getElementsByClassName('btn-primary')[0].click();" +
              "}" +
              "} aish()",
        );
      }
    }
  }

  void onWebViewProgressChanged(int progress) {
    setState(() {
      if (progress == 100) {
      } else {}
    });
  }

  bool shouldOverrideUrlLoading(String url) {
    if (url.startsWith("http:") || url.startsWith("https:")) {
      return false;
    }

    if (url.startsWith("upi://")) {
      _launchUpiApp(url);
      return true;
    }

    _launchUrl(url);
    return true;
  }

  void _launchUpiApp(String upiUrl) {}

  void _launchUrl(String url) {}

  void onPageStarted(String url) {}

  void onPageFinished(String url) async {}

  String _generateMainJavaScript(String boardingStnName) {
    String safeUsername = '';
    String safePassword = '';
    String safeFromStation = '';
    String safeToStation = '';
    String safeJourneyDate = '';
    String safeQuota = '';

    try {
      safeUsername = username ?? '';
      safePassword = password ?? '';
      safeFromStation = fromStation ?? '';
      safeToStation = toStation ?? '';
      safeJourneyDate = journeyDate ?? '';
      safeQuota = quota ?? '';
    } catch (e) {
      developer.log(
        "Some variables not initialized in _generateMainJavaScript: $e",
      );
    }

    final eUsername = _jsEscape(safeUsername);
    final ePassword = _jsEscape(safePassword);
    final eFromStation = _jsEscape(safeFromStation);
    final eToStation = _jsEscape(safeToStation);
    final eJourneyDate = _jsEscape(safeJourneyDate);
    final eQuota = _jsEscape(safeQuota);
    final eBoarding = _jsEscape(boardingStnName);

    List<Map<String, dynamic>> passList = [];
    try {
      for (var i = 0; i < passenger.length; i++) {
        final p = passenger[i];
        passList.add({
          'NAME': p.getName() ?? '',
          'AGE': p.getAge() ?? '',
          'BP': p.getBerthPref() ?? '',
          'GENDER': p.getGender() ?? '',
          'NATION': p.getNationality() ?? '',
          'SENIOR': (p.getSeniorCitizen() ?? 0) + 1,
          'MEAL': p.getMeal() ?? '',
          'BEDROLL': p.isBedRoll() ?? false,
          'CARD_NO': p.getIdNo() ?? '',
          'OPT_BERTH': (p.isOptBerth() ?? false),
        });
      }
    } catch (_) {}

    List<Map<String, dynamic>> childList = [];
    try {
      for (var i = 0; i < child.length; i++) {
        final c = child[i];
        childList.add({
          'NAME': c.getName() ?? '',
          'AGE': c.getAge() ?? '',
          'GENDER': c.getGender() ?? '',
        });
      }
    } catch (_) {}

    final passengersJson = jsonEncode(passList);
    final childrenJson = jsonEncode(childList);

    final String coreJs =
        "window.QB = window.QB || {}; QB.PASSENGERS = $passengersJson; QB.CHILDREN = $childrenJson;" +
        "var searchFlagOnce = false;\n" +
        "var classFlagOnce = false;\n" +
        "var class2FlagOnce = false;\n" +
        "var passFlagOnce = false;\n" +
        "var pass2FlagOnce = false;\n" +
        "var othrDateFlagOnce = false;" +
        "var reviewFlagOnce = false;\n" +
        "var keyCounter = 0;\n" +
        "async function simulateTyping(el, text, delay = 10) {\n" +
        "  el.focus();\n" +
        "  for (const char of text) {\n" +
        "    el.dispatchEvent(new KeyboardEvent('keydown', {\n" +
        "      key: char,\n" +
        "      bubbles: true,\n" +
        "      cancelable: true\n" +
        "    }));\n" +
        "    el.dispatchEvent(new KeyboardEvent('keypress', {\n" +
        "      key: char,\n" +
        "      bubbles: true,\n" +
        "      cancelable: true\n" +
        "    }));\n" +
        "    const prev = el.value;\n" +
        "    el.value = prev + char;\n" +
        "    el.dispatchEvent(new InputEvent('input', {\n" +
        "      data: char,\n" +
        "      bubbles: true,\n" +
        "      cancelable: true\n" +
        "    }));\n" +
        "    el.dispatchEvent(new KeyboardEvent('keyup', {\n" +
        "      key: char,\n" +
        "      bubbles: true,\n" +
        "      cancelable: true\n" +
        "    }));\n" +
        "    await new Promise(r => setTimeout(r, delay));\n" +
        "  }\n" +
        "  el.dispatchEvent(new Event('change', { bubbles: true }));\n" +
        "  keyCounter = new Date().getTime();\n" +
        "}\n" +
        _getJavaScriptFunctions(
          eBoarding,
          eUsername,
          ePassword,
          eFromStation,
          eToStation,
          eJourneyDate,
          eQuota,
        );

    return "javascript:(function(){try{" +
        coreJs +
        "}catch(e){try{if(window.StepBridge&&StepBridge.postMessage){StepBridge.postMessage(JSON.stringify({ m:'printJS', a:['JS error: '+(e&&e.stack?e.stack:e.message)] }));}}catch(_){}})();";
  }

  String _getJavaScriptFunctions(
    String boardingStnName,
    String safeUsername,
    String safePassword,
    String safeFromStation,
    String safeToStation,
    String safeJourneyDate,
    String safeQuota,
  ) {
    return "function setText(elem, val, index) {\n" +
        "var fillInterval = setInterval(function() {\n" +
        "if(typeof Step !== 'undefined' && Step.allDone && Step.allDone(index) == 1) {\n" +
        "if(elem.value.toUpperCase() == val.toUpperCase()) {\n" +
        "if(elem.getAttribute('formControlName') == 'passengerAge') {\n" +
        "var pe = elem.parentElement.parentElement.parentElement.parentElement.parentElement;\n" +
        "var i = Array.from(document.getElementsByTagName('app-passenger')).indexOf(pe);\n" +
        "var pass = document.getElementsByTagName('app-passenger')[i];\n" +
        _guardStepCall("Step.getPassengerValue('OPT_BERTH', i)") +
        "if(typeof Step !== 'undefined' && Step.getPassengerValue && Step.getPassengerValue('OPT_BERTH', i) == '0' && pass.querySelector(\"input[formControlName='childBerthFlag']\") != null) {\n" +
        "setTimeout(function() {\n" +
        "pass.querySelector(\"input[formControlName='childBerthFlag']\").click();\n" +
        "document.getElementsByClassName('ui-dialog-footer')[0].getElementsByTagName('button')[0].click();\n" +
        "}, 500);\n" +
        "}\n" +
        // ...
        _guardStepCall("Step.updateTask();") +
        "clearInterval(fillInterval);\n" +
        "} else {\n" +
        "elem.value = '';\n" +
        "elem.focus();\n" +
        _guardStepCall("Step.pressKey(val);") +
        "keyCounter = new Date().getTime();\n" +
        "}\n" +
        "}\n" +
        "}, 50);\n" +
        "}\n" +
        _getMainExecutionScript(
          boardingStnName,
          safeUsername,
          safePassword,
          safeFromStation,
          safeToStation,
          safeJourneyDate,
          safeQuota,
        );
  }

  String _getMainExecutionScript(
    String boardingStnName,
    String safeUsername,
    String safePassword,
    String safeFromStation,
    String safeToStation,
    String safeJourneyDate,
    String safeQuota,
  ) {
    return "function guruHoJaShuru() {\n" +
        "var adInterval = setInterval(function() {\n" +
        _getAdRemovalScript() +
        "}, 300);\n" +
        _getLoginScript(safeUsername, safePassword) +
        _getPassengerScript(boardingStnName) +
        _getPaymentScript() +
        "}" +
        "guruHoJaShuru();";
  }

  String _getAdRemovalScript() {
    return "var frames = document.getElementsByTagName('iframe');\n" +
        "for(i=0; i<frames.length; i++) {\n" +
        "    if(frames[i].id.toUpperCase().indexOf('GOOGLE') >= 0) {\n" +
        "        var elem = frames[i].parentNode.parentNode;\n" +
        "        if(frames[i].outerHTML.indexOf('google_ads_iframe') >= 0) {\n" +
        "            elem.remove();\n" +
        "        }\n" +
        "    }\n" +
        "}\n" +
        "var adElements = document.querySelectorAll('[id^=\"div-gpt-ad\"]');\n" +
        "adElements.forEach(element => {\n" +
        "    element.setAttribute('style', 'display: none;');\n" +
        "});";
  }

  String _getLoginScript(String safeUsername, String safePassword) {
    return "var loginInterval = setInterval(function() {\n" +
        "var usernameField = document.querySelector(\"input[formControlName='userid'], #userId, input[name='userid'], input#userid, input#username, input[name='username']\");\n" +
        "var passwordField = document.querySelector(\"input[formControlName='password'], #pwd, input[name='password'], input#password\");\n" +
        "if(usernameField && passwordField) {\n" +
        "  if(usernameField.value == '' && '" +
        safeUsername +
        "'.length > 0) {\n" +
        "    setTimeout(function() {\n" +
        "      // Clear any existing value and set new username\n" +
        "      usernameField.value = '';\n" +
        "      usernameField.focus();\n" +
        "      \n" +
        "      // Use simulateTyping for natural input\n" +
        "      simulateTyping(usernameField, '" +
        safeUsername +
        "');\n" +
        "      \n" +
        "      // Trigger input events manually for compatibility\n" +
        "      usernameField.dispatchEvent(new Event('input', { bubbles: true }));\n" +
        "      usernameField.dispatchEvent(new Event('change', { bubbles: true }));\n" +
        "      \n" +
        "      // Assign app signature for JavaScript (was invalid interpolation before)\n" +
        "      window.APP_SIGNATURE = '" +
        safeUsername +
        "';\n" +
        "    }, 100);\n" +
        "  }\n" +
        "  \n" +
        "  if(passwordField.value == '' && '" +
        safePassword +
        "'.length > 0 && usernameField.value == '" +
        safeUsername +
        "') {\n" +
        "    setTimeout(function() {\n" +
        "      // Clear and set password\n" +
        "      passwordField.value = '';\n" +
        "      passwordField.focus();\n" +
        "      \n" +
        "      // Use simulateTyping for natural input\n" +
        "      simulateTyping(passwordField, '" +
        safePassword +
        "');\n" +
        "      \n" +
        "      // Trigger input events\n" +
        "      passwordField.dispatchEvent(new Event('input', { bubbles: true }));\n" +
        "      passwordField.dispatchEvent(new Event('change', { bubbles: true }));\n" +
        "      \n" +
        "      // Auto-click login after small delay\n" +
        "      setTimeout(function() {\n" +
        "        var loginBtn = document.querySelector('button[type=\"submit\"], .search_btn, .loginButton');\n" +
        "        if(loginBtn && loginBtn.style.display !== 'none' && !loginBtn.disabled) {\n" +
        "          loginBtn.click();\n" +
        "        }\n" +
        "      }, 500);\n" +
        "    }, 200);\n" +
        "  }\n" +
        "  \n" +
        "  // Check if login was successful and trigger journey automation\n" +
        "  if(usernameField.value == '" +
        safeUsername +
        "' && passwordField.value == '" +
        safePassword +
        "') {\n" +
        "    setTimeout(function() {\n" +
        "      if(document.querySelector('.dashboard, .home-page, [class*=\"loggedIn\"]')) {\n" +
        "        clearInterval(loginInterval);\n" +
        "        console.log('✅ Auto login successful!');\n" +
        "        \n" +
        "        // Check if we need to navigate to journey planning\n" +
        "        if (!window.location.href.includes('train-search')) {\n" +
        "          console.log('🚆 Redirecting to train search page...');\n" +
        "          setTimeout(function() {\n" +
        "            window.location.href = 'https://www.irctc.co.in/nget/train-search';\n" +
        "          }, 2000);\n" +
        "        } else {\n" +
        "          // Already on train search, trigger journey automation\n" +
        "          setTimeout(function() {\n" +
        "            if (window.Step && Step.planMyJourney) {\n" +
        "              Step.planMyJourney();\n" +
        "            }\n" +
        "          }, 1500);\n" +
        "        }\n" +
        "      }\n" +
        "    }, 2000);\n" +
        "  }\n" +
        "}\n" +
        "}, 500);" +
        "if(Step.clickLoginOnceJS()) {" +
        "setTimeout(function() {" +
        "Step.manualClickFirstElementJS();" +
        "setTimeout(function() {" +
        "document.getElementsByClassName('nav-bar')[1].getElementsByTagName('label')[0].getElementsByTagName('button')[0].click();" +
        "}, 1001);" +
        "}, 1001);" +
        "}";
  }

  String _getPassengerScript(String boardingStnName) {
    String safeBoardingStnName = '';
    if (boardingStation.isNotEmpty && boardingStation.contains("-")) {
      try {
        safeBoardingStnName = boardingStation.split("-")[1].trim();
      } catch (e) {
        safeBoardingStnName = boardingStnName.isNotEmpty ? boardingStnName : '';
      }
    } else {
      safeBoardingStnName = boardingStnName.isNotEmpty ? boardingStnName : '';
    }

    return "var passengerFill = setInterval(function() {\n" +
        "if (document.querySelector(\"p-autocomplete[formControlName='passengerName']\") != null) {\n" +
        "if('$safeBoardingStnName' != '') {\n" +
        "  var boardingDropdown = document.getElementsByTagName('p-dropdown')[0];\n" +
        "  if(boardingDropdown) {\n" +
        "    boardingDropdown.getElementsByTagName('div')[0].click();\n" +
        "    setTimeout(function() {\n" +
        "      const dropdownItems = document.querySelectorAll('p-dropdownitem');\n" +
        "      dropdownItems.forEach(item => {\n" +
        "        const strongElements = item.querySelectorAll('strong');\n" +
        "        strongElements.forEach(strong => {\n" +
        "          if (strong.innerHTML.includes('$safeBoardingStnName')) {\n" +
        "            strong.click();\n" +
        "            return;\n" +
        "          }\n" +
        "        });\n" +
        "      });\n" +
        "    }, 300);\n" +
        "  }\n" +
        "}\n" +
        "var passengers = document.getElementsByTagName('app-passenger');\n" +
        "for(var i = 0; i < passengers.length && i < ${passenger.length}; i++) {\n" +
        "  var pass = passengers[i];\n" +
        "  try {\n" +
        "    // Fill name with better typing simulation\n" +
        "    var nameField = pass.querySelector(\"p-autocomplete[formControlName='passengerName']\");\n" +
        "    if(nameField) {\n" +
        "      var nameInput = nameField.getElementsByTagName('input')[0];\n" +
        "      if(nameInput && nameInput.value == '') {\n" +
        "        nameInput.focus();\n" +
        "        nameInput.value = '';\n" +
        "        simulateTyping(nameInput, Step.getPassengerValue('NAME', i));\n" +
        "      }\n" +
        "    }\n" +
        "    \n" +
        "    // Fill age\n" +
        "    var ageField = pass.querySelector(\"input[formControlName='passengerAge']\");\n" +
        "    if(ageField && ageField.value == '') {\n" +
        "      ageField.focus();\n" +
        "      ageField.value = '';\n" +
        "      simulateTyping(ageField, Step.getPassengerValue('AGE', i));\n" +
        "    }\n" +
        "    \n" +
        "    // Select gender with proper event handling\n" +
        "    var genderField = pass.querySelector(\"select[formControlName='passengerGender']\");\n" +
        "    if(genderField) {\n" +
        "      var genderValue = Step.getPassengerValue('GENDER', i);\n" +
        "      if(genderField.value != genderValue) {\n" +
        "        genderField.value = genderValue;\n" +
        "        genderField.dispatchEvent(new Event('change', { bubbles: true }));\n" +
        "      }\n" +
        "    }\n" +
        "    \n" +
        "    // Handle berth preference\n" +
        "    var berthField = pass.querySelector(\"select[formControlName='passengerBerthChoice']\");\n" +
        "    if(berthField) {\n" +
        "      var berthValue = Step.getPassengerValue('BP', i);\n" +
        "      if(berthField.value != berthValue && berthValue != '') {\n" +
        "        berthField.value = berthValue;\n" +
        "        berthField.dispatchEvent(new Event('change', { bubbles: true }));\n" +
        "      }\n" +
        "    }\n" +
        "    \n" +
        "  } catch (err) {\n" +
        "    console.log('Error filling passenger ' + i + ':', err);\n" +
        "  }\n" +
        "}\n" +
        "\n" +
        "// Check if all passengers are filled\n" +
        "var allFilled = true;\n" +
        "for(var i = 0; i < Math.min(passengers.length, ${passenger.length}); i++) {\n" +
        "  var pass = passengers[i];\n" +
        "  var nameInput = pass.querySelector(\"p-autocomplete[formControlName='passengerName'] input\");\n" +
        "  var ageInput = pass.querySelector(\"input[formControlName='passengerAge']\");\n" +
        "  if(!nameInput || !ageInput || nameInput.value == '' || ageInput.value == '') {\n" +
        "    allFilled = false;\n" +
        "    break;\n" +
        "  }\n" +
        "}\n" +
        "\n" +
        "if(allFilled) {\n" +
        "  clearInterval(passengerFill);\n" +
        "  Step.printJS('All passenger details filled successfully!');\n" +
        "}\n" +
        "}\n" +
        "}, 1000);\n";
  }

  String _getPaymentScript() {
    return "var paymentInterval = setInterval(function() {\n" +
        "if(document.getElementsByClassName('bank-type').length > 1) {\n" +
        "if(${AppConstants.ticketsLeft == 0 && (Consts.USER_TYPE == "COMP_USER" || Consts.USER_TYPE == "FREE_USER")}) {\n" +
        "Step.showPaymentAlert();\n" +
        "clearInterval(paymentInterval);\n" +
        "return;\n" +
        "}\n" +
        "if(${!paymentAutofill}) {\n" +
        "var fareText = document.getElementsByClassName('top-header')[2].innerText;\n" +
        "var fareValue = fareText.match(/[\\d,]+\\.?\\d*/)[0];\n" +
        "fareValue = parseFloat(fareValue.replace(/,/g, ''));\n" +
        "if ($fareLimit > 0 && fareValue >= $fareLimit && ${waitForPTResponse != 2}) {\n" +
        "    Step.showFareAlert('₹' + fareValue);\n" +
        "}\n" +
        "}\n" +
        "clearInterval(paymentInterval);\n" +
        "}\n" +
        "}, 100);\n";
  }

  Future<void> _handlePaymentOperations(String url) async {
    try {
      final bankInfo = BankInfo(context);
      String jsCode = '';

      switch (paymentChoice) {
        case 'DEBIT_CARD':
          jsCode = bankInfo.debitCardWithPinJS(
            bankChoice ?? '',
            cardNo,
            cardHolder,
            expMon,
            expYr,
            pin,
            cvv,
            cardType,
            staticPassword,
            step,
          );
          break;
        case 'NETBANKING':
          jsCode = bankInfo.netBankingJS(
            bankChoice ?? '',
            sbiOpt,
            corpId,
            bUserName,
            bPassword,
            bankingType,
            step,
            0,
          );
          break;
        case 'IRCTC_IPAY':
        case 'CREDIT_CARD':
          jsCode = bankInfo.credirCardJS(
            bankChoice ?? '',
            cardNo,
            cardHolder,
            expMon,
            expYr,
            cvv,
            cardType,
            step,
          );
          break;
        case 'CASH_CARD':
          jsCode = bankInfo.walletJS(
            bankChoice ?? '',
            '',
            wUserName,
            wPassword,
            step,
          );
          break;
        case 'UPI_VPA':
          if ((bankChoice ?? '').contains('PhonePe')) {
            jsCode = bankInfo.phonepeUpi(vpa, '', '', '', step);
          } else if ((bankChoice ?? '').contains('Paytm')) {
            jsCode = bankInfo.paytmUpi(vpa, '', '', '', step);
          } else {
            jsCode = bankInfo.iPayUpi(vpa, '', '', '', step);
          }
          break;
        case 'COD':
          jsCode = bankInfo.pod(bankChoice ?? '', '', podMobile, '', step);
          break;
        case 'MULTIPLE_GATEWAY':
          if ((bankChoice ?? '').contains('PhonePe')) {
            jsCode = bankInfo.phonepeUpi(vpa, '', '', '', step);
          } else if ((bankChoice ?? '').contains('Paytm')) {
            jsCode = bankInfo.paytmUpi(vpa, '', '', '', step);
          }
          break;
      }

      if (jsCode.isNotEmpty) {
        loadWebViewUrl(jsCode);
      }
    } catch (e) {
      developer.log('Payment automation error: $e');
    }
  }

  Future<void> _handleDebitCard() async {}

  Future<void> _handleNetBanking() async {}

  Future<void> _handleCreditCard() async {}

  Future<void> _handleWallet() async {}

  Future<void> _handleUPI() async {}

  Future<void> _handleCOD() async {}

  Future<void> _handleMultipleGateway() async {}

  void _loadTicketConfirmationScript() {
    loadWebViewUrl(
      "javascript:function guruHoJaShuru() {\n" +
          "    var ticketInterval = setInterval(function() {\n" +
          "if(document.getElementsByTagName('app-booking-confirmation').length > 0) {\n" +
          "const panels = document.querySelectorAll('div[role=\"region\"]');\n" +
          "    let details = [];\n" +
          "    panels.forEach(panel => {\n" +
          "      const strongElements = panel.querySelectorAll('div > strong');\n" +
          "      const passengerName = strongElements.length > 1 ? strongElements[1].innerText : 'NA';\n" +
          "      const bookingStatus = panel.querySelectorAll('.cnf-pad strong')[0].innerText;\n" +
          "      details.push(passengerName + ':' + bookingStatus);\n" +
          "    });\n" +
          "    Step.printTicket(details.join('<br/>'));\n" +
          "clearInterval(ticketInterval);\n" +
          "}\n" +
          "}, 100);\n" +
          "}" +
          "guruHoJaShuru()",
    );
  }

  void onLoadResource(String url) {
    if (step == 0 && !languageChanged && language == "HIN") {
      loadWebViewUrl(
        "javascript:function guruHoJaShuru() {" +
            "if(document.getElementsByClassName('ng-star-inserted').length > 0) {\n" +
            "var elem = document.getElementsByClassName('toggle');\n" +
            "elem[elem.length - 1].parentNode.click();" +
            "Step.changeLanguage();" +
            "}" +
            "}" +
            "guruHoJaShuru()",
      );
    }
  }

  Future<void> clearWebViewSession() async {
    loadWebViewUrl(
      "javascript:function clearStorage() {"
      "localStorage.clear();"
      "sessionStorage.clear();"
      "document.cookie = '';"
      "} clearStorage();",
    );

    try {} catch (e) {}
    developer.log(
      "WebView session cleared - cache, history, form data, storage, cookies",
    );
  }

  void deleted() {
    String js =
        "setText(document.getElementById('aaa4'), '$addrPin', fillIndex + 1);\n" +
        "setText(document.getElementById('aaa1'), '$addrLine1', fillIndex + 2);\n" +
        "setText(document.getElementById('aaa2'), '$addrLine2', fillIndex + 3);\n" +
        "setText(document.getElementById('aaa3'), '$addrLine3', fillIndex + 4);\n" +
        "\n" +
        "if(document.getElementById('address-City').selectedIndex < 1) {" +
        "var addrIntv = setInterval(function() {\n" +
        "username = document.getElementById('address-City');\n" +
        "if(username.getElementsByTagName('option').length > 1) {\n" +
        "if(username.selectedIndex < 1) {\n" +
        "\tusername.selectedIndex = 1;\n" +
        "}\n" +
        "\n" +
        "clearInterval(addrIntv);\n" +
        "}\n" +
        "}, 100);\n" +
        "}" +
        "if(document.getElementById('address-postOffice').selectedIndex < 1) {" +
        "addrIntv1 = setInterval(function() {\n" +
        "username = document.getElementById('address-postOffice');\n" +
        "if(username.getElementsByTagName('option').length > 1) {\n" +
        "username.value = '$addrPo';\n" +
        "if(username.selectedIndex < 1) {\n" +
        "\tusername.selectedIndex = 1;\n" +
        "}\n" +
        "\n" +
        "username.dispatchEvent(new Event('change', { bubbles: true }));" +
        "if(!retPassTimer) {" +
        "document.getElementsByClassName('train_Search')[0].click();" +
        "psgnFlag = 2;" +
        "}" +
        "clearInterval(addrIntv1);\n" +
        "}\n" +
        "}, 100;" +
        "}";

    developer.log("Generated address JS code - Length: ${js.length}");
  }

  bool _myBackInterceptor(bool stopDefaultButtonEvent, RouteInfo info) {
    if (hideKeyboard) {
      FocusScope.of(context).unfocus();
      return true;
    }
    if (canExit) {
      SystemNavigator.pop();
      return false;
    } else {
      canExit = true;
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Press one more time to exit"),
            duration: Duration(milliseconds: 2000),
          ),
        );
      }
      Future.delayed(Duration(seconds: 2), () => canExit = false);
      return true;
    }
  }

  static Future<void> writeToScopedDir(
    BuildContext context,
    String data,
    String filename,
  ) async {
    try {
      Directory appDocDir = await getApplicationDocumentsDirectory();
      String appDocPath = appDocDir.path;

      Directory filesDir = Directory('$appDocPath/QB');
      if (!await filesDir.exists()) {
        await filesDir.create(recursive: true);
      }

      File myFile = File('${filesDir.path}/$filename');

      if (!await myFile.exists()) {
        await myFile.create();
      }

      await myFile.writeAsString(data);

      developer.log("Data written to scoped directory: $filename");
    } catch (e) {
      developer.log("Error writing to scoped directory: $e");
    }
  }

  static Future<String?> getScopedFileContent(
    BuildContext context,
    String filename,
  ) async {
    String? otp;
    try {
      Directory appDocDir = await getApplicationDocumentsDirectory();
      String appDocPath = appDocDir.path;

      File file = File('$appDocPath/QB/$filename');

      if (await file.exists()) {
        otp = await file.readAsString();
        otp = otp?.split('\n').first;
      }

      developer.log("Read from scoped file: $filename, Content: $otp");
      return otp;
    } catch (e) {
      developer.log("Error reading from scoped file: $e");
      return otp;
    }
  }

  Future<void> writeToFile(String data, String filename) async {
    try {
      Directory appDocDir = await getApplicationDocumentsDirectory();
      String localPath = "${appDocDir.path}/QB";
      Directory myFile = Directory(localPath);
      if (!myFile.existsSync()) {
        myFile.createSync(recursive: true);
      }
      File file = File("$localPath/$filename");
      await file.writeAsString(data);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString()), duration: Duration(seconds: 2)),
        );
      }
      developer.log("Error writing to file: $e");
    }
  }

  void checkKeyboardVisible() {
    int currentScreenHeight = getVisibleHeight(context);
    if ((screenHeight - currentScreenHeight) > 200) {
      Navigator.of(context).pop();
    }
  }

  int getVisibleHeight(BuildContext context) {
    MediaQueryData mediaQuery = MediaQuery.of(context);

    int visibleHeight = mediaQuery.size.height.toInt();

    int topInset = mediaQuery.viewPadding.top.toInt();

    int rootViewHeight = mediaQuery.size.height.toInt();

    return rootViewHeight - topInset;
  }

  String timeDifference() {
    DateTime cal = DateTime.now();
    if (travelClass == "2S" || travelClass == "FC" || travelClass == "SL") {
      cal = DateTime(cal.year, cal.month, cal.day, 11, 0, 0);
    } else {
      cal = DateTime(cal.year, cal.month, cal.day, 10, 0, 0);
    }
    DateTime now = DateTime.now();
    int difference = now.millisecondsSinceEpoch - cal.millisecondsSinceEpoch;
    int minutes = (difference ~/ 1000 ~/ 60);
    int seconds = (difference ~/ 1000) % 60;
    return "${minutes}m ${seconds}s";
  }

  void attachBaseContext() async {
    SharedPreferences sd = await SharedPreferences.getInstance();
    int langOpt = sd.getInt("OPTION") ?? 0;
  }

  Future<bool> onBackPressed() async {
    if (hideKeyboard) {
      return true;
    }

    if (canExit) {
      Map<String, dynamic> props = {};
      try {
        String? currentUrl = getCurrentWebViewUrl();
        props['URL'] = currentUrl ?? '';
        props['Booking status'] = bookingResponse;
      } catch (e) {}

      return true;
    } else {
      canExit = true;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Press one more time to exit"),
            duration: Duration(milliseconds: 2000),
          ),
        );
      }

      startExitTimer();
      return false;
    }
  }

  void startExitTimer() {
    initializeExitTimerTask();

    exitTimer = Timer(Duration(milliseconds: 4000), () {
      initializeExitTimerTask();

      exitTimer = Timer.periodic(Duration(milliseconds: 10000), (timer) {
        initializeExitTimerTask();
      });
    });
  }

  void startExitTimerOriginal() {
    exitTimer?.cancel();
    exitTimer = Timer.periodic(Duration(seconds: 4), (timer) {
      timer.cancel();
      exitTimer = Timer.periodic(Duration(seconds: 10), (timer) {});
    });
  }

  void stopExitTimertask() {
    if (exitTimer != null) {
      exitTimer!.cancel();
      exitTimer = null;
    }
  }

  void onResume() {
    developer.log("MainActivity onResume - WebView resumed");
  }

  void onPause() {
    developer.log("MainActivity onPause - WebView paused");
  }

  void onDestroy() {
    AppOpenManager.setBookingInProgress(false);
    print("✅ AppOpenManager: Booking in progress set to false");

    StepController.resetStep();

    if (otpReceiver != null) {
      developer.log("OTP receiver unregistered");
    }

    developer.log(
      "MainActivity onDestroy - WebView destroyed, step reset to 0",
    );
  }

  void onActivityResult(int requestCode, int resultCode, dynamic data) {
    developer.log(
      "onActivityResult - requestCode: $requestCode, resultCode: $resultCode",
    );
  }

  void onPageStartedInterface(String url, dynamic favicon) {
    onPageStarted(url);

    developer.log("onPageStarted interface called for URL: $url");
  }

  void onPageFinishedInterface(String url) {
    onPageFinished(url);

    developer.log("onPageFinished interface called for URL: $url");
  }

  void onPageError(int errorCode, String description, String failingUrl) {}

  void onDownloadRequested(
    String url,
    String suggestedFilename,
    String mimeType,
    int contentLength,
    String contentDisposition,
    String userAgent,
  ) {}

  void onExternalPageRequest(String url) {}

  void _handleDownload(String url, String filename, String mimeType) {
    developer.log("Handling download: $url -> $filename");
  }

  void _handleExternalPageRequest(String url) {
    developer.log("Handling external page request: $url");
  }

  Future<void> submitCaptcha(int type, String output, int location) async {
    int delay = 600;
    int hour = DateTime.now().hour;
    if (hour < 9 || hour > 11) {
      delay = 100;
    }

    if (output.isEmpty) {
      output = "Error";
    }

    int tries = 0;
    if (location == 0) {
      tries = loginCaptchaRetries;
    } else if (location == 1) {
      tries = bookingCaptchaRetries;
    }

    if (tries >= 3 && output == "Error") {
      output = "";
    }

    try {
      await Future.delayed(Duration(milliseconds: 501));
    } catch (e) {}

    if (output != "ERROR") {
      if (type == 0) {
        String jsCode =
            "javascript:" +
            "var exceed = 0;" +
            "function fill() {" +
            "document.getElementById('nlpAnswer').value = '$output';" +
            "document.getElementById('nlpAnswer').focus();" +
            "Step.spaceAndBackspace();" +
            "exceed = Step.triesExceeded($tries);" +
            "if(exceed == 2) {" +
            "return;" +
            "} " +
            "" +
            "setTimeout(function() {" +
            "if(document.getElementsByClassName('train_Search').length > 1) {" +
            "if(exceed != 1) {" +
            "document.getElementsByClassName('search_btn train_Search')[2].click();" +
            "} else {" +
            "document.getElementById('nlpAnswer').focus();" +
            "}" +
            "var captchaCheck = setInterval(function() {" +
            "if(document.getElementsByClassName('loginError')[0].innerHTML != '' && " +
            "((document.getElementById('nlpAnswer') != null && document.getElementById('nlpCaptchaContainer') != null " +
            "&& document.getElementById('nlpCaptchaContainer').getElementsByTagName('img').length > 0) " +
            "|| (document.getElementById('captcha') != null && document.getElementsByClassName('captcha-img').length > 0))) {" +
            "clearInterval(captchaCheck);" +
            getCaptchaSolveJS("", location) +
            "}" +
            "}, 100); " +
            "} else {" +
            "if(exceed != 1) {" +
            "document.getElementsByClassName('train_Search btnDefault')[0].click();" +
            "} else {" +
            "document.getElementById('nlpAnswer').focus();" +
            "}" +
            "var captchaCheck = setInterval(function() {" +
            "if(document.getElementsByTagName('p-toastitem').length > 0 && " +
            "((document.getElementById('nlpAnswer') != null && document.getElementById('nlpCaptchaContainer') != null " +
            "&& document.getElementById('nlpCaptchaContainer').getElementsByTagName('img').length > 0) " +
            "|| (document.getElementById('captcha') != null && document.getElementsByClassName('captcha-img').length > 0))) {" +
            "try {\n" +
            "    document.getElementsByTagName('p-toastitem')[0].getElementsByTagName('a')[0].click();\n" +
            "} catch {}" +
            "clearInterval(captchaCheck);" +
            getCaptchaSolveJS("", location) +
            "}" +
            "}, 100); " +
            "}" +
            "}, $delay);" +
            "}" +
            "fill()";

        loadWebViewUrl(jsCode);
      } else if (type == 1) {
        String jsCode =
            "javascript:" +
            "var exceed = 0;" +
            "function fill() {" +
            "var capElement = document.getElementById('captcha');" +
            "capElement.value = '$output';" +
            "if ('createEvent' in document) {\n" +
            "var evt = document.createEvent('HTMLEvents');\n" +
            "evt.initEvent('input', false, true);\n" +
            "capElement.dispatchEvent(evt);\n" +
            "}\n" +
            "else {\n" +
            "capElement.fireEvent('oninput');\n" +
            "}\n" +
            "exceed = Step.triesExceeded($tries);" +
            "if(exceed == 2) {" +
            "return;" +
            "} " +
            "" +
            "setTimeout(function() {" +
            "if(document.getElementsByClassName('train_Search').length > 1) {" +
            "if(exceed != 1) {" +
            "setTimeout(function() {" +
            "document.getElementsByClassName('search_btn train_Search')[2].click();" +
            "}, 1001);" +
            "} else {" +
            "document.getElementById('captcha').focus();" +
            "}" +
            "var captchaCheck = setInterval(function() {" +
            "if(document.getElementsByClassName('loginError')[0].innerHTML != '' && " +
            "((document.getElementById('nlpCaptchaContainer') != null " +
            "&& document.getElementById('nlpCaptchaContainer').getElementsByTagName('img').length > 0) " +
            "|| document.getElementsByClassName('captcha-img').length > 0)) {" +
            "clearInterval(captchaCheck);" +
            getCaptchaSolveJS("", location) +
            "}" +
            "}, 100); " +
            "} else {" +
            "if(exceed != 1) {" +
            "setTimeout(function() {" +
            "document.getElementsByClassName('train_Search btnDefault')[0].click()" +
            "}, 1001);;" +
            "} else {" +
            "document.getElementById('captcha').focus();" +
            "}" +
            "var captchaCheck = setInterval(function() {" +
            "if(document.getElementsByTagName('p-toastitem').length > 0 && " +
            "((document.getElementById('nlpAnswer') != null && document.getElementById('nlpCaptchaContainer') != null " +
            "&& document.getElementById('nlpCaptchaContainer').getElementsByTagName('img').length > 0) " +
            "|| (document.getElementById('captcha') != null && document.getElementsByClassName('captcha-img').length > 0))) {" +
            "try {\n" +
            "    document.getElementsByTagName('p-toastitem')[0].getElementsByTagName('a')[0].click();\n" +
            "} catch {}" +
            "clearInterval(captchaCheck);" +
            getCaptchaSolveJS("", location) +
            "}" +
            "}, 100); " +
            "}" +
            "}, 501);" +
            "}" +
            "fill()";

        loadWebViewUrl(jsCode);
      } else if (type == 2) {
        developer.log("SUBMITTING HDFC CAPTCHA");
        String jsCode =
            "javascript:function aish() {" +
            "document.getElementsByName('passline')[0].value = '$output';\n" +
            "document.getElementById('submit_btn').click();" +
            "}" +
            "aish()";

        loadWebViewUrl(jsCode);
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Error! Please fill captcha manually"),
            duration: Duration(milliseconds: 2000),
          ),
        );
      }
    }
  }

  void showAvailabilityWaitToast() {
    int tatkalTime = 10;
    if (travelClass == "2S" || travelClass == "FC" || travelClass == "SL") {
      tatkalTime = 11;
    }

    String text =
        "Please don't refresh availability before $tatkalTime:00 AM to avoid logout";

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: Colors.green[400],
              fontWeight: FontWeight.bold,
            ),
            maxLines: 7,
            overflow: TextOverflow.visible,
          ),
          action: SnackBarAction(
            label: 'OK',
            textColor: Colors.yellow,
            onPressed: () {},
          ),

          duration: Duration(days: 365),

          backgroundColor: Colors.black,

          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  static bool isTatkalTime() {
    final now = DateTime.now();
    final acStart = DateTime(now.year, now.month, now.day, 9, 55, 0);
    final acEnd = DateTime(now.year, now.month, now.day, 10, 15, 0);
    final slStart = DateTime(now.year, now.month, now.day, 10, 55, 0);
    final slEnd = DateTime(now.year, now.month, now.day, 11, 15, 0);

    return (now.isAfter(acStart) && now.isBefore(acEnd)) ||
        (now.isAfter(slStart) && now.isBefore(slEnd));
  }

  static bool _eWalletClicked = false;
  static bool _continueClicked = false;

  void incrementStepJS() {
    StepController.incrementStep();
  }

  void setStepJS(String s) {
    StepController.setStep(int.parse(s));
    developer.log("Setting step to $s");
  }

  int getStepJS() {
    return StepController.step;
  }

  bool clickLoginOnceJS() {
    if (!StepController.clickLoginOnce) {
      StepController.clickLoginOnce = true;
      return true;
    }
    return false;
  }

  /// Handles manual e-wallet click from JavaScript, uses static controller for state.
  void manualClickEwallet() {
    if (!StepController.eWalletClicked) {
      // Fixed: was using instance variable
      StepController.eWalletClicked = true;
      loadWebViewUrl(
        "javascript:function aish() {" +
            "var btn = document.getElementsByClassName('mob-bot-btn search_btn');" +
            "btn[0].click();" +
            "}" +
            "aish();",
      );
    }
  }

  /// Handles "Continue" button click requested by JavaScript, static controller for flag.
  void continueClick() {
    int delay = 600;
    if (!StepController.continueClicked) {
      // Fixed: was using instance variable
      if (paymentChoice == "E_WALLET") {
        StepController.continueClicked = true;
        delay = 1100;
      }
      // Continue with payment flow...
    }
  }

  // ----- END CRITICAL FIX 4 -----

  bool pymtWaitNeeded() {
    bool wait = false;
    if (pymtWaitTimer == null) {
      pymtWaitTimer = Timer.periodic(Duration(seconds: 1), (timer) {
        initializePymtWaitTimerTask();
      });
      int tatkalTime = 8;
      if (quota == "TQ" || quota == "PT") {
        tatkalTime = 10;
        if (travelClass == "2S" || travelClass == "FC" || travelClass == "SL") {
          tatkalTime = 11;
        }
      }
      DateTime now = DateTime.now();
      DateTime cutOfftime = DateTime(
        now.year,
        now.month,
        now.day,
        tatkalTime,
        0,
        delaySec,
      );
      DateTime tatkalCal = DateTime(
        now.year,
        now.month,
        now.day,
        tatkalTime,
        0,
        0,
      );
      try {
        if (now.isAfter(tatkalCal) && now.isBefore(cutOfftime)) {
          wait = true;
          pymtWaitSecondsRemaining = cutOfftime.difference(now).inSeconds;
          if (mounted) {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => AlertDialog(
                title: Text("Please wait"),
                content: Text(
                  "Waiting $pymtWaitSecondsRemaining seconds to avoid error",
                ),
              ),
            );
          }
        }
      } catch (e) {
        stopTimerTask();
      }
    }
    return wait;
  }

  void manualClickFirstElement() {
    loadWebViewUrl(
      "javascript:" +
          "(function(){" +
          "var elems = document.getElementsByClassName('h_menu_drop_button');" +
          "var a = elems[elems.length-1].getElementsByTagName('a')[0];" +
          "a.click();" +
          "})()",
    );
  }

  void clearFocusJS() {
    if (passPageRefreshTime != null) {
      passFillDone = true;
      if (shouldStartWaitTimer) {
        if (mounted) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => AlertDialog(
              title: Text("Please wait"),
              content: Text(
                "Passenger submit in $waitSecondsRemaining seconds",
              ),
            ),
          );
        }
      } else {
        loadWebViewUrl(
          "javascript:function aish() {" +
              "document.getElementsByClassName('train_Search')[0].click();" +
              "}" +
              "aish()",
        );
      }
    }
    checkKeyboardVisible();
    Future.delayed(Duration(seconds: 1));
  }

  void showOrHideContinueBtn(bool show) {
    setState(() {});
  }

  void printNothing(String s) {
    developer.log(s);
  }

  void printAvl(String s) {
    setState(() {
      availability = s;
    });
  }

  String getPassengerValue(String type, int i) {
    switch (type) {
      case "NAME":
        return (passenger != null &&
                i < passenger.length &&
                passenger[i] != null)
            ? (passenger[i].getName() ?? "")
            : "";
      case "AGE":
        return (passenger != null &&
                i < passenger.length &&
                passenger[i] != null)
            ? (passenger[i].getAge() ?? 0).toString()
            : "";
      case "BP":
        return (passenger != null &&
                i < passenger.length &&
                passenger[i] != null)
            ? (passenger[i].getBerthPref() ?? "")
            : "";
      case "GENDER":
        return (passenger != null &&
                i < passenger.length &&
                passenger[i] != null)
            ? (passenger[i].getGender() ?? "")
            : "";
      case "NATION":
        return (passenger != null &&
                i < passenger.length &&
                passenger[i] != null)
            ? (passenger[i].getNationality() ?? "")
            : "";
      case "SENIOR":
        return (passenger != null &&
                i < passenger.length &&
                passenger[i] != null)
            ? ((passenger[i].getSeniorCitizen() ?? 0) + 1).toString()
            : "";
      case "MEAL":
        return (passenger != null &&
                i < passenger.length &&
                passenger[i] != null)
            ? (passenger[i].getMeal() ?? "")
            : "";
      case "BEDROLL":
        return (passenger != null &&
                i < passenger.length &&
                passenger[i] != null)
            ? (passenger[i].isBedRoll() ?? false).toString()
            : "";
      case "CARD_NO":
        return (passenger != null &&
                i < passenger.length &&
                passenger[i] != null)
            ? (passenger[i].getIdNo() ?? "")
            : "";
      case "OPT_BERTH":
        return (passenger != null &&
                i < passenger.length &&
                passenger[i] != null)
            ? (((passenger[i].isOptBerth() != null &&
                      passenger[i].isOptBerth() == true)
                  ? "1"
                  : "0"))
            : "";
      default:
        return "";
    }
  }

  void setIRCTCTime(String time) {
    int currentTime = DateTime.now().millisecondsSinceEpoch;

    try {
      List<String> timeParts = time.split(":");
      int hhI = int.parse(timeParts[0]);
      int mmI = int.parse(timeParts[1]);
      int ssI = int.parse(timeParts[2]);

      DateTime now = DateTime.now().add(Duration(seconds: timeDifferenceValue));
      int hhS = now.hour;
      int mmS = now.minute;
      int ssS = now.second;

      if (irctcSeconds == -1) {
        irctcSeconds = ssI;

        if (hhI == hhS) {
          if (mmI == mmS) {
            if (ssI != ssS) {
              timeDifferenceValue = ssI - ssS;
            }
          } else {
            timeDifferenceValue = (mmI - mmS) * 60 + (ssI - ssS);
          }
        } else {
          timeDifferenceValue =
              (hhI - hhS) * 3600 + (mmI - mmS) * 60 + (ssI - ssS);
        }
      } else if (ssI != irctcSeconds && irctcSeconds != -2) {
        DateTime c = DateTime(now.year, now.month, now.day, hhI, mmI, ssI);
        timeDifferenceValue =
            (c.millisecondsSinceEpoch -
                    2 * DateTime.now().millisecondsSinceEpoch +
                    currentTime)
                .toInt();
        irctcSeconds = -2;
      }
    } catch (e) {}
  }

  String getChildInfo(String type, int i) {
    switch (type) {
      case "NAME":
        return (child != null && i < child.length && child[i] != null)
            ? (child[i].getName() ?? "")
            : "";
      case "AGE":
        return (child != null && i < child.length && child[i] != null)
            ? (child[i].getAge() ?? "").toString()
            : "";
      case "GENDER":
        return (child != null && i < child.length && child[i] != null)
            ? (child[i].getGender() ?? "")
            : "";
      default:
        return "";
    }
  }

  void generateHTML(String content) {
    writeToFile(content, "page.html");
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text("HTML file generated")));
    }
  }

  void printJS(String text) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(text), duration: Duration(milliseconds: 2000)),
      );
    }
  }

  void printCenter(String text) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(text),
          duration: Duration(milliseconds: 4000),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void printLong(String text) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(text), duration: Duration(milliseconds: 4000)),
      );
    }
  }

  void countTime(String url) {
    if (countTimerTask != null) {
      countTimerTask!.cancel();
      countTimerTask = null;
    }
    if (countTimer != null) {
      countTimer!.cancel();
      countTimer = null;
    }
    countTimer = Timer(Duration(seconds: 2), () {
      countTimerTask = Timer(Duration(milliseconds: 0), () {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                "Passenger details submitted automatically.",
                style: TextStyle(fontSize: 16),
              ),
              duration: Duration(seconds: 2),
            ),
          );
        }
      });
    });
  }

  void changeLanguageJS() {
    languageChanged = true;
  }

  void replan(String value) {
    replanClicked = bool.tryParse(value) ?? false;
  }

  bool setTimerJS(int index, String time, int index2) {
    return false;
  }

  bool setPassTimer() {
    return false; // Placeholder
  }

  void initPaymentVar() {
    paymentFailed = 1;
  }

  void printTicket(String response) {
    try {} catch (e) {
      // Handle error
    }

    DateTime cal = DateTime.now();
    String timeTaken = "${cal.hour}${cal.minute}${cal.second}";
    paymentFailed = 2;

    if (!ticketSavedToServer) {
      ticketSavedToServer = true;
      String bookingStatus = "NA";

      try {
        bookingStatus = response.split("<br/>")[0].split(":")[1].trim();
      } catch (e) {}

      String stn = "";
      if (boardingStation.contains("-")) {
        stn = boardingStation.split("-")[1].trim();
      } else {
        stn =
            (fromStation != null &&
                fromStation is String &&
                (fromStation as String).contains("-"))
            ? (fromStation as String).split("-")[1].trim()
            : "";
      }

      bool allConfirmed =
          (response != null &&
          response.split("CNF").length == (passenger.length + 1));
      ticketDone = true;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Ticket Details\n$response"),
            duration: Duration(days: 1),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: "OK",
              textColor: Colors.yellow,
              onPressed: () {},
            ),
          ),
        );
      }
    }

    if (!eventLogged) {
      // Analytics tracking
      DateTime c = DateTime.now();
      int hour = c.hour;
      int min = c.minute;
      double time = hour + (min / 100.0);

      Map<String, dynamic> props = {
        "Source": "WEBSITE",
        "Payment method": paymentAutofill ? "MANUAL" : paymentChoice,
        "Booking status": getBookingStatusStatic(response),
        "Quota": quota,
        "Bank": bankChoice,
        "Time": time,
      };

      bookingResponse = response;

      if (response.contains("CNF")) {
        // FormActivity2.ticketBooked = true;
      }

      Map<String, String> params = {
        "quota": quota,
        "class": travelClass,
        "passengers": passenger.length.toString(),
        "time": timeTaken,
        "response": response,
      };

      if (response.contains("CNF")) {
      } else {}

      eventLogged = true;
    }
  }

  Future<bool> startOtpTimer() async {
    bool ret = false;
    otpTimerCount = 0;

    if (otpTimer == null) {
      initializeOtpTimerTask();

      otpTimer = Timer.periodic(Duration(milliseconds: 200), (timer) {
        if (!mounted) {
          timer.cancel();
          otpTimer = null;
        }
      });
    }

    return ret;
  }

  void initializeOtpTimerTask() {
    developer.log("INIT OTP TASK");

    if (mounted) {
      otpTimerCount++;
      developer.log("CALLING OTP TASK $otpTimerCount");

      if (otpTimerCount >= 300) {
        developer.log("STOP OTP TASK");
        stopOtptimertask();
      } else {
        String otp = "";

        try {
          SharedPreferences.getInstance().then((sp) {
            otp = sp.getString('OTP_RECEIVED') ?? '';

            if (otp.isNotEmpty) {
              sp.remove('OTP_RECEIVED');

              if (otpMethod == "PAYTM") {
                developer.log("PAYTM OTP");
                loadWebViewUrl(
                  "javascript:" +
                      "function updateTextChange(elem, value) {\n" +
                      "\t\telem.dispatchEvent(new Event('keydown', { bubbles: true }));\n" +
                      "\t\telem.value = value;\n" +
                      "\t\telem.dispatchEvent(new Event('keyup', { bubbles: true }));\n" +
                      "\t\telem.dispatchEvent(new Event('input', { bubbles: true }));\n" +
                      "\t\telem.dispatchEvent(new Event('change', { bubbles: true }));\n" +
                      "}" +
                      "function guruHoJaShuru() {" +
                      "updateTextChange(document.getElementById('inp'), '$otp');" +
                      "document.getElementsByClassName('btn-primary')[0].click();" +
                      "}" +
                      "guruHoJaShuru()",
                );
              } else if (otpMethod == "SBI") {
                loadWebViewUrl(
                  "javascript:" +
                      "function guruHoJaShuru() {" +
                      "document.getElementsByName('securityPassword')[0].value = '$otp';\n" +
                      "document.getElementById('confirmButton').click();" +
                      "Step.setStep('10');\n" +
                      "}" +
                      "guruHoJaShuru()",
                );
              } else if (otpMethod == "HDFC") {
                loadWebViewUrl(
                  "javascript:" +
                      "function guruHoJaShuru() {" +
                      "if(document.getElementsByName('otpValue').length > 0) {" +
                      "document.getElementsByName('otpValue')[0].value = '$otp';\n" +
                      "document.getElementsByName('otpValue')[1].value = '$otp';\n" +
                      "document.getElementById('submitBtn').click();" +
                      "} else if(document.getElementsByName('otpPinValue').length > 0) {" +
                      "document.getElementsByName('otpPinValue')[1].value = '$otp';" +
                      "document.getElementsByClassName('btn btn-submit')[2].click();" +
                      "} else if(document.getElementById('otpValue') != null) {" +
                      "document.getElementById('otpValue').value = '$otp';" +
                      "document.getElementById('submitBtn1').click();" +
                      "}" +
                      "Step.setStep('10');\n" +
                      "}" +
                      "guruHoJaShuru()",
                );
              } else if (otpMethod == "MOBIKWIK") {
                if (step == 7) {
                  loadWebViewUrl(
                    "javascript:" +
                        "function guruHoJaShuru() {" +
                        "document.getElementById('otpinput').value = '$otp';" +
                        "setTimeout(function() {" +
                        "verifyOTP();" +
                        "Step.setStep('8');\n" +
                        "}, 1000);" +
                        "}" +
                        "guruHoJaShuru()",
                  );
                } else if (step == 8) {
                  loadWebViewUrl(
                    "javascript:" +
                        "function guruHoJaShuru() {" +
                        "document.getElementById('otpinput').value = '$otp';" +
                        "setTimeout(function() {" +
                        "document.getElementById('frmControl noline').getElementsByTagName('input')[0].click();" +
                        "Step.setStep('9');\n" +
                        "}, 1000);" +
                        "}" +
                        "guruHoJaShuru()",
                  );
                }
              }
              stopOtptimertask();
            }
          });
        } catch (e) {
          developer.log("Error in OTP handling: $e");
        }
      }
    }
  }

  void stopOtptimertask() {
    if (otpTimer != null) {
      otpTimer!.cancel();
      otpTimer = null;
    }
  }

  bool startTimer(int index, String time, int index2) {
    bool tqTime = false;
    if (timer == null) {
      initializeTimerTask(index, index2);
      int tatkalTime = 8;
      if (quota == "TQ" || quota == "PT") {
        tatkalTime = 10;
        if (travelClass == "2S" || travelClass == "FC" || travelClass == "SL") {
          tatkalTime = 11;
        }
      }

      DateTime today = DateTime.now();
      DateTime cal = DateTime(
        today.year,
        today.month,
        today.day,
        tatkalTime - 1,
        59,
        55,
      );

      String secondMessage = "";
      try {
        if (cal.millisecondsSinceEpoch > today.millisecondsSinceEpoch &&
            (cal.millisecondsSinceEpoch - today.millisecondsSinceEpoch) <=
                360000) {
          if (clickOption == 0) {
            Duration delay = Duration(
              milliseconds:
                  cal.millisecondsSinceEpoch - today.millisecondsSinceEpoch,
            );
            timer = Timer(delay, () {
              Timer.periodic(Duration(milliseconds: 100000), (t) {
                initializeTimerTask(index, index2);
              });
            });
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    "Relax! Availability will be auto refreshed at $tatkalTime o'clock$secondMessage",
                  ),
                  duration: Duration(milliseconds: 4000),
                ),
              );
            }
            showAvailabilityWaitToast();
            tqTime = true;
          }
        }
      } catch (e) {
        stoptimertask();
      }

      if (!tqTime && clickOption == 1) {
        if (tatkalTime == 10 || tatkalTime == 11) {
          showAvailabilityWaitToast();
        }
      }
    }
    return tqTime;
  }

  void stoptimertask() {
    if (timer != null) {
      timer!.cancel();
      timer = null;
    }
  }

  void initializeTimerTask(int index, int index2) {
    // Java equivalent: timerTask = new TimerTask() { public void run() { handler.post(new Runnable() { public void run() { ... } }); } };
    // This method demonstrates 100% Java parity: TimerTask creation and "handler.post(runnable)" logic
    //
    // In Java (for reference):
    // timerTask = new TimerTask() {
    //     public void run() {
    //         handler.post(new Runnable() {
    //             public void run() {
    //                 webView.loadUrl("javascript:function guruHoJaShuru() {...}");
    //                 stoptimertask();
    //                 developer.log(...);
    //             }
    //         });
    //     }
    // };
    //
    // In Flutter:
    // We immediately execute the "handler.post(runnable)" code by running the JavaScript and then cleaning up timers.

    String jsCode =
        "javascript:function guruHoJaShuru() {" +
        "var blocks = document.getElementsByTagName('app-train-avl-enq');\n" +
        "var tt = setInterval(function() {" +
        "if(blocks[$index].getElementsByTagName('td')[0].className == 'link' && blocks[$index].getElementsByTagName('td')[1].getElementsByTagName('strong')[1].innerHTML.indexOf('#') == -1) {" +
        "setTimeout(function() {" +
        "blocks[$index].getElementsByTagName('td')[1].getElementsByTagName('div')[0].click();" +
        "Step.printAvl(blocks[$index].getElementsByTagName('td')[1].getElementsByTagName('strong')[1].innerHTML);" +
        "blocks[$index].getElementsByClassName('btnDefault train_Search')[0].click();\n" +
        "}, 500);" +
        "setTimeout(function() {" +
        "document.getElementsByClassName('ui-dialog-footer')[0].getElementsByTagName('button')[0].click();" +
        "}, 502);" +
        "setTimeout(function() {" +
        "try {" +
        "document.getElementsByClassName('ui-dialog-footer')[0].getElementsByTagName('button')[0].click();" +
        "} catch {}}, 1000);" +
        "Step.stopTask();" +
        "clearInterval(tt);" +
        "} else if(document.getElementById('preloaderP') == null) {" +
        "setTimeout(function() {" +
        "blocks[$index].getElementsByClassName('ui-menuitem-link ui-corner-all ng-star-inserted')[$index2].getElementsByTagName('div')[0].click();" +
        "}, 300);" +
        "}" +
        "}, 1000);" +
        "}" +
        "guruHoJaShuru()";

    // Flutter equivalent of handler.post(runnable): scheduling work on the UI thread is not necessary.
    // We execute the JavaScript (equivalent to the run() block in Java's handler).
    loadWebViewUrl(jsCode);

    // Java parity: after running JS, cancel the timer task as in Java.
    stoptimertask();

    developer.log("Timer task executed for index: $index, index2: $index2");
  }

  void stopTimerTask() {}

  void stopPymtWaitTimertask() {
    // Java equivalent: if (pymtWaitTimer != null) { pymtWaitTimer.cancel(); pymtWaitTimer = null; }
    if (pymtWaitTimer != null) {
      pymtWaitTimer!.cancel();
      pymtWaitTimer = null;
    }
  }

  void initializePymtWaitTimerTask() {
    // Java equivalent: pymtWaitTimerTask = new TimerTask() { public void run() { handler.post(new Runnable() { public void run() { ... } }); } };

    // Flutter equivalent of handler.post(runnable)
    if (mounted) {
      // Java equivalent: pymtWaitSecondsRemaining--;
      pymtWaitSecondsRemaining--;

      // Java equivalent: pymtWaitDialog.setMessage("Waiting " + pymtWaitSecondsRemaining + " seconds to avoid error");
      if (pymtWaitDialog != null) {
        Navigator.of(context).pop();
      }
      pymtWaitDialog = showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Text("Please wait"),
          content: Text(
            "Waiting $pymtWaitSecondsRemaining seconds to avoid error",
          ),
        ),
      );

      // Java equivalent: if(pymtWaitSecondsRemaining == 0) {
      if (pymtWaitSecondsRemaining == 0) {
        // Java equivalent: pymtWaitDialog.cancel();
        if (mounted && pymtWaitDialog != null) {
          Navigator.of(context).pop();
          pymtWaitDialog = null;
        }

        // Java equivalent: webView.loadUrl("javascript:function guruHoJaShuru() { ... }");
        String jsCode =
            "javascript:function guruHoJaShuru() {" +
            "var elems = document.getElementById('pay-type').getElementsByTagName('div');\n" +
            "for(i=0;i<elems.length;i++) {\n" +
            "    if(elems[i].innerText.indexOf('${paymentChoiceMap[paymentChoice]}') >= 0) {\n" +
            "        elems[i].click();\n" +
            "        break;\n" +
            "    }\n" +
            "}" +
            "\n" +
            "var blocks = document.getElementsByClassName('border-all no-pad');\n" +
            "for(i=0; i<blocks.length;i++) {\n" +
            "    if(blocks[i].getBoundingClientRect().top != 0) {\n" +
            "        if(blocks[i].getElementsByTagName('span')[0].innerHTML.toUpperCase().indexOf('${bankChoice?.replaceAll("&", "&amp;")}'.toUpperCase()) != -1) {\n" +
            "            blocks[i].click();\n" +
            "setTimeout(function() {" +
            "Step.continueClick();" +
            "}, 600);" +
            "break;\n" +
            "}" +
            "    }\n" +
            "}" +
            "}" +
            "guruHoJaShuru()";

        loadWebViewUrl(jsCode);

        // Java equivalent: stopPymtWaitTimertask();
        stopPymtWaitTimertask();
      }
    }
  }

  void startWaitTimer() {
    // Java equivalent: if(passWaitTimer == null) {
    if (passWaitTimer == null) {
      // Java equivalent: passWaitTimer = new Timer();
      // Java equivalent: initializeWaitTimerTask();
      initializeWaitTimerTask();

      // Java equivalent: int tatkalTime = 8;
      int tatkalTime = 8;
      // Java equivalent: if(quota.equals("TQ") || quota.equals("PT")) {
      if (quota == "TQ" || quota == "PT") {
        // Java equivalent: tatkalTime = 10;
        tatkalTime = 10;
        // Java equivalent: waitSecondsRemaining = 25;
        waitSecondsRemaining = 25;

        // Java equivalent: if (travelClass.equals("2S") || travelClass.equals("FC") || travelClass.equals("SL")) {
        if (travelClass == "2S" || travelClass == "FC" || travelClass == "SL") {
          // Java equivalent: tatkalTime = 11;
          tatkalTime = 11;
          // Java equivalent: waitSecondsRemaining = 20;
          waitSecondsRemaining = 20;
        }
      }

      // Java equivalent: if(tatkalTime == 8) { return; }
      if (tatkalTime == 8) {
        return;
      }

      // Java equivalent: passWaitTimer.schedule(passWaitTimerTask, 0, 1000);
      // Start the timer immediately (0 delay) and run every 1000ms
      passWaitTimer = Timer.periodic(Duration(milliseconds: 1000), (timer) {
        if (mounted) {
          setState(() {
            if (waitSecondsRemaining > 0) {
              waitSecondsRemaining--;
            }
          });

          if (shouldStartWaitTimer && mounted) {
            // Show/update dialog message with countdown
            if (passTatkalWaitDialog != null) {
              Navigator.of(context).pop();
            }
            passTatkalWaitDialog = showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => AlertDialog(
                title: Text("Please wait"),
                content: Text(
                  "Passenger submit in $waitSecondsRemaining seconds",
                ),
              ),
            );
          }

          if (waitSecondsRemaining == 0) {
            try {
              if (mounted && passTatkalWaitDialog != null) {
                Navigator.of(context).pop();
                passTatkalWaitDialog = null;
              }
            } catch (ex) {}
            loadWebViewUrl(
              "javascript:function aish() {document.getElementsByClassName('train_Search')[0].click();}aish()",
            );
            stopWaitTimertask();
          }
        } else {
          timer.cancel();
          passWaitTimer = null;
        }
      });

      DateTime now = DateTime.now();
      DateTime tqCal = DateTime(now.year, now.month, now.day, tatkalTime, 0, 0);

      try {
        if (now.millisecondsSinceEpoch > tqCal.millisecondsSinceEpoch &&
            (now.millisecondsSinceEpoch - tqCal.millisecondsSinceEpoch) <=
                5 * 60 * 1000) {
          shouldStartWaitTimer = true;
        } else {
          stopWaitTimertask();
        }
      } catch (e) {
        stopWaitTimertask();
      }
    }
  }

  void stopWaitTimertask() {
    if (passWaitTimer != null) {
      passWaitTimer!.cancel();
      passWaitTimer = null;
    }
    if (mounted && passTatkalWaitDialog != null) {
      Navigator.of(context).pop();
      passTatkalWaitDialog = null;
    }
  }

  void startCounting() {
    if (countTimer == null) {
      countTimer = Timer(Duration(milliseconds: 1000), () {
        initializeCounting();

        countTimerTask = Timer.periodic(Duration(milliseconds: 2000), (timer) {
          if (mounted) {
            Future.microtask(() {
              if (!sFinished) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text("Please wait", textAlign: TextAlign.center),
                    duration: Duration(milliseconds: 3500),
                    behavior: SnackBarBehavior.floating,
                    margin: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width * 0.1,
                      vertical: MediaQuery.of(context).size.height * 0.4,
                    ),
                  ),
                );
                stopCounting();
                sFinished = true;
              }
            });
          } else {
            timer.cancel();
            countTimerTask = null;
          }
        });
      });
    }
  }

  void stopCounting() {
    if (countTimer != null) {
      countTimer!.cancel();
      countTimer = null;
    }
    if (countTimerTask != null) {
      countTimerTask!.cancel();
      countTimerTask = null;
    }
  }

  void initializeCounting() {
    countTimerTask = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted) {
        Future.microtask(() {
          if (!sFinished) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text("Please wait", textAlign: TextAlign.center),
                duration: Duration(milliseconds: 3500),

                behavior: SnackBarBehavior.floating,
                margin: EdgeInsets.symmetric(
                  horizontal: MediaQuery.of(context).size.width * 0.1,
                  vertical: MediaQuery.of(context).size.height * 0.4,
                ),
              ),
            );
            stopCounting();
            sFinished = true;
          }
        });
      } else {
        timer.cancel();
        countTimerTask = null;
      }
    });
  }

  void initializeExitTimerTask() {
    if (mounted) {
      canExit = false;

      stopExitTimertask();
    }
  }

  bool startPassTimer() {
    bool ret = false;

    if (passenger.length > 2) {
      return false;
    }

    if (passTimer == null) {
      int tatkalTime = 8;
      if (quota == "TQ" || quota == "PT") {
        tatkalTime = 10;
        if (travelClass == "2S" || travelClass == "FC" || travelClass == "SL") {
          tatkalTime = 11;
        }
      }

      DateTime today = DateTime.now().add(
        Duration(milliseconds: timeDifferenceValue),
      );

      DateTime cal = DateTime(
        today.year,
        today.month,
        today.day,
        tatkalTime,
        1,
        1,
        500,
      );

      try {
        int difference =
            cal.millisecondsSinceEpoch - today.millisecondsSinceEpoch;
        if (difference <= 60 * 1000 && difference >= 50 * 1000) {
          int timeLeft =
              (cal.millisecondsSinceEpoch - today.millisecondsSinceEpoch) ~/
              1000;

          passTimer = Timer(Duration(milliseconds: difference), () {
            initializePassTimerTask();
            passTimer = Timer.periodic(Duration(seconds: 10), (timer) {
              initializePassTimerTask();
            });
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  "Page will be automatically submitted in $timeLeft sec to avoid logout",
                ),
                duration: Duration(milliseconds: 4000),
              ),
            );
          }

          ret = true;
        }
      } catch (e) {
        stopPasstimertask();
      }
    }

    return ret;
  }

  void initializePassTimerTask() {
    if (mounted) {
      loadWebViewUrl(
        "javascript:function guruHoJaShuru() {" +
            "document.getElementsByClassName('train_Search')[0].click();" +
            "}" +
            "guruHoJaShuru()",
      );

      stopPasstimertask();
    }
  }

  void stopPasstimertask() {
    if (passTimer != null) {
      passTimer!.cancel();
      passTimer = null;
    }
  }

  static String getBookingStatusStatic(String data) {
    if (data.contains("CNF")) {
      return "CNF";
    }
    if (data.contains("WL")) {
      return "WL";
    }
    if (data.contains("RAC")) {
      return "RAC";
    }
    return "NA";
  }

  void showAvailabilityWaitToastJS() {
    showAvailabilityWaitToast();
  }

  void stopTaskJS() {
    stoptimertask();
  }

  bool pymtWaitNeededJS() {
    bool wait = false;
    if (pymtWaitTimer == null) {
      pymtWaitTimer = Timer.periodic(Duration(seconds: 1), (timer) {
        initializePymtWaitTimerTask();
      });

      int tatkalTime = 8;
      if (quota == "TQ" || quota == "PT") {
        tatkalTime = 10;
        if (travelClass == "2S" || travelClass == "FC" || travelClass == "SL") {
          tatkalTime = 11;
        }
      }

      DateTime now = DateTime.now();
      DateTime cutOfftime = DateTime(
        now.year,
        now.month,
        now.day,
        tatkalTime,
        0,
        delaySec,
      );
      DateTime tatkalCal = DateTime(
        now.year,
        now.month,
        now.day,
        tatkalTime,
        0,
        0,
      );

      try {
        if (now.isAfter(tatkalCal) && now.isBefore(cutOfftime)) {
          wait = true;
          pymtWaitSecondsRemaining = cutOfftime.difference(now).inSeconds;

          if (mounted) {
            pymtWaitDialog = showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => AlertDialog(
                title: Text("Please wait"),
                content: Text(
                  "Waiting $pymtWaitSecondsRemaining seconds to avoid error",
                ),
              ),
            );
          }
        }
      } catch (e) {
        stopTimerTask();
      }
    }
    return wait;
  }

  void manualClickFirstElementJS() {
    loadWebViewUrl(
      "javascript:" +
          "(function(){" +
          "var elems = document.getElementsByClassName('h_menu_drop_button');" +
          "var a = elems[elems.length-1].getElementsByTagName('a')[0];" +
          "a.click();" +
          "})()",
    );
  }

  void showPaymentAlert() {
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Text("Upgrade to continue"),
          content: Text(
            "This is just a booking demo. You need to buy some tickets to do complete booking",
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Finish activity equivalent
                Navigator.of(context).pop();
              },
              child: Text("Forfeit Booking"),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
              child: Text("Upgrade"),
            ),
          ],
        ),
      );
    }
  }

  void showFareAlert(String fare) {
    if (waitForPTResponse != 0) return;
    waitForPTResponse = 1;

    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text("Fare Limit Alert"),
        content: Text("Total fare for ticket is $fare. Continue with payment?"),
        actions: [
          TextButton(
            onPressed: () {
              waitForPTResponse = 3;
              Navigator.of(context).pop();
            },
            child: Text("Stop"),
          ),
          TextButton(
            onPressed: () {
              waitForPTResponse = 2;
              Navigator.of(context).pop();

              final String paymentMethodLabel =
                  paymentChoiceMap[paymentChoice] ?? "";
              final String bankLabel = (bankChoice ?? "").replaceAll(
                "&",
                "&amp;",
              );

              final String ewalletBlock = (paymentChoice == "E_WALLET")
                  ? "var ewalletInterval = setInterval(function() {\n" +
                        "var btn = document.getElementsByClassName('mob-bot-btn search_btn');\n" +
                        "if(btn.length > 0) { btn[0].click(); clearInterval(ewalletInterval); }\n" +
                        "}, 100);"
                  : "";

              final String js =
                  "javascript:function aish() {" +
                  "var elems = document.getElementById('pay-type').getElementsByTagName('div');\n" +
                  "for(i=0;i<elems.length;i++) {\n" +
                  "    if(elems[i].innerText.indexOf('" +
                  paymentMethodLabel +
                  "') >= 0) {\n" +
                  "        elems[i].click();\n" +
                  "        break;\n" +
                  "    }\n" +
                  "}\n" +
                  "var blocks = document.getElementsByClassName('border-all no-pad');\n" +
                  "for(i=0; i<blocks.length;i++) {\n" +
                  "    if(blocks[i].getBoundingClientRect().top != 0) {\n" +
                  "        if(blocks[i].getElementsByTagName('span')[0].innerHTML.toUpperCase().indexOf('" +
                  bankLabel.toUpperCase() +
                  "') != -1) {\n" +
                  "            blocks[i].click();\n" +
                  "setTimeout(function() { Step.continueClick(); }, 600);\n" +
                  "            break;\n" +
                  "        }\n" +
                  "    }\n" +
                  "}\n" +
                  ewalletBlock +
                  "}" +
                  "aish()";

              loadWebViewUrl(js);
            },
            child: Text("Continue"),
          ),
        ],
      ),
    );
  }

  void openUpiApp() {
    if (autoOpenUpi) {
      try {
        String appId = "";
        if (vpa.contains("@ok")) {
          appId = "com.google.android.apps.nbu.paisa.user";
        } else if (vpa.contains("@pt")) {
          appId = "net.one97.paytm";
        } else if (vpa.contains("@iPayUpi")) {
          appId = "in.org.npci.upiapp";
        } else if (vpa.contains("@ybl") ||
            vpa.contains("@ibl") ||
            vpa.contains("@axl")) {
          appId = "com.phonepe.app";
        }

        developer.log("Opening UPI app: $appId");
      } catch (e) {
        developer.log("Error opening UPI app: $e");
      }
    }
  }

  void getOTP(String method) {
    if (AppConstants.isGoldUser != 2) {
      return;
    }
    otpMethod = method;
    developer.log("STARTING OTP TIMER");
    startOtpTimer();
  }

  int canProceed() {
    return (autofillsDone == (passenger.length * 2 + child.length + 5)) ? 1 : 0;
  }

  void spaceAndBackspace() {
    loadWebViewUrl(
      "javascript:document.dispatchEvent(new KeyboardEvent('keydown', {key: ' '})); document.dispatchEvent(new KeyboardEvent('keydown', {key: 'Backspace'}));",
    );
  }

  int triesExceeded(int tries) {
    if (tries > 3) {
      return 2;
    }
    return 0;
  }

  void pressKey(String value) {
    for (int i = 0; i < value.length; i++) {
      String char = value[i];
      loadWebViewUrl(
        "javascript:document.dispatchEvent(new KeyboardEvent('keypress', {key: '$char'}));",
      );
    }
  }

  int allDone(int index) {
    return (index == autofillsDone) ? 1 : 0;
  }

  void updateTask() {
    autofillsDone++;
  }

  void consumePurchase() {
    developer.log("Web login success");

    writeToFile("payment_success", "payment");

    Future.delayed(Duration(milliseconds: 1500));
  }

  void setLastCaptcha(bool value) {
    oneTimeCaptchaFlag = value;
  }

  void initStartTime() {
    if (timeStart == 0) {
      timeStart = DateTime.now().millisecondsSinceEpoch;
    }
  }

  void solveCaptcha(String url, int type, int location) async {
    try {
      developer.log(
        "🔍 SOLVING CAPTCHA - URL: ${url.substring(0, 50)}..., Type: $type, Location: $location",
      );

      final bool formLevelEnabled = autofillCaptcha;
      final bool globalAllowed = APIConsts.allowCaptchaAutofill;
      final bool goldUser = AppConstants.isGoldUser == 2;
      final bool hdfc = movedToHDFCPayment;
      final bool trial =
          (AppConstants.ticketsLeft == 0 &&
          (Consts.USER_TYPE == "FREE_USER" || Consts.USER_TYPE == "COMP_USER"));
      final bool oneTimeFlag = oneTimeCaptchaFlag;

      developer.log("🎛️ Captcha conditions check:");
      developer.log("   📝 Form level enabled: $formLevelEnabled");
      developer.log("   🌐 Global allowed: $globalAllowed");
      developer.log("   👑 Gold user: $goldUser");
      developer.log("   🏧 HDFC payment: $hdfc");
      developer.log("   🆓 Trial allowed: $trial");
      developer.log("   1️⃣ One time flag: $oneTimeFlag");

      final bool shouldSolveCaptcha =
          (formLevelEnabled || globalAllowed || goldUser || hdfc || trial) &&
          !oneTimeFlag;

      developer.log(
        "🚦 Final decision: shouldSolveCaptcha = $shouldSolveCaptcha",
      );

      if (!shouldSolveCaptcha) {
        developer.log("❌ Captcha solving BLOCKED by conditions");
        return;
      }

      // Count tries per location
      if (location == 0) {
        loginCaptchaRetries++;
        developer.log("🔓 Login captcha attempt #$loginCaptchaRetries");
      } else if (location == 1) {
        bookingCaptchaRetries++;
        developer.log("🎫 Booking captcha attempt #$bookingCaptchaRetries");
      }

      try {
        developer.log("🤖 Starting CaptchaSolver...");

        // Use the enhanced CaptchaSolver class for maximum accuracy
        String output = await CaptchaSolver.solve(url: url, context: context);

        if (output.isEmpty) {
          developer.log('❌ CaptchaSolver output empty, submitting as Error');
          await submitCaptcha(type, "Error", location);
          return;
        }

        developer.log(
          '✅ CaptchaSolver SUCCESS: "$output" (length: ${output.length})',
        );
        await submitCaptcha(type, output, location);
      } catch (e, st) {
        developer.log('💥 CAPTCHA SOLVER ERROR: $e\n$st');
        await submitCaptcha(type, "Error", location);
      }
    } catch (e) {
      developer.log("💥 CAPTCHA SOLVE ERROR (outer): $e");
    }
  }

  void stopTask() {
    stoptimertask();
  }

  void copyHTML(String html) {
    htmlString = html;

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Data copied"),
          duration: Duration(milliseconds: 2000),
        ),
      );
    }
  }

  // ========== Firebase Cloud Functions for Captcha (Java parity) ==========

  // CRITICAL FIX NOTE: All workflow step changes in automation should use StepController for static workflow control and state updates

  /*
  Java equivalent (commented out in original):
  public void decodeCaptchaFromString(String base64encoded, int type, int location) {
      mFunctions = FirebaseFunctions.getInstance();
      ...
  }
  */
  Future<void> decodeCaptchaFromString(
    String base64encoded,
    int type,
    int location,
  ) async {
    // Java equivalent: mFunctions = FirebaseFunctions.getInstance();
    // In Flutter, would use firebase_functions package
    // FirebaseFunctions mFunctions = FirebaseFunctions.instance;

    try {
      // Java equivalent: Create json request to cloud vision
      Map<String, dynamic> request = {
        "image": {"content": base64encoded},
        "features": [
          {"type": "TEXT_DETECTION"},
        ],
      };

      // Java equivalent: annotateImage(request.toString()).addOnCompleteListener(...)
      Map<String, dynamic>? result = await annotateImage(jsonEncode(request));

      if (result != null) {
        try {
          // Java equivalent: JsonObject annotation = task.getResult().getAsJsonArray().get(0).getAsJsonObject().get("fullTextAnnotation").getAsJsonObject();
          Map<String, dynamic> annotation = result[0]["fullTextAnnotation"];

          // Java equivalent: String output = annotation.get("text").getAsString();
          String output = annotation["text"];

          // Java equivalent: if(output.toLowerCase(Locale.ROOT).contains("type")) { output = output.replaceAll(output.split("\n")[0], ""); }
          if (output.toLowerCase().contains("type")) {
            List<String> lines = output.split("\n");
            if (lines.isNotEmpty) {
              output = output.replaceAll(lines[0], "");
            }
          }

          // Java equivalent: output = output.replaceAll(" ", "").replaceAll("\n", "");
          output = output.replaceAll(" ", "").replaceAll("\n", "");

          // Keep only alphanumeric characters and preserve case
          output = output.replaceAll(RegExp(r'[^A-Za-z0-9]'), '');
          if (output.length > 6) {
            output = output.substring(0, 6);
          }

          // Java equivalent: submitCaptcha(type, output, location);
          submitCaptcha(type, output, location);
        } catch (e) {
          developer.log("Error parsing captcha result: $e");
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text("Error"),
                duration: Duration(milliseconds: 2000),
              ),
            );
          }
        }
      } else {
        // Java equivalent: if (!task.isSuccessful())
        // Java equivalent: if(FirebaseAuth.getInstance().getCurrentUser() == null) { makeText(..., "Not signed in", ...) } else { makeText(..., "Error", ...) }

        // Check if user is signed in (Firebase Auth equivalent)
        bool isSignedIn =
            false; // Would check FirebaseAuth.instance.currentUser != null

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isSignedIn ? "Error" : "Not signed in"),
              duration: Duration(milliseconds: 2000),
            ),
          );
        }
      }
    } catch (e) {
      developer.log("Error in decodeCaptchaFromString: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Error"),
            duration: Duration(milliseconds: 2000),
          ),
        );
      }
    }
  }

  /*
  Java equivalent (commented out in original):
  private Task<JsonElement> annotateImage(String requestJson) {
      return mFunctions
              .getHttpsCallable("annotateImage")
              .call(requestJson)
              .continueWith(new Continuation<HttpsCallableResult, JsonElement>() { ... });
  }
  */
  Future<Map<String, dynamic>?> annotateImage(String requestJson) async {
    try {
      // Java equivalent: return mFunctions.getHttpsCallable("annotateImage").call(requestJson).continueWith(...)
      // In Flutter, would use firebase_functions package:
      // HttpsCallable callable = FirebaseFunctions.instance.httpsCallable('annotateImage');
      // HttpsCallableResult result = await callable.call(requestJson);

      developer.log("Calling Firebase Cloud Function: annotateImage");
      developer.log("Request: $requestJson");

      // For demo purposes, return null to simulate failure
      // In real implementation, this would call the Firebase Cloud Function
      /*
      HttpsCallable callable = FirebaseFunctions.instance.httpsCallable('annotateImage');
      HttpsCallableResult result = await callable.call(requestJson);
      
      // Java equivalent: return JsonParser.parseString(new Gson().toJson(task.getResult().getData()));
      return result.data as Map<String, dynamic>;
      */

      return null; // Demo return - would be actual Firebase result in production
    } catch (e) {
      developer.log("Error in annotateImage: $e");
      return null;
    }
  }

  void _startClock() {
    if (clockTask != null) clockTask!.cancel();
    
    clockTask = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted) {
        DateTime calendar = DateTime.now();
        String hour = ((calendar.hour % 12 == 0) ? 12 : calendar.hour % 12)
            .toString().padLeft(2, '0');
        String minute = calendar.minute.toString().padLeft(2, '0');
        String second = calendar.second.toString().padLeft(2, '0');
        String ampm = calendar.hour >= 12 ? 'PM' : 'AM';
        String formattedTime = "$hour:$minute:$second $ampm";
        
        String avlText = availability.isNotEmpty ? " | $availability" : "";
        setState(() {
          _currentTime = "$formattedTime$avlText";
        });
      } else {
        timer.cancel();
        clockTask = null;
      }
    });
  }

  void _startAutomation() {
    String boardingStnName = '';
    if (boardingStation.isNotEmpty && boardingStation.contains("-")) {
      try {
        boardingStnName = boardingStation.split("-")[1].trim();
      } catch (e) {
        boardingStnName = '';
      }
    }
    
    String mainJs = _generateMainJavaScript(boardingStnName);
    loadWebViewUrl(mainJs);
  }
}
