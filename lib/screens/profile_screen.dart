import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../core/Consts.dart';
import '../core/utils/AppColors.dart';
import '../core/AppConstants.dart';
import '../core/helper/mixpanel_manager.dart';
import '../database/LoginDB.dart';
import '../pg/BuyTicketsDialog.dart';
import '../routes/app_routes.dart';
import '../server/FirestoreFunctions.dart';
import '../screens/splash_screen.dart' show SplashScreenState;
import 'RateUsDialog.dart';
import 'SignUpScreen.dart';
import 'EditProfile.dart';
import 'FAQSupportScreen.dart';
import 'LegalScreenList.dart';
import '../quick/PhoneFragment.dart';
import 'package:share_plus/share_plus.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import '../pg/TicketInfoDialog.dart';
import '../quick/my_context_wrapper.dart';
import '../quick/ui/LanguageFragment.dart';
import 'package:google_sign_in/google_sign_in.dart';

/*
 * AUTHOR : LOKESH
 * GIT-telestic1
 * DATE : 11-JULY-25
 */
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool isLoggedIn = false;
  String tickets = "";
  String selectedLang = 'English';

  int rating = 0;
  static bool logoutClicked = false;
  bool _isLoggingOut = false;

  bool _isInitialized = false;
  bool _isDataLoaded = false;
  String? _lastDataHash;

  final GoogleSignIn _googleSignIn = GoogleSignIn.instance;
  bool _isGoogleSignInInitialized = false;

  @override
  void initState() {
    super.initState();
    _initAsync();
  }

  Future<void> _initAsync() async {
    if (_isInitialized) return;

    final mixpanelManager = MixpanelManager();
    mixpanelManager.track("Profile load");

    await AppConstants.loadUserData();
    _isDataLoaded = true;

    await _loadSelectedLanguage();

    _updateUI();
    _isInitialized = true;
    _initializeGoogleSignIn();
  }

  Future<void> _loadSelectedLanguage() async {
    try {
      final languageName = await MyContextWrapper.getCurrentLanguageName();
      setState(() {
        selectedLang = languageName;
      });
    } catch (e) {
      setState(() {
        selectedLang = 'English'; // Default fallback
      });
    }
  }

  Future<void> _initializeGoogleSignIn() async {
    try {
      await _googleSignIn.initialize();
      _isGoogleSignInInitialized = true;
    } catch (e) {
      debugPrint('Failed to initialize Google Sign-In: $e');
    }
  }

  Future<void> _ensureGoogleSignInInitialized() async {
    if (!_isGoogleSignInInitialized) {
      await _initializeGoogleSignIn();
    }
  }

  Future<void> _refreshAppConstantsFromSplashScreen() async {
    if (SplashScreenState.EMAIL != "NA") {
      AppConstants.email = SplashScreenState.EMAIL;
    }
    if (SplashScreenState.PRIMARY_EMAIL != "NA") {
      AppConstants.primaryEmail = SplashScreenState.PRIMARY_EMAIL;
    }
    if (SplashScreenState.CUST_NAME.isNotEmpty) {
      AppConstants.custName = SplashScreenState.CUST_NAME;
    }
    if (SplashScreenState.MOBILE_NO != "NA") {
      AppConstants.mobileNo = SplashScreenState.MOBILE_NO;
    }
    if (SplashScreenState.PROFILE_PIC != null &&
        SplashScreenState.PROFILE_PIC!.isNotEmpty) {
      AppConstants.profilePicUrl = SplashScreenState.PROFILE_PIC;
    }

    debugPrint("Profile data synced from SplashScreenState to AppConstants");
    debugPrint("Profile pic URL: ${AppConstants.profilePicUrl}");
  }

  void _openOtpScreen() async {
    final result = await Get.toNamed(
      AppRoutes.otp,
      arguments: {
        'mobile': AppConstants.mobileNo,
        'display': AppConstants.mobileNo,
      },
    );

    if (result == true) {
      _updateUI();
    }
  }

  void _updateUI() {
    final currentDataHash = _generateUserDataHash();

    if (_lastDataHash == currentDataHash && _isDataLoaded) {
      debugPrint("Profile UI Update: Skipped - No data changes detected");
      return;
    }

    _lastDataHash = currentDataHash;

    setState(() {
      AppConstants.isLoggedIn =
          (AppConstants.email != "NA" ||
          AppConstants.mobileNo != "NA" ||
          (AppConstants.loginMethod != "LATER" &&
              AppConstants.custName.isNotEmpty));

      tickets = AppConstants.ticketsLeft > 0
          ? "You have ${AppConstants.ticketsLeft} tickets left"
          : "No tickets left";

      isLoggedIn = AppConstants.isLoggedIn;

      debugPrint("Profile UI Update:");
      debugPrint("  - User email: ${AppConstants.email}");
      debugPrint("  - User name: ${AppConstants.custName}");
      debugPrint("  - Mobile: ${AppConstants.mobileNo}");
      debugPrint("  - Login method: ${AppConstants.loginMethod}");
      debugPrint("  - Profile pic URL: ${AppConstants.profilePicUrl}");
      debugPrint("  - Is logged in: ${AppConstants.isLoggedIn}");
      debugPrint(
        "  - custName.isNotEmpty: ${AppConstants.custName.isNotEmpty}",
      );
      debugPrint(
        "  - loginMethod != 'LATER': ${AppConstants.loginMethod != "LATER"}",
      );
      debugPrint("  - Data hash: $currentDataHash");

      if (AppConstants.isLoggedIn && AppConstants.email != "NA") {
        Fluttertoast.showToast(msg: "Logged in as ${AppConstants.email}");
      } else if (AppConstants.isLoggedIn && AppConstants.custName.isNotEmpty) {
        Fluttertoast.showToast(msg: "Logged in as ${AppConstants.custName}");
      }
    });
  }

  String _generateUserDataHash() {
    return '${AppConstants.email}_${AppConstants.custName}_${AppConstants.mobileNo}_'
        '${AppConstants.loginMethod}_${AppConstants.isLoggedIn}_${AppConstants.ticketsLeft}';
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_isInitialized && mounted) {
      final currentHash = _generateUserDataHash();
      if (_lastDataHash != currentHash) {
        debugPrint("Profile UI Update: Dependencies changed, updating UI");
        _updateUI();
      }
    }
  }

  void refreshUserData() {
    debugPrint("Profile UI Update: External refresh requested");
    AppConstants.loadUserData().then((_) => _updateUI());
  }

  @override
  Widget build(BuildContext context) {
    bool isLoggedIn = AppConstants.isLoggedIn;

    return WillPopScope(
      onWillPop: () async {
        Get.offAllNamed(AppRoutes.home);
        return false;
      },
      child: Scaffold(
        backgroundColor: AppColors.primaryColor,
        appBar: AppBar(
          backgroundColor: Colors.black,
          elevation: 4,
          automaticallyImplyLeading: false,
          title: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              "Quick Tatkal",
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        body: SafeArea(
          child: Column(
            children: [
              _buildTopBanner(isLoggedIn),
              Expanded(
                child: Column(
                  children: [
                    Expanded(
                      flex: 1,
                      child: _buildActionCard(
                        'Your Tickets',
                        tickets,
                        'assets/images/ticket_white.png',
                        () {
                          if (Consts.USER_TYPE == 'DIAMOND_USER') {
                            // Simple dialog for Diamond users
                            showDialog(
                              context: context,
                              builder: (_) => const BuyTicketsDialog(),
                            );
                          } else {
                            // Detailed ticket info dialog for other users
                            showDialog(
                              context: context,
                              builder: (_) => const TicketInfoDialog(type: 0),
                            );
                          }
                        },
                      ),
                    ),
                    _buildDivider(),
                    Expanded(
                      flex: 1,
                      child: _buildActionCard(
                        'Help & Support',
                        'Get help on train ticket booking',
                        'assets/images/support.png',
                        () {
                          Get.to(() => const FAQSupportScreen());
                        },
                      ),
                    ),
                    _buildDivider(),
                    Expanded(
                      flex: 1,
                      child: _buildActionCard(
                        'Language',
                        selectedLang,
                        'assets/images/language.png',
                        () {
                          LanguageFragment.showLanguageDialog(context);
                        },
                      ),
                    ),
                    _buildDivider(),
                    Expanded(
                      flex: 1,
                      child: _buildActionCard(
                        'Share App',
                        'Invite friends to Quick Tatkal',
                        'assets/images/share_white.png',
                        _shareApp,
                      ),
                    ),
                    _buildDivider(),
                    Expanded(
                      flex: 1,
                      child: _buildActionCard(
                        'Rate Us',
                        "We'd like your feedback",
                        'assets/images/star.png',
                        () {
                          print("Click Rate");
                          RateUsDialog.showRateUsDialog(context);
                        },
                      ),
                    ),
                    _buildDivider(),
                    Expanded(
                      flex: 1,
                      child: _buildActionCard(
                        'Legal',
                        'Privacy Policy, Terms & Conditions, Accessibility Services',
                        'assets/images/info_white.png',
                        () {
                          Get.to(() => const LegalActivityList());
                        },
                      ),
                    ),
                    _buildDivider(),
                  ],
                ),
              ),
              if (isLoggedIn)
                Container(
                  padding: const EdgeInsets.all(10),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _isLoggingOut = true;
                      });
                      _confirmLogout(context, Consts.loginMethod).then((value) {
                        setState(() {
                          _isLoggingOut = false;
                        });
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/logout.png',
                          width: 30,
                          height: 30,
                        ),
                        const SizedBox(width: 10),
                        const Text(
                          "Logout",
                          style: TextStyle(
                            fontFamily: 'Poppins-MediumItalic',
                            color: Color(0xFFCF6679),
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              if (_isLoggingOut)
                Positioned.fill(
                  child: Container(
                    color: const Color(0x44FFFFFF),
                    child: const Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(color: Colors.white),
                          SizedBox(height: 10),
                          Text(
                            'Logging out…',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopBanner(bool isLoggedIn) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF8E2DE2), Color(0xFF4A00E0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 37,
            backgroundImage: AppConstants.profilePicUrl != null
                ? NetworkImage(AppConstants.profilePicUrl!)
                : const AssetImage('assets/images/pic.png') as ImageProvider,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isLoggedIn ? AppConstants.custName : 'Awesome User',
                  style: const TextStyle(
                    fontSize: 20,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (isLoggedIn && AppConstants.email != "NA")
                  Text(
                    AppConstants.email,
                    style: const TextStyle(color: Colors.white70),
                  ),
                if (isLoggedIn &&
                    AppConstants.email == "NA" &&
                    AppConstants.loginMethod == "FACEBOOK")
                  Text(
                    "Logged in with Facebook",
                    style: const TextStyle(color: Colors.white70),
                  ),
                if (isLoggedIn && AppConstants.mobileNo != "NA")
                  Text(
                    "${AppConstants.mobileNo}",
                    style: const TextStyle(color: Colors.white70),
                  ),
                const SizedBox(height: 6),
                GestureDetector(
                  onTap: () async {
                    if (isLoggedIn) {
                      await _refreshAppConstantsFromSplashScreen();

                      final updated = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => EditProfile(
                            initialName: AppConstants.custName,
                            initialEmail: AppConstants.email,
                            initialMobile: AppConstants.mobileNo,
                          ),
                        ),
                      );
                      if (updated == true) {
                        AppConstants.email = SplashScreenState.EMAIL;
                        AppConstants.primaryEmail =
                            SplashScreenState.PRIMARY_EMAIL;
                        AppConstants.mobileNo = SplashScreenState.MOBILE_NO;
                        setState(() {});
                      }
                    } else {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => const SignUpScreen()),
                      ).then((_) {
                        _updateUI();
                      });
                    }
                  },
                  child: Text(
                    isLoggedIn ? 'Edit Profile' : 'Sign In >',
                    style: const TextStyle(
                      color: Colors.white,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (isLoggedIn) ...[
                  const SizedBox(height: 10),
                  GestureDetector(
                    onTap: () {
                      _showDeleteAccountDialog(context);
                    },
                    child: const Text(
                      'Delete Account',
                      style: TextStyle(
                        color: Colors.redAccent,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showPhoneFragmentDialog() async {
    final String? newNumber = await PhoneFragment.showOnProfile(context);
    if (newNumber != null && newNumber.isNotEmpty) {
      setState(() {
        AppConstants.mobileNo = newNumber;
        SplashScreenState.MOBILE_NO = newNumber;
      });
    }
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    String imagePath,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Image.asset(imagePath, width: 36, height: 36),
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(fontFamily: 'Poppins', color: Colors.white70),
      ),
      onTap: onTap,
    );
  }

  Widget _buildDivider() {
    return const Divider(color: Colors.white);
  }

  void _showDeleteAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text("Confirm Account Deletion"),
          content: const Text(
            "Are you sure you want to delete your account permanently? "
            "This action is irreversible and you'll lose all your purchases.",
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
              child: const Text("Cancel"),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                FirestoreFunctions(context).deleteAccount();
              },
              child: const Text(
                "Delete Permanently",
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _confirmLogout(BuildContext context, String loginMethod) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Logout"),
        content: const Text(
          "Are you sure you want to logout? You'll need to sign in again to use the app",
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text("Cancel"),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              setState(() => _isLoggingOut = true);

              MixpanelManager().track("Logout", {"Login method": loginMethod});

              switch (loginMethod) {
                case "FACEBOOK":
                  try {
                    await FacebookAuth.instance.logOut();
                    MixpanelManager().reset();
                  } catch (e) {
                    debugPrint("FACEBOOK SIGNIN ERROR: $e");
                    MixpanelManager().track("Graph request exception", {
                      "error": e.toString(),
                    });
                    showToast("Error signing out. Please try again.");
                    setState(() => _isLoggingOut = false);
                    return;
                  }
                  break;

                case "MOBILE":
                  try {
                    await LoginDB.deleteAll();
                    await LoginDB.closeDB();
                  } catch (e) {
                    debugPrint("MOBILE SIGNOUT ERROR: $e");
                    showToast("Error signing out. Please try again.");
                    setState(() => _isLoggingOut = false);
                    return;
                  }
                  break;

                case "GOOGLE":
                  try {
                    await _ensureGoogleSignInInitialized();
                    await FirebaseAuth.instance.signOut();
                    await _googleSignIn.signOut();
                    MixpanelManager().reset();
                  } catch (e) {
                    debugPrint("GOOGLE SIGNOUT ERROR: $e");
                    showToast("Error signing out. Please try again.");
                    setState(() => _isLoggingOut = false);
                    return;
                  }
                  break;

                default:
                  debugPrint("Unknown login method: $loginMethod");
                  break;
              }

              setState(() => _isLoggingOut = false);
              showToast("Logged out successfully.");
              resetVariablesAndLogout();
            },
            child: const Text("Logout"),
          ),
        ],
      ),
    );
  }

  void showToast(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
  }

  void resetVariablesAndLogout() async {
    AppConstants.isLoggedIn = false;
    AppConstants.mobileNo = "NA";
    AppConstants.email = "NA";
    AppConstants.primaryEmail = "NA";
    AppConstants.custName = "";
    AppConstants.profilePicUrl = null;
    AppConstants.loginMethod = "LATER";
    AppConstants.logoutClicked = true;
    AppConstants.ticketsLeft = 0;
    AppConstants.isGoldUser = 1;
    AppConstants.tid = "0";
    AppConstants.invitedBy = "NA";
    AppConstants.signupSource = "Logout";

    Consts.loginMethod = "LATER";
    Consts.SignupSource = "Logout";
    Consts.USER_TYPE = "FREE_USER";
    Consts.PACK_EXPIRY_DATE = "";
    Consts.PACK_EXPIRED = 1;

    MixpanelManager().reset();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    } catch (e) {
      debugPrint("Error clearing preferences: $e");
    }

    Get.offAllNamed(AppRoutes.home);
  }

  void _shareApp() {
    MixpanelManager().track("Click Share App");

    const String shareMessage =
        "Hey, if you want to book confirm tatkal tickets, download Quick Tatkal https://play.google.com/store/apps/details?id=com.tatkal.train.quick. It autofills everything and is the fastest app in market for booking tatkal as well as general train tickets. You'll definitely love it!";

    Share.share(shareMessage);

    FirebaseAnalytics.instance.logEvent(
      name: 'invite_sent',
      parameters: {'invited': 'true'},
    );
  }
}
