import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../screens/TrainsBetweenStations.dart';

class TrainAvlSearch extends StatefulWidget {
  const TrainAvlSearch({Key? key}) : super(key: key);

  @override
  State<TrainAvlSearch> createState() => _TrainAvlSearchState();
}

class _TrainAvlSearchState extends State<TrainAvlSearch> {
  // --- Java: AutoCompleteTextView and EditText fields ---
  late TextEditingController fromStnController;
  late TextEditingController toStnController;
  late TextEditingController dateEdController;
  late String selectedQuota;

  // --- Java: List<String> stations (simulate large station list from Java) ---
  List<String> stationList = [
    'NEW DELHI - NDLS',
    'MUMBAI CENTRAL - BCT',
    'CHENNAI CENTRAL - MAS',
    'KOLKATA - KOAA',
    'BANGALORE - SBC',
    'KANPUR CENTRAL - CNB',
    'PATNA JN - PNBE',
    'SECUNDERABAD JN - SC',
    'NAGPUR - NGP',
    'GUWAHATI - GHY',
    // add full list as needed
  ];

  List<String> quotaOptions = [
    'General',
    'Tatkal',
    'Premium Tatkal',
    'Physically Handicapped',
    'Ladies',
    'Duty Pass',
    'Lower Berth',
  ];

  @override
  void initState() {
    super.initState();
    fromStnController = TextEditingController();
    toStnController = TextEditingController();
    dateEdController = TextEditingController();
    selectedQuota = quotaOptions[0];
  }

  @override
  void dispose() {
    fromStnController.dispose();
    toStnController.dispose();
    dateEdController.dispose();
    super.dispose();
  }

  void _showDatePicker() async {
    DateTime now = DateTime.now();
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: now,
      firstDate: now,
      lastDate: now.add(const Duration(days: 120)),
      builder: (context, child) => Theme(data: ThemeData.dark(), child: child!),
    );
    if (picked != null) {
      String dateStr = DateFormat('dd-MM-yyyy').format(picked);
      setState(() => dateEdController.text = dateStr);
    }
  }

  void _onSearchPressed() {
    // --- Validation, identical to Java ---
    String from = fromStnController.text;
    String to = toStnController.text;
    String date = dateEdController.text;
    if (!from.contains('-')) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a from station')),
      );
      return;
    }
    if (!to.contains('-')) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select destination station')),
      );
      return;
    }
    if (!date.contains('-')) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please select a date')));
      return;
    }
    // If you want to check connection in real app, add check here.

    // --- On valid input, launch TrainsBetweenStations (as in Java) ---
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TrainsBetweenStations(
          from: from.split('-')[1].trim(),
          to: to.split('-')[1].trim(),
          date: date,
          quota: selectedQuota,
          fromActivity: 'AVL',
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Check Train Availability'),
        backgroundColor: Colors.black,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            // --- FROM Station ---
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'From',
                style: TextStyle(fontSize: 13, color: Colors.grey[300]),
              ),
            ),
            Autocomplete<String>(
              optionsBuilder: (TextEditingValue textEditingValue) {
                if (textEditingValue.text.isEmpty) return stationList;
                return stationList.where(
                  (option) => option.toLowerCase().contains(
                    textEditingValue.text.toLowerCase(),
                  ),
                );
              },
              onSelected: (selection) =>
                  setState(() => fromStnController.text = selection),
              fieldViewBuilder:
                  (context, controller, focusNode, onEditingComplete) {
                    controller.text = fromStnController.text;
                    controller.selection = TextSelection.fromPosition(
                      TextPosition(offset: controller.text.length),
                    );
                    controller.addListener(
                      () => fromStnController.text = controller.text,
                    );
                    return TextField(
                      controller: controller,
                      focusNode: focusNode,
                      decoration: InputDecoration(
                        fillColor: Colors.grey[800],
                        filled: true,
                        hintText: 'From Station',
                      ),
                      style: TextStyle(color: Colors.white),
                    );
                  },
            ),
            const SizedBox(height: 12),
            // --- TO Station ---
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'To',
                style: TextStyle(fontSize: 13, color: Colors.grey[300]),
              ),
            ),
            Autocomplete<String>(
              optionsBuilder: (TextEditingValue textEditingValue) {
                if (textEditingValue.text.isEmpty) return stationList;
                return stationList.where(
                  (option) => option.toLowerCase().contains(
                    textEditingValue.text.toLowerCase(),
                  ),
                );
              },
              onSelected: (selection) =>
                  setState(() => toStnController.text = selection),
              fieldViewBuilder:
                  (context, controller, focusNode, onEditingComplete) {
                    controller.text = toStnController.text;
                    controller.selection = TextSelection.fromPosition(
                      TextPosition(offset: controller.text.length),
                    );
                    controller.addListener(
                      () => toStnController.text = controller.text,
                    );
                    return TextField(
                      controller: controller,
                      focusNode: focusNode,
                      decoration: InputDecoration(
                        fillColor: Colors.grey[800],
                        filled: true,
                        hintText: 'To Station',
                      ),
                      style: TextStyle(color: Colors.white),
                    );
                  },
            ),
            const SizedBox(height: 12),
            // --- Date ---
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Date',
                style: TextStyle(fontSize: 13, color: Colors.grey[300]),
              ),
            ),
            GestureDetector(
              onTap: _showDatePicker,
              child: AbsorbPointer(
                child: TextField(
                  controller: dateEdController,
                  style: TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Colors.grey[800],
                    hintText: 'Journey Date',
                    suffixIcon: Icon(
                      Icons.calendar_today,
                      color: Colors.white54,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 12),
            // --- Quota Spinner ---
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Quota',
                style: TextStyle(fontSize: 13, color: Colors.grey[300]),
              ),
            ),
            DropdownButton<String>(
              value: selectedQuota,
              items: quotaOptions
                  .map((opt) => DropdownMenuItem(value: opt, child: Text(opt)))
                  .toList(),
              onChanged: (val) => setState(() => selectedQuota = val!),
              dropdownColor: Colors.grey[900],
              style: TextStyle(color: Colors.white),
              isExpanded: true,
            ),
            const SizedBox(height: 30),
            // --- Search Button ---
            ElevatedButton.icon(
              onPressed: _onSearchPressed,
              icon: const Icon(Icons.search),
              label: const Text('Search Trains'),
              style: ElevatedButton.styleFrom(
                minimumSize: Size(double.infinity, 50),
                backgroundColor: Colors.deepPurpleAccent,
                foregroundColor: Colors.white,
                textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
