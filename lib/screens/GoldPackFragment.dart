import 'dart:io';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:video_player/video_player.dart';
import '../core/helper/mixpanel_manager.dart';
import '../captcha/APIConsts.dart';

class GoldPackFragment extends StatefulWidget {
  final bool isGoldOfferRunning;
  final String goldPackAdSource;
  final bool payWithPG;
  final VoidCallback? onStartPayment;

  const GoldPackFragment({
    super.key,
    required this.isGoldOfferRunning,
    required this.goldPackAdSource,
    required this.payWithPG,
    this.onStartPayment,
  });

  @override
  State<GoldPackFragment> createState() => _GoldPackFragmentState();
}

class _GoldPackFragmentState extends State<GoldPackFragment> {
  @override
  void initState() {
    super.initState();
    _trackShowEvent();
  }

  Future<void> _trackShowEvent() async {
    if (widget.isGoldOfferRunning) {
      MixpanelManager().track("Show GOLD Pack Offer");
    } else {
      MixpanelManager().track("Show GOLD Pack Regular");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: GestureDetector(
        onTap: () {
          Navigator.of(context).pop();
        },
        child: Container(
          color: Colors.transparent,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FutureBuilder<File>(
                future: _getImageFile(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.done &&
                      snapshot.hasData &&
                      snapshot.data!.existsSync()) {
                    return GestureDetector(
                      onTap: () async {
                        await _handleImageTap();
                        Navigator.of(context).pop();
                      },
                      child: Image.file(snapshot.data!, fit: BoxFit.contain),
                    );
                  } else {
                    return Container(
                      width: 300,
                      height: 400,
                      color: Colors.grey[800],
                      child: const Center(
                        child: Text(
                          'Gold Pack Offer',
                          style: TextStyle(color: Colors.white, fontSize: 18),
                        ),
                      ),
                    );
                  }
                },
              ),
              if (!widget.payWithPG)
                Column(
                  children: [
                    Container(
                      color: const Color(0xFF00A99D),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 15,
                        vertical: 5,
                      ),
                      margin: const EdgeInsets.only(top: 10),
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          children: [
                            const TextSpan(
                              text:
                                  'You can manage your subscription or cancel anytime in the Google Play app. Specific ',
                              style: TextStyle(
                                color: Color(0xFF999999),
                                fontSize: 11,
                              ),
                            ),
                            TextSpan(
                              text: 'Terms',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 11,
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  launchUrl(
                                    Uri.parse(
                                      'https://www.afrestudios.com/index.php/quick-tatkal-specific-terms-for-paid-services/',
                                    ),
                                    mode: LaunchMode.externalApplication,
                                  );
                                },
                            ),
                            const TextSpan(
                              text: ' apply',
                              style: TextStyle(
                                color: Color(0xFF999999),
                                fontSize: 11,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 14),
                    StatefulBuilder(
                      builder: (context, setStateSB) {
                        bool autofillCaptchaEnabled = false;
                        return FutureBuilder<Map<String, dynamic>>(
                          future: _loadGoldAutofillState(),
                          builder: (context, snapshot) {
                            if (!snapshot.hasData) {
                              return const SizedBox();
                            }
                            var isGoldUser = snapshot.data?["isGoldUser"] == 2;
                            var captchaTrialOpted =
                                snapshot.data?["captchaTrialOpted"] ?? "Y";
                            bool trialAvailable = captchaTrialOpted != "N";
                            autofillCaptchaEnabled =
                                snapshot.data?["autofillCaptchaEnabled"] ==
                                true;
                            return CheckboxListTile(
                              value: autofillCaptchaEnabled,
                              onChanged: (val) async {
                                if (isGoldUser) {
                                  await _setGoldAutofillState(
                                    enabled: val == true,
                                  );
                                  setStateSB(() {});
                                  setState(() {});
                                  return;
                                }
                                if (trialAvailable && !(val ?? false)) {
                                  // Disabling, just turn off
                                  await _setGoldAutofillState(enabled: false);
                                  setStateSB(() {});
                                  setState(() {});
                                  return;
                                }
                                // Show GOLD gating dialog
                                showDialog(
                                  context: context,
                                  barrierDismissible: false,
                                  builder: (_) => AlertDialog(
                                    backgroundColor: Colors.black87,
                                    title: const Text(
                                      'Autofill Captcha',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                    content: const Text(
                                      'Buy Quick Tatkal GOLD Pack to unlock these features\n\n✦ Captcha autofill\n✦ OTP autofill\n✦ Unlimited tickets\n✦ No Ads',
                                      style: TextStyle(color: Colors.white70),
                                    ),
                                    actions: [
                                      if (trialAvailable)
                                        TextButton(
                                          child: const Text(
                                            'Try Once',
                                            style: TextStyle(
                                              color: Colors.amber,
                                            ),
                                          ),
                                          onPressed: () async {
                                            await _setGoldAutofillState(
                                              enabled: true,
                                              useTrial: true,
                                            );
                                            Navigator.pop(context);
                                            setStateSB(() {});
                                            setState(() {});
                                          },
                                        ),
                                      TextButton(
                                        child: const Text(
                                          'Buy GOLD',
                                          style: TextStyle(
                                            color: Colors.yellow,
                                          ),
                                        ),
                                        onPressed: () {
                                          Navigator.pop(context);
                                          _launchPremiumScreen();
                                        },
                                      ),
                                      TextButton(
                                        child: const Text(
                                          'Cancel',
                                          style: TextStyle(
                                            color: Colors.white54,
                                          ),
                                        ),
                                        onPressed: () => Navigator.pop(context),
                                      ),
                                    ],
                                  ),
                                );
                              },
                              activeColor: Color(0xFF9C5DF7),
                              side: const BorderSide(
                                color: Colors.white54,
                                width: 1.1,
                              ),
                              title: const Text(
                                'Autofill Captcha',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
                    Row(
                      children: [
                        IconButton(
                          icon: Icon(
                            Icons.play_circle_outline,
                            color: Colors.amber,
                            size: 28,
                          ),
                          onPressed: () {
                            GoldDemoDialog.show(context, "WEB");
                          },
                        ),
                        const SizedBox(width: 6),
                        TextButton(
                          child: const Text(
                            "Captcha Demo RC",
                            style: TextStyle(color: Colors.white),
                          ),
                          onPressed: () {
                            GoldDemoDialog.show(context, "RC");
                          },
                        ),
                        const SizedBox(width: 4),
                        Text("Demo", style: TextStyle(color: Colors.amber)),
                      ],
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleImageTap() async {
    final analytics = FirebaseAnalytics.instance;
    final properties = <String, String>{};

    if (widget.isGoldOfferRunning) {
      properties['Source'] = widget.goldPackAdSource;
      await analytics.logEvent(
        name: 'Click_GOLD_Pack_Offer',
        parameters: properties,
      );
      MixpanelManager().track("Click GOLD Pack Offer", properties);
    } else {
      properties['Source'] = widget.goldPackAdSource;
      await analytics.logEvent(
        name: 'Click_GOLD_Pack_Regular',
        parameters: properties,
      );
      MixpanelManager().track("Click GOLD Pack Regular", properties);
    }

    if (widget.onStartPayment != null) {
      widget.onStartPayment!();
    }
  }

  Future<File> _getImageFile() async {
    String suffix = widget.payWithPG ? "_pg" : "";
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final filename = widget.isGoldOfferRunning
        ? 'offer$suffix.png'
        : 'regular$suffix.png';
    return File('${documentsDirectory.path}/$filename');
  }

  Future<Map<String, dynamic>> _loadGoldAutofillState() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'isGoldUser': prefs.getInt('IS_GOLD_USER') ?? 0,
      'autofillCaptchaEnabled': prefs.getBool('AUTO_FILL_CAPTCHA') ?? false,
      'captchaTrialOpted':
          prefs.getString(APIConsts.CAPTCHA_TRIAL_OPTED) ?? 'Y',
    };
  }

  Future<void> _setGoldAutofillState({
    required bool enabled,
    bool useTrial = false,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setBool('AUTO_FILL_CAPTCHA', enabled);
    if (useTrial) {
      prefs.setString(APIConsts.CAPTCHA_TRIAL_OPTED, 'N');
    }
  }

  void _launchPremiumScreen() {
    // implement your logic here
  }
}

class GoldDemoDialog extends StatefulWidget {
  final String videoType; // 'WEB' or 'RC'
  const GoldDemoDialog({super.key, required this.videoType});

  @override
  State<GoldDemoDialog> createState() => _GoldDemoDialogState();

  static Future<void> show(BuildContext context, String videoType) async {
    await showDialog(
      context: context,
      barrierDismissible: true,
      builder: (_) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 60),
        child: GoldDemoDialog(videoType: videoType),
      ),
    );
  }
}

class _GoldDemoDialogState extends State<GoldDemoDialog> {
  late VideoPlayerController _controller;
  bool _initialized = false;
  bool _loadError = false;

  @override
  void initState() {
    super.initState();
    final assetPath = widget.videoType == "WEB"
        ? 'assets/raw/captcha_web.mp4'
        : 'assets/raw/captcha_rc.mp4';

    _controller = VideoPlayerController.asset(assetPath)
      ..setLooping(true)
      ..initialize()
          .then((_) {
            setState(() {
              _initialized = true;
              _controller.play();
            });
          })
          .catchError((error) {
            setState(() {
              _loadError = true;
            });
          });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double aspectRatio = widget.videoType == "WEB" ? 2.0 : 0.75;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_loadError)
              Container(
                height: 200,
                alignment: Alignment.center,
                child: const Text(
                  'GOLD Pack Demo Video Coming Soon',
                  style: TextStyle(
                    color: Colors.amber,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            else if (_initialized)
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                child: AspectRatio(
                  aspectRatio: aspectRatio,
                  child: VideoPlayer(_controller),
                ),
              )
            else
              Container(
                height: 200,
                alignment: Alignment.center,
                child: const CircularProgressIndicator(color: Colors.amber),
              ),
            Container(
              alignment: Alignment.centerRight,
              padding: const EdgeInsets.only(top: 10, right: 10),
              child: TextButton(
                child: const Text(
                  'Close',
                  style: TextStyle(color: Colors.amber, fontSize: 16),
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
