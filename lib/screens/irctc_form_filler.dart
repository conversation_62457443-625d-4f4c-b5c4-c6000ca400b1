import 'dart:convert';
import 'dart:developer' as developer;

/// Reliable IRCTC form filler - simplified and working version
class IRCTCFormFiller {
  static String _jsEscape(String input) {
    return input
        .replaceAll('\\', '\\\\')
        .replaceAll('\'', '\\\'')
        .replaceAll('"', '\\"')
        .replaceAll('\n', '\\n')
        .replaceAll('\r', '\\r')
        .replaceAll('\t', '\\t');
  }

  /// Get dummy form data for testing
  static Map getDummyFormData() {
    return {
      'fromStation': 'NDLS', // New Delhi
      'toStation': 'MUMBAI', // Mumbai
      'journeyDate': '25/12/2024', // DD/MM/YYYY
      'quota': 'TATKAL',
      'trainNo': '',
      'travelClass': 'SL',
      'fareLimit': 2000,
      'mobileNo': '9876543210',
      'insurance': 0,
      'bookingOption': 0,
      'username': '<EMAIL>',
      'password': 'testpassword123',
    };
  }

  /// Generate reliable JavaScript code for form filling
  static String generateFormFillingScript({
    String fromStation = 'NDLS',
    String toStation = 'MUMBAI',
    String journeyDate = '25/12/2024',
    String username = '',
    String password = '',
    bool enableLogin = false,
  }) {
    final safeFromStation = _jsEscape(fromStation);
    final safeToStation = _jsEscape(toStation);
    final safeJourneyDate = _jsEscape(journeyDate);
    final safeUsername = _jsEscape(username);
    final safePassword = _jsEscape(password);

    return '''(function(){
      console.log('[IRCTC-FILLER] 🚀 Starting reliable form filling...');
      console.log('[IRCTC-FILLER] From: $safeFromStation, To: $safeToStation, Date: $safeJourneyDate');
      
      var successCount = 0;
      var maxAttempts = 10;
      var attempt = 0;
      
      function fillForm() {
        attempt++;
        console.log('[IRCTC-FILLER] Attempt', attempt, 'of', maxAttempts);
        
        // Wait for page to load
        if (document.readyState !== 'complete') {
          console.log('[IRCTC-FILLER] Page not ready, waiting...');
          setTimeout(fillForm, 1000);
          return;
        }
        
        // Check if we're on the right page
        var isTrainPage = window.location.href.includes('train-search') || 
                         window.location.href.includes('nget') ||
                         document.title.toLowerCase().includes('train');
        
        if (!isTrainPage) {
          console.log('[IRCTC-FILLER] Not on train search page, waiting...');
          if (attempt < maxAttempts) {
            setTimeout(fillForm, 2000);
          }
          return;
        }
        
        console.log('[IRCTC-FILLER] On train search page, looking for form fields...');
        
        // FROM STATION - Simple and effective selectors
        var fromSelectors = [
          '#origin input[type="text"]',
          '#origin input',
          'input[name="origin"]',
          'input[formcontrolname="fromStation"]',
          'input[formControlName="fromStation"]',
          'input[placeholder*="From"]',
          'input[placeholder*="from"]',
          '.origin input',
          'input[id*="origin"]',
          'input[type="text"]:first-of-type'
        ];
        
        var fromField = null;
        for (var i = 0; i < fromSelectors.length; i++) {
          var elements = document.querySelectorAll(fromSelectors[i]);
          for (var j = 0; j < elements.length; j++) {
            var el = elements[j];
            if (el && el.offsetParent !== null && !el.disabled) {
              fromField = el;
              console.log('[IRCTC-FILLER] ✅ Found FROM field with selector:', fromSelectors[i]);
              break;
            }
          }
          if (fromField) break;
        }
        
        // TO STATION - Simple and effective selectors
        var toSelectors = [
          '#destination input[type="text"]',
          '#destination input',
          'input[name="destination"]',
          'input[formcontrolname="toStation"]',
          'input[formControlName="toStation"]',
          'input[placeholder*="To"]',
          'input[placeholder*="to"]',
          '.destination input',
          'input[id*="destination"]',
          'input[type="text"]:nth-of-type(2)'
        ];
        
        var toField = null;
        for (var i = 0; i < toSelectors.length; i++) {
          var elements = document.querySelectorAll(toSelectors[i]);
          for (var j = 0; j < elements.length; j++) {
            var el = elements[j];
            if (el && el.offsetParent !== null && !el.disabled) {
              toField = el;
              console.log('[IRCTC-FILLER] ✅ Found TO field with selector:', toSelectors[i]);
              break;
            }
          }
          if (toField) break;
        }
        
        // DATE FIELD - Simple and effective selectors
        var dateSelectors = [
          '#jDate input[type="text"]',
          '#jDate input',
          'input[name="jDate"]',
          'input[formcontrolname="journeyDate"]',
          'input[formControlName="journeyDate"]',
          'input[placeholder*="Date"]',
          'input[placeholder*="date"]',
          '.jDate input',
          'input[id*="date"]',
          'input[type="text"]:nth-of-type(3)'
        ];
        
        var dateField = null;
        for (var i = 0; i < dateSelectors.length; i++) {
          var elements = document.querySelectorAll(dateSelectors[i]);
          for (var j = 0; j < elements.length; j++) {
            var el = elements[j];
            if (el && el.offsetParent !== null && !el.disabled) {
              dateField = el;
              console.log('[IRCTC-FILLER] ✅ Found DATE field with selector:', dateSelectors[i]);
              break;
            }
          }
          if (dateField) break;
        }
        
        // Fill the fields
        if (fromField) {
          try {
            fromField.focus();
            fromField.value = '';
            fromField.value = '$safeFromStation';
            fromField.setAttribute('value', '$safeFromStation');
            fromField.dispatchEvent(new Event('input', { bubbles: true }));
            fromField.dispatchEvent(new Event('change', { bubbles: true }));
            fromField.dispatchEvent(new Event('blur', { bubbles: true }));
            console.log('[IRCTC-FILLER] ✅ Filled FROM field with: $safeFromStation');
            successCount++;
          } catch (e) {
            console.error('[IRCTC-FILLER] ❌ Error filling FROM field:', e);
          }
        } else {
          console.warn('[IRCTC-FILLER] ⚠️ FROM field not found');
        }
        
        // Wait a bit before filling TO field
        setTimeout(function() {
          if (toField) {
            try {
              toField.focus();
              toField.value = '';
              toField.value = '$safeToStation';
              toField.setAttribute('value', '$safeToStation');
              toField.dispatchEvent(new Event('input', { bubbles: true }));
              toField.dispatchEvent(new Event('change', { bubbles: true }));
              toField.dispatchEvent(new Event('blur', { bubbles: true }));
              console.log('[IRCTC-FILLER] ✅ Filled TO field with: $safeToStation');
              successCount++;
            } catch (e) {
              console.error('[IRCTC-FILLER] ❌ Error filling TO field:', e);
            }
          } else {
            console.warn('[IRCTC-FILLER] ⚠️ TO field not found');
          }
          
          // Wait a bit before filling DATE field
          setTimeout(function() {
            if (dateField) {
              try {
                dateField.focus();
                dateField.value = '';
                dateField.value = '$safeJourneyDate';
                dateField.setAttribute('value', '$safeJourneyDate');
                dateField.dispatchEvent(new Event('input', { bubbles: true }));
                dateField.dispatchEvent(new Event('change', { bubbles: true }));
                dateField.dispatchEvent(new Event('blur', { bubbles: true }));
                console.log('[IRCTC-FILLER] ✅ Filled DATE field with: $safeJourneyDate');
                successCount++;
              } catch (e) {
                console.error('[IRCTC-FILLER] ❌ Error filling DATE field:', e);
              }
            } else {
              console.warn('[IRCTC-FILLER] ⚠️ DATE field not found');
            }
            
            // Final results
            console.log('[IRCTC-FILLER] 📊 Final results:');
            console.log('[IRCTC-FILLER] - Success count:', successCount, '/ 3');
            console.log('[IRCTC-FILLER] - FROM:', fromField ? fromField.value : 'NOT FOUND');
            console.log('[IRCTC-FILLER] - TO:', toField ? toField.value : 'NOT FOUND');
            console.log('[IRCTC-FILLER] - DATE:', dateField ? dateField.value : 'NOT FOUND');
            
            if (successCount >= 2) {
              console.log('[IRCTC-FILLER] 🎉 Form filling completed successfully!');
              
              // Send success message to Flutter
              if (window.StepBridge) {
                window.StepBridge.postMessage(JSON.stringify({
                  type: 'FORM_FILLED',
                  message: 'Form fields filled successfully',
                  successCount: successCount
                }));
              }
            } else {
              console.warn('[IRCTC-FILLER] ⚠️ Partial success - some fields may need manual input');
            }
          }, 500);
        }, 500);
      }
      
      // Start filling after a short delay
      setTimeout(fillForm, 1000);
      
    })();''';
  }
}