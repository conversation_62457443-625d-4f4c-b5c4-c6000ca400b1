import 'package:flutter/material.dart';
import '../core/AppConstants.dart';
import '../quick/EmailFragment.dart';
import '../quick/PhoneFragment.dart';
import '../screens/splash_screen.dart';

class EditProfile extends StatefulWidget {
  final String initialName;
  final String initialEmail;
  final String initialMobile;

  const EditProfile({
    Key? key,
    required this.initialName,
    required this.initialEmail,
    required this.initialMobile,
  }) : super(key: key);

  @override
  State<EditProfile> createState() => _EditProfileState();
}

class _EditProfileState extends State<EditProfile> {
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _mobileController;
  bool isButtonEnabled = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.initialName);
    _emailController = TextEditingController(text: widget.initialEmail);
    _mobileController = TextEditingController(text: widget.initialMobile);

    _nameController.addListener(_onFieldsChanged);
    _emailController.addListener(_onFieldsChanged);
    _mobileController.addListener(_onFieldsChanged);

    // Sync profile data from AppConstants and SplashScreenState
    _syncProfileData();
  }

  void _syncProfileData() {
    // Sync profile picture URL from SplashScreenState to AppConstants
    if (SplashScreenState.PROFILE_PIC != null &&
        SplashScreenState.PROFILE_PIC!.isNotEmpty) {
      AppConstants.profilePicUrl = SplashScreenState.PROFILE_PIC;
    }

    debugPrint("Profile pic URL synced: ${AppConstants.profilePicUrl}");
    debugPrint(
      "SplashScreenState PROFILE_PIC: ${SplashScreenState.PROFILE_PIC}",
    );
  }

  void _onFieldsChanged() {
    setState(() {
      isButtonEnabled =
          _nameController.text.trim().isNotEmpty &&
          _emailController.text.trim().isNotEmpty &&
          _mobileController.text.trim().isNotEmpty &&
          (_nameController.text.trim() != widget.initialName ||
              _emailController.text.trim() != widget.initialEmail ||
              _mobileController.text.trim() != widget.initialMobile);
    });
  }

  void _onSave() {
    AppConstants.custName = _nameController.text.trim();
    AppConstants.email = _emailController.text.trim();
    AppConstants.mobileNo = _mobileController.text.trim();
    AppConstants.primaryEmail = _emailController.text.trim();

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Profile updated!')));
    Navigator.of(context).pop(true);
  }

  void _openPhoneFragment() async {
    final newMobile = await PhoneFragment.showOnProfile(context);
    if (newMobile != null && newMobile.isNotEmpty) {
      setState(() {
        _mobileController.text = newMobile;
        AppConstants.mobileNo = newMobile;
        SplashScreenState.MOBILE_NO = newMobile;
        _onFieldsChanged();
      });
    }
  }

  void _openEmailFragment() {
    EmailFragment(
      context,
      onEmailUpdated: () async {
        // Refresh email field after verification
        setState(() {
          _emailController.text = SplashScreenState.EMAIL;
          _onFieldsChanged(); // Check if save button should be enabled
        });

        // Also update AppConstants and save to SharedPreferences
        AppConstants.email = SplashScreenState.EMAIL;
        AppConstants.primaryEmail = SplashScreenState.PRIMARY_EMAIL;

        // Sync profile picture as well
        _syncProfileData();

        await AppConstants.saveUserData();

        // Refresh UI to show updated profile picture
        setState(() {});
      },
    ).show();
  }

  // Method to refresh profile data (can be called from outside)
  void refreshProfileData() {
    _syncProfileData();
    setState(() {
      _nameController.text = AppConstants.custName;
      _emailController.text = AppConstants.email;
      _mobileController.text = AppConstants.mobileNo;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            pinned: true,
            backgroundColor: const Color(0xFF1A1A1A),
            title: const Text('Edit Profile'),
          ),
          SliverPadding(
            padding: const EdgeInsets.all(16.0),
            sliver: SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: Colors.white,
                    backgroundImage:
                        AppConstants.profilePicUrl != null &&
                            AppConstants.profilePicUrl!.isNotEmpty
                        ? NetworkImage(AppConstants.profilePicUrl!)
                        : const AssetImage('assets/images/profile.png')
                              as ImageProvider,
                  ),
                  const SizedBox(height: 30),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text('Name', style: _labelStyle()),
                  ),
                  const SizedBox(height: 8),
                  _buildTextField(_nameController, enabled: true),
                  const SizedBox(height: 20),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text('Mobile No.', style: _labelStyle()),
                  ),
                  const SizedBox(height: 8),
                  GestureDetector(
                    onTap: _openPhoneFragment,
                    child: _buildTextField(
                      _mobileController,
                      enabled: false,
                      inputType: TextInputType.phone,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text('Email', style: _labelStyle()),
                  ),
                  const SizedBox(height: 8),
                  GestureDetector(
                    onTap: _openEmailFragment,
                    child: _buildTextField(
                      _emailController,
                      enabled: false,
                      inputType: TextInputType.emailAddress,
                    ),
                  ),
                  const SizedBox(height: 40),
                  SizedBox(
                    width: 150,
                    child: ElevatedButton(
                      onPressed: isButtonEnabled ? _onSave : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFA34BFF),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('Save'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  TextStyle _labelStyle() => const TextStyle(
    color: Colors.white,
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );

  Widget _buildTextField(
    TextEditingController controller, {
    bool enabled = false,
    TextInputType? inputType,
  }) {
    return TextField(
      controller: controller,
      enabled: enabled,
      keyboardType: inputType,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        filled: true,
        fillColor: const Color(0xFF2A2A2A),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide.none,
        ),
        suffixIcon: enabled
            ? null
            : const Icon(Icons.edit, color: Colors.grey, size: 16),
      ),
    );
  }
}
