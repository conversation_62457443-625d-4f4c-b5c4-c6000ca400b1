import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import '../core/Consts.dart';
import '../pojo/AutocompleteTrain.dart';

class TrainSearch extends StatefulWidget {
  const TrainSearch({Key? key}) : super(key: key);

  @override
  State<TrainSearch> createState() => _TrainSearchState();
}

class _TrainSearchState extends State<TrainSearch> {
  // Same variables as Java code
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<AutocompleteTrain> trainList = [];

  // AsyncTask equivalent
  Timer? _debounceTimer;
  bool _isFetching = false;

  // UI state variables - same as Java
  bool isProgressVisible = false;
  bool isNoTrainsVisible = false;

  // Same as Java public static String trainInfo
  static String trainInfo = '';

  @override
  void initState() {
    super.initState();

    // Same as Java addTextChangedListener
    _searchController.addListener(_onSearchChanged);

    // Same as Java Handler().postDelayed for focus and keyboard
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(Duration(milliseconds: 300), () {
        _focusNode.requestFocus();
        // Show keyboard automatically
        SystemChannels.textInput.invokeMethod('TextInput.show');
      });
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _focusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // Same as Java afterTextChanged logic
  void _onSearchChanged() {
    if (_debounceTimer?.isActive ?? false) _debounceTimer!.cancel();

    _debounceTimer = Timer(Duration(milliseconds: 300), () {
      // Same logic as Java afterTextChanged
      trainList.clear();
      setState(() {
        isNoTrainsVisible = false;
        isProgressVisible = true;
      });

      String searchText = _searchController.text.toString();
      if (searchText.isNotEmpty) {
        // Execute FetchTrains equivalent
        _fetchTrains(searchText);
      } else {
        setState(() {
          isProgressVisible = false;
          trainList.clear();
        });
      }
    });
  }

  // Same as FetchTrains AsyncTask from Java
  Future<void> _fetchTrains(String keyword) async {
    if (_isFetching) return;
    _isFetching = true;

    try {
      // Same URL construction as Java
      String path = Consts.TRAIN_AUTOCOMPLETE_URL.replaceAll(
        "{TRAIN}",
        keyword,
      );

      final response = await http.get(Uri.parse(path));
      String data = response.body;

      // Same as Java onPostExecute
      await _onFetchTrainsComplete(data);
    } catch (e) {
      // Same error handling as Java
      setState(() {
        isProgressVisible = false;
        isNoTrainsVisible = true;
      });
      debugPrint('Fetch trains error: $e');
    }

    _isFetching = false;
  }

  // Same as Java onPostExecute logic
  Future<void> _onFetchTrainsComplete(String jsonData) async {
    setState(() {
      isProgressVisible = false;
    });

    bool failed = false;
    try {
      // Same JSON parsing as Java
      List<dynamic> trains = json.decode(jsonData);
      List<AutocompleteTrain> newTrainList = [];

      for (int i = 0; i < trains.length; i++) {
        String trainNo = trains[i]["Number"] ?? "";
        String trainName = trains[i]["Name"] ?? "";

        // Same as Java - commented out source/destination for now
        /*String source = "";
        String sourceArrival = "";
        String destination = "";
        String destArrival = "";*/

        AutocompleteTrain train = AutocompleteTrain(
          trainNo,
          trainName,
          "",
          "",
          "",
          "",
        );
        newTrainList.add(train);
      }

      setState(() {
        trainList = newTrainList;
      });
    } catch (e) {
      setState(() {
        isNoTrainsVisible = true;
      });
      // Same as Java - check network connection
      // if (!ConnectionDetector.isConnectingToInternet()) {
      //   noTrainsText = "No network";
      // }
      debugPrint('JSON parsing error: $e');
    }
  }

  // Same as Java setOnItemClickListener
  void _onTrainItemClicked(int index) {
    AutocompleteTrain train = trainList[index];
    String selectedTrainInfo =
        "${train.getTrainNo()} - ${train.getTrainName()}";
    trainInfo =
        selectedTrainInfo; // Keep the static field for backward compatibility
    Navigator.of(
      context,
    ).pop(selectedTrainInfo); // Return the train info as result
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFF1A1A1A), // primary_color
      body: SafeArea(
        child: Column(
          children: [
            // Toolbar - matches toolbar in XML
            Container(
              width: double.infinity,
              height: kToolbarHeight,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    offset: Offset(0, 4),
                    blurRadius: 4,
                  ),
                ],
              ),
              child: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                title: Text(
                  'Train Search',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),

            // Main content with padding - matches android:padding="10dp"
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(10),
                child: Column(
                  children: [
                    // Search EditText - matches ed_search_train
                    Container(
                      width: double.infinity,
                      height: 40,
                      margin: EdgeInsets.only(top: 10),
                      decoration: BoxDecoration(
                        // Matches free_tickets_bg with gray_bg_disabled
                        color: Color(0xFF424259), // gray_bg_disabled color
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: TextField(
                        controller: _searchController,
                        focusNode: _focusNode,
                        style: TextStyle(
                          color: Colors.white, // textColor="#fff"
                          fontSize: 14,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Train No / Name',
                          hintStyle: TextStyle(
                            color: Color(0xFFBBBBBB), // textColorHint="#bbb"
                            fontSize: 14,
                          ),
                          contentPadding: EdgeInsets.only(left: 15),
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                        ),
                      ),
                    ),

                    // RelativeLayout equivalent for ProgressBar and No trains text
                    Container(
                      width: double.infinity,
                      height: 50,
                      child: Stack(
                        children: [
                          // ProgressBar - matches progressBar in XML
                          if (isProgressVisible)
                            Center(
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            ),

                          // No trains text - matches no_trains TextView
                          if (isNoTrainsVisible)
                            Center(
                              child: Text(
                                'No trains found',
                                style: TextStyle(
                                  color: Color(0xFFFF4400), // textColor="#f40"
                                  fontSize: 14,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),

                    // ListView - matches train_list with AutocompleteTrainAdapter equivalent
                    Expanded(
                      child: Container(
                        margin: EdgeInsets.only(top: 10),
                        child: ListView.builder(
                          itemCount: trainList.length,
                          itemBuilder: (context, index) {
                            return _buildTrainListItem(trainList[index], index);
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // AutocompleteTrainAdapter equivalent - same as getView method
  Widget _buildTrainListItem(AutocompleteTrain trainInfo, int index) {
    return InkWell(
      onTap: () => _onTrainItemClicked(index), // Same as setOnItemClickListener
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        margin: EdgeInsets.symmetric(vertical: 2),
        decoration: BoxDecoration(
          color: Color(0xFF2D2D2D),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.grey.withOpacity(0.3), width: 0.5),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Train number and name row
            Row(
              children: [
                // Train No - same as mViewHolder.trainNo.setText
                Text(
                  trainInfo.getTrainNo(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(width: 10),
                // Train Name - same as mViewHolder.trainName.setText
                Expanded(
                  child: Text(
                    trainInfo.getTrainName(),
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            // Source and destination info (when available)
            if (trainInfo.getSrcStn().isNotEmpty ||
                trainInfo.getDestStn().isNotEmpty)
              Container(
                margin: EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    // Source station info - same as mViewHolder.fromStn and fromArr
                    if (trainInfo.getSrcStn().isNotEmpty)
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              trainInfo.getSrcStn(),
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 12,
                              ),
                            ),
                            if (trainInfo.getSrcArrival().isNotEmpty)
                              Text(
                                trainInfo.getSrcArrival(),
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 11,
                                ),
                              ),
                          ],
                        ),
                      ),

                    // Arrow
                    if (trainInfo.getSrcStn().isNotEmpty &&
                        trainInfo.getDestStn().isNotEmpty)
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8),
                        child: Text("→", style: TextStyle(color: Colors.grey)),
                      ),

                    // Destination station info - same as mViewHolder.toStn and toArr
                    if (trainInfo.getDestStn().isNotEmpty)
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              trainInfo.getDestStn(),
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 12,
                              ),
                            ),
                            if (trainInfo.getDestArrival().isNotEmpty)
                              Text(
                                trainInfo.getDestArrival(),
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 11,
                                ),
                              ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Helper methods - same functionality as Java
  void showProgress() {
    setState(() {
      isProgressVisible = true;
      isNoTrainsVisible = false;
    });
  }

  void hideProgress() {
    setState(() {
      isProgressVisible = false;
    });
  }

  void showNoTrains() {
    setState(() {
      isProgressVisible = false;
      isNoTrainsVisible = true;
    });
  }

  void hideNoTrains() {
    setState(() {
      isNoTrainsVisible = false;
    });
  }
}
