import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class MultipleBookingScreen extends StatefulWidget {
  final String formName;
  final String lang;

  const MultipleBookingScreen({
    super.key,
    required this.formName,
    required this.lang,
  });

  @override
  State<MultipleBookingScreen> createState() => _MultipleBookingScreenState();
}

class _MultipleBookingScreenState extends State<MultipleBookingScreen> {
  late WebViewController webViewController1;
  late WebViewController webViewController2;
  double progress1 = 0;
  double progress2 = 0;
  late String boardingStation;

  List<Map<String, dynamic>> sessionState = [{}, {}];

  List<int> step = [0, 0];
  List<bool> paymentFailed = [false, false];
  List<String> username = ["", ""];
  List<String> password = ["", ""];
  List<String> trainNo = ["", ""];

  @override
  void initState() {
    super.initState();
    _initSessions();
  }

  void _initSessions() async {
    webViewController1 = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            print('WebView1 started loading: $url');
          },
          onPageFinished: (String url) {
            print('WebView1 finished loading: $url');
            _optimizePageForAutomation(webViewController1);
          },
          onWebResourceError: (WebResourceError error) {
            if (!error.description.contains('net::ERR_FAILED')) {
              print('WebView1 error: ${error.description}');
            }
          },
        ),
      )
      ..loadRequest(Uri.parse('https://www.irctc.co.in/nget/train-search'));

    webViewController2 = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            print('WebView2 started loading: $url');
          },
          onPageFinished: (String url) {
            print('WebView2 finished loading: $url');
            _optimizePageForAutomation(webViewController2);
          },
          onWebResourceError: (WebResourceError error) {
            if (!error.description.contains('net::ERR_FAILED')) {
              print('WebView2 error: ${error.description}');
            }
          },
        ),
      )
      ..loadRequest(Uri.parse('https://www.irctc.co.in/nget/train-search'));
  }

  Future<void> _optimizePageForAutomation(WebViewController controller) async {
    try {
      await controller.runJavaScript('''
        const boomerangScripts = document.querySelectorAll('script[src*="go-mpulse.net"]');
        boomerangScripts.forEach(script => script.remove());
        
        const preloadLinks = document.querySelectorAll('link[rel="preload"][href*="go-mpulse.net"]');
        preloadLinks.forEach(link => link.remove());
        
        const originalWarn = console.warn;
        console.warn = function(...args) {
          const message = args.join(' ');
          if (!message.includes('preload') && !message.includes('boomerang')) {
            originalWarn.apply(console, args);
          }
        };
        
        document.body.style.zoom = '0.8'; 
        
        console.log('Page optimized for automation');
      ''');
    } catch (e) {
      print('Failed to optimize page: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Multiple Booking - Optimized for Automation'),
        backgroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              webViewController1.reload();
              webViewController2.reload();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          LinearProgressIndicator(
            value: progress1,
            minHeight: 4,
            color: Colors.deepPurple,
            backgroundColor: Colors.deepPurple[100],
          ),
          Expanded(
            child: Column(
              children: [
                Expanded(child: WebViewWidget(controller: webViewController1)),
                Container(
                  width: double.infinity,
                  color: Colors.purple.shade900,
                  padding: const EdgeInsets.all(8),
                  child: const Text(
                    'Booking 1 - Automation Ready',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          LinearProgressIndicator(
            value: progress2,
            minHeight: 4,
            color: Colors.deepOrange,
            backgroundColor: Colors.deepOrange[100],
          ),
          Expanded(
            child: Column(
              children: [
                Expanded(child: WebViewWidget(controller: webViewController2)),
                Container(
                  width: double.infinity,
                  color: Colors.orange.shade900,
                  padding: const EdgeInsets.all(8),
                  child: const Text(
                    'Booking 2 - Automation Ready',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /*Set<JavascriptChannel> _jsChannels(int bookingIndex) {
    return {
      JavascriptChannel(
        name: 'Step',
        onMessageReceived: (JavascriptMessage message) {
          // Map incoming message to correct bookingIndex/session
          // Parse message and update state/handle next step as in Java
          // Example:
          // if (message.message.startsWith('setStep:')) { step[bookingIndex] = ...; ... }
          // Implement per your Java logic (MyJavaScriptInterface)
        },
      ),
    };
  }*/
}
