import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:quick_tatkal_v2/core/network/advanced_webview.dart';

class LegalScreen extends StatefulWidget {
  final String type;
  final String url;

  const LegalScreen({super.key, required this.type, required this.url});

  @override
  State<LegalScreen> createState() => _LegalScreenState();
}

class _LegalScreenState extends State<LegalScreen> {
  late final WebViewController _webViewController;
  double _progress = 0.0;
  bool _showInfo = false;
  FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    super.initState();
    _trackAnalyticsEvents(widget.url);
  }

  void _trackAnalyticsEvents(String url) {
    final params = {'value': 'true'};
    if (url.contains("order-food")) {
      analytics.logEvent(name: "railofy_food", parameters: params);
    } else if (url.contains("pnr-status")) {
      analytics.logEvent(name: "railofy_tg", parameters: params);
    } else if (url.contains("book-train")) {
      analytics.logEvent(name: "railofy_book", parameters: params);
    } else if (url.contains("bookings")) {
      analytics.logEvent(name: "railofy_mybookings", parameters: params);
    }
  }

  Future<bool> _handleBackNavigation() async {
    final canGoBack = await _webViewController.canGoBack();
    if (canGoBack) {
      _webViewController.goBack();
      return false;
    } else {
      return await _showExitDialog();
    }
  }

  Future<bool> _showExitDialog() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text("Exit"),
            content: const Text("Are you sure you want to exit?"),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text("No"),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text("Exit"),
              ),
            ],
          ),
        ) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    final bool isRailofyBlue =
        widget.url.contains("railofy") && !widget.url.contains("order-food");

    final GlobalKey<AdvancedWebViewState> webViewKey = GlobalKey();

    return WillPopScope(
      onWillPop: () async {
        final state = webViewKey.currentState;
        if (state != null) {
          return await state.onBackPressed();
        }
        return true;
      },
      child: AdvancedWebView(
        key: webViewKey,
        url: widget.url,
        title: widget.type,
        appBarColor: isRailofyBlue ? const Color(0xFF0064FF) : Colors.black,
        showBottomBanner: widget.url.contains("incovid19"),
        onProgress: (progress) {},
      ),
    );
  }
}
