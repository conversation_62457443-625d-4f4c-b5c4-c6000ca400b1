import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

class FloatingWidgetService {
  WindowManager? mWindowManager;
  Widget? mFloatingView;
  Widget? timeTv;
  Widget? statusTv;
  Widget? captcha;
  Widget? progress;
  Widget? ptLayout;
  Widget? fareMsg;
  Widget? yesBtn;
  Widget? noBtn;

  final LocalBinder binder = LocalBinder();
  dynamic params;

  Timer? clock;
  Timer? clockTask;

  static bool running = false;
  dynamic mgr;
  WindowManager? wmgr;

  static const int NOTIFY_ID = 9906;
  static const String EXTRA_RESULT_CODE = "resultCode";
  static const String EXTRA_RESULT_INTENT = "resultIntent";
  static const String EXTRA_STATUS = "status";
  static const String ACTION_RECORD = "com.tatkal.train.quick.RECORD";
  static const String ACTION_STOP = "com.tatkal.train.quick.STOP";
  static const String ACTION_SET_STATUS = "com.tatkal.train.quick.STATUS";
  static const int VIRT_DISPLAY_FLAGS = 1 | 2;

  dynamic projection;
  dynamic vdisplay;
  final handlerThread = "FloatingWidgetService";
  dynamic handler;
  dynamic it;
  int resultCode = 0;
  dynamic resultData;

  FloatingWidgetService();

  WindowManager? getWindowManager() {
    return wmgr;
  }

  void onCreate() {
    // Flutter equivalent of onCreate
  }

  LocalBinder onBind(dynamic intent) {
    return binder;
  }

  void intitalizeFloatingView() {
    if (mFloatingView == null) {
      // Flutter equivalent of inflate layout
      mFloatingView = _buildOverlayLayout();

      // Initialize views
      timeTv = _findViewById("textView70");
      statusTv = _findViewById("textView71");
      captcha = _findViewById("captchaImg");
      progress = _findViewById("progressBar");
      ptLayout = _findViewById("ptLayout");
      fareMsg = _findViewById("fareMsg");
      yesBtn = _findViewById("yes");
      noBtn = _findViewById("no");

      // Set click listeners
      _setOnClickListener(noBtn, () {
        hidePTAlert();
      });

      _setOnClickListener(yesBtn, () {
        MyAccessibilityService.getInstance()?.waitForPTResponse = 2;
        hidePTAlert();
      });

      if (Consts.accEnabledForWebsite) {
        _setText(statusTv, "UPI Assistant");
      }

      // Layout params
      params = WindowManagerLayoutParams(
        width: WindowManagerLayoutParams.WRAP_CONTENT,
        height: WindowManagerLayoutParams.WRAP_CONTENT,
        type: WindowManagerLayoutParams.TYPE_PHONE,
        flags: WindowManagerLayoutParams.FLAG_NOT_FOCUSABLE,
        format: PixelFormat.TRANSLUCENT,
      );

      // API level check equivalent
      if (Platform.isAndroid) {
        params = WindowManagerLayoutParams(
          width: WindowManagerLayoutParams.WRAP_CONTENT,
          height: WindowManagerLayoutParams.WRAP_CONTENT,
          type: WindowManagerLayoutParams.TYPE_APPLICATION_OVERLAY,
          flags: WindowManagerLayoutParams.FLAG_NOT_FOCUSABLE,
          format: PixelFormat.TRANSLUCENT,
        );
      }

      params.gravity = Gravity.TOP | Gravity.START;
      params.x = 0;
      params.y = 0;

      mWindowManager = WindowManager();
      try {
        mWindowManager?.addView(mFloatingView, params);
      } catch (e) {
        // Handle exception
      }

      _setVisibility(mFloatingView, ViewVisibility.VISIBLE);

      final collapsedView = _findViewById("collapse_view");
      final expandedView = _findViewById("expanded_container");

      final closeButtonCollapsed = _findViewById("close_btn");
      _setOnClickListener(closeButtonCollapsed, () {
        stopEverything();
      });

      startClock();

      // Touch listener equivalent
      _setOnTouchListener(_findViewById("root_container"));
    }
  }

  void stopEverything() {
    FormActivity2.serviceStartedFirstTime = true;

    if (Platform.isAndroid && MyAccessibilityService.getInstance() != null) {
      MyAccessibilityService.getInstance()?.disableSelf();
    }

    try {
      mWindowManager?.removeView(mFloatingView);
    } catch (e) {
      // Handle exception
    }
    mFloatingView = null;

    // Play sound
    _playSound("overlay_gone");

    // Vibrate
    _vibrate(200);

    TabActivity2.serviceStarted = false;
    stopForeground(true);
    stopSelf();
  }

  void showCaptcha() {
    if (captcha != null && _getVisibility(captcha) == ViewVisibility.GONE) {
      Future.delayed(Duration.zero, () {
        _setImageBitmap(captcha, ImageTransmogrifier.captchaCrop);
        _setVisibility(captcha, ViewVisibility.VISIBLE);
        _setVisibility(progress, ViewVisibility.VISIBLE);
      });
    }
  }

  void hideCaptcha() {
    if (captcha != null) {
      _setVisibility(captcha, ViewVisibility.GONE);
      _setVisibility(progress, ViewVisibility.GONE);
    }
    stopCapture();
  }

  void showPTAlert(String fare) {
    _setVisibility(ptLayout, ViewVisibility.VISIBLE);
    _setText(fareMsg, "Total fare for ticket is $fare. Continue with payment?");
  }

  void hidePTAlert() {
    _setVisibility(ptLayout, ViewVisibility.GONE);
  }

  dynamic getHandler() {
    return handler;
  }

  void processImage(final Uint8List png) {
    Future(() async {
      final directory = await getExternalStorageDirectory();
      final output = File('${directory?.path}/captcha.png');

      try {
        await output.writeAsBytes(png);
      } catch (e) {
        print("Exception writing out screenshot: $e");
      }
    });

    stopCapture();
  }

  void stopCapture() {
    if (projection != null) {
      projection.stop();
      vdisplay.release();
      projection = null;
    }
  }

  void startCapture() {
    if (resultData != null) {
      projection = mgr.getMediaProjection(resultCode, resultData);
      it = ImageTransmogrifier(this);

      final cb = MediaProjectionCallback(
        onStop: () {
          vdisplay.release();
        },
      );

      vdisplay = projection.createVirtualDisplay(
        "andshooter",
        it.getWidth(),
        it.getHeight(),
        160,
        // densityDpi equivalent
        VIRT_DISPLAY_FLAGS,
        it.getSurface(),
        null,
        handler,
      );

      projection.registerCallback(cb, handler);
    }
  }

  int onStartCommand(dynamic intent, int flags, int startId) {
    if (intent != null) {
      if (intent.getAction() == null && !_isHandlerThreadAlive()) {
        mgr = getSystemService("MEDIA_PROJECTION_SERVICE");
        wmgr = getSystemService("WINDOW_SERVICE") as WindowManager;

        _startHandlerThread();
        handler = _createHandler();

        resultCode = intent.getIntExtra(EXTRA_RESULT_CODE, 1337);
        resultData = intent.getParcelableExtra(EXTRA_RESULT_INTENT);

        try {
          startForeground(
            16494,
            getNotification(
              "Rail Connect Autofill service running",
              "Rail Connect Auotfill service by Quick Tatkal is running. Click Stop to stop autofill",
            ),
          );
        } catch (ex) {
          // Handle exception
        }
      } else if (intent.getAction() != null &&
          intent.getAction() == ACTION_STOP) {
        stopEverything();
      }
    }
    return START_STICKY;
  }

  static bool isAppRunning(final context, final String packageName) {
    // Flutter equivalent of checking running processes
    return false; // Simplified implementation
  }

  dynamic buildPendingIntent(String action) {
    final intent = Intent(action: action);
    return PendingIntent.getService(
      this,
      0,
      intent,
      PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE,
    );
  }

  dynamic getNotification(String title, String message) {
    const String NOTIFICATION_CHANNEL_ID = "10002";

    final resultIntent = Intent(
      action: "android.settings.ACCESSIBILITY_SETTINGS",
    );
    final stackBuilder = TaskStackBuilder.create(this);
    stackBuilder.addNextIntentWithParentStack(resultIntent);

    final resultPendingIntent = stackBuilder.getPendingIntent(
      0,
      PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE,
    );

    final mBuilder = NotificationBuilder(this)
      ..setContentTitle(title)
      ..setContentText(message)
      ..setSmallIcon("qt_notif_3")
      ..setLargeIcon("logo_4")
      ..setAutoCancel(true)
      ..setContentIntent(resultPendingIntent)
      ..setStyle(BigTextStyle(message));

    mBuilder.addAction(
      "ic_logo_airpay_mpp",
      "Stop",
      buildPendingIntent(ACTION_STOP),
    );

    final mNotificationManager = getSystemService("NOTIFICATION_SERVICE");

    if (Platform.isAndroid) {
      const importance = NotificationManager.IMPORTANCE_HIGH;
      final notificationChannel = NotificationChannel(
        NOTIFICATION_CHANNEL_ID,
        "Accessibility",
        importance,
      );

      notificationChannel.enableLights = true;
      notificationChannel.lightColor = Colors.cyan;
      notificationChannel.enableVibration = true;
      notificationChannel.vibrationPattern = [
        100,
        200,
        300,
        400,
        500,
        400,
        300,
        200,
        400,
      ];

      mBuilder.channelId = NOTIFICATION_CHANNEL_ID;
      mNotificationManager.createNotificationChannel(notificationChannel);
    }

    return mBuilder.build();
  }

  bool isViewCollapsed() {
    return mFloatingView == null ||
        _getVisibility(_findViewById("collapse_view")) ==
            ViewVisibility.VISIBLE;
  }

  void setStatus(String status) {
    if (status == "Decoding failed" || status.startsWith("CAPTCHA#")) {
      hideCaptcha();
      if (status.startsWith("CAPTCHA#")) {
        status = "Captcha: ${status.replaceFirst("CAPTCHA#", "")}";
      }
    }

    if (mFloatingView == null) {
      intitalizeFloatingView();
    }

    if (statusTv != null) {
      _setText(statusTv, status);
    }
  }

  void startClock() {
    initializeClockTask();
    clock = Timer.periodic(Duration(seconds: 1), (timer) {
      clockTask;
    });
  }

  void initializeClockTask() {
    clockTask = Timer.periodic(Duration(seconds: 1), (timer) {
      final calendar = DateTime.now().add(
        Duration(milliseconds: MyAccessibilityService.timeDifference),
      );
      String amPm = "AM";
      String hour = "0${calendar.hour % 12}";
      String minute = "0${calendar.minute}";
      String second = "0${calendar.second}";

      if (calendar.hour >= 12) {
        amPm = "PM";
      }

      final timeText =
          "${hour.substring(hour.length - 2)}:${minute.substring(minute.length - 2)}:${second.substring(second.length - 2)} $amPm";
      _setText(timeTv, timeText);
    });
  }

  // Helper methods for Flutter equivalents
  Widget _buildOverlayLayout() => Container(); // Placeholder
  Widget? _findViewById(String id) => null; // Placeholder
  void _setText(Widget? widget, String text) {} // Placeholder
  void _setOnClickListener(
    Widget? widget,
    VoidCallback callback,
  ) {} // Placeholder
  void _setVisibility(
    Widget? widget,
    ViewVisibility visibility,
  ) {} // Placeholder
  ViewVisibility _getVisibility(Widget? widget) =>
      ViewVisibility.VISIBLE; // Placeholder
  void _setImageBitmap(Widget? widget, dynamic bitmap) {} // Placeholder
  void _setOnTouchListener(Widget? widget) {} // Placeholder
  void _playSound(String soundName) {} // Placeholder
  void _vibrate(int duration) {} // Placeholder
  bool _isHandlerThreadAlive() => false; // Placeholder
  void _startHandlerThread() {} // Placeholder
  dynamic _createHandler() => null; // Placeholder
  dynamic getSystemService(String service) => null; // Placeholder
  void startForeground(int id, dynamic notification) {} // Placeholder
  void stopForeground(bool removeNotification) {} // Placeholder
  void stopSelf() {} // Placeholder
  static const int START_STICKY = 1;
}

// Helper classes
class LocalBinder {
  FloatingWidgetService getService() {
    return FloatingWidgetService();
  }
}

class WindowManager {
  void addView(Widget? view, dynamic params) {}

  void removeView(Widget? view) {}

  void updateViewLayout(Widget? view, dynamic params) {}
}

class WindowManagerLayoutParams {
  static const int WRAP_CONTENT = -2;
  static const int TYPE_PHONE = 2002;
  static const int TYPE_APPLICATION_OVERLAY = 2038;
  static const int FLAG_NOT_FOCUSABLE = 8;

  int width;
  int height;
  int type;
  int flags;
  int format;
  int gravity = 0;
  int x = 0;
  int y = 0;

  WindowManagerLayoutParams({
    required this.width,
    required this.height,
    required this.type,
    required this.flags,
    required this.format,
  });
}

class PixelFormat {
  static const int TRANSLUCENT = -3;
}

class Gravity {
  static const int TOP = 48;
  static const int START = 8388611;
}

enum ViewVisibility { VISIBLE, GONE, INVISIBLE }

// Placeholder classes for missing dependencies
class FormActivity2 {
  static bool serviceStartedFirstTime = false;
}

class TabActivity2 {
  static bool serviceStarted = false;
}

class MyAccessibilityService {
  static int timeDifference = 0;
  int waitForPTResponse = 0;

  static MyAccessibilityService? getInstance() => null;

  void disableSelf() {}
}

class Consts {
  static bool accEnabledForWebsite = false;
}

class ImageTransmogrifier {
  static dynamic captchaCrop;

  ImageTransmogrifier(dynamic service);

  int getWidth() => 0;

  int getHeight() => 0;

  dynamic getSurface() => null;
}

class Intent {
  String? action;

  Intent({this.action});

  int getIntExtra(String key, int defaultValue) => defaultValue;

  dynamic getParcelableExtra(String key) => null;

  String? getAction() => action;
}

class PendingIntent {
  static const int FLAG_UPDATE_CURRENT = 134217728;
  static const int FLAG_IMMUTABLE = 67108864;

  static PendingIntent getService(
    dynamic context,
    int requestCode,
    Intent intent,
    int flags,
  ) {
    return PendingIntent();
  }
}

class TaskStackBuilder {
  static TaskStackBuilder create(dynamic context) => TaskStackBuilder();

  void addNextIntentWithParentStack(Intent intent) {}

  PendingIntent getPendingIntent(int requestCode, int flags) => PendingIntent();
}

class NotificationBuilder {
  dynamic context;

  NotificationBuilder(this.context);

  void setContentTitle(String title) {}

  void setContentText(String text) {}

  void setSmallIcon(String icon) {}

  void setLargeIcon(String icon) {}

  void setAutoCancel(bool autoCancel) {}

  void setContentIntent(PendingIntent intent) {}

  void setStyle(dynamic style) {}

  void addAction(String icon, String title, dynamic pendingIntent) {}
  String? channelId;

  dynamic build() => null;
}

class BigTextStyle {
  String text;

  BigTextStyle(this.text);
}

class NotificationChannel {
  String id;
  String name;
  int importance;
  bool enableLights = false;
  Color? lightColor;
  bool enableVibration = false;
  List<int> vibrationPattern = [];

  NotificationChannel(this.id, this.name, this.importance);
}

class NotificationManager {
  static const int IMPORTANCE_HIGH = 4;

  void createNotificationChannel(NotificationChannel channel) {}
}

class MediaProjectionCallback {
  VoidCallback onStop;

  MediaProjectionCallback({required this.onStop});
}
