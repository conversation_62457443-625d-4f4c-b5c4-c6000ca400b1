import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:io';
import 'package:intl/intl.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../core/Consts.dart';
import '../screens/splash_screen.dart';
import '../screens/TrainSearch.dart' as train_search;
import '../screens/RunningStatus.dart';
import '../core/helper/mixpanel_manager.dart';
import '../quick/home_activity.dart';

class RunningStatusActivity extends StatefulWidget {
  const RunningStatusActivity({Key? key}) : super(key: key);

  @override
  State<RunningStatusActivity> createState() => _RunningStatusActivityState();
}

class _RunningStatusActivityState extends State<RunningStatusActivity> {
  // Same variables as Java code
  List<String> spinnerArray = [];
  late String selectedDate;

  Timer? timer;
  bool cd = true; // ConnectionDetector equivalent

  InterstitialAd? mInterstitialAd;
  bool fbAdLoaded = false;
  NativeAd? currentNativeAd;

  late MixpanelManager mixpanel;

  // UI Controllers - same names as Java
  final TextEditingController edTrain = TextEditingController();
  String startDtSelected = '';
  int startDtSelectedIndex = 0;

  @override
  void initState() {
    super.initState();
    onCreate();
  }

  void onCreate() {
    mixpanel = MixpanelManager();

    cd = true; // ConnectionDetector equivalent - assume connected initially
    checkNetworkConnection();

    if (Consts.TRAIN_LIST == "") {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text("Something went wrong")));
      Navigator.pushReplacementNamed(context, '/splash');
      return;
    }

    List<String> parts = Consts.TRAIN_LIST.split(",");
    final List<String> trains = List<String>.from(parts);

    // Facebook ads - same logic as Java
    if (SplashScreenState.isGoldUser != 2) {
      AdRequest adRequest = const AdRequest();
      // uncomment this - same comment as Java
      InterstitialAd.load(
        adUnitId: Consts.TATKAL_INTERSTITIAL_ID,
        request: adRequest,
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (InterstitialAd ad) {
            // The mInterstitialAd reference will be null until an ad is loaded.
            mInterstitialAd = ad;
          },
          onAdFailedToLoad: (LoadAdError error) {
            // Handle the error
            mInterstitialAd = null;
          },
        ),
      );
    }

    // Same spinner setup as Java
    Calendar c = Calendar();
    spinnerArray = [];

    SimpleDateFormat sdf = SimpleDateFormat("dd MMM", "en");

    String date = sdf.format(c.dateTime) + ", Today";
    spinnerArray.add(date);

    c = c.subtractDays(1);
    date = sdf.format(c.dateTime) + ", Yesterday";
    spinnerArray.add(date);

    for (int i = 1; i <= 4; i++) {
      c = c.subtractDays(1);
      date = sdf.format(c.dateTime);
      spinnerArray.add(date);
    }

    // Set default selection
    startDtSelected = spinnerArray[0];
    startDtSelectedIndex = 0;

    if (SplashScreenState.isGoldUser != 2) {
      // uncomment this - same comment as Java
      refreshAd();
    }
  }

  @override
  void dispose() {
    timer?.cancel();
    edTrain.dispose();
    mInterstitialAd?.dispose();
    currentNativeAd?.dispose();
    super.dispose();
  }

  // Same as Java onBackPressed
  void onBackPressed() {
    if (SplashScreenState.isGoldUser != 2) {
      showInterstitial();
    }
    Navigator.pushReplacementNamed(context, '/home');
  }

  // Same as Java showInterstitial
  void showInterstitial() {
    int time = DateTime.now().millisecondsSinceEpoch;
    int difference = time - Consts.AD_SHOW_TIME;
    if (mInterstitialAd != null) {
      if (difference > 30 * 1000) {
        Consts.AD_SHOW_TIME = time;
        mInterstitialAd!.show();
      }
    }
  }

  // Same as Java refreshAd
  void refreshAd() {
    currentNativeAd = NativeAd(
      adUnitId: Consts.TATKAL_NATIVE_ID,
      request: const AdRequest(),
      listener: NativeAdListener(
        onAdLoaded: (Ad ad) {
          setState(() {
            currentNativeAd = ad as NativeAd;
          });
        },
        onAdFailedToLoad: (Ad ad, LoadAdError error) {
          ad.dispose();
          // Handle the failure by logging, altering the UI, and so on.
          int x = 2; // Same as Java
        },
      ),
      nativeTemplateStyle: NativeTemplateStyle(
        templateType: TemplateType.medium,
      ),
    );
    currentNativeAd!.load();
  }

  // Same as Java checkNetworkConnection (using ConnectionDetector equivalent)
  void checkNetworkConnection() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    setState(() {
      cd = connectivityResult != ConnectivityResult.none;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A), // primary_color equivalent
      appBar: AppBar(
        backgroundColor: Theme.of(context).primaryColor,
        elevation: 4,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: onBackPressed,
        ),
        title: const Text(
          'Running Status',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20.0), // 20dp padding as in XML
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Train No/Name section - exactly as XML
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Label for train input
                      Container(
                        margin: const EdgeInsets.only(left: 10),
                        child: const Text(
                          'Train No / Name',
                          style: TextStyle(color: Colors.white, fontSize: 13),
                        ),
                      ),
                      const SizedBox(height: 5),
                      // Train input field
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              height: 40,
                              margin: const EdgeInsets.only(top: 5),
                              child: TextField(
                                controller: edTrain,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                                textCapitalization:
                                    TextCapitalization.characters,
                                decoration: InputDecoration(
                                  hintText: '',
                                  hintStyle: const TextStyle(
                                    color: Color(0xFFCCCCCC),
                                  ),
                                  filled: true,
                                  fillColor: const Color(0xFF2D2D2D),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                      color: Colors.white24,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                      color: Colors.white24,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                      color: Colors.white54,
                                    ),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 3,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          // Search icon - hidden as per XML visibility="gone"
                          Container(
                            width: 40,
                            height: 40,
                            margin: const EdgeInsets.only(left: 5, top: 5),
                            child: GestureDetector(
                              onTap: () async {
                                if (!cd) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        "Please check your network connection",
                                      ),
                                    ),
                                  );
                                  return;
                                }
                                final result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const train_search.TrainSearch(),
                                  ),
                                );

                                if (result != null &&
                                    result is String &&
                                    result.isNotEmpty) {
                                  setState(() {
                                    edTrain.text = result;
                                  });
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  color: const Color(0xFF2D2D2D),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.white24),
                                ),
                                child: const Icon(
                                  Icons.search,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  // Start Date section - hidden as per XML visibility="gone"
                  // This section is commented out as it's hidden in XML
                  /*
                  const SizedBox(height: 15),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(left: 10),
                        child: const Text(
                          'Start Date',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 13,
                          ),
                        ),
                      ),
                      const SizedBox(height: 5),
                      Container(
                        height: 40,
                        margin: const EdgeInsets.only(top: 5),
                        child: GestureDetector(
                          onTap: () => _showDatePicker(),
                          child: Container(
                            decoration: BoxDecoration(
                              color: const Color(0xFF2D2D2D),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.white24),
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  startDtSelected,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                  ),
                                ),
                                const Icon(
                                  Icons.arrow_drop_down,
                                  color: Colors.white,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  */
                  const SizedBox(height: 20), // 20dp margin as in XML
                  // Search Button - exactly as XML: 200dp width, centered
                  Center(
                    child: SizedBox(
                      width: 200,
                      height: 40,
                      child: ElevatedButton(
                        onPressed: () {
                          mixpanel.track("Running status search click");
                          if (!cd) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  "Please check your network connection",
                                ),
                              ),
                            );
                            return;
                          }
                          String train = edTrain.text.toString();
                          if (train.contains("-")) {
                            train = train.split("-")[0].trim();
                          } else if (train.length < 5) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  "Please enter a valid train number",
                                ),
                              ),
                            );
                            return;
                          }

                          FocusScope.of(context).unfocus();

                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => RunningStatus(
                                arguments: {
                                  'date': startDtSelectedIndex,
                                  'dateStr': startDtSelected,
                                  'train': train,
                                  'trainInfo': edTrain.text.toString(),
                                },
                              ),
                            ),
                          );

                          int current = DateTime.now().millisecondsSinceEpoch;
                          if (SplashScreenState.isGoldUser != 2) {
                            showInterstitial();
                            Consts.AD_SHOWN_TIME_IN_MILLIS =
                                DateTime.now().millisecondsSinceEpoch;
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF8A2BE2),
                          // Purple color as in image
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Search',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 20), // 20dp margin as in XML
                  // Ad placeholder - same as XML frameLayout
                  if (currentNativeAd != null &&
                      SplashScreenState.isGoldUser != 2)
                    SizedBox(
                      width: double.infinity,
                      height: 150,
                      child: AdWidget(ad: currentNativeAd!),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Date picker equivalent to Java spinner - kept for future use
  void _showDatePicker() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2D2D2D),
          title: const Text(
            'Select Start Date',
            style: TextStyle(color: Colors.white),
          ),
          content: Container(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: spinnerArray.length,
              itemBuilder: (context, index) {
                return ListTile(
                  title: Text(
                    spinnerArray[index],
                    style: const TextStyle(color: Colors.white),
                  ),
                  selected: startDtSelectedIndex == index,
                  selectedTileColor: Theme.of(
                    context,
                  ).primaryColor.withOpacity(0.3),
                  onTap: () {
                    setState(() {
                      startDtSelected = spinnerArray[index];
                      startDtSelectedIndex = index;
                    });
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }
}

// Helper class to match Java Calendar functionality
class Calendar {
  DateTime dateTime;

  Calendar() : dateTime = DateTime.now();

  Calendar.fromDateTime(this.dateTime);

  Calendar subtractDays(int days) {
    return Calendar.fromDateTime(dateTime.subtract(Duration(days: days)));
  }

  Calendar addHours(int hours) {
    return Calendar.fromDateTime(dateTime.add(Duration(hours: hours)));
  }
}

// Helper class to match Java SimpleDateFormat functionality
class SimpleDateFormat {
  final String pattern;
  final String locale;

  SimpleDateFormat(this.pattern, this.locale);

  String format(DateTime dateTime) {
    final formatter = DateFormat(pattern, locale);
    return formatter.format(dateTime);
  }
}
