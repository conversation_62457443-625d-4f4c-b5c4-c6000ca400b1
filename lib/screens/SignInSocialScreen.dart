// This Flutter implementation is a full functional conversion of the provided
// XML layout and Java code (SignInSocial.java) to a Flutter StatefulWidget.

import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:http/http.dart' as http;
import '../core/helper/mixpanel_manager.dart';
import '../main.dart';
import '../screens/splash_screen.dart';
import '../server/FirestoreFunctions.dart';

class SignInSocialScreen extends StatefulWidget {
  const SignInSocialScreen({Key? key}) : super(key: key);

  @override
  State<SignInSocialScreen> createState() => _SignInSocialScreenState();
}

class _SignInSocialScreenState extends State<SignInSocialScreen> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn.instance;
  bool isLoading = false;
  String? profilePicUrl;
  bool settingUpFb = false;
  bool _isGoogleSignInInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeMixpanel();
    _initializeGoogleSignIn();

    // Reset previous account check like in Java
    FirestoreFunctions.checkPreviousAccount = false;
    FirestoreFunctions.prevTid = "";
  }

  void _initializeMixpanel() async {
    await MixpanelManager().init();
    MixpanelManager().track("sign_in_social_screen_viewed");

    if (!MixpanelManager().askAiViewLogged) {
      MixpanelManager().trackEventOnce("ask_view");
    }
  }

  Future<void> _initializeGoogleSignIn() async {
    try {
      await _googleSignIn.initialize();
      _isGoogleSignInInitialized = true;
    } catch (e) {
      debugPrint('Failed to initialize Google Sign-In: $e');
    }
  }

  Future<void> _ensureGoogleSignInInitialized() async {
    if (!_isGoogleSignInInitialized) {
      await _initializeGoogleSignIn();
    }
  }

  void _signInWithGoogle() async {
    MixpanelManager().track("Google login clicked");
    analytics.logEvent(name: "social_login");

    try {
      await _ensureGoogleSignInInitialized();

      final GoogleSignInAccount googleUser = await _googleSignIn.authenticate();

      // In v7.1.1, authentication is now synchronous (no await needed)
      final GoogleSignInAuthentication googleAuth = googleUser.authentication;

      if (googleAuth.idToken == null) {
        debugPrint("GoogleAuth returned null idToken.");
        showToast("Google authentication failed. Try again.");
        return;
      }

      // Get access token using the new authorizationClient API
      final authClient = googleUser.authorizationClient;
      final authorization = await authClient.authorizationForScopes(['email']);
      final String? accessToken = authorization?.accessToken;

      final OAuthCredential credential = GoogleAuthProvider.credential(
        idToken: googleAuth.idToken,
        accessToken: accessToken,
      );

      setState(() => isLoading = true);

      final UserCredential userCredential = await _auth.signInWithCredential(
        credential,
      );
      final User? user = userCredential.user;

      if (user != null) {
        debugPrint("Firebase login success");
        debugPrint("E-mail: ${user.email}");
        debugPrint("Phone: ${user.phoneNumber}");

        // Set user data exactly like Java code
        SplashScreenState.CUST_NAME = user.displayName ?? "";
        SplashScreenState.EMAIL = user.email ?? "";
        SplashScreenState.PRIMARY_EMAIL = user.email ?? "";

        try {
          profilePicUrl = user.photoURL;
        } catch (ex) {
          // Handle photo URL error
        }

        if (SplashScreenState.PRIMARY_EMAIL.isEmpty) {
          throw Exception("Primary email is null");
        }

        MixpanelManager().track("Google signin success");
        await _setImageAndProceed("GOOGLE");
      } else {
        debugPrint("Firebase returned null user.");
        showToast("Sign-in failed. Try again.");
        setState(() => isLoading = false);
      }
    } catch (e) {
      MixpanelManager().track("Google signin exception", {
        "error": e.toString(),
      });

      debugPrint("Google Sign-In failed: $e");
      showToast("Error signing in. Please try again");

      setState(() => isLoading = false);
    }
  }

  void _signInWithFacebook() async {
    MixpanelManager().track("FB Login clicked");
    analytics.logEvent(name: "social_login");

    try {
      setState(() => isLoading = true);

      final LoginResult result = await FacebookAuth.instance.login(
        permissions: ['email', 'public_profile'],
      );

      if (result.status == LoginStatus.cancelled) {
        debugPrint("Facebook login cancelled");
        setState(() => isLoading = false);
        return;
      }

      if (result.status != LoginStatus.success) {
        MixpanelManager().track("Facebook login failed", {
          "status": result.status.toString(),
          "message": result.message ?? "Unknown error",
        });
        showToast("Error signing in. Please try again");
        setState(() => isLoading = false);
        return;
      }

      final AccessToken? accessToken = result.accessToken;
      if (accessToken == null) {
        showToast("Facebook access token was null.");
        setState(() => isLoading = false);
        return;
      }

      if (!settingUpFb) {
        settingUpFb = true;

        // Sign in to Firebase with Facebook credential
        final AuthCredential credential = FacebookAuthProvider.credential(
          accessToken.tokenString,
        );
        final UserCredential userCred = await _auth.signInWithCredential(
          credential,
        );

        if (userCred.user != null) {
          debugPrint("E-mail: ${userCred.user!.email}");
        } else {
          debugPrint("FACEBOOK AUTH ERROR: User is null");
        }

        // Get user data from Facebook Graph API - exactly like Java code
        final userData = await FacebookAuth.instance.getUserData(
          fields: "id,name,email,picture",
        );

        debugPrint("Facebook response: $userData");

        SplashScreenState.EMAIL = userData["email"] ?? "";
        SplashScreenState.PRIMARY_EMAIL = userData["email"] ?? "";
        SplashScreenState.CUST_NAME = userData["name"] ?? "";

        if (userData["picture"] != null &&
            userData["picture"]["data"] != null) {
          profilePicUrl = userData["picture"]["data"]["url"];
        }

        if (SplashScreenState.PRIMARY_EMAIL == null ||
            SplashScreenState.PRIMARY_EMAIL.isEmpty) {
          throw Exception("Primary email is null");
        }

        MixpanelManager().track("Graph request complete");
        await _setImageAndProceed("FACEBOOK");
      }
    } catch (e) {
      MixpanelManager().track("Graph request exception", {
        "error": e.toString(),
      });

      debugPrint("Facebook error: $e");
      showToast("Error signing in. Please try again");

      setState(() => isLoading = false);
      settingUpFb = false;
    }
  }

  Future<void> _setImageAndProceed(String source) async {
    try {
      // Download profile picture if URL exists
      if (profilePicUrl != null && profilePicUrl!.isNotEmpty) {
        final response = await http.get(Uri.parse(profilePicUrl!));
        if (response.statusCode == 200) {
          // In Java, PROFILE_PIC is a Bitmap, but in Flutter SplashScreenState.PROFILE_PIC is String?
          // So we store the URL like the rest of your Flutter code does
          SplashScreenState.PROFILE_PIC = profilePicUrl;
        }
      }
    } catch (e) {
      debugPrint("Error downloading profile picture: $e");
    }

    // Set login source exactly like Java code
    SplashScreenState.loginSource = "SOCIAL $source";

    // Set previous account check flag
    FirestoreFunctions.checkPreviousAccount = true;

    // Call FirestoreFunctions to get tickets - exactly like Java code
    final firestoreFunctions = FirestoreFunctions.withContext(context);
    await firestoreFunctions.getTicketsNew(source);
  }

  void goToDashboard() {
    Navigator.pushNamedAndRemoveUntil(
      context,
      "/dashboard", // Update this to match your actual dashboard route
      (route) => false,
    );
  }

  void showToast(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  void hideLoader() {
    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Theme.of(context).primaryColor,
        elevation: 4,
      ),
      body: Stack(
        children: [
          Column(
            children: [
              Image.asset(
                'assets/sign_up_train.png',
                width: double.infinity,
                fit: BoxFit.cover,
              ),
              const SizedBox(height: 20),
              const Text(
                "Sign in to Quick Tatkal",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              const Text(
                "Sign in now to access your tickets and purchases",
                style: TextStyle(color: Colors.white70),
              ),
              const Text(
                "from any device",
                style: TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 30),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  GestureDetector(
                    onTap: _signInWithGoogle,
                    child: Image.asset("assets/google_signin.png", height: 45),
                  ),
                  GestureDetector(
                    onTap: _signInWithFacebook,
                    child: Image.asset("assets/fb_signin.png", height: 45),
                  ),
                ],
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  MixpanelManager().track("Sign up later clicked");
                  analytics.logEvent(name: "social_cancel");

                  SplashScreenState.SPLASH_RUNNING = false;
                  goToDashboard();
                },
                child: const Text(
                  "Sign up later",
                  style: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
          if (isLoading)
            Container(
              color: Colors.white.withOpacity(0.3),
              child: const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
            ),
        ],
      ),
    );
  }
}
