import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../core/AppConstants.dart';
import '../core/Consts.dart';
import '../core/helper/mixpanel_manager.dart';
import '../screens/PremiumScreen.dart';

class FAQ {
  final String question;
  final String answer;

  FAQ(this.question, this.answer);
}

class FAQSupportScreen extends StatefulWidget {
  const FAQSupportScreen({super.key});

  @override
  State<FAQSupportScreen> createState() => _FAQSupportScreenState();
}

class _FAQSupportScreenState extends State<FAQSupportScreen>
    with TickerProviderStateMixin {
  final List<FAQ> _faqList = [];
  final Map<int, bool> _expandedMap = {};
  final Map<int, AnimationController> _animationControllers = {};
  String _appVersion = "1.0";

  @override
  void initState() {
    super.initState();
    _getAppVersion();
    _setupFAQs();
    _setupAnimationControllers();
  }

  @override
  void dispose() {
    for (final controller in _animationControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = packageInfo.version;
      });
    } catch (e) {
      _appVersion = "NA";
    }
  }

  void _setupAnimationControllers() {
    for (int i = 0; i < _faqList.length; i++) {
      _animationControllers[i] = AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: this,
      );
    }
  }

  void _setupFAQs() {
    // Dynamic GOLD info text based on user type
    String goldInfoText;
    if (AppConstants.isGoldUser == 2) {
      goldInfoText =
      "Just check the option \"Autofill Captcha\" and \"Autofill OTP\" while creating the form and Quick Tatkal will autofill it for you. OTP Autofill is done using Quick OTP app. If you're facing issues in OTP autofill, keep Quick OTP app open while booking";
    } else {
      goldInfoText =
      "Captcha & OTP autofill features are available in GOLD pack only. If you're a GOLD user, you need to check the option \"Autofill Captcha\" and \"Autofill OTP\" while creating form. If you're not a GOLD user, you can <font color='teal'><b><u>Buy GOLD Pack</u></b></font>";
    }

    _faqList.addAll([
      FAQ(
        "Create IRCTC account",
        "Follow these steps to create IRCTC account<br/>" +
            "<a href='https://www.india.com/travel/articles/step-by-step-guide-open-irctc-account-book-train-tickets-5060567'>https://www.india.com/travel/articles/step-by-step-guide-open-irctc-account-book-train-tickets-5060567</a>" +
            "<br/><br/>" +
            "Or watch video : <a href='https://www.youtube.com/watch?v=hY5b6ailq2o'>https://www.youtube.com/watch?v=hY5b6ailq2o</a>",
      ),
      FAQ("How can I autofill captcha and OTP?", goldInfoText),
      FAQ(
        "Tips for booking confirm tatkal ticket with Rail Connect autofill using Quick Tatkal",
        "Follow these tips in order to properly autofill rail connect app for booking<br/><br/>" +
            "1. Before starting booking, make sure rail connect app is closed.<br/><br/>" +
            "2. If you've started booking in rail connect and want to restart booking, do it in following sequence<br/><br/>" +
            "    i) Close accessibility service. You can do it by either clicking on Quick Tatkal notification, and turning accessibility option for Quick Tatkal off<br/>" +
            "Or you can click on the Rail Connect Assistant dock on top left corner, and then click the close (x) button<br/>" +
            "    ii) Close Rail Connect app<br/>" +
            "    iii) Close Quick Tatkal app<br/>" +
            "    iv) Start Quick Tatkal again and start the booking as normal<br/><br/>" +
            "3. If autofill is not working in rail connect even after following above steps, restart your phone",
      ),
      FAQ(
        "Ticket Booking Demo video of Quick Tatkal",
        "Rail Connect Booking Demo (with OTP autofill) : <a href='https://youtu.be/Lo7WnqOMonI'>https://youtu.be/Lo7WnqOMonI</a><br/><br/>" +
            "IRCTC Website Booking Demo (with CAPTCHA autofill) : <a href='https://youtu.be/0ttP7QPAVug'>https://youtu.be/0ttP7QPAVug</a><br/><br/>" +
            "Booking demo with Travel Guarantee (alternate flight, bus or train for waitlisted tickets) and Free Cancellation feature  : <a href='https://youtu.be/sLy7PzK8zqc'>https://youtu.be/sLy7PzK8zqc</a><br/><br/>",
      ),
      FAQ(
        "Sometimes autofill stops working, or takes a long time, when passenger page is loaded (in Rail Connect app). What to do?",
        "Just scroll on the screen a little bit forward or backward and it will start working.",
      ),
      FAQ(
        "Money deducted, but ticket not booked",
        "If money has been debited from your account and ticket is not booked, you will automatically get 100% refund from IRCTC in your account within 7 business days.",
      ),
      FAQ(
        "Ticket cancellation charges and rules",
        "<b><font color='#009900'>For GENERAL E-ticket:</font></b><br/><br/>" +
            "<b>1. For Confirmed/RAC Ticket</b><br/><br/>" +
            "<font color='#CF6679'>→ Cancelled more than 48 hrs before the scheduled departure of the train</font><br/>" +
            "✓ AC First Class/Executive Class - ₹240 + GST<br/>" +
            "✓ AC 2 Tier/First Class - ₹200 + GST<br/>" +
            "✓ AC 3 Tier/AC Chair car/ AC 3 Economy - ₹180 + GST<br/>" +
            "✓ Sleeper Class - ₹120 + GST<br/>" +
            "✓ Second Class - ₹60 + GST<br/><br/>" +
            "<font color='#CF6679'>→ Cancelled within 48 hrs and up to 12 hours before the scheduled departure of the train</font><br/>" +
            "✓ 25% of the fare, or amount mentioned in first point (whichever is higher)<br/><br/>" +
            "<font color='#CF6679'>→ Less than 12 hours and upto 4 hours before the scheduled departure of the train or up to chart preparation which is earlier</font><br/>" +
            "✓ 50% of the fare, or amount mentioned in first point (whichever is higher)<br/><br/>" +
            "<font color='#CF6679'>→ For cancellation after chart preparation, you need to file TDR</font><br/><br/>" +
            "<b>2. Waitlisted Ticket</b><br/><br/>" +
            "<font color='#CF6679'>→ Cancelled up to four hours before the Scheduled Departure of the Train</font><br/>" +
            "✓ ₹20 +GST per passenger<br/>" +
            "✓ If all passengers on a ticket remain on waiting list after first charting, user need not " +
            "cancel such tickets. Such tickets will be cancelled automatically through the system, and " +
            "the full refund will be credited back, without deducting any cancellation." +
            "<br/><br/>" +
            "<font color='#009900'><b>For TATKAL E-ticket:</b></font><br/><br/>" +
            "→ No refund will be granted on cancellation of confirmed Tatkal tickets<br/><br/>" +
            "<b><font color='yellow'>NOTE: </font></b><i>E-tickets (tickets booked online on IRCTC) can be cancelled online on IRCTC website or Rail Connect app only. Tickets cannot be cancelled at reservation (PRS) counters.</i><br/><br/>" +
            "More details on website <a href='https://contents.irctc.co.in/en/CancellationRulesforIRCTCTrain.pdf'>https://contents.irctc.co.in/en/CancellationRulesforIRCTCTrain.pdf</a>",
      ),
      FAQ(
        "How to get ticket SMS again, or print your ticket",
        "Watch this video to see how to resend ticket SMS again to mobile number provided while booking and how to print your ticket : <a href='https://www.youtube.com/watch?v=KpWcg_YLVnA'>https://www.youtube.com/watch?v=KpWcg_YLVnA</a>",
      ),
      FAQ(
        "How many days before does train bookings start for General and Tatkal quota?",
        "For Tatkal quota, booking window opens <b>1 day</b> before the start date of the train.<br/><br/>" +
            "For General quota, bookings open <b>60 days</b> prior to start date of the train. " +
            "To check when window will open for ticket booking on a particular date, visit the link <a href='https://ticketdate.in'>https://ticketdate.in</a>" +
            "<br/><br/>(Enter the date when train will start from source station. For example, to find booking date of train for travelling from JHANSI on 24 October, and the train starts from NEW DELHI on 23 October, you will enter the date as 23 October and it will give a result that bookings will open from 25 June)",
      ),
      FAQ(
        "At what time, does Tatkal and General ticket bookings open?",
        "• <b>Tatkal</b> Quota:<br/><br/>For AC classes, bookings start from <b>10:00 AM</b> (Indian Standard Time).<br/>" +
            "For non AC classes, bookings start from <b>11:00 AM</b> (Indian Standard Time)<br/><br/>" +
            "• <b>General</b> (Normal) Quota:<br/><br/>Bookings will start at <b>8:00 AM</b> (IST) 60 days prior to train's start date",
      ),
      FAQ(
        "What is TDR and how to file it?",
        "TDR (Ticket Deposit Receipt) is the process of claiming refunds for special reasons (like train missed, train cancelled, AC failure, etc) for a ticket booked via IRCTC. The refund process may take 60 days or more. Please refer to the following link for more details<br/>" +
            "<br/><a href='https://contents.irctc.co.in/en/REFUND%20RULES%20wef%2012-Nov-15.pdf'>https://contents.irctc.co.in/en/REFUND%20RULES%20wef%2012-Nov-15.pdf</a>",
      ),
      FAQ(
        "Maximum number of passengers on 1 ticket for General and Tatkal quota",
        "Maximum <b>6 passengers</b> and <b>2 children</b> (below 5 years) are allowed for booking in General quota in a single booking, while in Tatkal quota, <b>4 passengers</b> and <b>2 children</b> (below 5 years) are allowed.",
      ),
      FAQ(
        "How many tickets can be booked from one IP (One device or devices connected to one wifi)?",
        "User can book 2 tickets between 8 AM to 12 AM from a single IP address.<br/><br/>" +
            "Phones and computers connected to single wifi are considered as same IP, so you can either use Mobile data or another wifi if you want to book more than 2 tickets in a day from 8 AM - 12 AM",
      ),
      FAQ(
        "What is age criteria and concession for \"Opt berth\"",
        "Children with minimum 5 years and maximum 11 years have an option to opt berth.<br/><br/>" +
            "If you select \"Opt Berth\" while booking (in passenger details page), a berth (seat) will be allotted to the passenger. If you uncheck it, berth will NOT be allotted and only 50% of ticket amount will be charged for that passenger.",
      ),
      FAQ(
        "Can I change boarding station after ticket booking?",
        "Yes, you can change boarding station after you've booked your ticket (but you can do it only 1 time).<br/><br/>Follow these instructions to change your boarding station : <a href='https://www.youtube.com/watch?v=Q75P6Of2Vvk'>https://www.youtube.com/watch?v=Q75P6Of2Vvk</a>",
      ),
      FAQ(
        "How many bookings can I make in a month and in a day from one IRCTC account.",
        "You can book 6 tickets in a month from one account. If you want to book more than 6 tickets, you can do so by linking Aadhar of passengers. You can book up to 12 tickets in a month by linking your Aadhar. After you link Aadhar, you can book more than 6 tickets (up to 12 tickets) in a month if at least one aadhar verified passenger from Master passenger list on Passenger details page while booking. Know how to link Aadhar of passengers in IRCTC - <a href='https://www.livemint.com/news/india/book-12-train-tickets-in-a-month-by-linking-aadhaar-with-irctc-how-to-do-it-**************.html'>https://www.livemint.com/news/india/book-12-train-tickets-in-a-month-by-linking-aadhaar-with-irctc-how-to-do-it-**************.html</a>" +
            "<br/><br/>Watch video : <a href='https://www.youtube.com/watch?v=QNpn9JJkeac'>https://www.youtube.com/watch?v=QNpn9JJkeac</a>",
      ),
      FAQ(
        "How to create master passenger list?",
        "To know how to create Aadhar verified master passenger list and use it while booking, watch video <a href='https://www.youtube.com/watch?v=sKZEqc0sY7w'>https://www.youtube.com/watch?v=sKZEqc0sY7w</a>",
      ),
      FAQ(
        "Meaning of WL/NA/AVL/RAC/CNF/PQWL/TQWL/RLWL",
        "WL- Waitlist / Confirmation of seats is subject to availability.<br/><br/>" +
            "NA- Not Available / Seats are not available<br/><br/><br/>" +
            "AVL- Available / shows the available seats<br/><br/>" +
            "RAC- Reservation Against Cancellation. Booking is confirmed, however, the seat allocation will be done once the final chart is prepared. RAC booking status assures you half a seat on the train.<br/><br/>" +
            "CNF- Confirmed tickets<br/><br/>" +
            "PQWL- Pooled Quota Waiting List. The Pooled Quota is generally allotted for passengers travelling from the originating station to a station short of the terminating station, or from an intermediate station to the terminating station, or between two intermediate stations.<br/><br/>" +
            "TQWL- Tatkal Waiting List.<br/><br/>" +
            "RLWL- Remote Location Waiting List (RLWL). It means ticket is issued for intermediate stations (between the originating and terminating stations) because usually these are the most important towns or cities on that particular route.",
      ),
      FAQ(
        "How can I contact IRCTC in case of ticket booking, refund or other queries and complaints?",
        "You can contact IRCTC on toll free number <font color='teal'>0755 6610661</font> or email them at <u><font color='teal'><EMAIL></font></u>. You can find more contact details on this page <a href='https://www.irctc.co.in/eticketing/contact.jsf'>https://www.irctc.co.in/eticketing/contact.jsf</a>",
      ),
      FAQ(
        "How to enable accessibility for booking through Rail Conect app?",
        "You need to make sure that before starting booking on Rail Connect, Accessibility service for Quick Tatkal is turned off and enable it after clicking Book Now button in Rail Conect tab in form screen for Rail Conect app autofill to function correctly.<br/><br/>" +
            "Accessibility option is under Phone Settings / Advanced Settings. You need to find Quick Tatkal option, and enable it.<br/><br/>" +
            "PS: If you're still facing issue with rail connect autofill, restart your phone<br/>",
      ),
      FAQ(
        "Do we store your IRCTC credentials and bank information that you save in form?",
        "No, we do not store any sensitive user information on our servers. Moreover, it is stored in user's phone only and the data is also securely encrypted. For security reasons, you can also lock your form with a password so that no one other than you can open and see it.",
      ),
    ]);

    _setupAnimationControllers();
  }

  void _toggleExpansion(int index) {
    setState(() {
      final isExpanded = _expandedMap[index] ?? false;
      _expandedMap[index] = !isExpanded;

      if (!isExpanded) {
        _animationControllers[index]?.forward();
      } else {
        _animationControllers[index]?.reverse();
      }
    });
  }

  Widget _buildFAQItem(int index) {
    final faq = _faqList[index];
    final isExpanded = _expandedMap[index] ?? false;
    final animation = _animationControllers[index]!;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white12),
        borderRadius: BorderRadius.circular(12),
        color: const Color(0xFF1E1E1E),
      ),
      child: Column(
        children: [
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: () => _toggleExpansion(index),
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    AnimatedRotation(
                      turns: isExpanded ? 0.5 : 0,
                      duration: const Duration(milliseconds: 300),
                      child: Icon(
                        Icons.keyboard_arrow_down,
                        color: Theme
                            .of(context)
                            .primaryColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        faq.question,
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              return SizeTransition(
                sizeFactor: animation,
                child: Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(
                      left: 16, right: 16, bottom: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2A2A2A),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Html(
                    data: faq.answer,
                    style: {
                      "body": Style(
                        color: Colors.white,
                        fontSize: FontSize(14),
                        lineHeight: LineHeight(1.6),
                        margin: Margins.zero,
                        padding: HtmlPaddings.zero,
                      ),
                      "a": Style(
                        color: Colors.teal,
                        textDecoration: TextDecoration.underline,
                      ),
                      "b": Style(
                        fontWeight: FontWeight.bold,
                      ),
                      "font": Style(
                        color: Colors.white,
                      ),
                    },
                    onLinkTap: (url, _, __) => _handleLinkTap(url),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _handleLinkTap(String? url) async {
    if (url == null) return;

    // Analytics tracking based on URL
    String firebaseTag = _getAnalyticsTag(url);
    if (firebaseTag.isNotEmpty) {
      MixpanelManager().track(firebaseTag, {"value": "true"});
    }

    // Handle special cases
    if (url.toUpperCase().contains("BUY GOLD PACK")) {
      MixpanelManager().track("faq_click_buy", {"value": "true"});
      Consts.paymentSource = "FAQ";
      Get.to(() => const PremiumScreen(), arguments: {"GOLD": "Y"});
      return;
    }

    // Launch URL
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  String _getAnalyticsTag(String url) {
    if (url.contains("articles")) return "faq_guide_create_ac";
    if (url.contains("hY5b6ailq2o")) return "faq_video_create_ac";
    if (url.contains("CancellationRulesforIRCTCTrain"))
      return "faq_guide_cancel";
    if (url.contains("KpWcg_YLVnA")) return "faq_video_print_ticket";
    if (url.contains("ticketdate")) return "faq_web_general_open";
    if (url.contains("REFUND%20RULE")) return "faq_guide_tdr_refund";
    if (url.contains("Q75P6Of2Vvk")) return "faq_video_change_board";
    if (url.contains("livemint")) return "faq_guide_link_aadhar";
    if (url.contains("QNpn9JJkeac")) return "faq_video_link_aadhar";
    if (url.contains("sKZEqc0sY7w")) return "faq_video_pass_list";
    if (url.contains("eticketing")) return "faq_web_contact_irctc";
    if (url.contains("Lo7WnqOMonI")) return "faq_video_rc_demo";
    if (url.contains("0ttP7QPAVug")) return "faq_video_web_demo";
    if (url.contains("sLy7PzK8zqc")) return "faq_video_railofy";
    return "";
  }

  void _sendSupportEmail() async {
    MixpanelManager().track("Click Contact Us");

    String ids = "";
    if (Consts.loginMethod == "LATER") {
      ids = Consts.DEVICE_ID;
    } else {
      if (AppConstants.email != "NA") {
        ids = AppConstants.email;
      }
      if (AppConstants.mobileNo != "NA") {
        ids += " | ${AppConstants.mobileNo}";
      }
    }
    ids = "[Reg ID : $ids]";

    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'subject=Quick Tatkal Support v$_appVersion $ids',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No email apps installed.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF121212),
      appBar: AppBar(
        title: Text(
          'Help & Support',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: const Color(0xFF121212),
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: _faqList.length,
              itemBuilder: (context, index) => _buildFAQItem(index),
            ),
          ),
          _buildContactUsButton(),
        ],
      ),
    );
  }

  Widget _buildContactUsButton() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Material(
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: _sendSupportEmail,
          child: Ink(
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFA34BFF), Color(0xFF6100FF)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.purple.withOpacity(0.3),
                  blurRadius: 12,
                  spreadRadius: 2,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.support_agent,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Query not resolved? Send Support Email',
                      textAlign: TextAlign.center,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
