import 'dart:async';
import 'dart:math';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:quick_tatkal_v2/core/utils/AppColors.dart';
import 'package:quick_tatkal_v2/screens/splash_screen.dart';
import '../database/LoginDB.dart';
import '../server/FirestoreFunctions.dart';
import '../server/ServerTask.dart';

class OTPValidationScreen extends StatefulWidget {
  final String phone;
  final String displayPhone;

  const OTPValidationScreen({
    Key? key,
    required this.phone,
    required this.displayPhone,
  }) : super(key: key);

  @override
  State<OTPValidationScreen> createState() => _OTPValidationScreenState();
}

class _OTPValidationScreenState extends State<OTPValidationScreen> {
  final TextEditingController _otpController = TextEditingController();

  final FocusNode _otpFocusNode = FocusNode();

  bool _isVerifying = false;

  bool _canResend = false;
  bool _isLoading = false;
  int _waitSeconds = 15;
  String _verifyButtonText = "Verify";

  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startOtpTimer();
    _sendOtp();
    _otpController.addListener(_onOtpChanged);
  }

  void _onOtpChanged() {
    if (_otpController.text.length == 4 && !_isVerifying) {
      setState(() {
        _verifyButtonText = "Verify";
      });
    }
  }

  void _startOtpTimer() {
    _timer?.cancel();
    _waitSeconds = 15;
    _canResend = false;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _waitSeconds--;
        if (_waitSeconds <= 0) {
          _canResend = true;
          _timer?.cancel();
        }
      });
    });
  }

  void showLoader() {
    setState(() {
      _isLoading = true;
    });
  }

  void hideLoader() {
    setState(() => _isLoading = false);
  }

  void clearOTP() {
    _otpController.clear();
    FocusScope.of(context).requestFocus(_otpFocusNode);
  }

  Future<void> _sendOtp({bool voice = false}) async {
    String generatedOtp = _generateOtp();

    final response = await ServerTask.sendOTP(
      phone: widget.phone,
      otp: generatedOtp,
      voice: voice,
    );
    print('OTPsendresponse: ' + response.toString());

    if (response.contains("ERROR")) {
      _showToast("Unable to send OTP. Try again.");
    } else {
      _showToast(
        voice
            ? "You'll shortly receive a call with OTP"
            : "OTP sent to your number",
      );
    }
  }

  Future<void> _verifyOtp() async {
    final otp = _otpController.text.trim();

    if (otp.length != 4) return;

    showLoader();
    setState(() {
      _verifyButtonText = "Verifying...";
      _isVerifying = true;
    });

    final result = await ServerTask.verifyOtp(phone: widget.phone, otp: otp);

    if (result == 'success') {
      FirebaseAnalytics.instance.logEvent(
        name: 'otp_verified',
        parameters: {'value': 'true'},
      );

      await LoginDB.deleteAll();

      final db = await LoginDB.database;
      await db.insert('MOBILE', {'MOBILE_NO': widget.phone});

      SplashScreenState.MOBILE_NO = widget.phone;

      _showToast("OTP Verified");

      setState(() {
        _verifyButtonText = "Verified";
      });

      FirestoreFunctions.checkPreviousAccount = true;

      SplashScreenState.loginSource = "OTPValidation";

      Navigator.of(context).pop(true);
    } else {
      FirebaseAnalytics.instance.logEvent(
        name: 'otp_verify_error',
        parameters: {'value': 'true'},
      );

      await ServerTask(context).logError(
        widget.phone,
        "MOBILE_VERIFY",
        result,
        mobile: '',
        medium: '',
        error: '',
      );
      _showToast("Incorrect OTP");

      clearOTP();

      setState(() {
        _verifyButtonText = "Verify";
      });
    }

    hideLoader();
    setState(() => _isVerifying = false);
  }

  String _generateOtp() {
    return (Random().nextInt(8999) + 1000).toString();
  }

  void _showToast(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }



  @override
  void dispose() {
    _otpController.removeListener(_onOtpChanged);
    _otpController.dispose();
    _otpFocusNode.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: AppBar(
        title: const Text("Verify OTP"),
        backgroundColor: Colors.black,
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                const SizedBox(height: 20),
                Text(
                  "Please enter OTP sent \n to ${widget.displayPhone}",
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 40),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 40,
                    vertical: 20,
                  ),
                  child: PinCodeTextField(
                    appContext: context,
                    controller: _otpController,
                    focusNode: _otpFocusNode,
                    length: 4,
                    keyboardType: TextInputType.number,
                    animationType: AnimationType.fade,
                    autoFocus: true,
                    cursorColor: Colors.white,
                    textStyle: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                    ),
                    pinTheme: PinTheme(
                      shape: PinCodeFieldShape.box,
                      borderRadius: BorderRadius.circular(12),
                      fieldHeight: 60,
                      fieldWidth: 50,
                      activeColor: Colors.white,
                      selectedColor: Colors.white70,
                      inactiveColor: Colors.white30,
                      activeFillColor: Colors.black45,
                      selectedFillColor: Colors.black45,
                      inactiveFillColor: Colors.black45,
                    ),
                    enableActiveFill: true,
                    onChanged: (_) => setState(() {}),
                    onCompleted: (_) {},
                  ),
                ),
                const SizedBox(height: 20),
                if (!_canResend)
                  Text(
                    "Didn't receive OTP? Wait $_waitSeconds seconds",
                    style: const TextStyle(color: Colors.grey),
                  )
                else
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextButton(
                        onPressed: () {
                          _startOtpTimer();
                          _sendOtp();
                        },
                        child: const Text(
                          "Resend OTP",
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                      const SizedBox(width: 16),
                      TextButton(
                        onPressed: () {
                          _startOtpTimer();
                          _sendOtp(voice: true);
                        },
                        child: const Text(
                          "Resend via Call",
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ],
                  ),
                const SizedBox(height: 30),
                ElevatedButton(
                  onPressed: _otpController.text.length == 4 && !_isVerifying
                      ? _verifyOtp
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _otpController.text.length == 4 && !_isVerifying
                        ? Colors.deepPurple
                        : Colors.deepPurple.withOpacity(0.5),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 52,
                      vertical: 14,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: Text(
                    _verifyButtonText,
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  ),
                ),
              ],
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
            ),
        ],
      ),
    );
  }
}
