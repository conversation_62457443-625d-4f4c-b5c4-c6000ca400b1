import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:quick_tatkal_v2/core/utils/AppColors.dart';
import 'package:quick_tatkal_v2/routes/app_routes.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class PNRScreen extends StatefulWidget {
  const PNRScreen({super.key});

  @override
  State<PNRScreen> createState() => _PNRScreenState();
}

class _PNRScreenState extends State<PNRScreen> {
  final TextEditingController _pnrController = TextEditingController();
  int _currentIndex = 0;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _checkClipboard();
  }

  Future<void> _checkClipboard() async {
    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData != null) {
      final text = clipboardData.text ?? '';
      if (RegExp(r'^\d{10}\$').hasMatch(text)) {
        _animatePNRInput(text);
      } else {
        _focusInputField();
      }
    } else {
      _focusInputField();
    }
  }

  void _animatePNRInput(String pnr) {
    _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_currentIndex < 10) {
        _pnrController.text = pnr.substring(0, _currentIndex + 1);
        _currentIndex++;
      } else {
        _timer?.cancel();
      }
    });
  }

  void _focusInputField() {
    Future.delayed(const Duration(milliseconds: 300), () {
      FocusScope.of(context).requestFocus(FocusNode());
      SystemChannels.textInput.invokeMethod('TextInput.show');
    });
  }

  void _searchPNR() {
    final pnr = _pnrController.text;
    if (pnr.length != 10) {
      Get.snackbar(
        "Invalid PNR",
        "Please enter a valid 10 digit PNR number.",
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );

      return;
    }
    Get.toNamed(AppRoutes.PnrResultScreen, arguments: {'pnr': pnr});
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            backgroundColor: Colors.black,
            floating: true,
            pinned: true,
            expandedHeight: 40,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                "Quick Tatkal",
                style: TextStyle(color: Colors.white),
              ),
              centerTitle: true,
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 22),
            sliver: SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Enter PNR",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 13,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  const SizedBox(height: 16),
                  PinCodeTextField(
                    appContext: context,
                    length: 10,
                    controller: _pnrController,
                    keyboardType: TextInputType.number,
                    textStyle: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                    animationType: AnimationType.none,
                    cursorColor: Colors.amber,
                    pinTheme: PinTheme(
                      shape: PinCodeFieldShape.underline,
                      borderRadius: BorderRadius.circular(8),
                      fieldHeight: 44,
                      fieldWidth: 28,
                      inactiveColor: Colors.white38,
                      activeColor: Colors.white,
                      selectedColor: Color(0xFFA34BFF),
                    ),
                    backgroundColor: Colors.transparent,
                    enableActiveFill: false,
                    enablePinAutofill: true,
                    onChanged: (_) {},
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    beforeTextPaste: (text) =>
                        RegExp(r'^[0-9]{1,10}').hasMatch(text ?? ''),
                  ),
                  const SizedBox(height: 36),
                  Center(
                    child: GestureDetector(
                      onTap: _searchPNR,
                      child: Container(
                        width: 200,
                        height: 40,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          gradient: const LinearGradient(
                            colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: const Text(
                          'Search',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Container(
                    height: 80,
                    color: Colors.transparent,
                    child: Center(
                      child: Text(
                        "Ad Placeholder",
                        style: TextStyle(color: Colors.grey[700]),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
