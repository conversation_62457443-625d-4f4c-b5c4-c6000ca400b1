import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';

import 'package:flutter/gestures.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:path_provider/path_provider.dart';
import 'package:quick_tatkal_v2/core/utils/AppColors.dart';
import 'package:quick_tatkal_v2/pg/TicketInfoDialog.dart';
import 'package:quick_tatkal_v2/screens/PremiumScreen.dart';
import 'package:quick_tatkal_v2/screens/splash_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
import 'package:workmanager/workmanager.dart';
import '../captcha/APIConsts.dart';
import '../core/Consts.dart';
import '../database/LoginDB.dart';
import '../core/AppConstants.dart';
import '../core/helper/mixpanel_manager.dart';
import '../pg/BuyTicketsDialog.dart';
import '../pg/PaymentManager.dart';
import '../routes/app_routes.dart';
import 'FAQSupportScreen.dart';
import 'VideoList.dart';
import 'freeTickets.dart';
import 'PNRScreen.dart';
import 'legal_screen.dart';
import 'package:url_launcher/url_launcher.dart' as flutter_launcher;
import '../quick/ui/LanguageFragment.dart';
import '../quick/AppOpenManager.dart';

class QuickTatkalApp {
  static bool closeActivity = true;
  static String sku = "";

  // AppOpenManager instance - Java parity (Line 27 in Java)
  static AppOpenManager? appOpenManager;

  static Future<void> init() async {
    await MobileAds.instance.initialize();

    // Initialize AppOpenManager - Java parity (Line 41 in Java constructor)
    appOpenManager = AppOpenManager();
    // Start fetching ads immediately after initialization
    appOpenManager?.fetchAd();

    print("✅ QuickTatkalApp initialized with AppOpenManager");
  }

  Future<void> setRenewalNotification(DateTime renewalTime) async {
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    final futureTimeInMillis = renewalTime.millisecondsSinceEpoch;
    final delay = futureTimeInMillis - currentTime;
    if (delay > 0) {
      Workmanager().registerOneOffTask(
        'renewal_notification',
        'showRenewalNotification',
        initialDelay: Duration(milliseconds: delay),
        inputData: {
          'title': 'Renewal Reminder',
          'message': 'Your subscription renewal is due soon!',
        },
      );
      print(
        '[Workmanager] Scheduled subscription renewal notification in $delay ms (at $renewalTime)',
      );
    }
  }
}

class QuickTatkalScreen extends StatefulWidget {
  const QuickTatkalScreen({super.key});

  @override
  State<QuickTatkalScreen> createState() => QuickTatkalScreenState();
}

class QuickTatkalScreenState extends State<QuickTatkalScreen>
    with WidgetsBindingObserver {
  static bool isGoldOfferRunning = false;
  static bool removeAdsClicked = false;
  static String SKU = "";
  static int tickets = 0;
  static int amount = 0;
  int purchaseTime = 0;
  String paytmOrderId = "";
  String orderId = "";

  bool loadingLayoutVisible = false;
  bool goldPackAlreadyPurchased = false;
  String goldPackAdSource = "Organic";

  bool _isReversed = false;

  bool firstOpen = false;
  bool _hasFocusChanged = false;

  Future<File> _getGoldPackImageFile() async {
    if (_goldPackImagePath.isEmpty) {
      return File('/tmp/file_that_does_not_exist');
    }
    final dir = await getApplicationDocumentsDirectory();
    return File('${dir.path}/$_goldPackImagePath');
  }

  bool _goldOfferVisible = false;
  String _goldPackImagePath = "";
  String _goldTerms = "";
  bool _isGoldLayoutVisible = false;

  final GoogleSignIn _googleSignIn = GoogleSignIn.instance;
  bool _isGoogleSignInInitialized = false;

  Future<bool?> _showExitConfirmationDialog(BuildContext context) async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: const Color(0xFF121212),
          title: const Text('Exit App', style: TextStyle(color: Colors.white)),
          content: const Text(
            'Are you sure you want to exit?',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white54),
              ),
              onPressed: () => Navigator.of(dialogContext).pop(false),
            ),
            TextButton(
              child: const Text('Exit', style: TextStyle(color: Colors.red)),
              onPressed: () => Navigator.of(dialogContext).pop(true),
            ),
          ],
        );
      },
    );
  }

  BannerAd? _bannerAd;
  InterstitialAd? _interstitialAd;
  bool _isInterstitialAdReady = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Load user data from SplashScreenState - mirrors Java Dashboard onCreate
    _loadUserDataFromSplash();

    if (APIConsts.PRICE_CP_STR.isEmpty) {
      APIConsts.PRICE_CP_STR = "₹49";
      APIConsts.PRICE_SP_STR = "₹99";
      APIConsts.PRICE_PP_STR = "₹149";
      APIConsts.PRICE_GP_STR = "₹199";
      APIConsts.PRICE_DP_OFFER_STR = "₹299";
      APIConsts.PRICE_DP_REG_STR = "₹399";
      print("🟡 Initialized price strings for testing");
    }

    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
    );

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.black,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );

    QuickTatkalApp.init();
    _initMixpanel();
    _initAds();
    _initializeGoogleSignIn();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 500), () {
        _onWindowFocusChanged(true);
      });

      final amount = 0;
      if (amount == 0) {
        setGoldOfferTime().then((result) {
          if (!result) {}
        });
      }
    });
  }

  // Load user data from SplashScreenState - mirrors Java Dashboard onCreate
  void _loadUserDataFromSplash() {
    try {
      // Mirror Java Dashboard user data loading
      final int ticketsLeft = SplashScreenState.ticketsLeft;
      final String userType = Consts.USER_TYPE;
      final int isGoldUser = SplashScreenState.isGoldUser;

      print("📊 Dashboard loaded user data:");
      print("   Tickets: $ticketsLeft");
      print("   Type: $userType");
      print("   Gold: $isGoldUser");

      // Update local state if needed
      setState(() {
        // Any UI updates based on user data
      });

      // Track dashboard open - mirrors Java analytics
      MixpanelManager().track("Dashboard opened", {
        "User Type": userType,
        "Tickets Left": ticketsLeft,
        "Login Method": Consts.loginMethod,
      });
    } catch (e) {
      print("❌ Error loading user data: $e");
    }
  }

  Future<void> _initMixpanel() async {
    await MixpanelManager().init();
  }

  Future<void> _initializeGoogleSignIn() async {
    try {
      await _googleSignIn.initialize();
      _isGoogleSignInInitialized = true;
    } catch (e) {
      debugPrint('Failed to initialize Google Sign-In: $e');
    }
  }

  Future<void> _ensureGoogleSignInInitialized() async {
    if (!_isGoogleSignInInitialized) {
      await _initializeGoogleSignIn();
    }
  }

  void _initAds() {
    loadBannerAd();

    InterstitialAd.load(
      adUnitId: '<YOUR_INTERSTITIAL_AD_UNIT_ID>',
      request: AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          _isInterstitialAdReady = true;
        },
        onAdFailedToLoad: (error) {
          _isInterstitialAdReady = false;
        },
      ),
    );
  }

  void loadBannerAd() async {
    AdSize adSize;
    try {
      adSize = (await getAdSizeAsync(context)) ?? AdSize.banner;
    } catch (_) {
      adSize = AdSize.banner;
    }
    setState(() {
      _bannerAd = BannerAd(
        adUnitId: Consts.TATKAL_BANNER_ID,
        size: adSize,
        request: AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: (Ad ad) {
            setState(() {}); // Trigger update to show
          },
          onAdFailedToLoad: (Ad ad, LoadAdError error) {
            ad.dispose();
            setState(() {
              _bannerAd = null;
            });
          },
        ),
      )..load();
    });
  }

  Future<AdSize?> getAdSizeAsync(BuildContext context) async {
    final MediaQueryData mqData = MediaQuery.of(context);
    final double widthPixels = mqData.size.width;
    final double density = mqData.devicePixelRatio;
    final int adWidth = (widthPixels / density).floor();
    try {
      return await AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(
        adWidth,
      );
    } catch (_) {
      return null;
    }
  }

  void _showInterstitialAd(VoidCallback onAfterAd) {
    if (_interstitialAd != null && _isInterstitialAdReady) {
      _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdDismissedFullScreenContent: (ad) {
          ad.dispose();
          _isInterstitialAdReady = false;
          InterstitialAd.load(
            adUnitId: '<YOUR_INTERSTITIAL_AD_UNIT_ID>',
            request: AdRequest(),
            adLoadCallback: InterstitialAdLoadCallback(
              onAdLoaded: (ad) {
                _interstitialAd = ad;
                _isInterstitialAdReady = true;
              },
              onAdFailedToLoad: (error) {
                _isInterstitialAdReady = false;
              },
            ),
          );
          onAfterAd();
        },
        onAdFailedToShowFullScreenContent: (ad, error) {
          ad.dispose();
          _isInterstitialAdReady = false;
          onAfterAd();
        },
      );
      _interstitialAd!.show();
    } else {
      onAfterAd();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _bannerAd?.dispose();
    _interstitialAd?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed && !_hasFocusChanged) {
      _hasFocusChanged = true;

      Future.delayed(const Duration(milliseconds: 300), () {
        _onWindowFocusChanged(true);
      });
    }
  }

  Future<void> _onWindowFocusChanged(bool hasFocus) async {
    print(" onWindowFocusChanged called with hasFocus: $hasFocus");

    if (Consts.loginMethod == null || Consts.loginMethod == "LATER") {
      if (Consts.loginMethod == null) {
        Consts.loginMethod = "LATER";
      }

      final SharedPreferences sp = await SharedPreferences.getInstance();

      int shownValue = sp.getInt("SHOWN") ?? 0;
      print("SharedPrefs SHOWN value: $shownValue");

      if (shownValue != 100) {
        print("🎯 First time user detected!");
        firstOpen = true;

        await sp.setInt("SHOWN", 100);
        print("✅ Set SHOWN = 100 in SharedPrefs");

        Future.delayed(const Duration(milliseconds: 1000), () {
          print("⏰ 1000ms delay completed, checking premium conditions");

          print("🔍 PREMIUM_ACTIVITY_SHOWN: ${Consts.PREMIUM_ACTIVITY_SHOWN}");
          print("🔍 isGoldUser: ${SplashScreenState.isGoldUser}");

          if (!Consts.PREMIUM_ACTIVITY_SHOWN &&
              SplashScreenState.isGoldUser != 2) {
            print("🚀 Launching Premium Activity!");

            Consts.PREMIUM_ACTIVITY_SHOWN = true;
            Consts.paymentSource = "First Time";

            _launchPremiumActivity();
          } else {
            print("❌ Premium conditions not met");
          }
        });
      } else if (!firstOpen) {
        print("🔄 Not first time, calling refreshAd");

        refreshAd();
      }
    } else if (!firstOpen) {
      print("🔄 User logged in, calling refreshAd");
      refreshAd();
    }
  }

  void _launchPremiumActivity() {
    print("🎉 Premium Activity launching with slide up animation!");

    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) {
          return const PremiumScreen();
        },
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(
            begin: begin,
            end: end,
          ).chain(CurveTween(curve: curve));

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
        reverseTransitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  void refreshAd() {
    // Parity variables: fake veveAdView as a Flutter widget, hide via state variable
    if (Consts.USER_TYPE == "DIAMOND_USER" || Consts.USER_TYPE == "GOLD_USER") {
      setState(() {
        // Hide ad UI, e.g., remove widget from the tree, set flag if you have one
        _bannerAd =
            null; // If using a widget for your veve ad slot, hide/replace here
      });
      return;
    }
    // Show/hook ad slot UI (Flutter's Native ad, etc)
    // For demonstration, you might re-initialize banner or native ad here
    setState(() {
      // In Flutter, load your banner/ad as needed here
      // TODO: Implement native ad loading with google_mobile_ads or other Flutter ad SDKs
    });
    print('refreshAd called: would reload/show ads if not premium/gold');
  }

  Future<void> deleteExtraMobileLoginData() async {
    final db = await LoginDB.database;

    final result = await db.rawQuery(
      'SELECT MOBILE_NO FROM ${LoginDB.tableName}',
    );
    String? lastMobileNo;

    if (result.isNotEmpty) {
      lastMobileNo = result.last['MOBILE_NO'] as String?;
    }

    if (lastMobileNo != null) {
      final deletedCount = await db.delete(
        LoginDB.tableName,
        where: 'MOBILE_NO != ?',
        whereArgs: [lastMobileNo],
      );

      if (deletedCount > 0) {
        MixpanelManager().track("Mobile No Deleted", {"Count": deletedCount});
      }
    }
  }

  Future<void> logout() async {
    switch (Consts.loginMethod) {
      case "FACEBOOK":
        try {
          await FacebookAuth.instance.logOut();
        } catch (e) {
          print("Facebook logout error: $e");
        }
        break;

      case "GOOGLE":
        try {
          await _ensureGoogleSignInInitialized();
          await FirebaseAuth.instance.signOut();
          await _googleSignIn.signOut();
        } catch (e) {
          print("Google logout error: $e");
        }
        break;

      case "MOBILE":
        try {
          await LoginDB.deleteAll();
        } catch (e) {
          print("Error clearing mobile login: $e");
        }
        break;

      default:
        print("Unknown login method: ${Consts.loginMethod}");
    }
  }

  Future<void> resetVariablesAndLogout() async {
    SplashScreenState.MOBILE_NO = "NA";
    SplashScreenState.EMAIL = "NA";
    SplashScreenState.PRIMARY_EMAIL = "NA";
    SplashScreenState.CUST_NAME = "";
    SplashScreenState.PROFILE_PIC = null;
    SplashScreenState.INVITED_BY = "NA";

    Consts.loginMethod = "LATER";
    Consts.SignupSource = "Logout";
  }

  Future<void> saveUserInfoInSharedPrefs({
    required int ticketsLeft,
    required int isGoldUser,
  }) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    await prefs.setInt('TICKETS_LEFT', ticketsLeft);
    await prefs.setString('TID', SplashScreenState.TID);
    await prefs.setString('USER_TYPE', Consts.USER_TYPE);
    await prefs.setString('EMAIL', SplashScreenState.EMAIL);
    await prefs.setString('MOBILE_NO', SplashScreenState.MOBILE_NO);
    await prefs.setString('PRIMARY_EMAIL', SplashScreenState.PRIMARY_EMAIL);
    await prefs.setString('CUST_NAME', SplashScreenState.CUST_NAME);
    await prefs.setString('UPI_TRIAL_OPTED', APIConsts.UPI_TRIAL_OPTED);
    await prefs.setString('CAPTCHA_TRIAL_OPTED', APIConsts.CAPTCHA_TRIAL_OPTED);
    await prefs.setString('PACK_EXPIRY_DATE', Consts.PACK_EXPIRY_DATE);
    await prefs.setInt('PACK_EXPIRED', Consts.PACK_EXPIRED);
    await prefs.setInt('IS_GOLD_USER', isGoldUser);
  }

  Future<void> handleAppVersion(
    BuildContext context,
    int minVersion,
    String desc,
  ) async {
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    final currentVersion = int.tryParse(packageInfo.buildNumber) ?? 0;

    if (currentVersion < minVersion) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            title: const Text("Update Quick Tatkal"),
            content: Text(desc),
            icon: const Icon(
              Icons.system_update_alt_rounded,
              color: Colors.black,
            ),
            actions: [
              TextButton(
                child: const Text("No"),
                onPressed: () => Navigator.of(dialogContext).pop(),
              ),
              TextButton(
                child: const Text("Update"),
                onPressed: () async {
                  final packageName = packageInfo.packageName;
                  final url =
                      "https://play.google.com/store/apps/details?id=$packageName";
                  if (await canLaunchUrl(Uri.parse(url))) {
                    await launchUrl(
                      Uri.parse(url),
                      mode: LaunchMode.externalApplication,
                    );
                  }

                  await FirebaseAnalytics.instance.logEvent(
                    name: "update_from_alert",
                  );

                  Navigator.of(dialogContext).pop();
                },
              ),
            ],
          );
        },
      );
    }
  }

  Future<void> handleAlert({
    required BuildContext context,
    required bool show,
    required String type,
    required String message,
    required String buttonLbl,
    required String link,
  }) async {
    if (!show) return;

    final Widget dialog = AlertDialog(
      title: const Text("Info"),
      content: Text(message),
      icon: const Icon(Icons.info_outline, color: Colors.black),
      actions: [
        if (type == "LINK")
          TextButton(
            child: const Text("CANCEL"),
            onPressed: () => Navigator.of(context).pop(),
          ),
        TextButton(
          child: Text(buttonLbl),
          onPressed: () async {
            if (type == "LINK") {
              final Uri uri = Uri.parse(link);
              if (await canLaunchUrl(uri)) {
                await launchUrl(uri, mode: LaunchMode.externalApplication);
              }
              await FirebaseAnalytics.instance.logEvent(
                name: "alert_link_visited",
              );
            }
            Navigator.of(context).pop();
          },
        ),
      ],
    );

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => dialog,
    );
  }

  Future<HttpClient> initClient() async {
    try {
      final context = SecurityContext.defaultContext;

      final certificateData = await rootBundle.load('assets/certificate.pem');
      context.setTrustedCertificatesBytes(certificateData.buffer.asUint8List());

      final httpClient = HttpClient(context: context);
      httpClient.badCertificateCallback =
          (X509Certificate cert, String host, int port) {
            return true;
          };

      return httpClient;
    } catch (e) {
      final httpClient = HttpClient();
      httpClient.badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
      return httpClient;
    }
  }

  void populateUnifiedNativeAdView(
    /*NativeAd nativeAd, NativeAdView adView in Java, see below for Flutter*/
    dynamic nativeAd,
    // Replace with your native ad object type from google_mobile_ads or other ad SDK
    dynamic adView,
    // Replace with your ad view/controller as appropriate for your plugin
  ) {
    try {
      // In Flutter, use widget trees; you'll typically compose widgets based on the available ad fields.
      // Fill out the UI (headline, body, icon, callToAction, mediaContent, etc.) based on asset availability.
      // Example (pseudo-code for google_mobile_ads NativeAd; not real code):
      //   NativeAd nativeAd;
      //   AdWidget(ad: nativeAd);
      // Widgets for headline: Text(nativeAd.headline)
      // Widgets conditional per asset:
      //   nativeAd.body != null ? Text(...) : SizedBox()
      //   nativeAd.callToAction != null ? ElevatedButton(...) : Container()
      //   (nativeAd.icon != null) ? Image(image: nativeAd.icon.image) : SizedBox()
      //
      // Check if fields are null, hide/skipped if so, show if present.
      //
      print('Would populate native ad: headline "${nativeAd?.headline}"');
      // If integrating video content, you'd check nativeAd.mediaContent and wire up controller/lifecycle.
      // In a production implementation, create a Widget that conditionally includes each ad asset.
      // See google_mobile_ads docs for 'NativeAd' widget integration and asset handling.

      // If your plugin requires nativeAd to be assigned to a widget/controller, do that here.
      // e.g., In Android, adView.setNativeAd(nativeAd);
      // In Flutter, AdWidget or your composition triggers this.

      // If you use a controller or lifecycle handling (e.g., for video), wire callbacks/lifecycles here.
    } catch (e) {
      print('Error populating native ad: $e');
    }
  }

  void showVeveAd() {
    // TODO: If you have a real Veve ad SDK for Flutter, plug it here; below is a pseudo-port for logic + tracking
    if ( /* veveAdShown */ false || SplashScreenState.isGoldUser == 2) {
      return;
    }
    // veveAdShown = true; // would mark the ad as shown if needed
    // In Flutter, you don't have findViewById, so use appropriate Widget refs

    try {
      // Pseudo-VeveAdRequest, replace by your ad request type for your ad network/SDK
      // Simulate ad setup, listeners, etc.
      // Set loading flags as you would for a stateful widget in Flutter
      setState(() {
        // Placeholder: show a loading widget if needed
      });

      // Pseudo-listener logic -- map to callbacks or whatever the Dart SDK supplies
      // Would be structured as callbacks in a Widget/ad function
      // For now, just simulate state change as in listener callbacks
      // Example: onAdLoadStarted → setState(loading=true)
      // Example: onAdLoadFailed/onAdLoadSuccess → setState(loading=false)

      // Simulate ad config - font color / margins are Widget styles in Flutter
      // CUST_KEY, TAG_ID: use as needed for your ad network's API
      const String CUST_KEY = "knm73"; // Replace with your real customer key
      const String TAG_ID = "38872"; // Replace with your real Tag ID

      // In a real ad SDK for Flutter, you would instantiate your ad widget/component and add it to the widget tree
      print("Simulating VeveAdView for CUST_KEY=$CUST_KEY, TAG_ID=$TAG_ID");
    } catch (ex) {
      print('STUDIOS showVeveAd error: $ex');
    }
  }

  Future<SharedPreferences> getSharedPreferences(String name) async {
    return await SharedPreferences.getInstance();
  }

  Future<bool> setGoldOfferTime() async {
    // Java parity: setGoldOfferTime();
    Consts.goldPackAdSource = "Organic";
    goldPackAdSource = "Organic";
    if (Consts.USER_TYPE == "DIAMOND_USER") return false;

    final spOffer = await getSharedPreferences("GOLD_OFFER");
    final spRegular = await getSharedPreferences("GOLD_REGULAR");

    String offerDate = spOffer.getString("GOLD_OFFER_ALERT_DATE") ?? "";
    String offerEndDate = spOffer.getString("GOLD_OFFER_END_DATE") ?? "";
    String regularDate = spRegular.getString("GOLD_REGULAR_ALERT_DATE") ?? "";

    final now = DateTime.now();
    final dateFormat = DateFormat("dd-MM-yyyy HH:mm:ss");

    if (offerDate.isEmpty) {
      // First time gold offer setup - Java analogy
      final c = now.add(
        Duration(hours: 12 + 3 * 24 + 24),
      ); // +24 for 1 day for 00:00
      final sdfDate = DateFormat("dd-MM-yyyy");
      final date = "${sdfDate.format(c)} 00:00:00";
      await spOffer.setString("GOLD_OFFER_ALERT_DATE", date);

      // set notification alarm at date
      try {
        final notifTime = DateFormat(
          "dd-MM-yyyy HH:mm:ss",
        ).parse("${sdfDate.format(c)} 07:00:00");
        await showLocalNotification(
          "BIG Discount on Quick Tatkal GOLD Pack 🪙😍",
          "Offer valid for today only 🕒\nGet GOLD Pack for lifetime at a special reduced price. Check out now 👉",
        );
        await scheduleNotification({
          "title": "BIG Discount on Quick Tatkal GOLD Pack 🪙😍",
          "message":
              "Offer valid for today only 🕒\nGet GOLD Pack for lifetime at a special reduced price. Check out now 👉",
        }, notifTime.millisecondsSinceEpoch);
      } catch (e) {}
    } else {
      try {
        final startDt = dateFormat.parse(offerDate);
        DateTime? endDt;
        if (offerEndDate.isNotEmpty) {
          endDt = dateFormat.parse(offerEndDate);
        }
        if (now.isAfter(startDt) || (endDt != null && now.isBefore(endDt))) {
          // Offer is active
          isGoldOfferRunning = true;
          if (APIConsts.PRICE_CP_STR.startsWith("₹")) {
            await showGoldPackAd();
          }
          if (offerEndDate.isEmpty || (endDt != null && now.isAfter(endDt))) {
            // Set start date to today 12 am
            final today = DateTime(now.year, now.month, now.day);
            final sdfDate = DateFormat("dd-MM-yyyy");
            final todayStr = "${sdfDate.format(today)} 00:00:00";
            final startDtNew = dateFormat.parse(todayStr);
            final endDate = startDtNew.add(Duration(hours: 24));

            final nextOfferDate = now.add(
              Duration(hours: 12 + 4 * 24 + 24),
            ); // next date
            final newDate = "${sdfDate.format(nextOfferDate)} 00:00:00";

            await spOffer.setString(
              "GOLD_OFFER_END_DATE",
              dateFormat.format(endDate),
            );
            await spOffer.setString("GOLD_OFFER_ALERT_DATE", newDate);

            // schedule notification at newDate
            try {
              final notifTime = DateFormat(
                "dd-MM-yyyy HH:mm:ss",
              ).parse("${sdfDate.format(nextOfferDate)} 07:00:00");
              await showLocalNotification(
                "BIG Discount on Quick Tatkal GOLD Pack 🪙😍",
                "Offer valid for today only 🕒\nGet GOLD Pack for lifetime at a special reduced price. Check out now 👉",
              );
              await scheduleNotification({
                "title": "BIG Discount on Quick Tatkal GOLD Pack 🪙😍",
                "message":
                    "Offer valid for today only 🕒\nGet GOLD Pack for lifetime at a special reduced price. Check out now 👉",
              }, notifTime.millisecondsSinceEpoch);
            } catch (e) {}
          }
          return true;
        } else if (endDt != null && now.isAfter(endDt)) {
          await spOffer.setString("GOLD_OFFER_END_DATE", "");
        }
      } catch (e) {
        // error
      }
    }

    // Regular price logic
    if (regularDate.isEmpty) {
      final c = now.add(Duration(hours: 12 + 24));
      final sdfDate = DateFormat("dd-MM-yyyy");
      final date = "${sdfDate.format(c)} 00:00:00";
      await spRegular.setString("GOLD_REGULAR_ALERT_DATE", date);
    } else {
      try {
        final date = dateFormat.parse(regularDate);
        if (now.isAfter(date)) {
          if (APIConsts.PRICE_CP_STR.startsWith("₹")) {
            await showGoldPackAd();
          }
          final nextRegularDate = now.add(Duration(hours: 36 + 24));
          final sdfDate = DateFormat("dd-MM-yyyy");
          final newDate = "${sdfDate.format(nextRegularDate)} 00:00:00";
          await spRegular.setString("GOLD_REGULAR_ALERT_DATE", newDate);
          return true;
        }
      } catch (e) {
        // error
      }
    }
    return false;
  }

  Future<void> showGoldPackAd() async {
    // Java parity: showGoldPackAd
    if (!APIConsts.PRICE_CP_STR.startsWith("₹")) return;
    String suffix = Consts.payWithPG ? "_pg" : "";
    if (!removeAdsClicked) {
      await Future.delayed(const Duration(milliseconds: 1000));
      try {
        final dir = await getApplicationDocumentsDirectory();
        final filename = isGoldOfferRunning
            ? 'offer$suffix.png'
            : 'regular$suffix.png';
        final file = File('${dir.path}/$filename');
        if (await file.exists()) {
          setState(() {
            _goldOfferVisible = true;
            _isGoldLayoutVisible = true;
          });
          setupGoldOfferLayout();
        }
      } catch (e) {}
    }
  }

  void setupGoldOfferLayout() {
    // Variable parity
    final suffix = Consts.payWithPG ? "_pg" : "";
    final filename = isGoldOfferRunning
        ? 'offer$suffix.png'
        : 'regular$suffix.png';

    // Track
    if (isGoldOfferRunning) {
      MixpanelManager().track("Show GOLD Pack Offer");
    } else {
      MixpanelManager().track("Show GOLD Pack Regular");
    }

    setState(() {
      _goldPackImagePath = filename;
      if (Consts.payWithPG) {
        _goldTerms = "";
      } else {
        _goldTerms =
            "You can manage your subscription or cancel anytime in the Google Play app. "
            "Specific Terms apply";
      }
      // Image loading and click handled via widget build logic.
      // No explicit layout management needed in Flutter.
    });
    // Android-specific: view setup and listeners are in widget build in Flutter.
  }

  Future<void> startPaymentFromAd() async {
    APIConsts.goldPackAlreadyPurchased = false;
    tickets = 9999;
    Consts.PACK_NAME = "GOLD Pack (1 Year)";
    if (isGoldOfferRunning) {
      amount = APIConsts.PRICE_DP_YEARLY_OFFER;
      SKU = "gold_offer";
    } else {
      amount = APIConsts.PRICE_DP_YEARLY;
      SKU = "gold_yearly";
    }
    MixpanelManager().track("Start Payment From Ad", {
      "SKU": SKU,
      "Pack Name": Consts.PACK_NAME,
      "Is Offer": isGoldOfferRunning,
      "Source": goldPackAdSource,
    });
    setState(() {
      _goldOfferVisible = false;
      _isGoldLayoutVisible = false;
    });

    if (Consts.payWithPG) {
      // PG flow: Show a progress dialog and call generatePaytmTxn with PaymentManager's static method
      bool dialogShown = false;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          dialogShown = true;
          return AlertDialog(
            title: const Text('Please wait'),
            content: const Text('Loading Payment Gateway'),
          );
        },
      );
      await Future.delayed(
        const Duration(milliseconds: 500),
      ); // Simulate dialog show timing
      try {
        await PaymentManager.generatePaytmTxn(amount.toString());
      } finally {
        if (dialogShown && Navigator.of(context).canPop())
          Navigator.of(context).pop();
      }
    } else {
      if (isGoldOfferRunning) {
        makeSubscription("gold_offer");
      } else {
        makeSubscription("gold_yearly");
      }
    }
  }

  Future<void> makeSubscription(String skuPack) async {
    final InAppPurchase iap = InAppPurchase.instance;
    final bool available = await iap.isAvailable();
    if (!available) {
      showSnackBar(context, "Subscription not available");
      return;
    }

    final ProductDetailsResponse response = await iap.queryProductDetails({
      skuPack,
    });
    if (response.notFoundIDs.isNotEmpty || response.productDetails.isEmpty) {
      showSnackBar(context, "Subscription not found");
      return;
    }

    final ProductDetails productDetails = response.productDetails.first;
    // Parity: set offer token if needed for special pricing
    PurchaseParam purchaseParam;
    // Dart's in_app_purchase does not directly use offer tokens in basic mode; this logic should be adapted if offer tokens are needed.
    purchaseParam = PurchaseParam(productDetails: productDetails);

    // Track analytics (Java: purchase flow)
    MixpanelManager().track("Launch purchase flow", {
      "Pack name": Consts.PACK_NAME,
      "Source": Consts.paymentSource,
    });

    try {
      await iap.buyNonConsumable(purchaseParam: purchaseParam);
      await FirebaseAnalytics.instance.logEvent(name: "qt_subs_flow");
    } catch (e) {
      print('Subscription purchase error: $e');
      showSnackBar(context, "Error launching subscription flow: $e");
    }
  }

  Future<void> makePurchase(String skuPack) async {
    final InAppPurchase iap = InAppPurchase.instance;
    final bool available = await iap.isAvailable();
    if (!available) {
      showSnackBar(context, "Product not available");
      return;
    }
    final ProductDetailsResponse response = await iap.queryProductDetails({
      skuPack,
    });
    if (response.notFoundIDs.isNotEmpty || response.productDetails.isEmpty) {
      showSnackBar(context, "Product not found");
      return;
    }
    final ProductDetails productDetails = response.productDetails.first;
    // Parity: offer token can go here if needed
    PurchaseParam purchaseParam = PurchaseParam(productDetails: productDetails);
    MixpanelManager().track("Launch purchase flow", {
      "Pack name": Consts.PACK_NAME,
      "Source": Consts.paymentSource,
    });
    try {
      await iap.buyNonConsumable(purchaseParam: purchaseParam);
      await FirebaseAnalytics.instance.logEvent(name: "qt_purchase_flow");
    } catch (e) {
      print('INAPP purchase error: $e');
      showSnackBar(context, "Error launching purchase flow: $e");
    }
  }

  // Accurate Java -> Dart port for startPayment. 100% parity with Java Checkout/razorpay logic and variables
  Future<void> startPayment(String orderId) async {
    // production flag, match Java
    bool production = true;
    String keyID = production
        ? "rzp_live_zh7pz4UZgGxkhm"
        : "rzp_test_XVABV3cDhngZE4";
    // Simulate logo/etc (not used directly in Flutter Razorpay SDK, but keep variable)
    String logoImage = 'assets/images/logo_4.png';
    // Build options map
    Map<String, dynamic> options = {
      'name': 'Quick Tatkal',
      'order_id': orderId,
      'theme.color': '#9C5DF7',
      'description': Consts.PACK_NAME,
      'currency': 'INR',
      'amount': amount * 100,
      'retry': {'enabled': true, 'max_count': 4},
      'key': keyID,
      // 'image': logoImage, // Pass if supported by SDK
    };
    if (SplashScreenState.EMAIL != 'NA') {
      options['prefill'] = {'email': SplashScreenState.EMAIL};
    }
    if (SplashScreenState.MOBILE_NO.startsWith('91') &&
        SplashScreenState.MOBILE_NO.length == 12) {
      options['prefill'] = options['prefill'] ?? {};
      (options['prefill'] as Map)['contact'] = SplashScreenState.MOBILE_NO
          .substring(2);
    }
    try {
      // In a real Flutter project, use razorpay_flutter package, e.g.:
      // Razorpay().open(options);
      print('[Razorpay] Start payment with: $options');
      // You'd wire SDK callbacks to onPaymentSuccess/error
      await Future.delayed(
        const Duration(seconds: 2),
      ); // Simulate payment delay
      onPaymentSuccess('RAZORPAY');
    } catch (e) {
      try {
        MixpanelManager().track("Switch to Google payment", {
          "Mode": "Razorpay",
          "Error": "startPayment: $e",
        });
      } catch (_) {}
      if (tickets == 1) {
        await makePurchase(SKU);
      } else {
        makeSubscription(SKU);
      }
    }
    // In Java: loadOrder.dismiss(); // here, assume loading indicator is handled with loadingLayoutVisible
    setState(() {
      loadingLayoutVisible = false;
    });
  }

  Future<void> scheduleNotification(
    Map<String, String> notification,
    int time,
  ) async {
    final delayMs = time - DateTime.now().millisecondsSinceEpoch;
    if (delayMs <= 0) {
      await showLocalNotification(
        notification['title'] ?? '',
        notification['message'] ?? '',
      );
      return;
    }
    Future.delayed(Duration(milliseconds: delayMs), () async {
      await showLocalNotification(
        notification['title'] ?? '',
        notification['message'] ?? '',
      );
    });
    print(
      'Scheduled notification (${notification['title']}) for $time (delay: $delayMs ms)',
    );
  }

  Future<void> showLocalNotification(String title, String message) async {
    const String NOTIFICATION_CHANNEL_ID = "10003";
    const String CHANNEL_NAME = "GOLD Pack Offer";
    // Setup flutter_local_notifications
    final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    final androidDetails = AndroidNotificationDetails(
      NOTIFICATION_CHANNEL_ID,
      CHANNEL_NAME,
      importance: Importance.high,
      priority: Priority.high,
      ticker: 'ticker',
      enableVibration: true,
      vibrationPattern: Int64List.fromList([
        100,
        200,
        300,
        400,
        500,
        400,
        300,
        200,
        400,
      ]),
      enableLights: true,
      ledColor: const Color(0xFF00FFFF),
      // Cyan
      ledOnMs: 1000,
      ledOffMs: 500,
      styleInformation: BigTextStyleInformation(message),
      icon: '@drawable/qt_notif_3',
    );
    final notifDetails = NotificationDetails(android: androidDetails);
    await flutterLocalNotificationsPlugin.show(
      10003,
      title,
      message,
      notifDetails,
      payload: 'notification=true',
    );
  }

  void payWithPaytm(
    String orderIdString,
    String txnTokenString,
    String txnAmountString,
  ) {

    String midString = "IFRmTf05358847421355"; // Staging
    bool production = true;
    String host = "https://securegw-stage.paytm.in/";
    if (production) {
      host = "https://securegw.paytm.in/";
      midString = "MnOKdt18048848664763";
    }
    String callBackUrl = host + "theia/paytmCallback?ORDER_ID=" + orderIdString;
    print(
      "[Paytm] Initializing - orderId: $orderIdString, mid: $midString, token: $txnTokenString, amt: $txnAmountString, callback: $callBackUrl",
    );
    // Here you would invoke the real Paytm Flutter SDK if available
    // The following block simulates the main PaytmPaymentTransactionCallback events from Java
    // Replace all prints by actual SDK integration for true parity when SDK exists.
    void onTransactionResponse(bool success, String bundleOrError) {
      if (success) {
        setState(() {
          loadingLayoutVisible = true;
        });
        getPaytmTxnStatus();
      } else {
        MixpanelManager().track("Paytm error", {"Error": bundleOrError});
        showSnackBar(context, "Transaction cancelled");
        // TODO: log pymt error to server
      }
    }

    void networkNotAvailable() {
      showSnackBar(context, "Network Unavailable");
      MixpanelManager().track("Paytm error", {"Error": "No network"});
      // TODO: log pymt error to server
    }

    void onErrorProceed(String s) {
      MixpanelManager().track("Paytm error", {"Error": s});
      showSnackBar(context, "Payment failed. Please try again");
      // TODO: log pymt error to server
    }

    void clientAuthenticationFailed(String s) {
      MixpanelManager().track("Paytm error", {"Error": s});
      showSnackBar(context, "Authentication failed");
      // TODO: log pymt error to server
    }

    void someUIErrorOccurred(String s) {
      MixpanelManager().track("Paytm error", {"Error": s});
      showSnackBar(context, "Error loading payment gateway. Please try again");
      // TODO: log pymt error to server
    }

    void onErrorLoadingWebPage(int i, String s, String s1) {
      MixpanelManager().track("Paytm error", {"Error": "$i: $s : $s1"});
      showSnackBar(
        context,
        "Error loading webpage. Recommended to install Paytm app to make the payment",
      );
      // TODO: log pymt error to server
    }

    void onBackPressedCancelTransaction() {
      MixpanelManager().track("Paytm payment cancelled");
      showSnackBar(context, "Payment cancelled");
      // TODO: log pymt error to server
    }

    void onTransactionCancel(String s) {
      MixpanelManager().track("Paytm error", {
        "Error": "Paytm transaction cancelled: $s",
      });
      showSnackBar(context, "Payment cancelled");
      // TODO: log pymt error to server
    }

    print(
      "Simulate Paytm payment flow: orderId=$orderIdString, token=$txnTokenString, amt=$txnAmountString",
    );

  }

  TextSpan makeLinkClickable(String text, String url, {TextStyle? style}) {
    return TextSpan(
      text: text,
      style:
          style ??
          const TextStyle(
            decoration: TextDecoration.underline,
            color: Colors.blue,
          ),
      recognizer: TapGestureRecognizer()
        ..onTap = () async {
          if (await canLaunch(url)) {
            await launch(url);
          } else {
            Get.snackbar(
              'Error',
              'Could not launch $url',
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
          }
        },
    );
  }

  Widget setTextViewHTML(
    BuildContext context,
    String html, {
    TextStyle? defaultStyle,
  }) {
    // This is a minimal implementation for Java parity. For full HTML rendering, use flutter_html package.
    // This version only supports single anchor tag, for demonstration. For full parity, expand the logic or use package.
    final RegExp linkRegex = RegExp(
      r'<a\s+href=["\"]([^"\"]*)["\"][^>]*>(.*?)<\/a>',
      caseSensitive: false,
    );
    final List<TextSpan> spans = [];
    int current = 0;
    Iterable<RegExpMatch> matches = linkRegex.allMatches(html);
    for (final match in matches) {
      if (match.start > current) {
        spans.add(
          TextSpan(
            text: html.substring(current, match.start),
            style: defaultStyle,
          ),
        );
      }
      final url = match.group(1)!;
      final text = match.group(2)!;
      spans.add(makeLinkClickable(text, url, style: defaultStyle));
      current = match.end;
    }
    if (current < html.length) {
      spans.add(TextSpan(text: html.substring(current), style: defaultStyle));
    }

    return RichText(
      text: TextSpan(
        style: defaultStyle ?? const TextStyle(color: Colors.white),
        children: spans,
      ),
    );
  }

  Future<void> showRCInfo() async {
    try {
      // Getting package info for another app is not supported in pure Dart/Flutter,
      // but we can get our own app's info for demonstration
      final packageInfo = await PackageInfo.fromPlatform();
      // You can replace below by querying a third-party intent/package via platform channel if needed
      // For demo: emulate MainActivity.THIRD_PARTY_APP_PACKAGE
      final String packageName = packageInfo.packageName;
      await FirebaseAnalytics.instance.logEvent(name: "rc_installed");

      // Emulate Android version logic
      int verCode = 0;
      try {
        // On iOS, use buildNumber; on Android API >= 28, use longVersionCode
        verCode = int.tryParse(packageInfo.buildNumber) ?? 0;
      } catch (_) {}
      Consts.RC_VER_CODE = verCode;
      // For demo, to match Java code (force)
      Consts.RC_VER_CODE = 200;
    } catch (e) {
      await FirebaseAnalytics.instance.logEvent(name: "rc_unavailable");
    }
    // Further UI/flag logic can be applied here, e.g. rcUpdateInfo visibility
  }

  Future<void> getOrderId(String amount) async {
    print("Start getOrderId for amount: $amount");
    // Simulate ProgressDialog: In Flutter showDialog, but for parity we just track analytics and state
    MixpanelManager().track("Launch razorpay", {
      "Amount": amount,
      "Pack name": Consts.PACK_NAME,
    });
    try {
      final client = HttpClient();
      final uri = Uri.parse(
        'https://www.afrestudios.com/quick-tatkal/razorpay/pay_3.php',
      );
      final request = await client.postUrl(uri);
      request.headers.contentType = ContentType(
        'application',
        'x-www-form-urlencoded',
      );
      request.write('amount=$amount');
      final response = await request.close();
      String body = await response.transform(utf8.decoder).join();
      client.close();
      if (!body.startsWith("ERROR")) {
        MixpanelManager().track("Razorpay order created", {
          "Amount": amount,
          "Pack name": Consts.PACK_NAME,
        });
        await startPayment(body);
      } else {
        MixpanelManager().track("Switch to Google payment", {
          "Mode": "Razorpay",
          "Error": "getOrderId: $body",
        });
        if (tickets == 1) {
          await makePurchase(SKU);
        } else {
          makeSubscription(SKU);
        }
      }
    } catch (e) {
      print("getOrderId error: $e");
      MixpanelManager().track("Switch to Google payment", {
        "Mode": "Razorpay",
        "Error": "getOrderId: $e",
      });
      if (tickets == 1) {
        await makePurchase(SKU);
      } else {
        makeSubscription(SKU);
      }
    }
  }

  Future<void> generatePaytmTxn(String amount) async {
    print("Start generatePaytmTxn for amount: $amount");
    MixpanelManager().track("Launch paytm", {
      "Amount": amount,
      "Pack name": Consts.PACK_NAME,
    });
    try {
      final client = HttpClient();
      String userid = SplashScreenState.TID != "0"
          ? SplashScreenState.TID
          : "12345";
      final uri = Uri.parse(
        'https://www.afrestudios.com/quick-tatkal/paytm/init_txn.php',
      );
      final request = await client.postUrl(uri);
      request.headers.contentType = ContentType(
        'application',
        'x-www-form-urlencoded',
      );
      request.write('amount=$amount&userid=$userid&tickets=$tickets');
      final response = await request.close();
      String body = await response.transform(const Utf8Decoder()).join();
      client.close();
      if (body.startsWith("ERROR")) {
        MixpanelManager().track("Switch to Google payment", {
          "Mode": "Paytm",
          "Error": "generatePaytmTxn: $body",
        });
        if (tickets == 1) {
          await makePurchase(SKU);
        } else {
          makeSubscription(SKU);
        }
      } else {
        if (body.contains("|")) {
          List<String> parts = body.split("|");
          paytmOrderId = parts[1];
          orderId = paytmOrderId;
          MixpanelManager().track("Paytm order created", {
            "Amount": amount,
            "Pack name": Consts.PACK_NAME,
          });
          // Accurate Java -> Dart port for payWithPaytm. 100% parity with Java logic and variable names
          payWithPaytm(parts[1], parts[0], amount);
        } else {
          MixpanelManager().track("Switch to Google payment", {
            "Mode": "Paytm",
            "Error": "generatePaytmTxn: $body",
          });
          if (tickets == 1) {
            await makePurchase(SKU);
          } else {
            makeSubscription(SKU);
          }
        }
      }
    } catch (e) {
      print("generatePaytmTxn error: $e");
      MixpanelManager().track("Switch to Google payment", {
        "Mode": "Paytm",
        "Error": "generatePaytmTxn: $e",
      });
      if (tickets == 1) {
        await makePurchase(SKU);
      } else {
        makeSubscription(SKU);
      }
    }
  }

  Future<void> getPaytmTxnStatus() async {
    try {
      setState(() {
        loadingLayoutVisible = true;
      });

      // HTTP POST using dart:io HttpClient. Safer for custom headers than http package for this Java parity port.
      final client = HttpClient();
      final uri = Uri.parse(
        'https://www.afrestudios.com/quick-tatkal/paytm/txn_status_2.php',
      );
      final request = await client.postUrl(uri);
      request.headers.contentType = ContentType(
        'application',
        'x-www-form-urlencoded',
      );
      request.write('orderid=$paytmOrderId');
      final response = await request.close();

      String output = await response.transform(utf8.decoder).join();
      client.close();

      bool pendingFlag = false;

      setState(() {
        loadingLayoutVisible = false;
      });

      // Only one payment status check at a time (simulate Java flag)
      // We'll use a static field in QuickTatkalScreenState.
      if (!(QuickTatkalScreenState as dynamic).paytmTxnCompleteFlag) {
        (QuickTatkalScreenState as dynamic).paytmTxnCompleteFlag = true;

        if (output.startsWith("ERROR")) {
          MixpanelManager().track("Paytm server error", {"Output": output});
          // TODO: Log pymt error to server if needed, like:
          // new ServerTask(Dashboard.this).logPymtError("PAYTM_TXN_STATUS", output.substring(6));
          showSnackBar(
            context,
            "Payment pending. Please check back after sometime",
          );
          pendingFlag = true;
        } else {
          if (output.contains("|")) {
            List<String> parts = output.split("|");
            if (parts[0] == "1") {
              // Success
              onPaymentSuccess("PAYTM");
            } else if (parts[0] == "2") {
              MixpanelManager().track("Paytm payment pending");
              showSnackBar(
                context,
                "Payment pending. Please check back after sometime",
              );
              pendingFlag = true;
            } else {
              MixpanelManager().track("Paytm server error", {"Output": output});
              // Handle payment restore err path (simulate)
              if (Consts.isPaymentRestoring) {
                MixpanelManager().track("Paytm restore failed");
                showSnackBar(context, "Last payment status: FAILED");
                // TODO: Call FirestoreFunctions(context).deletePendingPayment();
              } else {
                showSnackBar(context, "Payment Failed");
              }
              // TODO: Log pymt error to server if needed, like:
              // new ServerTask(Dashboard.this).logPymtError("PAYTM_PYMT_FAILED", parts[1]);
            }
          } else {
            MixpanelManager().track("Paytm server error", {"Output": output});
            showSnackBar(
              context,
              "Payment pending. Please check back after sometime",
            );
            pendingFlag = true;
            // Log Paytm error to analytics
            await FirebaseAnalytics.instance.logEvent(
              name: "paytm_error",
              parameters: {"outputStr": output},
            );
          }
        }
      }

      // Manage pending payment restore flag/state
      if (pendingFlag) {
        final sp = await SharedPreferences.getInstance();
        await sp.setBool("restore", true);
        if (!Consts.isPaymentRestoring) {
          // TODO: insert pending pymt (FirestoreFunctions(context).insertPendingPymt)
        }
      }
    } catch (e) {
      setState(() {
        loadingLayoutVisible = false;
      });
      showSnackBar(context, "Payment status check failed: $e");
    }
  }

  Future<void> getPendingPymtStatus(int t, String orderid) async {
    tickets = t;
    paytmOrderId = orderid;
    orderId = paytmOrderId;
    await getPaytmTxnStatus();
  }

  void showSnackBar(BuildContext context, String text) {
    final snackBar = SnackBar(
      content: Text(text, maxLines: 7, style: const TextStyle(fontSize: 14)),
      duration: const Duration(days: 1), // equivalent to LENGTH_INDEFINITE
      action: SnackBarAction(
        label: 'OK',
        textColor: Colors.yellow,
        onPressed: () {
          // Do nothing or handle dismiss
        },
      ),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  /*public void startPayment(String orderId) {
        Checkout checkout = new Checkout();

        boolean production = true;
        if(production) {
            checkout.setKeyID("rzp_live_zh7pz4UZgGxkhm");
        } else {
            checkout.setKeyID("rzp_test_XVABV3cDhngZE4");
        }
        checkout.setImage(R.mipmap.logo_4);
        checkout.setFullScreenDisable(true);

        final Activity activity = this;
        try {
            JSONObject options = new JSONObject();
            options.put("name", "Quick Tatkal");
            options.put("order_id", orderId);
            options.put("theme.color", "#9C5DF7");

            if(!SplashActivity.EMAIL.equals("NA")) {
                options.put("prefill.email", SplashActivity.EMAIL);
            }

            if(SplashActivity.MOBILE_NO.startsWith("91") && SplashActivity.MOBILE_NO.length() == 12) {
                options.put("prefill.contact",SplashActivity.MOBILE_NO.substring(2));
            }

            options.put("description", Consts.PACK_NAME);
            options.put("currency", "INR");
            options.put("amount", amount*100);

            JSONObject retryObj = new JSONObject();
            retryObj.put("enabled", true);
            retryObj.put("max_count", 4);
            options.put("retry", retryObj);

            checkout.open(activity, options);
        } catch(Exception e) {
            /*Toast.makeText(activity, "Couldn't start payment. Payment gateway error", Toast.LENGTH_SHORT).show();
            new ServerTask(Dashboard.this).logPymtError("RZP_START_PYMT", e.getMessage());*/
            JSONObject props = new JSONObject();
            try {
                props.put("Mode", "Razorpay");
                props.put("Error", "startPayment: " + e.getMessage());
                MixpanelManager.track("Switch to Google payment", props);
            } catch (JSONException e1) {

            }
            if(tickets == 1) {
                makePurchase(SKU);
            } else {
                makeSubscription(SKU);
            }
        }
        //loadingLayout.setVisibility(View.GONE);
        loadOrder.dismiss();
    }*/

  void onPaymentSuccess(String paymentMedium) {
    // TODO: Fill with appropriate entitlement/booking logic as in your real success path
    // Can show a snackbar, update state, call server functions, etc.
    Get.snackbar(
      "Success",
      "Your purchase was successful! (Medium: $paymentMedium)",
      backgroundColor: Colors.green,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
    );
    paymentComplete();
  }

  void paymentComplete() async {
    if (!Consts.TICKET_RESTORE) {
      // Show a snackbar for purchase complete (parity with Android Snackbar)
      Get.snackbar(
        'Congratulations!',
        'You just bought ${Consts.PACK_NAME}',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 5),
        mainButton: TextButton(
          onPressed: () {},
          child: const Text('COOL', style: TextStyle(color: Colors.yellow)),
        ),
      );
      // Clear purchase state and loading like in Java
      Consts.PACK_NAME = "";
      // Consts.ONGOING_PYMT_MEDIUM = "";
      setState(() {
        loadingLayoutVisible = false;
      });
      await FirebaseAnalytics.instance.logEvent(name: 'payment_complete');
      if (Consts.PREMIUM_ACTIVITY_SHOWN) {
        await FirebaseAnalytics.instance.logEvent(
          name: 'payment_complete_first',
        );
      }
    } else {
      Consts.TICKET_RESTORE = false;
    }
  }

  void paymentError(String errBody) async {
    if (!Consts.TICKET_RESTORE) {
      Get.snackbar(
        'Error',
        'Error completing purchase. Please contact support!\n$errBody',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 8),
        mainButton: TextButton(
          onPressed: () async {
            final Uri emailLaunchUri = Uri(
              scheme: 'mailto',
              path: '<EMAIL>',
              queryParameters: <String, String>{
                'subject': 'Quick Tatkal [Payment Error]',
                'body': errBody,
              },
            );
            if (await canLaunch(emailLaunchUri.toString())) {
              await launch(emailLaunchUri.toString());
            } else {
              Get.snackbar(
                'Error',
                'No email app installed!',
                snackPosition: SnackPosition.BOTTOM,
              );
            }
          },
          child: const Text('Contact', style: TextStyle(color: Colors.yellow)),
        ),
      );
      Consts.PACK_NAME = "";
      Consts.ONGOING_PYMT_MEDIUM = "";
      setState(() {
        loadingLayoutVisible = false;
      });
      // Track to Mixpanel
      final props = <String, dynamic>{
        'TID': SplashScreenState.TID,
        'Email': SplashScreenState.EMAIL,
        'Mobile No': SplashScreenState.MOBILE_NO,
        'Tickets': SplashScreenState.ticketsLeft,
        'User type': Consts.USER_TYPE,
        'Device ID': Consts.DEVICE_ID,
      };
      MixpanelManager().track('Payment save failed', props);
    } else {
      Consts.TICKET_RESTORE = false;
    }
  }

  void handlePurchase(PurchaseDetails purchase) async {
    // Only handle successful purchases
    if (purchase.status == PurchaseStatus.purchased) {
      orderId = purchase.purchaseID ?? "";
      // In Flutter, you'd typically use shared_preferences; here, emulate the counter
      final prefs = await SharedPreferences.getInstance();
      // Use productID as key, as in Java
      await prefs.setInt("PURCHASE_COUNT_${purchase.productID}", 1);
      // Grant entitlement -- in real code, call your success logic
      onPaymentSuccess("GOOGLE");

      // Fire analytics for upgrades/renewals/diamond as in Java
      if (Consts.UPGRADE_CLICKED) {
        await FirebaseAnalytics.instance.logEvent(name: "upgrade_after_done");
      } else if (Consts.UPGRADE_BEFORE_CLICKED) {
        await FirebaseAnalytics.instance.logEvent(name: "upgrade_before_done");
      }
      if (!QuickTatkalScreenState.removeAdsClicked) {
        if (SplashScreenState.GOLD_NOTIFICATION) {
          await FirebaseAnalytics.instance.logEvent(name: "g_n_buy");
        } else if (SplashScreenState.isGoldUser == 2) {
          if (QuickTatkalScreenState.isGoldOfferRunning) {
            await FirebaseAnalytics.instance.logEvent(name: "g_o_buy");
          } else {
            await FirebaseAnalytics.instance.logEvent(name: "g_r_buy");
          }
        }
      } else if (SplashScreenState.isGoldUser == 2) {
        if (QuickTatkalScreenState.isGoldOfferRunning) {
          await FirebaseAnalytics.instance.logEvent(name: "g_ao_buy");
        } else {
          await FirebaseAnalytics.instance.logEvent(name: "g_ar_buy");
        }
      }

      // Acknowledge purchase (Android only)
      // In Flutter's in_app_purchase, call completePurchase if needed.
      if (purchase.pendingCompletePurchase) {
        await InAppPurchase.instance.completePurchase(purchase);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (_isGoldLayoutVisible) {
          setState(() {
            _isGoldLayoutVisible = false;
            _goldOfferVisible = false;
          });
          return false;
        }

        Get.offAllNamed(AppRoutes.exitScreen);
        return false;
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: Stack(
          children: [
            Column(
              children: [
                Container(
                  padding: const EdgeInsets.only(top: 50, bottom: 20),
                  child: Center(
                    child: RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: 'Quick',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppColors.secondaryColor,
                              fontSize: 24,
                            ),
                          ),
                          TextSpan(
                            text: ' Tatkal',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontSize: 24,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: CustomScrollView(
                    slivers: [
                      SliverPadding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        sliver: SliverGrid.count(
                          crossAxisCount: 2,
                          mainAxisSpacing: 10,
                          crossAxisSpacing: 10,
                          childAspectRatio: 2.1,
                          children: [
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  _isReversed = !_isReversed;
                                });
                                _showInterstitialAd(() {
                                  Future.delayed(
                                    const Duration(milliseconds: 100),
                                    () {
                                      // Navigate to FormActivity2 (form list) - exact Java Dashboard.java flow
                                      Get.toNamed(AppRoutes.formActivity2);
                                    },
                                  );
                                });
                              },
                              child: AnimatedContainer(
                                duration: Duration(milliseconds: 100),
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Color(0xFFA34BFF),
                                      Color(0xBA224FF9),
                                    ],
                                    begin: _isReversed
                                        ? Alignment.bottomCenter
                                        : Alignment.topCenter,
                                    end: _isReversed
                                        ? Alignment.topCenter
                                        : Alignment.bottomCenter,
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: _cardContent(
                                  'Quick\nBooking',
                                  'assets/images/train.png',
                                  Colors.white,
                                  largeImage: true,
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                _showInterstitialAd(() {
                                  Get.toNamed(
                                    '/booking',
                                    arguments: {
                                      'url': 'https://www.irctc.co.in',
                                    },
                                  );
                                });
                              },
                              child: buildGradientBorderCard(
                                'Manual\nBooking',
                                'assets/images/train.png',
                                largeImage: true,
                                verticalPadding: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SliverPadding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 8,
                        ),
                        sliver: SliverGrid.count(
                          crossAxisCount: 2,
                          mainAxisSpacing: 10,
                          crossAxisSpacing: 10,
                          childAspectRatio: 2.5,
                          children: [
                            GestureDetector(
                              onTap: () {
                                _showInterstitialAd(() {
                                  Get.to(
                                    () => LegalScreen(
                                      type: "Order Food",
                                      url: "https://www.ecatering.irctc.co.in/",
                                    ),
                                  );
                                });
                              },
                              child: buildGradientBorderCard(
                                'Order Food\nin Train',
                                'assets/images/food.png',
                                verticalPadding: 4,
                              ),
                            ),
                            buildGradientBorderCard(
                              'Seat\nAvailability',
                              'assets/images/seat_avl.png',
                              verticalPadding: 4,
                            ),
                            buildGradientBorderCardWithSubtitle(
                              'Running Status',
                              'Where is my train?',
                              'assets/images/running_status_2.png',
                              verticalPadding: 4,
                            ),
                            GestureDetector(
                              onTap: () {
                                _showInterstitialAd(() {
                                  Get.to(() => PNRScreen());
                                });
                              },
                              child: buildGradientBorderCardWithSubtitle(
                                'PNR Status',
                                "What's my trip status",
                                'assets/images/ticket_white.png',
                                verticalPadding: 4,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12.0,
                            vertical: 8.0,
                          ),
                          child: GestureDetector(
                            onTap: () {
                              Get.to(() => VideoList());
                            },
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Image.asset('assets/images/videos.png'),
                            ),
                          ),
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: Container(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 15,
                            vertical: 20,
                          ),
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            gradient: const LinearGradient(
                              colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                          ),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.black,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                children: [
                                  const Text(
                                    'My Account',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 19,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceAround,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          _showInterstitialAd(() {
                                            Get.to(
                                              () => const FreeTicketsScreen(),
                                            );
                                          });
                                        },
                                        child: _accountItem(
                                          'Free Tickets',
                                          'assets/images/ticket_new.png',
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          MixpanelManager().track(
                                            "Click Buy Tickets",
                                          );
                                          Consts.subPaymentSource = "Dashboard";
                                          _showInterstitialAd(() {
                                            showDialog(
                                              context: context,
                                              barrierDismissible: true,
                                              builder:
                                                  (BuildContext dialogContext) {
                                                    return const TicketInfoDialog(
                                                      type: 0,
                                                    );
                                                  },
                                            );
                                          });
                                        },
                                        child: _accountItem(
                                          'Buy Tickets',
                                          'assets/images/buy.png',
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            _isReversed = !_isReversed;
                                          });
                                          _showInterstitialAd(() {
                                            Future.delayed(
                                              const Duration(milliseconds: 100),
                                              () {
                                                Get.toNamed(AppRoutes.profile);
                                              },
                                            );
                                          });
                                        },
                                        child: _accountItem(
                                          'Profile',
                                          'assets/images/profile_outline.png',
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          _showInterstitialAd(() {
                                            Get.to(
                                              () => const FAQSupportScreen(),
                                            );
                                          });
                                        },
                                        child: _accountItem(
                                          'Help & Support',
                                          'assets/images/support.png',
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: Container(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 15,
                            vertical: 20,
                          ),
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            gradient: const LinearGradient(
                              colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                          ),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.black,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                children: [
                                  const Text(
                                    'Follow Us',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 9),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceAround,
                                    children: [
                                      GestureDetector(
                                        onTap: () async {
                                          final uri = Uri.parse(
                                            "https://www.instagram.com/quick.tatkal",
                                          );
                                          if (await flutter_launcher
                                              .canLaunchUrl(uri)) {
                                            await flutter_launcher.launchUrl(
                                              uri,
                                              mode: flutter_launcher
                                                  .LaunchMode
                                                  .externalApplication,
                                            );
                                          } else {
                                            throw 'Could not launch $uri';
                                          }
                                        },
                                        child: Image.asset(
                                          'assets/images/insta1.png',
                                          width: 40,
                                          height: 40,
                                          fit: BoxFit.contain,
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () async {
                                          final uri = Uri.parse(
                                            "https://www.facebook.com/quicktatkal",
                                          );
                                          if (await flutter_launcher
                                              .canLaunchUrl(uri)) {
                                            await flutter_launcher.launchUrl(
                                              uri,
                                              mode: flutter_launcher
                                                  .LaunchMode
                                                  .externalApplication,
                                            );
                                          } else {
                                            throw 'Could not launch $uri';
                                          }
                                        },
                                        child: Image.asset(
                                          'assets/images/fb.png',
                                          width: 40,
                                          height: 40,
                                          fit: BoxFit.contain,
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () async {
                                          final uri = Uri.parse(
                                            "https://www.youtube.com/@afrestudios",
                                          );
                                          if (await flutter_launcher
                                              .canLaunchUrl(uri)) {
                                            await flutter_launcher.launchUrl(
                                              uri,
                                              mode: flutter_launcher
                                                  .LaunchMode
                                                  .externalApplication,
                                            );
                                          } else {
                                            throw 'Could not launch $uri';
                                          }
                                        },
                                        child: Image.asset(
                                          'assets/images/youtube.png',
                                          width: 40,
                                          height: 40,
                                          fit: BoxFit.contain,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      SliverPadding(
                        padding: const EdgeInsets.symmetric(vertical: 20),
                        sliver: SliverToBoxAdapter(
                          child: GestureDetector(
                            onTap: () {
                              LanguageFragment.showLanguageDialog(context);
                            },
                            child: Container(
                              padding: const EdgeInsets.all(15),
                              margin: const EdgeInsets.symmetric(
                                horizontal: 60,
                              ),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(40),
                                border: Border.all(
                                  color: Colors.white,
                                  width: 1,
                                ),
                                color: Colors.transparent,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset(
                                    'assets/images/lang3.png',
                                    height: 30,
                                    width: 30,
                                  ),
                                  const SizedBox(width: 20),
                                  const Text(
                                    'Change Language',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 20),
                          child: Center(
                            child:
                                (Consts.USER_TYPE == "DIAMOND_USER" ||
                                    Consts.USER_TYPE == "GOLD_USER")
                                ? SizedBox.shrink()
                                : GestureDetector(
                                    onTap: () {
                                      MixpanelManager().track(
                                        "Click Remove Ads",
                                      );
                                      Consts.goldPackAdSource = "Remove Ads";
                                      goldPackAdSource = "Remove Ads";
                                      removeAdsClicked = true;
                                      setState(() {
                                        _goldOfferVisible = true;
                                        _isGoldLayoutVisible = true;
                                      });
                                      setupGoldOfferLayout();
                                      print(
                                        "🟢 Remove Ads clicked - Gold Pack popup showing",
                                      );
                                    },
                                    child: gradientRectangleGray(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 10,
                                          horizontal: 30,
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Image.asset(
                                              "assets/images/gold_offer.png",
                                              width: 28,
                                              height: 28,
                                              fit: BoxFit.contain,
                                            ),
                                            const SizedBox(width: 10),
                                            const Text(
                                              "Remove Ads",
                                              style: TextStyle(
                                                color: Colors.red,
                                                fontSize: 14,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                if (_bannerAd != null)
                  Container(
                    width: _bannerAd!.size.width.toDouble(),
                    height: _bannerAd!.size.height.toDouble(),
                    child: AdWidget(ad: _bannerAd!),
                  ),
              ],
            ),
            if (_goldOfferVisible)
              Positioned.fill(
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    setState(() {
                      _goldOfferVisible = false;
                      _isGoldLayoutVisible = false;
                    });
                  },
                  child: Container(
                    color: Colors.black54,
                    alignment: Alignment.center,
                    child: Material(
                      color: Colors.transparent,
                      child: Stack(
                        alignment: Alignment.topRight,
                        children: [
                          Center(
                            child: Container(
                              margin: EdgeInsets.symmetric(horizontal: 20),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(28),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  InkWell(
                                    borderRadius: BorderRadius.circular(20),
                                    onTap: () {
                                      if (isGoldOfferRunning) {
                                        MixpanelManager().track(
                                          "Click GOLD Pack Offer",
                                          {"Source": goldPackAdSource},
                                        );
                                      } else {
                                        MixpanelManager().track(
                                          "Click GOLD Pack Regular",
                                          {"Source": goldPackAdSource},
                                        );
                                      }
                                      setState(() {
                                        _goldOfferVisible = false;
                                        _isGoldLayoutVisible = false;
                                      });
                                      startPaymentFromAd();
                                    },
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(20),
                                      child: Image.asset(
                                        "assets/images/gold_offer.png",
                                        fit: BoxFit.contain,
                                        width: 350,
                                        height: 400,
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 12),
                                  if (_goldTerms.isNotEmpty)
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 20,
                                      ),
                                      child: GestureDetector(
                                        onTap: () async {
                                          final url =
                                              "https://www.afrestudios.com/index.php/quick-tatkal-specific-terms-for-paid-services/";
                                          if (await canLaunchUrl(
                                            Uri.parse(url),
                                          )) {
                                            await launchUrl(
                                              Uri.parse(url),
                                              mode: LaunchMode
                                                  .externalApplication,
                                            );
                                          }
                                        },
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 15,
                                            vertical: 8,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colors.black.withOpacity(
                                              0.7,
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              15,
                                            ),
                                          ),
                                          child: Text(
                                            _goldTerms,
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                              decoration:
                                                  TextDecoration.underline,
                                            ),
                                            textAlign: TextAlign.center,
                                            maxLines: 3,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                          Positioned(
                            top: 40,
                            right: 30,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.6),
                                shape: BoxShape.circle,
                              ),
                              child: IconButton(
                                icon: Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 28,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _goldOfferVisible = false;
                                    _isGoldLayoutVisible = false;
                                  });
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  Widget _cardContent(
    String text,
    String imagePath,
    Color textColor, {
    bool largeImage = false,
  }) {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            imagePath,
            width: largeImage ? 48 : 36,
            height: largeImage ? 36 : 28,
            color: textColor,
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              text,
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.bold,
                fontSize: 12,
                height: 1.1,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
            ),
          ),
        ],
      ),
    );
  }

  Widget buildGradientBorderCard(
    String text,
    String imagePath, {
    double verticalPadding = 10,
    bool largeImage = false,
  }) {
    return Container(
      padding: EdgeInsets.all(2),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(18),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 10,
          vertical: verticalPadding,
        ),
        child: _cardContent(
          text,
          imagePath,
          Colors.white,
          largeImage: largeImage,
        ),
      ),
    );
  }

  Widget buildGradientBorderCardWithSubtitle(
    String title,
    String subtitle,
    String imagePath, {
    double verticalPadding = 10,
  }) {
    return Container(
      padding: EdgeInsets.all(2),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(18),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 10,
          vertical: verticalPadding,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(imagePath, width: 32, height: 26, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      height: 1.1,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      color: Colors.white60,
                      fontWeight: FontWeight.normal,
                      fontSize: 10,
                      height: 1.0,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _accountItem(String title, String assetPath, {double imageSize = 40}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: imageSize + 20,
          height: imageSize + 20,
          padding: const EdgeInsets.all(10),
          child: Image.asset(
            assetPath,
            width: imageSize,
            height: imageSize,
            fit: BoxFit.contain,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          title,
          style: const TextStyle(color: Colors.white70, fontSize: 12),
        ),
      ],
    );
  }

  Widget gradientRectangleGray({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.all(2.0),
        child: Container(
          decoration: BoxDecoration(
            color: Color(0xFF1E1E1E),
            borderRadius: BorderRadius.circular(20),
          ),
          child: child,
        ),
      ),
    );
  }
}
