import 'dart:async';
import 'package:flutter/material.dart';
import 'package:quick_tatkal_v2/core/network/advanced_webview.dart';

class BookingScreen extends StatefulWidget {
  final String? url; // Comes as argument. In real usage, pass via Navigator.

  const BookingScreen({Key? key, required this.url}) : super(key: key);

  @override
  _BookingScreenState createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen> {
  final GlobalKey<AdvancedWebViewState> _webViewKey = GlobalKey();
  late final ValueNotifier<int> _progress = ValueNotifier<int>(0);

  @override
  void dispose() {
    _progress.dispose();
    super.dispose();
  }

  // Replicates showing a native Android Toast
  void _showToast(String msg) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(msg)));
  }

  // Replicates the confirmation dialog onBack
  Future<bool> _onWillPop() async {
    if (await _webViewKey.currentState?.canGoBack() ?? false) {
      await _webViewKey.currentState?.goBack();
      return false;
    }
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Exit Booking"),
        content: const Text(
          "Are you sure you want to cancel and exit the booking?",
        ),
        actions: [
          TextButton(
            child: const Text("No"),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          TextButton(
            child: const Text("Exit"),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// The JS injected, as in the original file
  String get _adBlockerJs => """
    (function(){
      function safeRemove(el){ try{ if(el && el.parentNode){ el.parentNode.removeChild(el); } }catch(e){} }
      function rmSel(sel){ try{ (document.querySelectorAll(sel)||[]).forEach(safeRemove); }catch(e){} }
      try{
        var ifr = document.getElementsByTagName('iframe')||[];
        for(var i=0;i<ifr.length;i++){
          var f = ifr[i];
          try{
            var id = (f.id||'').toUpperCase();
            var html = (f.outerHTML||'');
            if(id.indexOf('GOOGLE')>=0 || html.indexOf('google_ads_iframe')>=0){
              var host = f && f.parentNode && f.parentNode.parentNode ? f.parentNode.parentNode : f;
              safeRemove(host);
            }
          }catch(e){}
        }
        rmSel('[id^="div-gpt-ad"]');
        var as = document.getElementsByTagName('a')||[];
        for(var j=0;j<as.length;j++){
          try{ if((as[j].href||'').indexOf('corover.ai')>=0){ safeRemove(as[j]); } }catch(e){}
        }
        var imgs = document.getElementsByTagName('img')||[];
        for(var k=0;k<imgs.length;k++){
          try{ if(((imgs[k].src)||'').toUpperCase().indexOf('DEAL')>=0){ safeRemove(imgs[k].parentNode||imgs[k]); } }catch(e){}
        }
        safeRemove(document.getElementById('askDishaSdk'));
        safeRemove(document.getElementById('cube'));
        safeRemove(document.getElementById('div-ub-irctc'));
      }catch(e){}
    })();
  """;

  @override
  Widget build(BuildContext context) {
    final url = widget.url;
    if (url == null || url.isEmpty) {
      // Equivalent to Toast and finish()
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showToast("Something went wrong. Please try again");
        Navigator.of(context).pop();
      });
      return const Scaffold();
    }

    // Set the primary color as per your Theme or a specific color code if you want to match the Android one
    final primaryColor = Theme.of(context).colorScheme.primary;

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: primaryColor, // mimic android:background
        appBar: AppBar(
          title: const Text("Train Booking"),
          backgroundColor: primaryColor,
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ValueListenableBuilder<int>(
              valueListenable: _progress,
              builder: (context, value, child) {
                return LinearProgressIndicator(
                  value: value == 100 ? 1.0 : value / 100,
                  backgroundColor: Colors.transparent,
                  minHeight: 3,
                  color: Theme.of(context).colorScheme.secondary,
                );
              },
            ),
            Expanded(
              child: AdvancedWebView(
                key: _webViewKey,
                url: url,
                title: "Train Booking",
                onProgress: (progress) {
                  _progress.value = (progress is int)
                      ? progress
                      : (progress is double ? (progress * 100).toInt() : 0);
                },
                onPageFinished: (url) async {
                  // Run immediately and schedule a retry to avoid early DOM access
                  try {
                    await _webViewKey.currentState?.runJavaScript(_adBlockerJs);
                  } catch (_) {}
                  Future.delayed(const Duration(milliseconds: 500), () async {
                    try {
                      await _webViewKey.currentState?.runJavaScript(
                          _adBlockerJs);
                    } catch (_) {}
                  });
                },
                onPageError: (errorCode, description, failingUrl) {
                  _showToast("Page load error: $description");
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
