import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:ui';
import 'dart:ui' as ui;
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:quick_tatkal_v2/core/AppConstants.dart';
import 'package:quick_tatkal_v2/core/Consts.dart';
import 'package:quick_tatkal_v2/screens/PNRScreen.dart';
import 'package:quick_tatkal_v2/screens/SignInSocialScreen.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';

import '../captcha/APIConsts.dart' as CaptchaAPIConsts;
import '../core/utils/APIConsts.dart' as CoreAPIConsts;
import '../core/helper/mixpanel_manager.dart';
import '../database/LoginDB.dart';
import '../database/MainDB.dart';
import '../routes/app_routes.dart';

import '../server/FirestoreFunctions.dart';
import 'ExitScreen.dart';
import 'FormActivity2.dart';
import 'PremiumScreen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => SplashScreenState();
}

class SplashScreenState extends State<SplashScreen> {
  FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn.instance;
  GoogleSignInAccount? _currentUser;
  bool _isGoogleSignInInitialized = false;

  static int ticketsLeft = 0;
  static int isGoldUser = 1;
  static String MOBILE_NO = "NA";
  static String CUST_NAME = "";
  static String PRIMARY_EMAIL = "NA";
  static Image? profilePic;
  static String tid = "0";
  static String INVITED_BY = "NA";
  static String EMAIL = "NA";
  static String TID = "0";
  static String? PROFILE_PIC;
  static bool SPLASH_RUNNING = false;

  static bool firstTime = true;
  static bool GOLD_NOTIFICATION = false;

  static String laterSource = "";
  static String loginSource = "";

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  bool _available = false;

  static const int UI_ANIMATION_DELAY = 300;
  Timer? _hideHandler;

  int downloadsComplete = 0;
  bool goldPackRestored = false;

  String? profilePicUrl;
  late int time;

  void logSplashOpenEvent() {
    analytics.logEvent(name: 'splash_open', parameters: {});
  }

  Future<String?> getAndroidDeviceId() async {
    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id;
    }
    return null;
  }

  void initDeviceId() async {
    String? deviceId = await getAndroidDeviceId();

    if (deviceId != null && deviceId.isNotEmpty) {
      Consts.DEVICE_ID = deviceId;
      print('Device ID: $deviceId');
    } else {
      Consts.DEVICE_ID = "UNKNOWN_DEVICE_ID";
      print('Device ID is null or empty');
    }
  }

  Future<void> getLoginFlag() async {
    final prefs = await SharedPreferences.getInstance();
    Consts.isLoggedInBefore = prefs.getInt('FLAG') ?? 0;
  }

  Future<void> checkPackageAndSetConsts() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String packageName = packageInfo.packageName;

    if (packageName == "com.tatkal.train.quick") {
      Consts.APP = 3;
      Consts.GOLD_SKU_NAME = "gold_monthly";
    }
  }

  static Future<void> checkAndCreatePaymentFolder() async {
    Directory? externalDir = await getExternalStorageDirectory();
    if (externalDir == null) return;

    final paymentDir = Directory("${externalDir.path}/Documents/payment");

    if (await paymentDir.exists()) {
      Consts.payWithPG = true;
    } else {
      try {
        await paymentDir.create(recursive: true);
        Consts.payWithPG = true;
      } catch (e) {
        Consts.payWithPG = false;
      }
    }

    print("payWithPG = ${Consts.payWithPG}");
  }

  @override
  void initState() {
    super.initState();
    logSplashOpenEvent();
    initDeviceId();
    getLoginFlag();
    FirestoreFunctions.findActivePromoCodes();
    Consts.canShowAppOpenAd = false;

    _initNonCriticalInBackground();

    SplashScreenState.SPLASH_RUNNING = true;
    Future.delayed(
      const Duration(milliseconds: UI_ANIMATION_DELAY),
      _hideSystemUI,
    );

    _trackSplashEvent();

    _hideHandler = Timer(const Duration(seconds: 3), () {
      _checkUserLoginAndNavigate();
    });
    _initializeGoogleSignIn();
    // Force premium and enable captcha autofill globally
    _forcePremiumAndCaptcha();
  }

  Future<void> _initializeGoogleSignIn() async {
    try {
      await _googleSignIn.initialize();
      _isGoogleSignInInitialized = true;
      var user = await _googleSignIn.attemptLightweightAuthentication();
      setState(() {
        _currentUser = user;
      });
    } catch (e) {
      debugPrint('Failed to initialize Google Sign-In: $e');
    }
  }

  void _initNonCriticalInBackground() async {
    await checkPackageAndSetConsts();
    await checkAndCreatePaymentFolder();
    _initBilling();
    MixpanelManager().track("App launch");
  }

  void _hideSystemUI() {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [],
    );
  }

  void _trackSplashEvent() {
    MixpanelManager().track("Splash Screen Opened");
  }

  void checkUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('userToken');
    debugPrint("Token found: $token");
  }

  Future<void> _initBilling() async {
    final bool available = await _inAppPurchase.isAvailable();
    if (mounted) {
      setState(() {
        _available = available;
      });
    }
  }

  @override
  void dispose() {
    _hideHandler?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: SplashLayout());
  }

  Future<void> downloadFromServer(String url, String filename) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        Uint8List bytes = response.bodyBytes;

        final dir = await getApplicationDocumentsDirectory();
        final file = File('${dir.path}/$filename');

        await file.writeAsBytes(bytes);

        // Increment and check
        downloadsComplete++;
        if (downloadsComplete == 2) {
          afterImageDownload(null);
        }
      } else {
        print("Download failed: ${response.statusCode}");
      }
    } catch (e) {
      print("Download error: $e");
    }
  }

  void _navigateAndFinish(String route, {Map<String, dynamic>? arguments}) {
    Get.offAllNamed(route, arguments: arguments);
  }

  void afterImageDownload(
    Uri? deepLinkUri, {
    Map<String, dynamic>? extras,
  }) async {
    log("DEEP LINK URL: ${deepLinkUri?.path}");

    if (deepLinkUri != null && deepLinkUri.path.isNotEmpty) {
      final path = deepLinkUri.path;

      if (path.contains("general-booking")) {
        return;
      } else if (path.contains("pnr-screen")) {
        _navigateAndFinish(AppRoutes.pnrScreen);
        return;
      } else if (path.contains("running-status")) {
        return;
      } else if (path.contains("request-data-deletion-for-quick-tatkal")) {
        _navigateAndFinish(AppRoutes.profile, arguments: {"DELETE": 1});
        return;
      }
    }

    final db = await MainDB.instance.database;
    final result = await db.rawQuery('SELECT * FROM ${MainDB.tableName}');
    final userExists = result.isNotEmpty;

    String? origin = extras?['ORIGIN'];

    if (userExists && Consts.loginMethod == "LATER") {
      _navigateAndFinish(AppRoutes.signInSocialScreen);
    } else if (origin == "PAYMENT") {
      Consts.paymentSource = "Notification";
    } else if (origin == "RENEW") {
      Consts.paymentSource = "Notification";
      MixpanelManager().track("Renewal notification open");
    } else {
      _navigateAndFinish(AppRoutes.exitScreen);
    }

    SplashScreenState.SPLASH_RUNNING = false;
  }

  Future<void> downloadGoldPackOfferImages() async {
    final String bucketUrl = "gs://rapid-tatkal.firebasestorage.app";
    final FirebaseStorage storage = FirebaseStorage.instanceFor(
      bucket: bucketUrl,
    );

    final Directory dir =
        await getExternalStorageDirectory() ??
        await getApplicationDocumentsDirectory();

    if (!Consts.payWithPG) {
      File file = File('${dir.path}/regular.png');
      if (await file.exists()) {
        await file.delete();
      }

      final StorageReference = storage.ref().child(
        'gold/subs/regular${CaptchaAPIConsts.APIConsts.SUBS_IMG_FILE_VER}.png',
      );
      try {
        final uri = await StorageReference.getDownloadURL();
        if (downloadsComplete == 2) return;
        await downloadFromServer(uri, file.path);
      } catch (e) {
        if (downloadsComplete == 2) return;
        downloadsComplete = 2;
        afterImageDownload(null);
        debugPrint('ERROR: $e');
      }

      file = File('${dir.path}/offer.png');
      if (await file.exists()) {
        await file.delete();
      }

      final offerRef = storage.ref().child(
        'gold/subs/offer${CaptchaAPIConsts.APIConsts.SUBS_IMG_FILE_VER}.png',
      );
      try {
        final uri = await offerRef.getDownloadURL();
        if (downloadsComplete == 2) return;
        await downloadFromServer(uri, file.path);
      } catch (e) {
        if (downloadsComplete == 2) return;
        downloadsComplete = 2;
        afterImageDownload(null);
        debugPrint('ERROR: $e');
      }
    } else {
      // PG
      File file = File('${dir.path}/regular_pg.png');
      if (await file.exists()) {
        await file.delete();
      }

      final StorageReference = storage.ref().child(
        'gold/subs/regular${CaptchaAPIConsts.APIConsts.SUBS_IMG_FILE_VER}_pg.png',
      );
      try {
        final uri = await StorageReference.getDownloadURL();
        if (downloadsComplete == 2) return;
        await downloadFromServer(uri, file.path);
      } catch (e) {
        if (downloadsComplete == 2) return;
        downloadsComplete = 2;
        afterImageDownload(null);
        debugPrint('ERROR: $e');
      }

      file = File('${dir.path}/offer_pg.png');
      if (await file.exists()) {
        await file.delete();
      }

      final offerRef = storage.ref().child(
        'gold/subs/offer${CaptchaAPIConsts.APIConsts.SUBS_IMG_FILE_VER}_pg.png',
      );
      try {
        final uri = await offerRef.getDownloadURL();
        if (downloadsComplete == 2) return;
        await downloadFromServer(uri, file.path);
      } catch (e) {
        if (downloadsComplete == 2) return;
        downloadsComplete = 2;
        afterImageDownload(null);
        debugPrint('ERROR: $e');
      }
    }
  }

  Future<bool> goldPriceVersionUpToDate() async {
    try {
      Directory? filesDir = await getExternalStorageDirectory();
      if (filesDir == null) return false;

      final documentsDir = Directory("${filesDir.path}/Documents");

      if (!await documentsDir.exists()) {
        await documentsDir.create(recursive: true);
      }

      if (!Consts.payWithPG) {
        final file1 = File(
          "${documentsDir.path}/S${CaptchaAPIConsts.APIConsts.SUBS_IMG_FILE_VER}",
        );

        if (!await file1.exists()) {
          try {
            await file1.create();
          } catch (e) {
            if (kDebugMode) print("Error creating file1: $e");
          }
          return false;
        } else {
          final f1 = File("${documentsDir.path}/regular.png");
          final f2 = File("${documentsDir.path}/offer.png");

          if (await f1.exists() && await f2.exists()) {
            return true;
          } else {
            return false;
          }
        }
      } else {
        final file1 = File(
          "${documentsDir.path}/P${CaptchaAPIConsts.APIConsts.SUBS_IMG_FILE_VER}",
        );

        if (!await file1.exists()) {
          try {
            await file1.create();
          } catch (e) {
            if (kDebugMode) print("Error creating file1: $e");
          }
          return false;
        } else {
          final f1 = File("${documentsDir.path}/regular_pg.png");
          final f2 = File("${documentsDir.path}/offer_pg.png");

          if (await f1.exists() && await f2.exists()) {
            return true;
          } else {
            return false;
          }
        }
      }
    } catch (e) {
      if (kDebugMode) print("Error in goldPriceVersionUpToDate: $e");
      return false;
    }
  }

  Future<void> checkGoldPackGooglePurchase(BuildContext context) async {
    final InAppPurchase iap = InAppPurchase.instance;
    final bool available = await iap.isAvailable();
    if (!available) {
      goToDashboardFinal(context);
      return;
    }
    await setSubsPrices();
    await setProductPrice();

    final List<PurchaseDetails> allPurchases = [];
    bool goldPackRestored = false;
    List<String> purchasedSkus = [];
    String orderId = "";
    int purTime = 0;
    String sId = "", pId = "", gmId = "", goId = "", gyId = "";
    int starterPurchaseTime = 0,
        premiumPurchaseTime = 0,
        gMonthlyPurchaseTime = 0,
        gOfferPurchaseTime = 0,
        gYearlyPurchaseTime = 0;
    for (var p in allPurchases) {
      if (p.status == PurchaseStatus.purchased) {
        await iap.completePurchase(p);
        MixpanelManager().track("Purchase acknowledge later");
      }
      if (p.productID.contains("complimentary")) {
        goldPackRestored = true;
        await iap.completePurchase(p);
        MixpanelManager().track("Purchase consume later");
        break;
      }
    }
    if (goldPackRestored) return;
    final List<PurchaseDetails> subs = allPurchases;
    if (subs.isEmpty) {
      goToDashboardFinal(context);
      return;
    }
    for (var p in subs) {
      if (p.status == PurchaseStatus.purchased) {
        String sku = p.productID;
        purchasedSkus.add(sku);
        switch (sku) {
          case "gold_yearly":
            gyId = p.purchaseID ?? "";
            gYearlyPurchaseTime =
                p.transactionDate != null && p.transactionDate!.isNotEmpty
                ? int.tryParse(p.transactionDate!) ?? 0
                : 0;
            break;
          case "gold_offer":
            goId = p.purchaseID ?? "";
            gOfferPurchaseTime =
                p.transactionDate != null && p.transactionDate!.isNotEmpty
                ? int.tryParse(p.transactionDate!) ?? 0
                : 0;
            break;
          case "gold_subscription":
          case "gold_monthly":
          case "gold_subscription":
            gmId = p.purchaseID ?? "";
            gMonthlyPurchaseTime =
                p.transactionDate != null && p.transactionDate!.isNotEmpty
                ? int.tryParse(p.transactionDate!) ?? 0
                : 0;
            break;
          case "premium_subs":
            pId = p.purchaseID ?? "";
            premiumPurchaseTime =
                p.transactionDate != null && p.transactionDate!.isNotEmpty
                ? int.tryParse(p.transactionDate!) ?? 0
                : 0;
            break;
          case "starter_subs":
            sId = p.purchaseID ?? "";
            starterPurchaseTime =
                p.transactionDate != null && p.transactionDate!.isNotEmpty
                ? int.tryParse(p.transactionDate!) ?? 0
                : 0;
            break;
        }
        await iap.completePurchase(p);
        MixpanelManager().track("Purchase acknowledge later");
      }
    }
    int tickets = 0;
    int amount = 0;
    String packName = "";
    String sku = "";
    if (purchasedSkus.contains("gold_yearly")) {
      amount = CaptchaAPIConsts.APIConsts.PRICE_DP_YEARLY;
      packName = "GOLD Pack (Yearly)";
      tickets = 9999;
      sku = "gold_yearly";
      orderId = gyId;
      purTime = gYearlyPurchaseTime;
    } else if (purchasedSkus.contains("gold_offer")) {
      amount = CaptchaAPIConsts.APIConsts.PRICE_DP_YEARLY_OFFER;
      packName = "GOLD Pack (Offer)";
      tickets = 9999;
      sku = "gold_offer";
      orderId = goId;
      purTime = gOfferPurchaseTime;
    } else if (purchasedSkus.contains("gold_subscription") ||
        purchasedSkus.contains("gold_monthly") ||
        purchasedSkus.contains(Consts.GOLD_SKU_NAME)) {
      amount = CaptchaAPIConsts.APIConsts.PRICE_GP_REGULAR;
      packName = "GOLD Pack (Monthly)";
      tickets = 999;
      sku = Consts.GOLD_SKU_NAME;
      orderId = gmId;
      purTime = gMonthlyPurchaseTime;
    } else if (purchasedSkus.contains("premium_subs")) {
      amount = CaptchaAPIConsts.APIConsts.PRICE_PP;
      packName = "Premium Pack";
      tickets = 10;
      sku = "premium_subs";
      orderId = pId;
      purTime = premiumPurchaseTime;
    } else if (purchasedSkus.contains("starter_subs")) {
      amount = CaptchaAPIConsts.APIConsts.PRICE_SP;
      packName = "Starter Pack";
      tickets = 2;
      sku = "starter_subs";
      orderId = sId;
      purTime = starterPurchaseTime;
    }
    if (Consts.PACK_EXPIRED == 1) {
      final prefs = await SharedPreferences.getInstance();
      final purCount = (prefs.getInt("PURCHASE_COUNT_$sku") ?? 0) + 1;
      await prefs.setInt("PURCHASE_COUNT_$sku", purCount);
      MixpanelManager().track("Subscription renewed");
      Consts.PACK_EXPIRY_DATE = DateFormat(
        "dd-MMM-yyyy",
      ).format(DateTime.fromMillisecondsSinceEpoch(purTime));
      provideTickets(context, tickets);
      await prefs.setString("user_type", Consts.USER_TYPE);
      await prefs.setInt("tickets", SplashScreenState.ticketsLeft);
      await prefs.setString("expiry_date", Consts.PACK_EXPIRY_DATE);
      final now = DateTime.now();
      await FirestoreFunctions(context).savePaymentWithDate(
        pack: packName,
        amount: amount,
        orderId: orderId,
        email: EMAIL,
        mobile: MOBILE_NO,
        renew: 1,
        mode: "GOOGLE",
        paymentDate: DateFormat("dd-MM-yyyy HH:mm:ss").format(now),
      );
      log("FIRESTORE DEBUG: Go to dashboard");
      goToDashboardFinal(context);
    } else {
      goToDashboardFinal(context);
    }
  }

  Future<void> setSubsPrices() async {
    final List<String> skuList = [
      'starter_subs',
      'premium_subs',
      Consts.GOLD_SKU_NAME,
      'gold_offer',
      'gold_yearly',
    ];
    final productDetailsResponse = await InAppPurchase.instance
        .queryProductDetails(skuList.toSet());
    if (productDetailsResponse.notFoundIDs.isEmpty &&
        productDetailsResponse.productDetails.isNotEmpty) {
      for (final ProductDetails productDetails
          in productDetailsResponse.productDetails) {
        final String price = productDetails.price;
        switch (productDetails.id) {
          case 'starter_subs':
            CaptchaAPIConsts.APIConsts.PRICE_SP_STR = price;
            break;
          case 'premium_subs':
            CaptchaAPIConsts.APIConsts.PRICE_PP_STR = price;
            break;
          case 'gold_subscription':
          case 'gold_monthly':
            CaptchaAPIConsts.APIConsts.PRICE_GP_STR = price;
            break;
          case 'gold_offer':
            CaptchaAPIConsts.APIConsts.PRICE_DP_OFFER_STR = price;
            break;
          case 'gold_yearly':
            CaptchaAPIConsts.APIConsts.PRICE_DP_REG_STR = price;
            break;
        }
      }
    }
  }

  Future<void> goToDashboardFinal(BuildContext context) async {
    await doPreDashboardStuff(context);
  }

  void goToDashboard(BuildContext context) {
    checkGoldPackGooglePurchase(context);
  }

  Future<void> setTicketsAfterTask(BuildContext context) async {
    final prefs = await SharedPreferences.getInstance();
    bool signupLater = prefs.getBool("LATER") ?? false;
    final Database db = await LoginDB.database;
    final List<Map<String, dynamic>> records = await db.query(
      LoginDB.tableName,
    );

    if (records.isNotEmpty) {
      final last = records.last;
      MixpanelManager().track("Mobile logged in");
      MOBILE_NO = last['mobile'] ?? '';
      Consts.loginMethod = "MOBILE";
      loginSource = "MOBILE";
      FirestoreFunctions.checkPreviousAccount = false;
      await FirestoreFunctions(context).getTicketsNew("MOBILE");
    } else {
      final user = FirebaseAuth.instance.currentUser;
      final accessToken = await FacebookAuth.instance.accessToken;
      final isLoggedInWithFb = accessToken != null;

      if (user != null && await isSignedInWithGoogle(user)) {
        MixpanelManager().track("Google logged in");
        try {
          CUST_NAME = user.displayName ?? '';
          EMAIL = user.email ?? '';
          PRIMARY_EMAIL = user.email ?? '';
          profilePicUrl = user.photoURL ?? '';
          Consts.loginMethod = "GOOGLE";
          if (SplashScreenState.PRIMARY_EMAIL == "NA") throw Exception();
          await setImageTask(
            context: context,
            source: "GOOGLE",
            profilePicUrl: profilePicUrl!,
          );
        } catch (e) {
          await prefs.setBool("LATER", true);
          MixpanelManager().track("Later logged in");
          Consts.loginMethod = "LATER";
          laterSource = "SplashActivity";
          await FirestoreFunctions(context).getTicketsSignUpLater();
        }
      } else if (isLoggedInWithFb) {
        MixpanelManager().track("FB logged in");
        final profile = await FacebookAuth.instance.getUserData(
          fields: "id,name,email,picture.width(200).height(200)",
        );
        MixpanelManager().track("Graph request complete");
        try {
          EMAIL = profile['email'] ?? '';
          PRIMARY_EMAIL = profile['email'] ?? '';
          CUST_NAME = profile['name'] ?? '';
          profilePicUrl = profile['picture']['data']['url'] ?? '';
          Consts.loginMethod = "FACEBOOK";
          if (SplashScreenState.PRIMARY_EMAIL == "NA") throw Exception();
          await setImageTask(
            context: context,
            source: "FACEBOOK",
            profilePicUrl: profilePicUrl!,
          );
        } catch (e) {
          MixpanelManager().track("Graph request exception");
          await prefs.setBool("LATER", false);
          MixpanelManager().track("Later logged in");
          Consts.loginMethod = "LATER";
          laterSource = "SplashActivity";
          await FirestoreFunctions(context).getTicketsSignUpLater();
        }
      } else {
        if (signupLater) {
          MixpanelManager().track("Later logged in");
          Consts.loginMethod = "LATER";
          laterSource = "SplashActivity";
          await FirestoreFunctions(context).getTicketsSignUpLater();
          return;
        } else {
          MixpanelManager().track("First time login");
          firstTime = true;
          loginWithDeviceId(context);
        }
      }
    }
  }

  void loginWithDeviceId(BuildContext context) {
    final params = {'value': 'true'};

    FirebaseAnalytics.instance.logEvent(
      name: 'signup_later',
      parameters: params,
    );

    Consts.loginMethod = "LATER";
    laterSource = 'First Time';
    FirestoreFunctions(context).getTicketsSignUpLater();
  }

  void provideTickets(BuildContext context, int tickets) async {
    String packName = "";
    switch (tickets) {
      case 2:
        packName = "Starter Pack";
        Consts.USER_TYPE = "STARTER_USER";
        break;
      case 10:
        packName = "Premium Pack";
        Consts.USER_TYPE = "PREMIUM_USER";
        break;
      case 999:
        packName = "GOLD Pack (Monthly)";
        Consts.USER_TYPE = "GOLD_USER";
        break;
      case 9999:
        packName = "GOLD Pack (Yearly)";
        Consts.USER_TYPE = "DIAMOND_USER";
        break;
      default:
        Consts.USER_TYPE = "FREE_USER";
        break;
    }

    if (tickets == 999 || tickets == 9999) {
      AppConstants.isGoldUser = 2;
      SplashScreenState.isGoldUser = 2;
    }

    Consts.PACK_EXPIRED = 0;
    SplashScreenState.ticketsLeft = tickets;

    DateTime now = DateTime.now();
    DateFormat sdf1 = DateFormat("dd-MMM-yyyy");
    DateTime expiryDate = now;

    try {
      if (Consts.PACK_EXPIRY_DATE.isNotEmpty) {
        expiryDate = sdf1.parse(Consts.PACK_EXPIRY_DATE);
      }
    } catch (e) {}

    switch (packName) {
      case "Starter Pack":
        expiryDate = expiryDate.add(Duration(days: 7));
        break;
      case "Premium Pack":
      case "GOLD Pack (Monthly)":
        expiryDate = DateTime(
          expiryDate.year,
          expiryDate.month + 1,
          expiryDate.day,
        );
        break;
      case "GOLD Pack (Yearly)":
        expiryDate = DateTime(
          expiryDate.year + 1,
          expiryDate.month,
          expiryDate.day,
        );
        break;
    }

    Consts.PACK_EXPIRY_DATE = sdf1.format(expiryDate);
    Consts.updateSource += "3";

    FirestoreFunctions.debugSequence += "Splashactivity.updateTickets -> ";
    await FirestoreFunctions(context).updateTickets();
  }

  Future<void> setProductPrice() async {
    final InAppPurchase iap = InAppPurchase.instance;

    const String sku = 'complimentary_pack';
    final Set<String> productIds = {sku};

    final ProductDetailsResponse response = await iap.queryProductDetails(
      productIds,
    );

    if (response.error == null && response.productDetails.isNotEmpty) {
      for (final ProductDetails product in response.productDetails) {
        final String price = product.price;
        CaptchaAPIConsts.APIConsts.PRICE_CP_STR = price;
      }
    } else {}
  }

  Future<void> doPreDashboardStuff(BuildContext context) async {
    if (await goldPriceVersionUpToDate()) {
      debugPrint("STUDIOS: IMAGE VERSION UP TO DATE");

      final Uri? data = Uri.base;

      if (data != null) {
        debugPrint("STUDIOS: DEEP LINK URL: ${data.path}");

        final path = data.path;
        if (path.contains("general-booking")) {
          Get.offAllNamed(AppRoutes.formActivity2);
          SPLASH_RUNNING = false;
          return;
        } else if (path.contains("pnr-status")) {
          Get.offAllNamed(AppRoutes.pnrScreen);
          SPLASH_RUNNING = false;
          return;
        } else if (path.contains("running-status")) {
          SPLASH_RUNNING = false;
          return;
        } else if (path.contains("request-data-deletion-for-quick-tatkal")) {
          SPLASH_RUNNING = false;
          return;
        }
      }

      final dbHelper = MainDB.instance;
      final db = await dbHelper.database;
      final cur = await db.query(MainDB.tableName);

      String? origin;
      try {
        origin = ModalRoute.of(context)?.settings.arguments as String?;
      } catch (_) {}

      if (cur.isNotEmpty && Consts.loginMethod == "LATER") {
        Get.offAllNamed(AppRoutes.signInSocialScreen);
      } else if (origin != null && origin == "PAYMENT") {
        Consts.paymentSource = "Notification";
        Get.offAllNamed(AppRoutes.premiumScreen);
      } else if (origin != null && origin == "RENEW") {
        MixpanelManager().track("Renewal notification open");
        Consts.paymentSource = "Notification";
        Get.offAllNamed(AppRoutes.premiumScreen);
      } else {
        Get.offAllNamed(AppRoutes.home);
      }

      SPLASH_RUNNING = false;
    } else {
      debugPrint("STUDIOS: DOWNLOAD IMAGE");
      downloadGoldPackOfferImages();
    }
  }

  Future<void> _checkUserLoginAndNavigate() async {
    await setTicketsAfterTask(context);
  }

  bool isSignedInWithGoogle(User user) {
    for (final userInfo in user.providerData) {
      if (userInfo.providerId == GoogleAuthProvider.PROVIDER_ID) {
        return true;
      }
    }
    return false;
  }

  Future<void> setImageTask({
    required BuildContext context,
    required String source,
    required String profilePicUrl,
  }) async {
    ui.Image? profilePic;

    try {
      final response = await http.get(Uri.parse(profilePicUrl));
      if (response.statusCode == 200) {
        final Completer<ui.Image> completer = Completer();
        ui.decodeImageFromList(response.bodyBytes, (ui.Image img) {
          completer.complete(img);
        });
        profilePic = await completer.future;
      }
    } catch (_) {}

    try {
      MixpanelManager().track("Image set", {"Source": source});
    } catch (_) {}

    if (profilePic != null) {
      SplashScreenState.profilePic = Image.memory(
        Uint8List.fromList([]), // Placeholder
      );
    }

    laterSource = source;
    FirestoreFunctions.checkPreviousAccount = false;

    FirestoreFunctions(context).getTicketsNew(source);
  }

  Future<void> _forcePremiumAndCaptcha() async {
    try {
      // Premium flags across modules
      SplashScreenState.isGoldUser = 2;
      AppConstants.isGoldUser = 2;

      // Enable captcha autofill flags (both APIConsts variants)
      CoreAPIConsts.APIConsts.allowCaptchaAutofill = true;
      CaptchaAPIConsts.APIConsts.allowCaptchaAutofill = true;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('IS_GOLD_USER', 2);
      await prefs.setInt('is_gold_user', 2);
    } catch (_) {}
  }
}

class SplashLayout extends StatelessWidget {
  const SplashLayout({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF110624), Color(0xFF6B33F2)],
        ),
      ),
      child: SafeArea(
        child: Stack(
          children: [
            Align(
              alignment: Alignment.topCenter,
              child: Padding(
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).size.height * 0.2,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(
                      'assets/images/text_logo_white.png',
                      height: 70,
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Image.asset(
                'assets/images/social.png',
                fit: BoxFit.contain,
              ),
            ),
            Positioned(
              bottom: 10,
              left: 0,
              right: 0,
              child: Image.asset(
                'assets/images/trusted.png',
                fit: BoxFit.contain,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
