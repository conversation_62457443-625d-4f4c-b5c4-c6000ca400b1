import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:share_plus/share_plus.dart';

import '../core/AppConstants.dart';
import '../core/helper/mixpanel_manager.dart';

/*
 AUTHOR : LOKESH
 DATE : 30-06-25.
 */
class FreeTicketsScreen extends StatefulWidget {
  const FreeTicketsScreen({super.key});

  @override
  State<FreeTicketsScreen> createState() => _FreeTicketsScreenState();
}

class _FreeTicketsScreenState extends State<FreeTicketsScreen> {
  final FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  bool isLoading = false;
  bool _mixpanelInitialized = false;
  String deviceId = '';
  late MixpanelManager _mixpanelManager;

  @override
  void initState() {
    super.initState();
    _getDeviceId();
    _loadReferralCode();
    _mixpanelManager = MixpanelManager();
    _mixpanelManager.init();
  }

  Future<void> _getDeviceId() async {
    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceId = androidInfo.id ?? 'unknown';
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? 'unknown';
      } else {
        deviceId = 'unknown';
      }
    } catch (e) {
      deviceId = 'unknown';
    }
  }

  void _loadReferralCode() async {
    setState(() => isLoading = true);
    setState(() => isLoading = false);
  }

  Future<bool> _checkInternetConnection() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> _onInviteClicked() async {
    try {
      _mixpanelManager.track("Click invite");
      _mixpanelManager.incrementPeopleProperty("Referrals", 1.0);
    } catch (e) {
      print('Mixpanel tracking error: $e');
    }

    await _createDynamicLink();
  }

  Future<void> _createDynamicLink() async {
    setState(() => isLoading = true);

    try {
      if (!await _checkInternetConnection()) {
        _showErrorMessage("Please check your network connection");
        return;
      }

      String id = _getUserId();

      final DynamicLinkParameters parameters = DynamicLinkParameters(
        link: Uri.parse(
          'https://www.afrestudios.com/index.php/quick-tatkal-irctc-train-ticket/?invitedby=$id',
        ),
        uriPrefix: 'https://afrestudios.page.link',
        androidParameters: const AndroidParameters(
          packageName: 'com.tatkal.train.quick',
          minimumVersion: 1,
        ),
        iosParameters: const IOSParameters(
          bundleId: 'com.tatkal.train.quick',
          appStoreId: 'YOUR_APP_STORE_ID',
          minimumVersion: '1.0.0',
        ),
        socialMetaTagParameters: SocialMetaTagParameters(
          title: 'Quick Tatkal - Fast Train Ticket Booking',
          description:
              'The fastest app to book tatkal tickets with CAPTCHA autofill feature!',
          imageUrl: Uri.parse(
            'https://www.afrestudios.com/assets/images/quick-tatkal-logo.png',
          ),
        ),
      );

      final ShortDynamicLink shortLink = await FirebaseDynamicLinks.instance
          .buildShortLink(parameters);
      final Uri shortUrl = shortLink.shortUrl;

      final String msg = _createInvitationMessage(shortUrl.toString());

      await analytics.logEvent(
        name: "referral",
        parameters: {'shared': 'true'},
      );

      await _shareText(msg);
    } catch (e) {
      print('Dynamic link creation error: $e');
      _showErrorMessage("Unable to generate link at the moment.");
    } finally {
      setState(() => isLoading = false);
    }
  }

  String _getUserId() {
    String id = AppConstants.email;
    if (id == "NA" || id.isEmpty) {
      id = AppConstants.mobileNo;
      if (id == "NA" || id.isEmpty) {
        id = deviceId.isNotEmpty ? deviceId : "unknown";
      }
    }
    return id;
  }

  String _createInvitationMessage(String invitationLink) {
    return "Hey, Try Quick Tatkal, the fastest app on Play Store to book tatkal tickets and the one and only app with CAPTCHA autofill feature! Use my referrer link: $invitationLink";
  }

  Future<void> _shareText(String text) async {
    try {
      await Share.share(text);
      //                     _showSuccessMessage("Referral link shared successfully!");
    } catch (e) {
      print('Share error: $e');
      await _systemShare(text);
    }
  }

  Future<void> _systemShare(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      _showSuccessMessage("Link copied to clipboard!");
    } catch (e) {
      print('System share error: $e');
      _showErrorMessage("Unable to share or copy link");
    }
  }

  void _showErrorMessage(String message) {
    Get.snackbar(
      "Error",
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(10),
      borderRadius: 8,
    );
  }

  void _showSuccessMessage(String message) {
    Get.snackbar(
      "Success",
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(10),
      borderRadius: 8,
    );
  }

  Future<bool> _onWillPop() async {
    // Navigate back to Dashboard (or previous screen)
    Get.back();
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: const Color(0xFF1E1E29),
        body: CustomScrollView(
          slivers: [
            SliverAppBar(
              backgroundColor: Colors.black,
              elevation: 0,
              floating: true,
              pinned: true,
              automaticallyImplyLeading: true,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Get.back(),
              ),
              title: Text(
                "Quick Tatkal",
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              centerTitle: true,
              systemOverlayStyle: SystemUiOverlayStyle.light,
            ),
            SliverFillRemaining(
              hasScrollBody: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    const SizedBox(height: 40),

                    // Header text
                    Text(
                      "Share with Friends",
                      style: GoogleFonts.poppins(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 15),

                    Text(
                      "And get one extra ticket",
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 5),

                    Text(
                      "absolutely Free",
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        color: Colors.white70,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 30),

                    // Creative image
                    Expanded(
                      child: Center(
                        child: Container(
                          constraints: const BoxConstraints(maxHeight: 300),
                          child: Image.asset(
                            'assets/images/free_tickets_creative.png',
                            fit: BoxFit.contain,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                height: 200,
                                width: 200,
                                decoration: BoxDecoration(
                                  color: Colors.grey[800],
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                child: const Icon(
                                  Icons.image_not_supported,
                                  size: 50,
                                  color: Colors.grey,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 30),

                    // Invite button
                    Container(
                      width: double.infinity,
                      height: 60,
                      child: ElevatedButton(
                        onPressed: isLoading ? null : _onInviteClicked,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          padding: EdgeInsets.zero,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                        child: Ink(
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF9C5DF7), Color(0xFF7E3FF2)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Container(
                            alignment: Alignment.center,
                            child: isLoading
                                ? const SizedBox(
                                    height: 24,
                                    width: 24,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Text(
                                    "Invite Now",
                                    style: GoogleFonts.poppins(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Info text
                    Text(
                      "Share your referral link with friends and family to earn free tickets!",
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[400],
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 30),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
