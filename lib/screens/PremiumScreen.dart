import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';
import '../server/FirestoreFunctions.dart' as FS;
import '../core/Consts.dart' as AppConsts;
import '../screens/splash_screen.dart';
import '../screens/QuickTatkalScreen.dart';
import '../core/helper/mixpanel_manager.dart';
import '../pg/SelectPGFragment.dart';
import '../pg/PaymentManager.dart';
import '../quick/ui/promo_code_dialog.dart' as PromoDialog;
import '../captcha/APIConsts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'dart:async'; // Import dart:async

// AUTHOR : LOKESH
// GIT-telestic1
// DATE : 11-JULY-25

class PremiumScreen extends StatefulWidget {
  final String? goldFlag;

  const PremiumScreen({super.key, this.goldFlag});

  @override
  State<PremiumScreen> createState() => _PremiumScreenState();
}

class _PremiumScreenState extends State<PremiumScreen> {
  // Java variables equivalent
  int selectedPack = -1;
  int tickets = 0;
  int amount = 0;
  bool isLoading = false;
  bool isSubscription = true;
  bool alreadyOwned = false;
  bool isRestore = false;
  bool isGoldOfferRunning = false;
  bool payWithPG = false;
  bool discountApplied = false;

  // Exact same variables as Java PremiumActivity
  int tempImgResId = 0;
  Widget? tempLayout;
  int? tempImg;

  // Price display variables
  String goldPriceStr = "";
  String goldPriceYearlyStr = "";

  int purchaseTime = 0;

  final InAppPurchase _iap = InAppPurchase.instance;
  Stream<List<PurchaseDetails>>? _subscription;
  bool _iapAvailable = false;

  @override
  void initState() {
    super.initState();
    FirebaseAnalytics.instance.logEvent(name: 'payment_view');
    MixpanelManager().track("Payment screen load", {
      "Source": AppConsts.Consts.paymentSource,
    });
    _initializePremiumScreen();
    _initializeBilling();
    // Java: Check if GOLD flag is set
    if (widget.goldFlag != null && widget.goldFlag == "Y") {
      goldClick();
    }
  }

  Future<void> _initializePremiumScreen() async {
    // Java equivalent: FirestoreFunctions.findActivePromoCodes()
    await FS.FirestoreFunctions.findActivePromoCodes();

    // For testing: Add some sample promo codes if none exist
    if (FS.FirestoreFunctions.activePromoCodes.isEmpty) {
      FS.FirestoreFunctions.activePromoCodes.addAll(["SAVE20", "DISCOUNT50"]);
    }

    // Java: Set price strings - exactly like Java
    if (APIConsts.PRICE_GP_STR.isEmpty) {
      goldPriceStr = "₹${APIConsts.PRICE_GP_REGULAR}.00";
    } else {
      goldPriceStr = APIConsts.PRICE_GP_STR;
    }

    // Java: Set yearly price based on offer
    if (QuickTatkalScreenState.isGoldOfferRunning) {
      if (APIConsts.PRICE_DP_OFFER_STR.isEmpty) {
        goldPriceYearlyStr = "₹${APIConsts.PRICE_DP_YEARLY_OFFER}.00";
      } else {
        goldPriceYearlyStr = APIConsts.PRICE_DP_OFFER_STR;
      }
    } else {
      if (APIConsts.PRICE_DP_REG_STR.isEmpty) {
        goldPriceYearlyStr = "₹${APIConsts.PRICE_DP_YEARLY}.00";
      } else {
        goldPriceYearlyStr = APIConsts.PRICE_DP_REG_STR;
      }
    }

    isGoldOfferRunning = QuickTatkalScreenState.isGoldOfferRunning;

    // Refresh UI after loading promo codes
    if (mounted) {
      setState(() {});
    }
  }

  void _initializeBilling() async {
    final bool available = await _iap.isAvailable();
    setState(() {
      _iapAvailable = available;
    });
    _subscription = _iap.purchaseStream;
    _subscription?.listen(_onPurchasesUpdated);
  }

  Future<void> makePurchase(String skuPack) async {
    final response = await _iap.queryProductDetails({skuPack});
    if (response.productDetails.isNotEmpty) {
      final productDetails = response.productDetails.first;
      final purchaseParam = PurchaseParam(productDetails: productDetails);
      await _iap.buyNonConsumable(purchaseParam: purchaseParam);
      await FirebaseAnalytics.instance.logEvent(name: 'qt_purchase_flow');
      MixpanelManager().track('Launch purchase flow', {
        'Pack name': AppConsts.Consts.PACK_NAME,
        'Source': AppConsts.Consts.paymentSource,
      });
    }
  }

  Future<void> makeSubscription(String skuPack) async {
    final response = await _iap.queryProductDetails({skuPack});
    if (response.productDetails.isNotEmpty) {
      final productDetails = response.productDetails.first;
      final purchaseParam = PurchaseParam(productDetails: productDetails);
      // InAppPurchase does not provide buySubscription in its latest API.
      // Subscriptions are also bought by buyNonConsumable but with correct SKU.
      // TODO: For full parity, consider using platform channel or 3rd party billing wrapper.
      await _iap.buyNonConsumable(purchaseParam: purchaseParam);
      await FirebaseAnalytics.instance.logEvent(name: 'qt_subs_flow');
      MixpanelManager().track('Launch purchase flow', {
        'Pack name': AppConsts.Consts.PACK_NAME,
        'Source': AppConsts.Consts.paymentSource,
      });
    }
  }

  void _onPurchasesUpdated(List<PurchaseDetails> purchases) async {
    for (var purchase in purchases) {
      if (purchase.status == PurchaseStatus.purchased) {
        // Acknowledge if needed
        if (purchase.pendingCompletePurchase) {
          await _iap.completePurchase(purchase);
        }
        // For one-time packs, consume purchase as in Java
        if (tickets == 1) {
          // Not directly possible in Flutter - simulate user can re-buy
        }
        // Save details to FirestoreFunctions if available
        // Log analytics
        await FirebaseAnalytics.instance.logEvent(
          name: purchase.productID.contains('subs')
              ? 'qt_subs_paid'
              : 'qt_purchase_paid',
        );
        _handlePurchase(purchase);
      } else if (purchase.status == PurchaseStatus.error) {
        await FirebaseAnalytics.instance.logEvent(name: 'qt_pymt_error');
        Get.snackbar(
          "Error",
          "Error completing purchase: " +
              (purchase.error?.message ?? "Unknown"),
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      } else if (purchase.status == PurchaseStatus.canceled) {
        await FirebaseAnalytics.instance.logEvent(name: 'qt_pymt_cancel');
        Get.snackbar(
          "Transaction Cancelled",
          "You cancelled the purchase",
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
      }
    }
  }

  void _handlePurchase(PurchaseDetails purchase) {
    provideTickets();
    final props = <String, dynamic>{
      "Source": AppConsts.Consts.paymentSource,
      "Pack name": AppConsts.Consts.PACK_NAME,
      "TID": "TID", // Fill with real TID logic if available
    };
    MixpanelManager().track("Purchase complete", props);
    Get.snackbar(
      "Purchase Successful",
      "Congratulations! You bought " + AppConsts.Consts.PACK_NAME,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  // Java equivalent: Use real FirestoreFunctions.activePromoCodes
  bool get shouldShowDiscount =>
      FS.FirestoreFunctions.activePromoCodes.isNotEmpty && !isGoldOfferRunning;

  // Java equivalent: goldClick() method
  void goldClick() {
    final props = <String, dynamic>{"Pack Name": "Diamond"};
    MixpanelManager().track("Select Pack", props);

    tickets = 9999;
    if (isGoldOfferRunning) {
      amount = APIConsts.PRICE_DP_YEARLY_OFFER;
    } else {
      amount = APIConsts.PRICE_DP_YEARLY;
    }

    setState(() {
      selectedPack = 4; // Gold yearly index
    });
  }

  // Java equivalent: applyDiscount() method
  void applyDiscount() {
    // Java: invalidateOptionsMenu() equivalent
    setState(() {
      isGoldOfferRunning = true;
      discountApplied = true;
    });

    AppConsts.Consts.DISCOUNT_APPLIED = true;
    QuickTatkalScreenState.isGoldOfferRunning = true;

    if (APIConsts.PRICE_DP_OFFER_STR.isEmpty) {
      goldPriceYearlyStr = "₹${APIConsts.PRICE_DP_YEARLY_OFFER}.00";
    } else {
      goldPriceYearlyStr = APIConsts.PRICE_DP_OFFER_STR;
    }

    goldClick();

    final json = <String, dynamic>{"code": AppConsts.Consts.PROMO_CODE};
    MixpanelManager().track("Apply Discount", json);
  }

  // Java equivalent: restoreSubscriptions() method
  Future<void> restoreSubscriptions() async {
    setState(() {
      isLoading = true;
    });

    bool found = false;
    late StreamSubscription<List<PurchaseDetails>> subscription;
    subscription = _iap.purchaseStream.listen((
      List<PurchaseDetails> purchases,
    ) {
      for (final purchase in purchases) {
        switch (purchase.productID) {
          case "gold_yearly":
          case "gold_offer":
            tickets = 9999;
            found = true;
            break;
          case "gold_subscription":
          case "gold_monthly":
            tickets = 999;
            found = true;
            break;
          case "premium_subs":
            tickets = 10;
            found = true;
            break;
          case "starter_subs":
            tickets = 2;
            found = true;
            break;
        }
        if (found) break;
      }
      if (found) {
        MixpanelManager().track("Restore successful", {
          "Source": AppConsts.Consts.paymentSource,
          "Pack name": AppConsts.Consts.PACK_NAME,
        });
        isRestore = true;
        provideTickets();
        Get.snackbar(
          "Restore Complete",
          "Congratulations! Subscriptions restored successfully",
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        Get.snackbar(
          "No Subscriptions Found",
          "No subscriptions found to restore",
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
      setState(() {
        isLoading = false;
      });
      subscription.cancel();
    });

    // On some platforms, you may need to run InAppPurchase.instance.restorePurchases() (esp. for iOS)
    try {
      await InAppPurchase.instance.restorePurchases();
    } catch (_) {}

    // Give up after 5 seconds if no purchases are delivered
    Future.delayed(const Duration(seconds: 5), () {
      subscription.cancel();
      if (!found) {
        setState(() {
          isLoading = false;
        });
        Get.snackbar(
          "No Subscriptions Found",
          "No subscriptions found to restore",
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    });
  }

  // Java equivalent: provideTickets() method
  void provideTickets() {
    String packName = "";
    switch (tickets) {
      case 1:
        packName = "Complimentary Pack";
        AppConsts.Consts.USER_TYPE = "COMP_USER";
        break;
      case 2:
        packName = "Starter Pack";
        AppConsts.Consts.USER_TYPE = "STARTER_USER";
        break;
      case 10:
        packName = "Premium Pack";
        AppConsts.Consts.USER_TYPE = "PREMIUM_USER";
        break;
      case 999:
        packName = "GOLD Pack (Monthly)";
        AppConsts.Consts.USER_TYPE = "GOLD_USER";
        break;
      case 9999:
        packName = "GOLD Pack (Yearly)";
        AppConsts.Consts.USER_TYPE = "DIAMOND_USER";
        break;
    }

    if (tickets == 999 || tickets == 9999) {
      SplashScreenState.isGoldUser = 2;
    }

    if (tickets == 1) {
      SplashScreenState.ticketsLeft += tickets;
    }

    AppConsts.Consts.PACK_EXPIRED = 0;

    DateTime now = purchaseTime > 0
        ? DateTime.fromMillisecondsSinceEpoch(purchaseTime)
        : DateTime.now();
    switch (packName) {
      case "Starter Pack":
        AppConsts.Consts.PACK_EXPIRY_DATE = _formatDate(
          now.add(Duration(days: 7)),
        );
        break;
      case "Premium Pack":
      case "GOLD Pack (Monthly)":
        AppConsts.Consts.PACK_EXPIRY_DATE = _formatDate(
          DateTime(now.year, now.month + 1, now.day),
        );
        break;
      case "GOLD Pack (Yearly)":
        AppConsts.Consts.PACK_EXPIRY_DATE = _formatDate(
          DateTime(now.year + 1, now.month, now.day),
        );
        break;
    }

    AppConsts.Consts.ONGOING_PYMT_MEDIUM = AppConsts.Consts.GOOGLE;

    setState(() {
      isLoading = true;
    });

    _updateHomeActivity();
  }

  String _formatDate(DateTime date) {
    return "${date.day.toString().padLeft(2, '0')}-${_getMonthName(date.month)}-${date.year}";
  }

  String _getMonthName(int month) {
    const months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    return months[month - 1];
  }

  void _updateHomeActivity() async {
    // Simulate HomeActivity.update() functionality
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('TICKETS_LEFT', SplashScreenState.ticketsLeft);
    await prefs.setString('USER_TYPE', AppConsts.Consts.USER_TYPE);
    await prefs.setInt('IS_GOLD_USER', SplashScreenState.isGoldUser);

    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF232946),
      appBar: AppBar(
        title: const Text("Buy Tickets"),
        backgroundColor: const Color(0xFF232946),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(color: Colors.white, fontSize: 20),
        actions: _buildAppBarActions(),
      ),
      body: Stack(
        children: [
          _buildContent(),
          if (isLoading)
            Container(
              color: Colors.black54,
              child: const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
            ),
        ],
      ),
    );
  }

  List<Widget> _buildAppBarActions() {
    List<Widget> actions = [];

    if (shouldShowDiscount) {
      actions.add(
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          color: const Color(0xFF121629),
          onSelected: (String value) {
            switch (value) {
              case 'discount':
                _showPromoCodeDialog();
                break;
              case 'restore':
                restoreSubscriptions();
                break;
            }
          },
          itemBuilder: (BuildContext context) {
            List<PopupMenuEntry<String>> items = [];

            items.add(
              const PopupMenuItem<String>(
                value: 'discount',
                child: Row(
                  children: [
                    Icon(Icons.discount, color: Colors.white, size: 20),
                    SizedBox(width: 8),
                    Text('DISCOUNT', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            );

            items.add(
              const PopupMenuItem<String>(
                value: 'restore',
                child: Row(
                  children: [
                    Icon(Icons.restore, color: Colors.white, size: 20),
                    SizedBox(width: 8),
                    Text('RESTORE', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            );

            return items;
          },
        ),
      );
    } else {
      actions.add(
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          color: const Color(0xFF121629),
          onSelected: (String value) {
            if (value == 'restore') {
              restoreSubscriptions();
            }
          },
          itemBuilder: (BuildContext context) {
            return [
              const PopupMenuItem<String>(
                value: 'restore',
                child: Row(
                  children: [
                    Icon(Icons.restore, color: Colors.white, size: 20),
                    SizedBox(width: 8),
                    Text('RESTORE', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            ];
          },
        ),
      );
    }

    return actions;
  }

  void _showPromoCodeDialog() {
    showDialog(
      context: context,
      builder: (context) => PromoDialog.PromoCodeDialog(),
    ).then((result) {
      if (result == true || AppConsts.Consts.PROMO_CODE.isNotEmpty) {
        applyDiscount();
      }
    });
  }

  Widget _buildContent() {
    return SafeArea(
      bottom: true,
      child: Column(
        children: [
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                ...List.generate(
                  packs.length,
                  (index) => _buildPackCard(index),
                ),
                const SizedBox(height: 20),

                // Terms text - exactly like Java
                if (AppConsts.Consts.payWithPG)
                  Container() // Hide terms for PG
                else
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: GestureDetector(
                      onTap: () async {
                        final url =
                            "https://www.afrestudios.com/index.php/quick-tatkal-specific-terms-for-paid-services/";
                        // Launch URL
                      },
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          style: TextStyle(color: Colors.white70, fontSize: 12),
                          children: [
                            TextSpan(
                              text:
                                  "You can manage your subscription or cancel anytime in the Google Play app. Specific ",
                            ),
                            TextSpan(
                              text: "Terms",
                              style: TextStyle(
                                color: Colors.white,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                            TextSpan(text: " apply"),
                          ],
                        ),
                      ),
                    ),
                  ),

                const SizedBox(height: 80),
              ],
            ),
          ),
          _buildBuyButton(),
        ],
      ),
    );
  }

  final List<PackInfo> packs = [
    PackInfo(
      id: 'complimentary_pack',
      title: 'Complimentary Pack',
      priceStr: APIConsts.PRICE_CP_STR.isEmpty
          ? '₹${APIConsts.PRICE_CP}.00'
          : APIConsts.PRICE_CP_STR,
      subtitle: 'Lifetime • 1 Ticket',
      color: const Color(0xFF7C4DFF),
      image: 'assets/images/c.png',
      tickets: 1,
      desc: 'Lifetime, 1 Ticket',
      isSubscription: false,
      amount: APIConsts.PRICE_CP,
    ),
    PackInfo(
      id: 'starter_subs',
      title: 'Starter Pack',
      priceStr: APIConsts.PRICE_SP_STR.isEmpty
          ? '₹${APIConsts.PRICE_SP}.00'
          : APIConsts.PRICE_SP_STR,
      subtitle: AppConsts.Consts.payWithPG
          ? '1 Week'
          : 'Weekly • Unlimited tickets',
      color: const Color(0xFF00B8D4),
      image: 'assets/images/starter_pack.png',
      tickets: 2,
      desc: '1 Week, Unlimited tickets',
      isSubscription: true,
      amount: APIConsts.PRICE_SP,
    ),
    PackInfo(
      id: 'premium_subs',
      title: 'Premium Pack',
      priceStr: APIConsts.PRICE_PP_STR.isEmpty
          ? '₹${APIConsts.PRICE_PP}.00'
          : APIConsts.PRICE_PP_STR,
      subtitle: AppConsts.Consts.payWithPG
          ? '1 Month'
          : 'Monthly • Unlimited tickets',
      color: const Color(0xFF1DE9B6),
      image: 'assets/images/premium_pack.png',
      tickets: 10,
      desc: '1 Month, Unlimited tickets',
      isSubscription: true,
      amount: APIConsts.PRICE_PP,
    ),
    PackInfo(
      id: 'gold_monthly',
      title: 'Gold Pack',
      priceStr: '',
      // Set dynamically
      subtitle: AppConsts.Consts.payWithPG
          ? '1 Month'
          : 'Monthly • Unlimited tickets, Ad Free',
      color: const Color(0xFFFFD600),
      image: 'assets/images/gold_pack.png',
      tickets: 999,
      desc: '1 Month, Unlimited, Ad Free',
      isSubscription: true,
      amount: APIConsts.PRICE_GP_REGULAR,
    ),
    PackInfo(
      id: 'gold_yearly',
      title: 'Gold Pack (Yearly)',
      priceStr: '',
      // Set dynamically
      subtitle: AppConsts.Consts.payWithPG
          ? '1 Year'
          : 'Yearly • Ultimate Perks!',
      color: const Color(0xFF6EC6FF),
      image: 'assets/images/gold_pack.png',
      tickets: 9999,
      desc: '1 Year, Ultimate',
      isSubscription: true,
      amount: 0, // Set dynamically
    ),
  ];

  Widget _buildPackCard(int index) {
    final pack = packs[index];
    final bool selected = index == selectedPack;
    final bool isGoldMonthly = pack.id == 'gold_monthly';
    final bool isGoldYearly = pack.id == 'gold_yearly';
    final bool isGold = isGoldMonthly || isGoldYearly;
    String displayPrice = pack.priceStr;
    if (isGoldMonthly) {
      displayPrice = goldPriceStr;
    } else if (isGoldYearly) {
      displayPrice = goldPriceYearlyStr;
    }
    return AnimatedContainer(
      duration: Duration(milliseconds: 250),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: selected ? pack.color : Colors.transparent,
          width: selected ? 4 : 2,
        ),
        gradient: isGold
            ? LinearGradient(
                colors: isGoldMonthly
                    ? [Color(0xFFB68D4C), Color(0xFFD8B064)]
                    : [Color(0xFF6B86FF), Color(0xFF6EC6FF)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: isGold ? null : Color(0xFF121629),
      ),
      margin: const EdgeInsets.symmetric(vertical: 7),
      child: InkWell(
        borderRadius: BorderRadius.circular(18),
        onTap: () {
          MixpanelManager().track("Select Pack", {
            "Pack Name": _getPackNameForTracking(pack.id),
          });
          setState(() {
            selectedPack = index;
            tickets = pack.tickets;
            amount = _getAmountForPack(pack, isGoldMonthly, isGoldYearly);
            purchaseTime = 0; // Reset so expiry uses now
          });
        },
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  Image.asset(pack.image, height: 50, width: 50),
                  if (selected)
                    AnimatedOpacity(
                      opacity: 1,
                      duration: Duration(milliseconds: 200),
                      child: Container(
                        height: 60,
                        width: 60,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          shape: BoxShape.circle,
                        ),
                        child: Transform.rotate(
                          angle: -0.3,
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(width: 18),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      spacing: 6,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        Text(
                          pack.title,
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (isGold)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: isGoldMonthly ? Colors.red : Colors.blue,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              isGoldMonthly ? 'Monthly' : 'Yearly',
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    if (!isGold)
                      Text(
                        pack.subtitle,
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          color: Colors.white70,
                        ),
                      ),
                    if (isGold) ...[
                      _goldFeature('Unlimited Tickets'),
                      _goldFeature('Captcha Autofill'),
                      _goldFeature('PhonePe, GPay,\n    Paytm UPI Autofill'),
                      _goldFeature('OTP Autofill'),
                      _goldFeature('Ad Free'),
                    ],
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Text(
                displayPrice,
                style: GoogleFonts.poppins(
                  fontSize: 17,
                  fontWeight: FontWeight.w700,
                  color: Colors.yellow,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _goldFeature(String text) {
    return Padding(
      padding: const EdgeInsets.only(top: 2),
      child: Text(
        '✓ $text',
        style: const TextStyle(
          fontSize: 14,
          color: Colors.white,
          fontWeight: FontWeight.bold,
          height: 1.2,
        ),
      ),
    );
  }

  String _getPackNameForTracking(String packId) {
    switch (packId) {
      case 'complimentary_pack':
        return 'Complimentary';
      case 'starter_subs':
        return 'Starter';
      case 'premium_subs':
        return 'Premium';
      case 'gold_monthly':
        return 'GOLD';
      case 'gold_yearly':
        return 'Diamond';
      default:
        return 'Unknown';
    }
  }

  int _getAmountForPack(PackInfo pack, bool isGoldMonthly, bool isGoldYearly) {
    if (isGoldMonthly) {
      return APIConsts.PRICE_GP_REGULAR;
    } else if (isGoldYearly) {
      return isGoldOfferRunning
          ? APIConsts.PRICE_DP_YEARLY_OFFER
          : APIConsts.PRICE_DP_YEARLY;
    }
    return pack.amount;
  }

  Widget _buildBuyButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: selectedPack != -1
                ? packs[selectedPack].color
                : Colors.grey,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(28),
            ),
          ),
          onPressed: selectedPack != -1
              ? () async {
                  final packTickets = <String, int>{
                    "FREE_USER": 0,
                    "COMP_USER": 1,
                    "STARTER_USER": 2,
                    "PREMIUM_USER": 10,
                    "GOLD_USER": 999,
                    "DIAMOND_USER": 9999,
                  };
                  if (tickets != 1 &&
                      (packTickets[AppConsts.Consts.USER_TYPE] ?? 0) >=
                          tickets) {
                    Get.snackbar(
                      "Already Subscribed",
                      "You already have an active subscription of a higher or equivalent pack",
                      backgroundColor: Colors.orange,
                      colorText: Colors.white,
                      snackPosition: SnackPosition.BOTTOM,
                    );
                    return;
                  }
                  switch (tickets) {
                    case 1:
                      QuickTatkalScreenState.SKU = "complimentary_pack";
                      AppConsts.Consts.PACK_NAME =
                          "Complimentary Pack (1 Ticket)";
                      await makePurchase("complimentary_pack");
                      break;
                    case 2:
                      QuickTatkalScreenState.SKU = "starter_subs";
                      AppConsts.Consts.PACK_NAME = "Starter Pack (1 Week)";
                      await makeSubscription("starter_subs");
                      break;
                    case 10:
                      QuickTatkalScreenState.SKU = "premium_subs";
                      AppConsts.Consts.PACK_NAME = "Premium Pack (1 Month)";
                      await makeSubscription("premium_subs");
                      break;
                    case 999:
                      QuickTatkalScreenState.SKU =
                          AppConsts.Consts.GOLD_SKU_NAME;
                      AppConsts.Consts.PACK_NAME = "GOLD Pack (1 Month)";
                      await makeSubscription(AppConsts.Consts.GOLD_SKU_NAME);
                      break;
                    case 9999:
                      if (isGoldOfferRunning) {
                        QuickTatkalScreenState.SKU = "gold_offer";
                        await makeSubscription("gold_offer");
                      } else {
                        QuickTatkalScreenState.SKU = "gold_yearly";
                        await makeSubscription("gold_yearly");
                      }
                      AppConsts.Consts.PACK_NAME = "GOLD Pack (1 Year)";
                      break;
                  }
                }
              : null,
          child: Text(
            "Buy Now",
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  void _showPaymentMethodDialog(PackInfo pack) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: const Color(0xFF121629),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            width: 350,
            padding: const EdgeInsets.symmetric(vertical: 28, horizontal: 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.white,
                      size: 25,
                    ),
                    const SizedBox(width: 15),
                    Text(
                      "Select Payment Method",
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white70),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 18),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.white10),
                    borderRadius: BorderRadius.circular(10),
                    color: const Color(0xFF232946),
                  ),
                  padding: const EdgeInsets.all(18),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(pack.image, height: 40, width: 40),
                      const SizedBox(width: 18),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              pack.title,
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 3),
                            Text(
                              pack.subtitle,
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        pack.id == 'gold_monthly'
                            ? goldPriceStr
                            : pack.id == 'gold_yearly'
                            ? goldPriceYearlyStr
                            : pack.priceStr,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          color: Colors.yellow,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 22),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "Choose a payment option",
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                _buildPaymentOption(
                  icon: Icons.account_balance_wallet,
                  title: "Pay with UPI",
                  subtitle: "UPI apps like GPay, PhonePe, Paytm",
                  onTap: () => _handleUPIPayment(pack),
                ),
                const SizedBox(height: 7),
                _buildPaymentOption(
                  icon: Icons.credit_card,
                  title: "Credit/Debit Card",
                  subtitle: "Visa, Mastercard, Rupay, Amex & more",
                  onTap: () => _handleCardPayment(pack),
                ),
                const SizedBox(height: 7),
                _buildPaymentOption(
                  icon: Icons.card_giftcard,
                  title: "Apply Promo / Redeem code",
                  subtitle: "Gift card, Coupon or Referral",
                  onTap: () => _handleRedeemCode(pack),
                ),
                const SizedBox(height: 13),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPaymentOption({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: const Color(0xFF232946),
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Color(0xFF121629),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, color: Colors.white, size: 20),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          subtitle,
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.white60,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                const Icon(Icons.add, color: Colors.white, size: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Payment method handlers - Similar to Java implementation
  void _handleCardPayment(PackInfo pack) {
    Navigator.pop(context);
    _processRazorpayPayment(pack);
  }

  void _handleUPIPayment(PackInfo pack) {
    Navigator.pop(context);
    _processPaytmPayment(pack);
  }

  void _handleRedeemCode(PackInfo pack) {
    Navigator.pop(context);
    _showRedeemCodeDialog(pack);
  }

  // Razorpay Payment - Similar to Java getOrderId() and startPayment()
  void _processRazorpayPayment(PackInfo pack) {
    setState(() {
      isLoading = true;
    });

    // Simulate Razorpay order creation like Java
    Future.delayed(const Duration(seconds: 2), () {
      // Java: getOrderId(amount) -> startPayment(orderId)
      _startRazorpayPayment(pack);
    });
  }

  void _startRazorpayPayment(PackInfo pack) {
    // Simulate payment success - In real app, integrate Razorpay SDK
    Future.delayed(const Duration(seconds: 2), () {
      _onPaymentSuccess('razorpay_payment_123', pack);
    });
  }

  // Paytm Payment - Similar to Java generatePaytmTxn() and payWithPaytm()
  void _processPaytmPayment(PackInfo pack) {
    setState(() {
      isLoading = true;
    });

    // Simulate Paytm transaction generation like Java
    Future.delayed(const Duration(seconds: 2), () {
      // Java: generatePaytmTxn(amount) -> payWithPaytm(...)
      _startPaytmPayment(pack);
    });
  }

  void _startPaytmPayment(PackInfo pack) {
    // Simulate payment success - In real app, integrate Paytm SDK
    Future.delayed(const Duration(seconds: 2), () {
      _onPaymentSuccess('paytm_txn_456', pack);
    });
  }

  // Redeem Code Dialog
  void _showRedeemCodeDialog(PackInfo pack) {
    final TextEditingController codeController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF121629),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Text(
            'Redeem Code',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: codeController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Enter promo code',
                  hintStyle: const TextStyle(color: Colors.white60),
                  enabledBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Colors.white30),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: pack.color),
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white60),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(backgroundColor: pack.color),
              onPressed: () {
                Navigator.pop(context);
                _processRedeemCode(codeController.text, pack);
              },
              child: const Text(
                'Redeem',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _processRedeemCode(String code, PackInfo pack) {
    if (code.isNotEmpty &&
        FS.FirestoreFunctions.activePromoCodes.contains(code)) {
      _onPaymentSuccess('promo_$code', pack);
    } else {
      Get.snackbar(
        "Invalid Code",
        "The promo code you entered is not valid",
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // Payment Success Handler - Similar to Java onPaymentSuccess()
  void _onPaymentSuccess(String paymentId, PackInfo pack) {
    // Use FirestoreFunctions.provideTickets() like Java
    FS.FirestoreFunctions.provideTickets(pack.tickets);

    setState(() {
      isLoading = false;
      tickets = pack.tickets;
    });

    // Track payment success like Java
    final props = <String, dynamic>{
      "PaymentId": paymentId,
      "Pack": pack.title,
      "Amount": pack.priceStr,
      "Tickets": pack.tickets,
    };
    MixpanelManager().track("Payment Success", props);

    Get.snackbar(
      "Purchase Successful",
      "Congratulations! You have successfully purchased ${pack.title}",
      backgroundColor: Colors.green,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );

    // Auto navigate back like Java
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.pop(context);
      }
    });
  }
}

class PackInfo {
  final String id;
  final String title;
  final String priceStr;
  final String subtitle;
  final Color color;
  final String image;
  final int tickets;
  final String desc;
  final bool isSubscription;
  final int amount;

  PackInfo({
    required this.id,
    required this.title,
    required this.priceStr,
    required this.subtitle,
    required this.color,
    required this.image,
    required this.tickets,
    required this.desc,
    required this.isSubscription,
    required this.amount,
  });
}
