import 'package:flutter/material.dart';
import 'package:quick_tatkal_v2/screens/QuickTatkalScreen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';

import '../core/AppConstants.dart';
import '../pojo/Video.dart';
import '../core/Consts.dart';
import 'TabActivity2.dart';

class VideoList extends StatefulWidget {
  const VideoList({super.key});

  @override
  State<VideoList> createState() => _VideoListState();
}

class _VideoListState extends State<VideoList> with TickerProviderStateMixin {
  final List<Video> _videoList = [];
  bool _isLoading = true;
  BannerAd? _bannerAd;
  late final AnimationController _shimmerController;
  late final Animation<double> _shimmerAnimation;
  Mixpanel? _mixpanel;

  @override
  void initState() {
    super.initState();

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    )..repeat(reverse: true);

    _shimmerAnimation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.easeInOut),
    );

    _initMixpanel();
    _fetchVideos();

    if (AppConstants.isGoldUser != 2) {
      _loadBanner();
    }
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    _bannerAd?.dispose();
    super.dispose();
  }

  Future<void> _initMixpanel() async {
    try {
      _mixpanel = await Mixpanel.init(
        const String.fromEnvironment('MIXPANEL_TOKEN', defaultValue: ''),
        optOutTrackingDefault: false,
        trackAutomaticEvents: true,
      );
    } catch (_) {
      // ignore errors – Mixpanel not essential
    }
  }

  Future<void> _fetchVideos() async {
    try {
      final res = await http.get(
        Uri.parse(
          'https://www.afrestudios.com/quick-tatkal/2.0/fetch_videos.php',
        ),
      );
      if (res.statusCode == 200 && res.body.length > 2) {
        final data = res.body;
        final splitterRow = data.substring(1, 2);
        final splitterCol = data.substring(0, 1);
        final payload = data.substring(2, data.length - 2);
        final rows = payload.split(splitterRow);
        for (final row in rows) {
          final parts = row.split(splitterCol);
          if (parts.length >= 7) {
            _videoList.add(
              Video(
                parts[0],
                // videoUrl
                parts[2],
                // thumbUrl
                parts[1],
                // description
                parts[3],
                // info
                parts[4],
                // releaseDate
                parts[5],
                // duration
                parts[6], // firebaseEvent
              ),
            );
          }
        }
      }
    } catch (_) {
      // network or parsing error
    }

    if (mounted) {
      setState(() => _isLoading = false);
    }
  }

  void _loadBanner() {
    _bannerAd = BannerAd(
      adUnitId: Consts.TATKAL_BANNER_ID,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: const BannerAdListener(),
    )..load();
  }

  int _itemCount() {
    if (_videoList.length > 2 && AppConstants.isGoldUser != 2) {
      return _videoList.length + 1;
    }
    return _videoList.length;
  }

  bool _isAdPosition(int index) =>
      _videoList.length > 2 && index == 2 && AppConstants.isGoldUser != 2;

  int _videoIndex(int listPosition) {
    if (_videoList.length > 2 &&
        listPosition > 2 &&
        AppConstants.isGoldUser != 2) {
      return listPosition - 1;
    }
    return listPosition;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).primaryColor,
        elevation: 4,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: _onBackPressed,
        ),
        title: const Text(
          'Tutorials',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        titleSpacing: 0,
      ),
      body: Column(
        children: [
          // Content Area
          Expanded(
            child: Stack(
              children: [
                // RecyclerView equivalent - Video List
                Visibility(
                  visible: !_isLoading && _videoList.isNotEmpty,
                  child: Container(
                    margin: const EdgeInsets.only(top: 10),
                    child: ListView.builder(
                      itemCount: _itemCount(),
                      itemBuilder: (context, index) {
                        if (_isAdPosition(index)) {
                          return _nativeAdPlaceholder();
                        }
                        final v = _videoList[_videoIndex(index)];
                        return _videoTile(v);
                      },
                    ),
                  ),
                ),

                // No Videos Text
                Visibility(
                  visible: !_isLoading && _videoList.isEmpty,
                  child: const Center(
                    child: Text(
                      'No tutorials to show',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ),
                ),

                // Shimmer Loading (Facebook Shimmer equivalent)
                Visibility(
                  visible: _isLoading,
                  child: Container(
                    margin: const EdgeInsets.only(top: 10),
                    child: ListView.builder(
                      itemCount: 3,
                      itemBuilder: (context, index) =>
                          _buildShimmerPlaceholder(),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Ad Container at bottom
          if (AppConstants.isGoldUser != 2 && _bannerAd != null)
            Container(
              width: _bannerAd!.size.width.toDouble(),
              height: _bannerAd!.size.height.toDouble(),
              alignment: Alignment.center,
              child: AdWidget(ad: _bannerAd!),
            ),
        ],
      ),
    );
  }

  Widget _buildShimmerPlaceholder() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        // Card-like container matching the XML placeholder you provided
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.grey[800],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ---------------- THUMBNAIL ----------------
              Stack(
                children: [
                  // Large thumbnail area
                  Container(
                    width: double.infinity,
                    height: 200,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment(_shimmerAnimation.value - 1, 0),
                        end: Alignment(_shimmerAnimation.value, 0),
                        colors: [
                          Colors.grey[400]!,
                          Colors.grey[200]!,
                          Colors.grey[400]!,
                        ],
                      ),
                    ),
                  ),
                  // Duration overlay sized 40dp x 20dp (approx.)
                  Positioned(
                    bottom: 10,
                    right: 10,
                    child: Container(
                      width: 40,
                      height: 20,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        gradient: LinearGradient(
                          begin: Alignment(_shimmerAnimation.value - 1, 0),
                          end: Alignment(_shimmerAnimation.value, 0),
                          colors: [
                            Colors.grey[500]!,
                            Colors.grey[300]!,
                            Colors.grey[500]!,
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              // ---------------- DESCRIPTION ----------------
              Container(
                margin: const EdgeInsets.fromLTRB(10, 10, 10, 0),
                height: 16,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  gradient: LinearGradient(
                    begin: Alignment(_shimmerAnimation.value - 1, 0),
                    end: Alignment(_shimmerAnimation.value, 0),
                    colors: [
                      Colors.grey[400]!,
                      Colors.grey[200]!,
                      Colors.grey[400]!,
                    ],
                  ),
                ),
              ),

              // ---------------- INFO ROW ----------------
              Container(
                margin: const EdgeInsets.fromLTRB(10, 5, 10, 10),
                child: Row(
                  children: [
                    _bottomLine(),
                    const SizedBox(width: 8),
                    const Text(' • ', style: TextStyle(color: Colors.grey)),
                    const SizedBox(width: 8),
                    _bottomLine(),
                    const SizedBox(width: 8),
                    const Text(' • ', style: TextStyle(color: Colors.grey)),
                    const SizedBox(width: 8),
                    _bottomLine(),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Helper – bottom info line shimmer
  Widget _bottomLine() {
    return Container(
      width: 80,
      height: 12,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        gradient: LinearGradient(
          begin: Alignment(_shimmerAnimation.value - 1, 0),
          end: Alignment(_shimmerAnimation.value, 0),
          colors: [Colors.grey[400]!, Colors.grey[200]!, Colors.grey[400]!],
        ),
      ),
    );
  }

  Widget _nativeAdPlaceholder() => Container(
    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    height: 100,
    decoration: BoxDecoration(
      color: Colors.grey[850],
      borderRadius: BorderRadius.circular(8),
    ),
    alignment: Alignment.center,
    child: const Text('Native Ad', style: TextStyle(color: Colors.white70)),
  );

  Widget _videoTile(Video v) => InkWell(
    onTap: () => _openVideo(v),
    child: Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      // No grey card background – keep transparent so it blends with page colour
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Thumbnail with overlays
          Stack(
            children: [
              // Main thumbnail
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  v.getThumbUrl(),
                  width: double.infinity,
                  height: 200,
                  fit: BoxFit.cover,
                  errorBuilder: (_, __, ___) => Container(
                    width: double.infinity,
                    height: 200,
                    color: Colors.grey[400],
                    child: const Icon(Icons.play_circle_outline, size: 48),
                  ),
                ),
              ),
              // Play icon overlay (centre)
              const Positioned.fill(
                child: Align(
                  alignment: Alignment.center,
                  child: Icon(
                    Icons.play_circle_outline,
                    color: Colors.white,
                    size: 60,
                  ),
                ),
              ),
              // Duration overlay bottom-right
              if (v.getDuration().isNotEmpty)
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black87,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      v.getDuration(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
            ],
          ),

          // Description – white text, no background box
          Padding(
            padding: const EdgeInsets.fromLTRB(0, 8, 0, 4),
            child: Text(
              v.getDescription(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Bottom info row – plain texts separated by bullet, single line
          Row(
            children: [
              Flexible(
                child: Text(
                  v.getInfo(),
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Text(' • ', style: TextStyle(color: Colors.white54)),
              Text(
                _uploadTime(v.getReleaseDate()),
                style: const TextStyle(color: Colors.white54, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    ),
  );

  String _uploadTime(String dateStr) {
    try {
      final release = DateTime.parse(dateStr);
      final diffDays = DateTime.now().difference(release).inDays;
      if (diffDays >= 730) return '${(diffDays / 365).floor()} years ago';
      if (diffDays >= 365) return '1 year ago';
      if (diffDays >= 60) return '${(diffDays / 30).floor()} months ago';
      if (diffDays >= 30) return '1 month ago';
      if (diffDays >= 14) return '${(diffDays / 7).floor()} weeks ago';
      return '$diffDays days ago';
    } catch (_) {
      return '';
    }
  }

  Future<void> _openVideo(Video v) async {
    try {
      await _mixpanel?.track(
        'Open video',
        properties: {'Title': v.getDescription()},
      );
    } catch (_) {}

    try {
      await FirebaseAnalytics.instance.logEvent(name: v.getFirebaseEvent());
    } catch (_) {}

    final url = Uri.parse(v.getVideoUrl());
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  void _onBackPressed() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (_) => const QuickTatkalScreen()),
    );
  }
}
