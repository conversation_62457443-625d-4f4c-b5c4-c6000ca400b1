import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../core/AppConstants.dart';
import '../core/Consts.dart';
import '../screens/splash_screen.dart';
import '../screens/FormActivity2.dart';
import '../screens/PremiumScreen.dart';
import '../screens/SignInSocialScreen.dart';

// Dashboard Screen - mirrors Java Dashboard.java
class HomeActivity extends StatefulWidget {
  const HomeActivity({Key? key}) : super(key: key);

  @override
  State<HomeActivity> createState() => _HomeActivityState();
}

class _HomeActivityState extends State<HomeActivity> {
  bool isGoldUser = false;
  int ticketsLeft = 0;
  String userName = "";
  String userType = "FREE_USER";
  String packExpiryDate = "";

  BannerAd? _bannerAd;
  bool _isBannerAdLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadBannerAd();
  }

  Future<void> _loadUserData() async {
    setState(() {
      // Load from SplashScreenState - mirrors Java Dashboard onCreate
      ticketsLeft = SplashScreenState.ticketsLeft;
      userName = SplashScreenState.CUST_NAME.isNotEmpty
          ? SplashScreenState.CUST_NAME
          : "User";
      userType = Consts.USER_TYPE;
      packExpiryDate = Consts.PACK_EXPIRY_DATE;

      isGoldUser = (userType == "GOLD_USER" || userType == "DIAMOND_USER") ||
          SplashScreenState.isGoldUser == 2;
    });
  }

  void _loadBannerAd() {
    if (!isGoldUser) {
      _bannerAd = BannerAd(
        adUnitId: Consts.TATKAL_BANNER_ID,
        size: AdSize.banner,
        request: const AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: (_) {
            setState(() {
              _isBannerAdLoaded = true;
            });
          },
          onAdFailedToLoad: (ad, error) {
            ad.dispose();
          },
        ),
      );
      _bannerAd?.load();
    }
  }

  // Ticket Booking Navigation - exact Java Dashboard flow
  void _navigateToTicketBooking() {
    Get.to(() => FormScreen());
  }

  void _navigateToPremium() {
    Get.to(() => PremiumScreen());
  }

  void _showAccountDialog() {
    showDialog(
      context: context,
      builder: (ctx) =>
          AlertDialog(
            backgroundColor: const Color(0xFF1B1A26),
            title: const Text(
                'Account Details', style: TextStyle(color: Colors.white)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Name: $userName',
                    style: const TextStyle(color: Colors.white70)),
                const SizedBox(height: 8),
                Text('Type: $userType',
                    style: const TextStyle(color: Colors.white70)),
                if (packExpiryDate.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text('Expires: $packExpiryDate',
                      style: const TextStyle(color: Colors.white70)),
                ],
                const SizedBox(height: 8),
                Text('Login Method: ${Consts.loginMethod}',
                    style: const TextStyle(color: Colors.white70)),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(ctx),
                child: const Text(
                    'Close', style: TextStyle(color: Colors.amber)),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF110624),
      appBar: AppBar(
        title: Text(
          'Quick Tatkal',
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF1F1F2A),
        elevation: 0,
        actions: [
          // User account info - mirrors Java Dashboard
          GestureDetector(
            onTap: _showAccountDialog,
            child: Container(
              margin: const EdgeInsets.only(right: 16),
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isGoldUser
                      ? [Color(0xFFFFD700), Color(0xFFFFA500)]
                      : [Color(0xFFA34BFF), Color(0xFF224FF9)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  _buildBadgeText(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF110624), Color(0xFF6B33F2)],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Welcome section
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome, $userName!',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Poppins',
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isGoldUser
                          ? 'Gold Member'
                          : '$ticketsLeft tickets remaining',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),

              Expanded(
                child: Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(top: 20),
                  decoration: const BoxDecoration(
                    color: Color(0xFF1F1F2A),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: 30),
                      // Main action buttons - mirrors Java Dashboard layout
                      Expanded(
                        child: GridView.count(
                          crossAxisCount: 2,
                          padding: const EdgeInsets.all(20),
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                          children: [
                            // Ticket Booking - main action from Java Dashboard
                            _buildDashboardCard(
                              title: 'Ticket Booking',
                              subtitle: 'Book Tatkal tickets',
                              icon: 'assets/images/train_white.png',
                              gradient: const LinearGradient(
                                colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                              ),
                              onTap: _navigateToTicketBooking,
                            ),

                            // Premium/Gold Pack
                            _buildDashboardCard(
                              title: isGoldUser ? 'Gold Member' : 'Upgrade',
                              subtitle: isGoldUser
                                  ? 'Premium features'
                                  : 'Get premium',
                              icon: 'assets/images/dashboard_icon.png',
                              gradient: LinearGradient(
                                colors: isGoldUser
                                    ? [Color(0xFFFFD700), Color(0xFFFFA500)]
                                    : [Color(0xFF00C851), Color(0xFF007E33)],
                              ),
                              onTap: _navigateToPremium,
                            ),

                            // PNR Status
                            _buildDashboardCard(
                              title: 'PNR Status',
                              subtitle: 'Check ticket status',
                              icon: 'assets/images/home.png',
                              gradient: const LinearGradient(
                                colors: [Color(0xFFFF5722), Color(0xFFE64A19)],
                              ),
                              onTap: () {
                                // Navigate to PNR screen
                              },
                            ),

                            // Running Status
                            _buildDashboardCard(
                              title: 'Running Status',
                              subtitle: 'Live train tracking',
                              icon: 'assets/images/home_new.png',
                              gradient: const LinearGradient(
                                colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
                              ),
                              onTap: () {},
                            ),
                          ],
                        ),
                      ),

                      // Bottom banner ad - mirrors Java Dashboard
                      if (_isBannerAdLoaded && _bannerAd != null)
                        Container(
                          height: _bannerAd!.size.height.toDouble(),
                          width: _bannerAd!.size.width.toDouble(),
                          child: AdWidget(ad: _bannerAd!),
                        ),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDashboardCard({
    required String title,
    required String subtitle,
    required String icon,
    required Gradient gradient,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                icon,
                width: 40,
                height: 40,
                color: Colors.white,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Poppins',
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _buildBadgeText() {
    switch (userType) {
      case "DIAMOND_USER":
        return "D";
      case "GOLD_USER":
        return "G";
      case "PREMIUM_USER":
        return "P";
      case "STARTER_USER":
        return "S";
      default:
        return '$ticketsLeft';
    }
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }
}
