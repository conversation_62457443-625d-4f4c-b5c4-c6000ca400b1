import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:intl/intl.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:http/http.dart' as http;

import '../core/Consts.dart';
import '../core/helper/mixpanel_manager.dart';
import '../pojo/Train.dart';
import '../pojo/TrainClass.dart';
import '../screens/splash_screen.dart';

class TrainsBetweenStations extends StatefulWidget {
  final String from;
  final String to;
  final String date;
  final String quota;
  final String fromActivity;

  const TrainsBetweenStations({
    Key? key,
    required this.from,
    required this.to,
    required this.date,
    required this.quota,
    this.fromActivity = '',
  }) : super(key: key);

  @override
  State<TrainsBetweenStations> createState() => _TrainsBetweenStationsState();
}

class _TrainsBetweenStationsState extends State<TrainsBetweenStations> {
  // Same variables as Java code
  List<Train> trainList = [];
  List<Train> trainListFromWeb = [];
  InAppWebView? webView;
  InAppWebViewController? webViewController;
  bool webComplete = false;

  Timer? timer;
  Timer? timerTask;

  Map<String, int> trainPositions = {};
  Map<String, Map<String, int>> classPositions = {};

  static String travelDate = '';
  String? time;
  bool fetched = false;
  bool fetchedTask = false;
  static String fromActivity = '';

  bool fetchComplete = false;
  BannerAd? adView;

  // UI state variables
  bool isShimmerVisible = true;
  bool isRecyclerViewVisible = false;
  bool isNoTrainsInfoVisible = false;
  bool isTrainLayoutVisible = false;

  // ClassAdapter equivalent callback
  Function(int, String, String)? onAvailabilityRefreshCallback;

  @override
  void initState() {
    super.initState();

    // Initialize mixpanel and track events - same as Java
    MixpanelManager().track("TrainsBtwStn Load");
    MixpanelManager().getPeople()?.increment("Availability searched", 1);

    fromActivity = widget.fromActivity;

    // Process date same as Java
    _processDate();

    // Initialize UI
    _initializeUI();

    // Load banner ad if not gold user
    if (SplashScreenState.isGoldUser != 2) {
      _loadBanner();
    }
  }

  void _processDate() {
    String dateTxt = widget.date;
    String quota =
        widget.quota.contains("Premium") || widget.quota.contains("Physically")
        ? "General"
        : widget.quota;

    try {
      DateTime c = DateFormat("dd-MM-yyyy").parse(dateTxt);
      travelDate = DateFormat("yyyyMMdd").format(c);
    } catch (e) {
      debugPrint('Date parsing error: $e');
    }
  }

  void _initializeUI() {
    trainList.clear();

    setState(() {
      isTrainLayoutVisible = true;
      isShimmerVisible = true;
      isRecyclerViewVisible = false;
      isNoTrainsInfoVisible = false;
    });

    _startWebViewFetch();
  }

  void _startWebViewFetch() {
    bool isGnQuota = widget.quota == "General";
    int quotaIndex = 1;
    if (widget.quota == "Lower Berth") {
      quotaIndex = 2;
    } else if (widget.quota == "Ladies") {
      quotaIndex = 3;
    }

    MixpanelManager().track("Set Avl Data", {"event": "started"});

    String url =
        "https://www.ixigo.com/search/result/train/${widget.from}/${widget.to}/${widget.date.replaceAll("-", "")}//1/0/0/0/ALL";

    // Create WebView and load URL
    _createWebView(url, isGnQuota, quotaIndex);

    // Also start FetchTrains AsyncTask equivalent
    _fetchTrainsFromAPI();
  }

  void _createWebView(String url, bool isGnQuota, int quotaIndex) {
    webView = InAppWebView(
      initialUrlRequest: URLRequest(url: WebUri(url)),
      initialSettings: InAppWebViewSettings(
        javaScriptEnabled: true,
        domStorageEnabled: true,
        loadWithOverviewMode: false,
        supportZoom: false,
      ),
      onWebViewCreated: (controller) {
        webViewController = controller;

        // Add JavaScript interface - same as Java
        controller.addJavaScriptHandler(
          handlerName: 'Step',
          callback: (args) {
            if (args.isNotEmpty) {
              String method = args[0];
              if (method == 'setData') {
                _onWebDataReceived(args[1]);
              } else if (method == 'print') {
                _onJavaScriptPrint(args[1]);
              } else if (method == 'refreshAvl') {
                _onJavaScriptRefreshAvl(args[1], int.parse(args[2]));
              }
            }
          },
        );
      },
      onLoadStop: (controller, url) async {
        if (!fetched) {
          fetched = true;
          // Execute the same JavaScript as in Java code
          await _executeJavaScript(controller, isGnQuota);
        }
      },
    );
  }

  Future<void> _executeJavaScript(
    InAppWebViewController controller,
    bool isGnQuota,
  ) async {
    String jsCode =
        '''
      javascript:function aish() {
        var finalData = '';
        var loaderShown = false;
        var waitInterval = setInterval(function() {
          try {
            var blocks = document.getElementsByClassName('train-listing-row');
            if(document.getElementsByClassName('c-loader ').length == 0) {
              if(blocks.length == 0) {
                if(loaderShown && document.getElementsByClassName('no-results').length > 0) {
                  window.flutter_inappwebview.callHandler('Step', 'print', '0');
                  clearInterval(waitInterval);
                }
                return;
              }
              clearInterval(waitInterval);
            } else {
              loaderShown = true;
              return;
            }

            for(x=0; x<blocks.length; x++) {
              var trainNo = blocks[x].getElementsByClassName('number u-font-w-semi-bold')[0].innerHTML;
              var trainName = blocks[x].getElementsByClassName('name u-overflow-ellipsis')[0].innerHTML;
              var fromStn = blocks[x].getElementsByClassName('station')[0].innerText;
              var toStn = blocks[x].getElementsByClassName('station')[1].innerText;
              var duration = blocks[x].getElementsByClassName('train-duration-value')[0].innerText;
              var fromCode = blocks[x].getElementsByClassName('station-text')[0].innerText.split('(')[1].split(')')[0];
              var toCode = blocks[x].getElementsByClassName('station-text')[1].innerText.split('(')[1].split(')')[0];

              var days = blocks[x].getElementsByClassName('day');
              var running = 'All Days';
              if(days[0].innerText != running) {
                running = '';
                for(i=0; i<days.length; i++) {
                  if(!days[i].classList.contains('fade')) {
                    running+= i + ' ';
                  }
                }
              }

              var arrival = blocks[x].getElementsByClassName('train-arrive')[0].getElementsByTagName('div')[0].innerHTML;
              var dep = blocks[x].getElementsByClassName('train-depart')[0].getElementsByTagName('div')[0].innerHTML;

              var classes = blocks[x].getElementsByClassName('train-class-item ');
              var classData = '';
              for(y=0; y<classes.length; y++) {
                var tc = classes[y].getElementsByClassName('train-class')[0].innerText;
                var tf = classes[y].getElementsByClassName('train-fare')[0].innerText;
                
                var elem = classes[y].getElementsByClassName('train-fare-item-row')[1].getElementsByTagName('div');
                var avl = elem[0].innerText.toUpperCase();
                if(avl == 'TAP TO REFRESH' || !$isGnQuota) {
                  avl = 'NA';
                }
                var probability = '-';
                if(elem.length > 1 && $isGnQuota) {
                  probability = elem[1].innerText;
                }
                
                classData+= (tc + '/' + tf + '/' + avl + '/' + probability) + ':';
              }

              finalData += (trainNo + '|' + trainName + '|' + fromStn + '|' + toStn + '|' + duration + '|' + running + '|' + arrival + '|' + dep + '|' + classData + '|' + fromCode + '|' + toCode) + '\\n';
            }
            
            if(!$isGnQuota) {
              document.getElementsByClassName('filter-strip-icon-cntnr')[0].click();
              var xyz = setInterval(function() {
                if(document.getElementById('all-filters-btn') != null) {
                  document.getElementsByClassName('radio-list-item')[2].click();
                  document.getElementById('all-filters-btn').click();
                  clearInterval(xyz);
                  window.flutter_inappwebview.callHandler('Step', 'setData', finalData);
                }
              }, 100);
            } else {
              window.flutter_inappwebview.callHandler('Step', 'setData', finalData);
            }
          } catch (err) {
            window.flutter_inappwebview.callHandler('Step', 'setData', err.message);
          }
        }, 200);
      }
      aish();
    ''';

    await controller.evaluateJavascript(source: jsCode);
  }

  // Equivalent of FetchTrains AsyncTask from Java
  Future<void> _fetchTrainsFromAPI() async {
    if (fetchedTask) return;
    fetchedTask = true;

    try {
      String path = Consts.SEAT_AVL_URL
          .replaceAll("{FROM}", widget.from)
          .replaceAll("{TO}", widget.to)
          .replaceAll("{DATE}", travelDate);

      final response = await http.get(Uri.parse(path));
      String data = response.body;

      bool failed = false;
      try {
        Map<String, dynamic> obj = json.decode(data);
        String responseCode = obj["ResponseCode"] ?? "";

        if (responseCode != "200") {
          failed = true;
        } else {
          List<dynamic> trainsArr = obj["Trains"] ?? [];
          List<Train> apiTrains = [];

          for (int i = 0; i < trainsArr.length; i++) {
            var trainData = trainsArr[i];
            String tNo = trainData["TrainNo"] ?? "";
            String tName = trainData["TrainName"] ?? "";
            String depTime =
                (trainData["DepartureTime"] ?? "").split(" ").length > 1
                ? (trainData["DepartureTime"] ?? "").split(" ")[1]
                : "";
            String depCity = trainData["Source"] ?? "";
            String arrTime =
                (trainData["ArrivalTime"] ?? "").split(" ").length > 1
                ? (trainData["ArrivalTime"] ?? "").split(" ")[1]
                : "";
            String arrCity = trainData["Destination"] ?? "";

            List<TrainClass> classList = getTrainClasses(tNo);

            if (classList.isNotEmpty) {
              Train train = Train(
                tNo,
                tName,
                depTime,
                depCity,
                time ?? "",
                arrTime,
                arrCity,
                classList,
              );
              apiTrains.add(train);
            }
          }

          if (apiTrains.isNotEmpty) {
            trainList.clear();
            trainList.addAll(apiTrains);

            if (mounted) {
              setState(() {
                isShimmerVisible = false;
                isRecyclerViewVisible = true;
                isNoTrainsInfoVisible = false;
              });
            }

            FirebaseAnalytics.instance.logEvent(
              name: "avl_search",
              parameters: {"success": "true"},
            );
          } else {
            failed = true;
          }
        }
      } catch (e) {
        failed = true;
      }

      if (failed) {
        // Fallback to web data if available
        if (trainListFromWeb.isNotEmpty) {
          trainList.clear();
          trainList.addAll(trainListFromWeb);

          if (mounted) {
            setState(() {
              isShimmerVisible = false;
              isRecyclerViewVisible = true;
              isNoTrainsInfoVisible = false;
            });
          }

          FirebaseAnalytics.instance.logEvent(
            name: "avl_search",
            parameters: {"success": "true"},
          );
        } else {
          FirebaseAnalytics.instance.logEvent(
            name: "avl_search_fail",
            parameters: {"success": "false"},
          );
          _showError();
        }
      }
    } catch (e) {
      // Handle network error - fallback to web data
      if (trainListFromWeb.isNotEmpty) {
        trainList.clear();
        trainList.addAll(trainListFromWeb);

        if (mounted) {
          setState(() {
            isShimmerVisible = false;
            isRecyclerViewVisible = true;
            isNoTrainsInfoVisible = false;
          });
        }
      } else {
        _showError();
      }
    }
  }

  void _onWebDataReceived(String data) {
    Map<String, dynamic> props = {};
    try {
      props["Data"] = data;
      MixpanelManager().track("Set Avl Data", props);
    } catch (e) {
      debugPrint('Mixpanel error: $e');
    }

    trainPositions.clear();
    classPositions.clear();

    if (data.isEmpty) {
      props = {};
      try {
        props["Error"] = "NULL";
        props["Data"] = "BLANK";
        MixpanelManager().track("Set avl data error", props);
      } catch (e) {
        debugPrint('Mixpanel error: $e');
      }
      _showError();
      return;
    }

    try {
      List<String> parts = data.split("\n");
      trainListFromWeb.clear();

      int j = 0;
      for (String s in parts) {
        if (s.trim().isEmpty) continue;

        List<String> parts1 = s.split("|");
        if (parts1.length < 11) continue;

        String tNo = parts1[0];
        String tName = parts1[1];
        String depTime = parts1[6];
        String depCity = parts1[9];
        String arrTime = parts1[7];
        String arrCity = parts1[10];
        String trainTime = parts1[4];
        List<String> classData = parts1[8].split(":");

        String running = parts1[5];

        List<TrainClass> classList = [];
        Map<String, int> classPosData = {};

        int i = 0;
        for (String p in classData) {
          if (p.trim().isEmpty) continue;

          List<String> parts2 = p.split("/");
          if (parts2.length < 4) continue;

          String cName = parts2[0];
          String fare = parts2[1];
          String avl = parts2[2];
          String confirm = parts2[3];

          TrainClass tc = TrainClass(cName, fare, avl, confirm);
          classList.add(tc);
          classPosData[cName] = i;
          i++;
        }

        if (classList.isNotEmpty) {
          classPositions[tNo] = classPosData;
          trainPositions[tNo] = j;
          j++;

          Train train = Train(
            tNo,
            tName,
            depTime,
            depCity,
            trainTime,
            arrTime,
            arrCity,
            classList,
          );
          trainListFromWeb.add(train);
        }
      }

      if (trainListFromWeb.isNotEmpty) {
        MixpanelManager().track("Set Avl Data success");

        FirebaseAnalytics.instance.logEvent(
          name: "avl_search",
          parameters: {"success": "true"},
        );

        trainList.clear();
        trainList.addAll(trainListFromWeb);

        if (mounted) {
          setState(() {
            isShimmerVisible = false;
            isRecyclerViewVisible = true;
            isNoTrainsInfoVisible = false;
          });
        }
      } else {
        props = {};
        try {
          props["Error"] = "NA";
          props["Data"] = data;
          MixpanelManager().track("Set avl data error", props);
        } catch (e) {
          debugPrint('Mixpanel error: $e');
        }

        FirebaseAnalytics.instance.logEvent(
          name: "avl_search_fail",
          parameters: {"success": "false"},
        );
        _showError();
      }
    } catch (ex) {
      props = {};
      try {
        props["Error"] = ex.toString();
        props["Data"] = data;
        MixpanelManager().track("Set avl data error", props);
      } catch (e) {
        debugPrint('Mixpanel error: $e');
      }
      _showError();
    }

    webComplete = true;
  }

  void _onJavaScriptPrint(String text) {
    if (text == "0") {
      if (mounted) {
        setState(() {
          isShimmerVisible = false;
          isRecyclerViewVisible = true;
          isNoTrainsInfoVisible = true;
        });
      }

      FirebaseAnalytics.instance.logEvent(
        name: "avl_search",
        parameters: {"success": "true"},
      );
    } else {
      Fluttertoast.showToast(
        msg: text,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }

  void _onJavaScriptRefreshAvl(String data, int index) {
    List<String> parts = data.split(":");
    if (parts.length >= 2) {
      String availability = parts[0];
      String percentStr = parts[1].replaceAll("%", "").trim();
      int percent = int.tryParse(percentStr) ?? -1;

      if (onAvailabilityRefreshCallback != null) {
        onAvailabilityRefreshCallback!(index, availability, percent.toString());
      }
    }
  }

  void _showError() {
    Fluttertoast.showToast(
      msg: "Cannot fetch availability. Please try again",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
    Navigator.of(context).pop();
  }

  void _loadBanner() {
    adView = BannerAd(
      adUnitId: Consts.TATKAL_BANNER_ID,
      size: AdSize.banner,
      request: AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (_) => setState(() {}),
        onAdFailedToLoad: (_, __) => setState(() {}),
      ),
    )..load();
  }

  // Same function as Java - refresh availability with WebView JavaScript
  void refreshAvl(int listIndex, String trainNo, String className) {
    try {
      int trainIndex = trainPositions[trainNo] ?? -1;
      int classIndex = classPositions[trainNo]?[className] ?? -1;

      if (trainIndex == -1 || classIndex == -1) {
        debugPrint('Train or class position not found');
        return;
      }

      onAvailabilityRefreshCallback =
          (int index, String availability, String percent) {
            if (index == listIndex && mounted) {
              setState(() {
                if (index < trainList.length &&
                    classIndex < trainList[index].classList.length) {
                  trainList[index].classList[classIndex].setAvailability(
                    availability,
                  );
                  trainList[index].classList[classIndex].setProbability(
                    percent + "%",
                  );
                }
              });
            }
          };

      // Execute JavaScript to refresh availability - same as Java
      String jsCode =
          '''
        javascript:function aish() {
          var blocks = document.getElementsByClassName('train-listing-row');
          var classes = blocks[$trainIndex].getElementsByClassName('train-class-item ');
          classes[$classIndex].getElementsByClassName('train-class-main')[0].click();

          var refreshInterval = setInterval(function() {
            var statusBlock = blocks[$trainIndex].getElementsByClassName('train-status-wrapper');
            if(statusBlock.length > 0) {
              var refAvl = statusBlock[0].getElementsByClassName('seat-main-status')[0].getElementsByTagName('div')[0].innerText;
              var refProb = '-1';
              if(statusBlock[0].getElementsByClassName('seat-main-status')[0].getElementsByClassName('avail-probability').length > 0) {
                refProb = statusBlock[0].getElementsByClassName('seat-main-status')[0].getElementsByClassName('avail-probability')[0].innerText;
              }
              window.flutter_inappwebview.callHandler('Step', 'refreshAvl', refAvl + ':' + refProb, '$listIndex');
              clearInterval(refreshInterval);
            }
          }, 200);
        }
        aish();
      ''';

      webViewController?.evaluateJavascript(source: jsCode);
    } catch (e) {
      debugPrint('Refresh availability error: $e');
    }
  }

  // Same function as Java - get train classes
  List<TrainClass> getTrainClasses(String trainNo) {
    for (Train t in trainListFromWeb) {
      if (t.getTrainNo() == trainNo) {
        time = t.getTime();
        return t.getClassList();
      }
    }
    return [];
  }

  void startTimer() {
    if (timer == null) {
      timer = Timer.periodic(Duration(milliseconds: 100), (timer) {
        if (webComplete) {
          _fetchTrainsFromAPI();
          timer.cancel();
          this.timer = null;
        }
      });
    }
  }

  @override
  void dispose() {
    timer?.cancel();
    adView?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get date info for toolbar - same as Java logic
    String dateTxt = widget.date;
    DateTime c = DateFormat("dd-MM-yyyy").parse(dateTxt);
    List<String> days = ["", "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    int dayOfWeek = c.weekday == 7 ? 1 : c.weekday + 1;
    String weekDay = days[dayOfWeek];
    String formattedDate = DateFormat("dd MMM").format(c);

    return Scaffold(
      backgroundColor: Color(0xFF1A1A1A), // primary_color
      appBar: AppBar(
        backgroundColor: Color(0xFF2D2D2D), // dialog_color
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "${widget.from} ➝ ${widget.to}",
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              "$weekDay, $formattedDate, ${widget.quota}",
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Hidden WebView for data fetching - same as Java (webView is hidden)
          Container(width: 0, height: 0, child: webView ?? Container()),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(top: 5),
              child: Stack(
                children: [
                  // Shimmer loading - matches shimmerViewContainer
                  if (isShimmerVisible && isTrainLayoutVisible)
                    Container(
                      margin: EdgeInsets.only(top: 10),
                      child: Column(
                        children: [
                          _buildShimmerPlaceholder(),
                          _buildShimmerPlaceholder(),
                          _buildShimmerPlaceholder(),
                        ],
                      ),
                    ),

                  // RecyclerView equivalent - train list
                  if (isRecyclerViewVisible && isTrainLayoutVisible)
                    Container(
                      margin: EdgeInsets.only(top: 10),
                      child: ListView.builder(
                        itemCount: trainList.length,
                        itemBuilder: (context, index) {
                          return _buildTrainCard(trainList[index], index);
                        },
                      ),
                    ),

                  // No trains info - matches no_trains_info
                  if (isNoTrainsInfoVisible)
                    Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.warning, color: Colors.orange, size: 20),
                          SizedBox(width: 8),
                          Text(
                            "No trains found for given criteria",
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Banner ad container - matches ad_view_container
          if (adView != null && SplashScreenState.isGoldUser != 2)
            Container(
              width: adView!.size.width.toDouble(),
              height: adView!.size.height.toDouble(),
              child: AdWidget(ad: adView!),
            ),
        ],
      ),
    );
  }

  // Build shimmer placeholder - matches placeholder_avaialbility.xml
  Widget _buildShimmerPlaceholder() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Card(
        elevation: 10,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Container(
          padding: EdgeInsets.all(10),
          child: _buildSimpleShimmer(
            child: Column(
              children: [
                // First row - train number and name
                Row(
                  children: [
                    Container(width: 50, height: 16, color: Colors.grey[300]),
                    SizedBox(width: 20),
                    Container(width: 150, height: 16, color: Colors.grey[300]),
                  ],
                ),
                SizedBox(height: 5),

                // Second row - timing info
                Row(
                  children: [
                    Container(width: 30, height: 16, color: Colors.grey[300]),
                    SizedBox(width: 7),
                    Container(width: 40, height: 16, color: Colors.grey[300]),
                    SizedBox(width: 20),
                    Text("➝", style: TextStyle(fontSize: 16)),
                    SizedBox(width: 20),
                    Container(width: 30, height: 16, color: Colors.grey[300]),
                    SizedBox(width: 7),
                    Container(width: 40, height: 16, color: Colors.grey[300]),
                  ],
                ),

                // Third row - class boxes
                Container(
                  margin: EdgeInsets.only(top: 10),
                  child: Row(
                    children: [
                      _buildClassPlaceholder(),
                      SizedBox(width: 10),
                      _buildClassPlaceholder(),
                      SizedBox(width: 10),
                      _buildClassPlaceholder(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Simple shimmer effect using opacity animation - matches shimmer duration from Java
  Widget _buildSimpleShimmer({required Widget child}) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.3, end: 1.0),
      duration: Duration(milliseconds: 800), // Same duration as Java shimmer
      builder: (context, value, child) {
        return AnimatedOpacity(
          duration: Duration(milliseconds: 400),
          opacity: value,
          child: child,
        );
      },
      child: child,
    );
  }

  // Build class placeholder box
  Widget _buildClassPlaceholder() {
    return Container(
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          Container(width: 20, height: 16, color: Colors.grey[300]),
          SizedBox(height: 5),
          Container(
            width: 40,
            height: 16,
            margin: EdgeInsets.symmetric(horizontal: 10),
            color: Colors.grey[300],
          ),
        ],
      ),
    );
  }

  // Build actual train card - matches train_listing_row structure
  Widget _buildTrainCard(Train train, int index) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Card(
        elevation: 10,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Container(
          padding: EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Train number and name - matches number u-font-w-semi-bold and name u-overflow-ellipsis
              Row(
                children: [
                  Text(
                    train.trainNo,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(width: 20),
                  Expanded(
                    child: Text(
                      train.trainName,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),

              // Day information - matches day classes
              if (train.trainNo.isNotEmpty) // Running days placeholder
                Container(
                  margin: EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Text(
                        "Runs: ",
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      Text(
                        "All Days",
                        // This would come from running data in real implementation
                        style: TextStyle(fontSize: 12, color: Colors.black),
                      ),
                    ],
                  ),
                ),

              // Timing information - matches train-arrive and train-depart
              Row(
                children: [
                  Text(train.depTime, style: TextStyle(fontSize: 14)),
                  SizedBox(width: 5),
                  Text(
                    train.depCity,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  SizedBox(width: 20),
                  Text("➝", style: TextStyle(fontSize: 16)),
                  SizedBox(width: 20),
                  Text(train.arrivalTime, style: TextStyle(fontSize: 14)),
                  SizedBox(width: 5),
                  Text(
                    train.arrivalCity,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  Spacer(),
                  Text(
                    train.time,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
              SizedBox(height: 10),

              // Class information - matches train-class-item
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: train.classList.map((trainClass) {
                  return _buildClassCard(trainClass, index, train.trainNo);
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build class card - matches train-class-item structure with train-class, train-fare, etc.
  Widget _buildClassCard(
    TrainClass trainClass,
    int trainIndex,
    String trainNo,
  ) {
    Color availabilityColor = _getAvailabilityColor(trainClass.availability);
    bool isRefreshing =
        false; // This would be managed by ClassAdapter equivalent

    return InkWell(
      onTap: () => refreshAvl(trainIndex, trainNo, trainClass.className),
      child: Container(
        padding: EdgeInsets.all(8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(4),
          color: Colors.white,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Class name - matches train-class
            Text(
              trainClass.className,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
            SizedBox(height: 4),

            // Fare - matches train-fare
            Text(
              trainClass.fare,
              style: TextStyle(fontSize: 10, color: Colors.grey[600]),
            ),
            SizedBox(height: 4),

            // Availability - matches train-fare-item-row
            isRefreshing
                ? SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Column(
                    children: [
                      Text(
                        trainClass.availability,
                        style: TextStyle(
                          fontSize: 11,
                          color: availabilityColor,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      // Probability - matches avail-probability
                      if (trainClass.probability != '-' &&
                          trainClass.probability.isNotEmpty)
                        Text(
                          trainClass.probability,
                          style: TextStyle(fontSize: 10, color: Colors.green),
                        ),
                    ],
                  ),
          ],
        ),
      ),
    );
  }

  // Same color logic as Java - matches availability status colors
  Color _getAvailabilityColor(String availability) {
    if (availability.contains('AVAILABLE') || availability.contains('AVL')) {
      return Colors.green;
    } else if (availability.contains('WL') ||
        availability.contains('WAITING')) {
      return Colors.orange;
    } else if (availability.contains('RAC')) {
      return Colors.blue;
    } else if (availability.contains('CONFIRM') ||
        availability.contains('CNF')) {
      return Colors.green;
    } else if (availability.contains('REGRET') || availability.contains('NA')) {
      return Colors.red;
    } else {
      return Colors.red;
    }
  }

  // Same as Java onOptionsItemSelected
  void _onBackPressed() {
    Navigator.of(context).pop();
  }
}
