import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

class RateUsDialog {
  static double userRating = 0;

  static void showRateUsDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.transparent,
          contentPadding: EdgeInsets.zero,
          content: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFF4A4A4A), Color(0xFF2A2A2A)],
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 20),
                  child: Image.asset(
                    'assets/images/train_white.png',
                    height: 60,
                    width: 60,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(Icons.train, size: 60, color: Colors.white);
                    },
                  ),
                ),

                SizedBox(height: 15),

                Container(
                  margin: EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    "Rate Us",
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                SizedBox(height: 5),

                Container(
                  margin: EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    "Your feedback is important for us",
                    style: TextStyle(color: Colors.white, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ),

                SizedBox(height: 10),

                RatingBar.builder(
                  initialRating: 0,
                  minRating: 1,
                  direction: Axis.horizontal,
                  allowHalfRating: false,
                  itemCount: 5,
                  itemSize: 40,
                  glow: false,
                  itemBuilder: (context, index) =>
                      Icon(Icons.star, color: Colors.amber),
                  onRatingUpdate: (rating) {
                    userRating = rating;
                  },
                ),

                SizedBox(height: 20),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () async {
                SharedPreferences prefs = await SharedPreferences.getInstance();
                await prefs.setInt('RATING_VIEW', 0);
                Navigator.of(context).pop();
              },
              child: Text("Cancel", style: TextStyle(color: Colors.grey)),
            ),

            ElevatedButton(
              onPressed: () async {
                if (userRating == 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text("Please select a rating"),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                Navigator.of(context).pop();
                await _handleRatingSubmission(context, userRating.toInt());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: Text("Submit"),
            ),
          ],
        );
      },
    );
  }

  static Future<void> _handleRatingSubmission(
    BuildContext context,
    int rating,
  ) async {
    print("Rating submitted: $rating");

    if (rating < 4) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            "Thanks for your feedback. We'll try to keep improving and make your app experience better ☺",
          ),
          duration: Duration(seconds: 4),
          backgroundColor: Colors.orange,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            "Please rate us on Play Store & share your valuable feedback ↓↓↓",
          ),
          duration: Duration(seconds: 3),
          backgroundColor: Colors.green,
        ),
      );

      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setInt('RATING_DONE', 1);

      await _openPlayStore(context);
    }
  }

  static Future<void> _openPlayStore(BuildContext context) async {
    String packageName = "com.tatkal.train.quick";
    String playStoreUrl =
        "https://play.google.com/store/apps/details?id=$packageName";

    try {
      final Uri url = Uri.parse(playStoreUrl);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Could not open Play Store"),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      print("Error opening Play Store: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Error opening Play Store"),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  static Future<bool> shouldShowRatingDialog() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int ratingDone = prefs.getInt('RATING_DONE') ?? 0;
    return ratingDone == 0;
  }
}
