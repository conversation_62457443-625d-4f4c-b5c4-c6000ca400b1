import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';

class Pnrresult extends StatefulWidget {
  const Pnrresult({Key? key}) : super(key: key);

  @override
  State<Pnrresult> createState() => _PnrResultScreenState();
}

class _PnrResultScreenState extends State<Pnrresult> {
  late InAppWebViewController webViewController;
  late String pnrNo;
  String train = "", date = "", stn = "", pnrStatus = "";

  @override
  void initState() {
    super.initState();
    pnrNo = Get.arguments['pnr'] ?? "";

    if (pnrNo.isEmpty) {
      Future.microtask(() {
        Get.snackbar("Error", "Invalid PNR");
        Navigator.pop(context);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF22232E), // match dialog_color
      appBar: AppBar(
        title: Text("Quick Tatkal"),
        backgroundColor: Theme
            .of(context)
            .primaryColor, // match ?attr/colorPrimary
        automaticallyImplyLeading: false,
      ),
      body: InAppWebView(
        initialUrlRequest: URLRequest(
          url: WebUri("https://www.indianrail.gov.in/enquiry/PNR/PnrEnquiry.html?locale=en"),
        ),
        initialSettings: InAppWebViewSettings(
          javaScriptEnabled: true,
          domStorageEnabled: true,
          allowsInlineMediaPlayback: true,
        ),
        onWebViewCreated: (controller) {
          webViewController = controller;

          controller.addJavaScriptHandler(handlerName: 'Step_show', callback: (_) {
            print("PNR Data Ready");
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("PNR loaded")));
          });

          controller.addJavaScriptHandler(handlerName: 'Step_print', callback: (args) {
            if (args.isNotEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(args[0])));
            }
          });

          controller.addJavaScriptHandler(handlerName: 'Step_setPNRStatus', callback: (args) {
            if (args.isNotEmpty) pnrStatus = args[0];
          });

          controller.addJavaScriptHandler(handlerName: 'Step_setTrainData', callback: (args) {
            if (args.length >= 3) {
              train = args[0];
              date = args[1];
              stn = args[2];
              print("Train: $train | Date: $date | Station: $stn");
            }
          });

          controller.addJavaScriptHandler(handlerName: 'Step_startRailofyActivity', callback: (args) {
            if (args.length >= 2) {
              Get.toNamed("/legal", arguments: {
                'type': args[1],
                'url': args[0],
              });
            }
          });
        },
        onLoadStop: (controller, url) async {
          await Future.delayed(Duration(milliseconds: 800));
          await controller.evaluateJavascript(source: """
            function aish() {
              var bot = setInterval(function() {
                var closeBtn = document.getElementById('corover-close-btn');
                if(closeBtn) {
                  closeBtn.click();
                  clearInterval(bot);
                }
              }, 100);

              document.getElementById('inputPnrNo').value = '${pnrNo}';

              if(document.getElementById('modal1')) {
                document.getElementById('modal1').click();
              } else {
                document.getElementById('submitPnrNo').click();
              }

              var cap = setInterval(function() {
                if((document.getElementById('inputCaptcha') && document.getElementById('inputCaptcha').getBoundingClientRect().top > 0)
                    || document.getElementById('pnrOutputDiv').getBoundingClientRect().top > 0) {
                  
                  try {
                    var t0 = document.getElementsByTagName('table')[0];
                    if (t0) {
                      var tb = t0.getElementsByTagName('tbody')[0];
                      if (tb) {
                        var rows = tb.getElementsByTagName('tr');
                        if (rows && rows.length > 0) {
                          var cells = rows[0].getElementsByTagName('td');
                          var tNo = (cells && cells.length>0) ? cells[0].innerText : '';
                          var date = (cells && cells.length>2) ? cells[2].innerText : '';
                          var stn = (cells && cells.length>6) ? cells[6].innerText : '';
                          window.flutter_inappwebview.callHandler('Step_setTrainData', tNo, date, stn);
                        }
                      }
                    }
                  } catch (e) {}

                  try {
                    var t1 = document.getElementsByTagName('table')[1];
                    if (t1) {
                      var tb1 = t1.getElementsByTagName('tbody')[0];
                      if (tb1) {
                        var rows = tb1.getElementsByTagName('tr');
                        var status = "";
                        for (var i = 0; i < rows.length; i++) {
                          var tds = rows[i].getElementsByTagName('td');
                          if (tds && tds.length>2) status += (tds[2].innerText||'') + "|";
                        }
                        window.flutter_inappwebview.callHandler('Step_setPNRStatus', status);
                      }
                    }
                  } catch (e) {}

                  window.flutter_inappwebview.callHandler('Step_show');
                  clearInterval(cap);
                }
              }, 100);
            }
            aish();
          """);
        },
        onConsoleMessage: (controller, message) {
          print("Console log: ${message.message}");
        },
        onJsAlert: (controller, jsAlertRequest) async {
          return JsAlertResponse(handledByClient: true);
        },

        onLoadError: (controller, url, code, message) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("Page load error: $message")));
        },
        onLoadHttpError: (controller, url, statusCode, description) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("HTTP error: $statusCode")));
        },
      ),
    );
  }
}
