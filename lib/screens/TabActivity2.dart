import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../database/MainDB.dart';
import '../database/PassengerDB.dart';
import '../database/ChildDB.dart';
import '../database/RCPaymentDB.dart';
import '../database/PodDB.dart';
import '../l10n/app_localizations.dart';
import '../utils/Cryptography.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../core/helper/mixpanel_manager.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import '../models/passenger_model.dart';
import '../models/child_model.dart';
import '../quick/AppOpenManager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../screens/GoldPackFragment.dart';
import '../core/Consts.dart' as AppConsts;
import 'PremiumScreen.dart';

const Color kPrimaryColor = Color(0xFF232334);
const Color kDialogColor = Color(0xFF272728);
const Color kGrayBgEnabled = Color(0xFF424259);
const Color kGrayBgDisabled = Color(0xFF21212A);
const double kBigRadius = 30.0;

class TabActivity2 extends StatefulWidget {
  final String formName;
  final String lang;

  const TabActivity2({Key? key, required this.formName, required this.lang})
    : super(key: key);

  @override
  State<TabActivity2> createState() => _TabActivity2State();
}

class _TabActivity2State extends State<TabActivity2>
    with SingleTickerProviderStateMixin {
  /*
   * JOURNEY VARIABLES CORRECTION - Matching Java TabActivity2.java
   * 
   * The following variables have been corrected to match the Java implementation:
   * 
   * Java Variable -> Flutter Variable:
   * - AutoCompleteTextView fromStn -> TextEditingController fromStnController
   * - AutoCompleteTextView toStn -> TextEditingController toStnController  
   * - EditText journeyDate -> TextEditingController journeyDateController
   * - AutoCompleteTextView train -> TextEditingController trainController
   * - AutoCompleteTextView boardingStn -> TextEditingController boardingController
   * - EditText fareLimitEd -> TextEditingController fareLimitController
   * - RadioButton option1/option2/option3 -> bool option1Selected/option2Selected/option3Selected
   * - Spinner quota -> String selectedQuota
   * - Spinner travelClass -> String selectedClass
   * - TextView fareLimitTv -> String? fareLimitTvText
   * 
   * Methods added to match Java patterns:
   * - _initializeJourneyVariables() -> matches journeyListeners()
   * - _loadJourneyData() -> matches loadJourney()
   * - _validateJourneyFields() -> matches Java validation logic
   * - _getJourneyDataMap() -> matches Java ContentValues
   * - Getters for text values -> matches getText().toString()
   */

  // Use MainDB singleton instance
  final MainDB _mainDB = MainDB.instance;

  // Journey Tab Variables - matching Java TabActivity2.java
  final TextEditingController fromStnController = TextEditingController();
  final TextEditingController toStnController = TextEditingController();

  // Radio Button Variables - matching Java: option1, option2, option3
  // In Java these map to BOOKING_OPT values: 0, 1, 2
  String bookingOption = 'none'; // Maps to Java radio button selection
  bool option1Selected = false; // Java: option1 (BOOKING_OPT = 1)
  bool option2Selected = false; // Java: option2 (BOOKING_OPT = 2)
  bool option3Selected = false; // Java: option3 (BOOKING_OPT = 0)

  // Other Journey Variables
  String? fareLimitTvText; // Java: fareLimitTv TextView text

  late TabController _tabController;

  // Journey helper methods - matching Java TabActivity2.java patterns
  void _initializeJourneyVariables() {
    // This matches the Java journeyListeners() method
    // In Java, variables are bound like: fromStn = binding.journey.editText3;
    // In Flutter, we use the controllers directly

    // Initialize radio button states based on bookingOption
    _updateRadioButtonStates();
  }

  // Load journey data from database - matches Java loadJourney() method
  Future<void> _loadJourneyData(String formName) async {
    try {
      final db = await _mainDB.database;
      final List<Map<String, dynamic>> results = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [formName],
      );

      if (results.isNotEmpty) {
        final row = results.first;

        // Load journey data - matches Java cursor.getString() patterns
        fromStnController.text = row['FROM_STN']?.toString() ?? '';
        toStnController.text = row['TO_STN']?.toString() ?? '';
        journeyDateController.text = row['TRVL_DATE']?.toString() ?? '';
        trainController.text = row['TRAIN']?.toString() ?? '';
        boardingController.text = row['BOARDING']?.toString() ?? '';
        fareLimitController.text = row['FARE_LIMIT']?.toString() ?? '';

        // Load manual/auto payment flags to mirror Java behavior
        final pAuto = row['PYMT_AUTO']?.toString();
        manualPymtCheckboxFlag = (pAuto == 'true');

        // Load spinner values
        selectedQuota = row['QUOTA']?.toString() ?? 'General';
        selectedClass = row['TRVL_CLASS']?.toString() ?? 'SL';

        // Load booking option and update radio button states
        int bookingOptValue =
            int.tryParse(row['BOOKING_OPT']?.toString() ?? '0') ?? 0;
        _setBookingOptionFromValue(bookingOptValue);
        _updateRadioButtonStates();
      }
    } catch (e) {
      print('Error loading journey data: $e');
    }
  }

  // Convert BOOKING_OPT integer back to string - reverse of _getBookingOptValue()
  void _setBookingOptionFromValue(int value) {
    switch (value) {
      case 1:
        bookingOption = 'same_coach';
        break;
      case 2:
        bookingOption = 'min_lower_berth';
        break;
      case 3:
        bookingOption = 'confirm_2_lower';
        break;
      default:
        bookingOption = 'none';
        break;
    }
  }

  void _updateRadioButtonStates() {
    // Match Java radio button logic: option1, option2, option3
    option1Selected = (bookingOption == 'same_coach');
    option2Selected = (bookingOption == 'min_lower_berth');
    option3Selected = (bookingOption == 'none');
  }

  // Getters that match Java getText().toString() patterns
  String get fromStationText => fromStnController.text;

  String get toStationText => toStnController.text;

  String get journeyDateText => journeyDateController.text;

  String get trainText => trainController.text;

  String get boardingStationText => boardingController.text;

  String get fareLimitText => fareLimitController.text;

  // Journey validation methods - matching Java validation patterns
  bool _validateJourneyFields() {
    List<String> emptyFields = [];

    // Match Java empty field validation
    if (fromStationText.isEmpty) {
      emptyFields.add('From Station');
    }
    if (toStationText.isEmpty) {
      emptyFields.add('To Station');
    }
    if (journeyDateText.isEmpty) {
      emptyFields.add('Journey Date');
    }
    if (trainText.isEmpty) {
      emptyFields.add('Train');
    }

    if (emptyFields.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please fill: ${emptyFields.join(', ')}')),
      );
      return false;
    }

    return true;
  }

  // Method to save journey data - matches Java ContentValues pattern
  Map<String, dynamic> _getJourneyDataMap() {
    return {
      'FROM_STN': fromStationText,
      'TO_STN': toStationText,
      'TRVL_DATE': journeyDateText,
      'TRAIN': trainText,
      'BOARDING': boardingStationText,
      'QUOTA': selectedQuota,
      'TRVL_CLASS': selectedClass,
      'FARE_LIMIT': fareLimitText,
      'BOOKING_OPT': _getBookingOptValue(),
    };
  }

  // Convert bookingOption string to integer like Java
  int _getBookingOptValue() {
    switch (bookingOption) {
      case 'same_coach':
        return 1; // Java: option1
      case 'min_lower_berth':
        return 2; // Java: option2
      case 'confirm_2_lower':
        return 3;
      default:
        return 0; // Java: option3 (none)
    }
  }

  BannerAd? mAdView;
  InterstitialAd? mAdmobInterstitialAd;
  bool fbAdLoaded = false;
  Widget? frameLayout;
  Widget? frameLayout2;

  Map<String, String> formErrors = {};

  Map<String, dynamic>? mainCursor;
  List<int> key = [];
  bool dataLoaded = false;

  bool shouldStartAfterGrant = false;
  bool drawOverFlag = false;
  bool tncFlag = false;
  static bool accFlag = false;
  static bool serviceStarted = false;
  static bool otpAutofillEnabled = false;
  static bool hdfcAutofillEnabled = false;
  final int REQUEST_SCREENSHOT = 59706;
  final int CODE_DRAW_OVER_OTHER_APP_PERMISSION = 2084;
  final int PERMISSION_REQUEST_CODE = 1111;

  int limit = 6;
  int limitRC = 6;
  int passengerCount = 1;
  int childCount = 0;
  int passengerCountDb = 0;
  int childCountDb = 0;

  List<TextEditingController> emptyElements = [];
  List<String> emptySpinners = [];

  bool coachPreferred = false;
  final TextEditingController coachController = TextEditingController();
  final TextEditingController mobileController = TextEditingController();

  String _selectedFormName = '';
  bool _isFormExist = false;

  String selectedGSTOption = 'no';
  String selectedNationality = "IN";
  String fareFormat = "FT";

  Future<void> _loadInsuranceData() async {
    final db = await MainDB.instance.database;
    final result = await db.query(
      MainDB.tableName,
      columns: ['INSURANCE'],
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );

    if (result.isNotEmpty) {
      String? insVal = result.first['INSURANCE']?.toString();
      setState(() {
        travelInsurance = (insVal == null || insVal == '' || insVal == '1')
            ? 'yes'
            : 'no';
      });
    } else {
      setState(() {
        travelInsurance = 'yes';
      });
    }
  }

  Future<void> _loadStationsAndTrains() async {
    final stationsRaw = await rootBundle.loadString('assets/stations.txt');
    final match = RegExp(r'\[.*\]', dotAll: true).firstMatch(stationsRaw);
    if (match != null) {
      setState(() {
        stationList = List<String>.from(json.decode(match.group(0)!));
        stationsLoaded = true;
      });
    } else {
      setState(() => stationsLoaded = true);
    }

    final trainsRaw = await rootBundle.loadString('assets/trains.txt');
    List<String> parsedTrainList;
    if (trainsRaw.trim().startsWith('[')) {
      parsedTrainList = List<String>.from(json.decode(trainsRaw));
    } else {
      parsedTrainList = List<String>.from(
        json.decode('[${trainsRaw.replaceAll(RegExp(r'[\[\]\n;]'), '')}]'),
      );
    }
    setState(() {
      trainList = parsedTrainList;
      trainsLoaded = true;
    });
  }

  bool _autofillCaptchaWasTried = false;

  void _onAutofillCaptchaChanged(bool? val) {
    final goldUser = SplashActivity.isGoldUser == 2;
    final selectedRC = _tabController.index == 1;

    if (selectedRC) {
      if (goldUser) {
        if ((val ?? false) && !serviceStarted) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (ctx) => AlertDialog(
              backgroundColor: kDialogColor,
              title: const Text(
                'Permission Required',
                style: TextStyle(color: Colors.white),
              ),
              content: const Text(
                'Permission is needed to autofill captcha in Rail Connect. Grant permission?',
                style: TextStyle(color: Colors.white70),
              ),
              actions: [
                TextButton(
                  child: const Text(
                    'Grant',
                    style: TextStyle(color: Colors.amber),
                  ),
                  onPressed: () {
                    // Mark serviceStarted = true as if permission is granted.
                    setState(() {
                      serviceStarted = true;
                      autofillCaptchas = true;
                    });
                    Navigator.of(ctx).pop();
                  },
                ),
                TextButton(
                  child: const Text(
                    'Cancel',
                    style: TextStyle(color: Colors.white),
                  ),
                  onPressed: () {
                    setState(() => autofillCaptchas = false);
                    Navigator.of(ctx).pop();
                  },
                ),
              ],
            ),
          );
          // Fire Mixpanel event for gating dialog
          MixpanelManager().track("GOLD Pack alert show", {
            "Source": "Autofill Captcha",
          });
          return;
        }
        setState(() {
          autofillCaptchas = val ?? false;
        });
      } else {
        setState(() => autofillCaptchas = false);
        _showGoldUpgradeDialog(
          context,
          'Autofill Captcha',
          'Buy Quick Tatkal GOLD Pack to unlock these features\n\n✦ Captcha autofill\n✦ OTP autofill\n✦ Unlimited tickets\n✦ No Ads',
        );
        MixpanelManager().track("GOLD Pack alert show", {
          "Source": "Autofill Captcha",
        });
      }
      return;
    }

    // WEBSITE TAB
    if (!goldUser) {
      setState(() => autofillCaptchas = false);
      _showGoldUpgradeDialog(
        context,
        'Autofill Captcha',
        'Buy Quick Tatkal GOLD Pack to unlock these features\n\n✦ Captcha autofill\n✦ OTP autofill\n✦ Unlimited tickets\n✦ No Ads',
      );
      MixpanelManager().track("GOLD Pack alert show", {
        "Source": "Autofill Captcha",
      });
      return;
    }
    setState(() => autofillCaptchas = val ?? false);
  }

  void _showGoldUpgradeDialog(BuildContext ctx, String title, String message) {
    showDialog(
      context: ctx,
      barrierDismissible: false,
      builder: (_) => AlertDialog(
        backgroundColor: kDialogColor,
        title: Text(title, style: TextStyle(color: Colors.white)),
        content: Text(message, style: TextStyle(color: Colors.white70)),
        actions: [
          TextButton(
            child: Text('Cancel', style: TextStyle(color: Colors.white)),
            onPressed: () => Navigator.pop(ctx),
          ),
          TextButton(
            child: Text('Buy GOLD', style: TextStyle(color: Colors.amber)),
            onPressed: () {
              Navigator.pop(ctx);
              _navigateToPremium('Autofill Captcha');
            },
          ),
        ],
      ),
    );
  }

  void _showCaptchaTrialDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => AlertDialog(
        backgroundColor: kDialogColor,
        title: const Text(
          'Try Captcha Autofill',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'CAPTCHA autofill is available only in GOLD Pack. You can try this feature for free for 1 ticket booking.\n\nDo you want to try this feature now or buy GOLD Pack with lifetime validity with all the unlimited features unlocked?',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                autofillCaptchas = true;
                _autofillCaptchaWasTried = true;
              });
              Navigator.pop(context);
            },
            child: const Text(
              'Try Once',
              style: TextStyle(color: Colors.white),
            ),
          ),
          TextButton(
            onPressed: () {
              setState(() => autofillCaptchas = false);
              Navigator.pop(context);
              _navigateToPremium('Captcha trial');
            },
            child: const Text(
              'Buy GOLD Pack',
              style: TextStyle(color: Colors.amber),
            ),
          ),
        ],
      ),
    );
  }

  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController rcPinController = TextEditingController();

  bool _credentialsLoaded = false;
  bool _isSavingCredentials = false;
  bool _showLoginPassword = false;
  bool _isFormLocked = false;
  static const String _formLockStorageKey = 'FORM_LOCK_PASSWORD';
  String _credentialsError = '';

  bool vikalp = false;
  bool autoUpgradation = false;
  bool onlyConfirmBerths = false;
  String travelInsurance = 'yes';
  bool autofillCaptchas = false;

  // Miscellaneous Payment options - updated to Java-style detailed labels
  final List<String> paymentModeItems = [
    'Pay through Credit & Debit Card / Net Banking',
    'Pay through BHIM/UPI',
  ];
  String paymentModeWeb = 'Pay through Credit & Debit Card / Net Banking';
  final TextEditingController journeyDateController = TextEditingController();
  final TextEditingController trainController = TextEditingController();
  final TextEditingController boardingController = TextEditingController();
  final TextEditingController fareLimitController = TextEditingController();

  final TextEditingController gstNumberController = TextEditingController();
  final TextEditingController gstCompanyController = TextEditingController();
  final TextEditingController gstEmailController = TextEditingController();
  final TextEditingController gstAddressController = TextEditingController();
  final TextEditingController gstFlatController = TextEditingController();
  final TextEditingController gstStreetController = TextEditingController();
  final TextEditingController gstPinController = TextEditingController();
  final TextEditingController gstAreaController = TextEditingController();
  final TextEditingController gstCityController = TextEditingController();
  bool gstRequired = false;

  final TextEditingController cardNumberController = TextEditingController();
  final TextEditingController expiryController = TextEditingController();
  final TextEditingController cvvController = TextEditingController();
  final TextEditingController upiIdController = TextEditingController();

  final TextEditingController cardNumber1Controller = TextEditingController();
  final TextEditingController cardNumber2Controller = TextEditingController();
  final TextEditingController cardNumber3Controller = TextEditingController();
  final TextEditingController cardNumber4Controller = TextEditingController();
  final TextEditingController cardExpiryMonthController =
      TextEditingController();
  final TextEditingController cardExpiryYearController =
      TextEditingController();

  // --- FocusNodes for card number input fields ---
  final FocusNode cardNo1Focus = FocusNode();
  final FocusNode cardNo2Focus = FocusNode();
  final FocusNode cardNo3Focus = FocusNode();
  final FocusNode cardNo4Focus = FocusNode();

  final TextEditingController cardNo1Controller = TextEditingController();
  final TextEditingController cardNo2Controller = TextEditingController();
  final TextEditingController cardNo3Controller = TextEditingController();
  final TextEditingController cardNo4Controller = TextEditingController();
  final TextEditingController cardHolderController = TextEditingController();
  final TextEditingController pinController = TextEditingController();
  final TextEditingController debitCvvController = TextEditingController();
  final TextEditingController staticPassController = TextEditingController();

  // === Java-style variables for CardType UI ===
  // Text editing not needed for label, but defined for stateful show/hide and tight Java-parity
  late Widget cardTypeTxt; // Java-equivalent to the 'CardType' label
  // Spinner (DropdownButton<String>) for card types; Java name 'cardType'
  String cardType = 'Master Card'; // Initial value Java-like
  // CardType options exactly matched to Java
  final List<String> cardTypeMap = ['Master Card', 'VISA', 'RuPay'];
  bool cardTypeShow = false; // Java-style visibility flag
  String selectedBankName = '';
  String selectedExpiryMonth = '';
  String selectedExpiryYear = '';
  bool hdfcAutofill = false;
  bool creditCardSet = false;

  final TextEditingController nbUsernameController = TextEditingController();
  final TextEditingController nbPasswordController = TextEditingController();
  final TextEditingController corpIdController = TextEditingController();

  final TextEditingController walletUsernameController =
      TextEditingController();
  final TextEditingController walletPasswordController =
      TextEditingController();
  String selectedWallet = '';

  final TextEditingController irctcWalletPinController =
      TextEditingController();
  final TextEditingController podMobileController = TextEditingController();
  final TextEditingController multiplePaymentAmountController =
      TextEditingController();
  final TextEditingController iMudramobileNo = TextEditingController();

  String selectedMultiplePaymentOption = 'Select Payment Method';
  String netBankName = '';
  String netBankingType = 'Personal';
  String sbiBankName = '';
  bool sbiBankNameVisible = false;
  bool corpIdVisible = false;

  final Map<String, int> sbiBankMap = {
    'State Bank of Bikaner and Jaipur': 0,
    'State Bank of Patiala': 1,
    'State Bank of Hyderabad': 2,
    'State Bank of Travancore': 3,
    'State Bank of Mysore': 4,
  };

  final TextEditingController vpaController = TextEditingController();
  final TextEditingController upiPinEdController = TextEditingController();
  String selectedUpiBank = '';
  bool autoOpenUPIApp = false;
  bool automateUPICb = false;
  bool defaultbankCb = false;
  bool isLoadingUpi = false;

  // Java-style UPI suboption for web tab (0: PhonePe, 1: BHIM/Paytm, 2: IRCTC iPay UPI)
  // 1. List for UPI sub-options (Java-style)
  final List<String> upiSubOptionList = [
    'Credit & Debit cards / Wallet / UPI (Powered by PhonePe...)',
    'Pay using BHIM (Powered by PAYTM)',
    'IRCTC iPay UPI',
  ];
  int upiSubOption = 0;

  final TextEditingController vpaRCController = TextEditingController();
  final TextEditingController upiPinEdRCController = TextEditingController();
  bool autoOpenUPIAppRC = false;
  bool automateUPICbRC = false;
  bool defaultbankCbRC = false;

  final List<String> upiBankOptions = [
    'Select Bank',
    'State Bank of India',
    'HDFC Bank',
    'ICICI Bank',
    'Axis Bank',
    'Punjab National Bank',
    'Bank of Baroda',
    'Canara Bank',
    'Union Bank of India',
    'Bank of India',
    'Indian Bank',
  ];

  Widget _sectionPayByUpiRC() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: buildGradientBorderCard(
        color: Colors.grey[900]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.upi_bhim_ussd,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 20,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'UPI VPA',
              style: TextStyle(color: Colors.white, fontSize: 13),
            ),
            const SizedBox(height: 5),
            TextField(
              controller: vpaRCController,
              onChanged: (v) async {
                await saveFormRC(showSnackbar: false);
              },
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                filled: true,
                fillColor: kGrayBgEnabled,
                hintText: "Enter UPI VPA (e.g. user@upi)",
                hintStyle: TextStyle(color: Colors.white54),
              ),
            ),
            const SizedBox(height: 10),
            CheckboxListTile(
              value: autoOpenUPIAppRC,
              onChanged: (v) async {
                setState(() => autoOpenUPIAppRC = v ?? false);
                await saveFormRC(showSnackbar: false);
              },
              title: const Text(
                'Auto-open UPI app',
                style: TextStyle(color: Colors.white),
              ),
              activeColor: const Color(0xFF9C5DF7),
              side: const BorderSide(color: Colors.white54, width: 1.2),
              controlAffinity: ListTileControlAffinity.leading,
              dense: true,
            ),
            CheckboxListTile(
              value: automateUPICbRC,
              onChanged: (v) async {
                setState(() => automateUPICbRC = v ?? false);
                await saveFormRC(showSnackbar: false);
              },
              title: const Text(
                'Automate UPI with PIN',
                style: TextStyle(color: Colors.white),
              ),
              activeColor: const Color(0xFF9C5DF7),
              side: const BorderSide(color: Colors.white54, width: 1.2),
              controlAffinity: ListTileControlAffinity.leading,
              dense: true,
            ),
            if (automateUPICbRC) ...[
              const SizedBox(height: 8),
              const Text(
                'UPI PIN',
                style: TextStyle(color: Colors.white, fontSize: 13),
              ),
              TextField(
                controller: upiPinEdRCController,
                obscureText: true,
                maxLength: 6,
                keyboardType: TextInputType.number,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  /* as Java */ counterText: '',
                  hintText: 'Enter UPI PIN',
                  hintStyle: TextStyle(color: Colors.white54),
                ),
              ),
              CheckboxListTile(
                value: defaultbankCbRC,
                onChanged: (v) async {
                  setState(() => defaultbankCbRC = v ?? false);
                  await saveFormRC(showSnackbar: false);
                },
                title: const Text(
                  'Select default bank in UPI app',
                  style: TextStyle(color: Colors.white),
                ),
                activeColor: const Color(0xFF9C5DF7),
                side: const BorderSide(color: Colors.white54, width: 1.2),
                controlAffinity: ListTileControlAffinity.leading,
                dense: true,
              ),
            ],
          ],
        ),
      ),
    );
  }

  static const String APP_ID_GPAY = 'com.google.android.apps.nbu.paisa.user';
  static const String APP_ID_PAYTM = 'net.one97.paytm';
  static const String APP_ID_PHONEPE = 'com.phonepe.app';
  static const String APP_ID_BHIM = 'in.org.npci.upiapp';

  String selectedPaymentMethod = 'payment_gateway';
  String selectedDelayTime = '5 seconds';
  bool manualPayment = false;
  bool autofillOTP = false;
  bool showAdvancedOptions = false;
  bool manualPymtCheckboxFlagRC = false;

  String selectedQuota = 'General';
  String selectedClass = 'SL';
  String selectedBookingOption = 'auto_wait';
  bool isLoading = true;
  bool showNoCreditsInfo = false;
  bool showFareLimit = false;
  String tatkalHint = '';

  List<PassengerModel> passengers = [];
  int maxPassengers = 6;
  bool passengersLoaded = false;

  List<ChildModel> children = [];
  bool childrenLoaded = false;
  bool _isChildSectionVisible =
      false; // Java: equivalent to main_layoutCh.getVisibility() == View.VISIBLE
  final int maxChildren = 2;

  int checkedPymtMeth = 6;
  bool manualPymtCheckboxFlag = false;

  // Localization-aware payment choice map
  Map<int, String> getPymtChoiceMap(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return {
      6: l10n.payment_gateway_credit_card,
      7: l10n.cash_card_wallets,
      8: l10n.irctc_ewallet,
      9: l10n.net_banking,
      10: l10n.payment_gateway_credit_card,
      11: l10n.upi_bhim_ussd,
      12: l10n.cash_card_wallets,
      14: l10n.irctc_prepaid,
      15: l10n.irctc_ipay,
    };
  }

  bool showCardSection = true;
  bool showUPISection = false;
  bool showNetbankSection = false;
  bool showWalletSection = false;
  bool showMultipleSection = false;
  bool showIMudraSection = false;

  Map<String, String> netBankTypeMap = {'Personal': 'R', 'Corporate': 'C'};

  final List<String> bankOptions = [
    'State Bank of India',
    'HDFC Bank',
    'ICICI Bank',
    'Axis Bank',
    'Punjab National Bank',
    'Bank of Baroda',
    'Canara Bank',
    'Union Bank of India',
    'Bank of India',
    'Indian Bank',
  ];

  String selectedBank = '';

  final List<String> walletOptions = [
    'Paytm',
    'PhonePe',
    'Google Pay',
    'Amazon Pay',
    'Mobikwik',
    'Freecharge',
    'JioMoney',
    'Airtel Money',
  ];

  final List<String> delayOptions = [
    '5 seconds',
    '10 seconds',
    '15 seconds',
    '20 seconds',
    '30 seconds',
  ];

  List<String> stationList = [];
  List<String> trainList = [];
  bool stationsLoaded = false;
  bool trainsLoaded = false;

  final List<String> quotaOptions = [
    'General',
    'Tatkal',
    'Premium Tatkal',
    'Physically Handicapped',
    'Ladies',
    'Duty Pass',
    'Lower Berth',
  ];
  final List<String> classOptions = [
    'SL',
    '3A',
    '2A',
    '1A',
    '2S',
    'CC',
    'FC',
    'EC',
    'EA',
  ];
  final List<String> genderOptions = ['Male', 'Female', 'Transgender'];
  final List<String> childAgeOptions = [
    'Below one year',
    'One year',
    'Two years',
    'Three years',
    'Four years',
  ];

  Map<String, int> childAgeMap = {
    'Below one year': 0,
    'One year': 1,
    'Two years': 2,
    'Three years': 3,
    'Four years': 4,
  };

  @override
  void initState() {
    super.initState();

    // Initialize journey variables - matches Java journeyListeners() call
    _initializeJourneyVariables();

    cardType = cardTypeMap[0];
    cardTypeShow = false;
    cardTypeTxt = const SizedBox.shrink();
    _loadStationsAndTrains();
    _tabController = TabController(length: 2, vsync: this);
    _loadFormData();
    _loadPassengers();
    _loadChildren();
    _loadSavedCredentials();

    if (children.isEmpty) {
      children.add(ChildModel());
    }

    _reloadPaymentDataFromDatabase();
  }

  Future<void> _loadFormData() async {
    final db = await MainDB.instance.database;
    final result = await db.query(
      MainDB.tableName,
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );
    if (result.isNotEmpty) {
      final row = result.first;

      usernameController.text = row['USERNAME']?.toString() ?? '';
      passwordController.text = row['PASSWORD']?.toString() ?? '';

      _loadDebitCardWithPinFromDb(row);
      _loadWalletPaymentData(row);
      _loadUpiFromDb(row);
      rcPinController.text = row['RC_PIN'] != null
          ? Cryptography.decryptPassword(row['RC_PIN'] as String)
          : '';
      autofillOTP = (row['OTP_AUTOFILL']?.toString() == '1');
      fromStnController.text = row['FROM_STN']?.toString() ?? '';
      toStnController.text = row['TO_STN']?.toString() ?? '';
      journeyDateController.text = row['TRVL_DATE']?.toString() ?? '';
      trainController.text = row['TRAIN']?.toString() ?? '';
      boardingController.text = row['BOARDING']?.toString() ?? '';

      fareLimitController.text = row['FARE_LIMIT']?.toString() ?? '';

      selectedQuota = quotaOptions.contains(row['QUOTA'])
          ? (row['QUOTA']?.toString() ?? 'General')
          : 'General';
      selectedClass = classOptions.contains(row['TRVL_CLASS'])
          ? (row['TRVL_CLASS']?.toString() ?? 'SL')
          : 'SL';

      _updateQuotaLogic(selectedQuota);

      selectedBookingOption = row['BOOKING_OPTION']?.toString() ?? 'auto_wait';

      passengerCountDb = int.tryParse(row['P_COUNT']?.toString() ?? '1') ?? 1;
      childCountDb = int.tryParse(row['C_COUNT']?.toString() ?? '0') ?? 0;

      _loadMisc(row);
      await _loadSavedCredentials();

      // Debug: Verify journey fields loaded for auto-fill
      // This helps confirm that BOOKING_INFO has a row for the current FORM_NAME
      // and that the UI will reflect these values.
      // You can remove these logs after verification.
      try {
        // ignore: avoid_print
        print(
          'Journey loaded: FROM="${fromStnController.text}" TO="${toStnController.text}" DATE="${journeyDateController.text}" TRAIN="${trainController.text}" CLASS="$selectedClass" QUOTA="$selectedQuota" BOARDING="${boardingController.text}" FARE_LIMIT="${fareLimitController.text}"',
        );
      } catch (_) {}
    }
    setState(() {
      dataLoaded = true;
      isLoading = false;
    });
  }

  void _loadMisc(Map<String, dynamic> row) {
    coachController.text = row['COACH_ID']?.toString() ?? '';
    if (coachController.text.isNotEmpty) {
      coachPreferred = true;
    }

    vikalp =
        row['VIKALP']?.toString() == '1' ||
        row['VIKALP']?.toString().toLowerCase() == 'true';
    autoUpgradation =
        row['AUTO_UPG']?.toString() == '1' ||
        row['AUTO_UPG']?.toString().toLowerCase() == 'true';
    onlyConfirmBerths =
        row['ONLY_CONFIRM']?.toString() == '1' ||
        row['ONLY_CONFIRM']?.toString().toLowerCase() == 'true';

    int bookingOptValue =
        int.tryParse(row['BOOKING_OPT']?.toString() ?? '0') ?? 0;
    switch (bookingOptValue) {
      case 0:
        bookingOption = 'none';
        break;
      case 1:
        bookingOption = 'same_coach';
        break;
      case 2:
        bookingOption = 'min_lower_berth';
        break;
      case 3:
        bookingOption = 'confirm_2_lower';
        break;
      default:
        bookingOption = 'none';
    }

    mobileController.text = row['MOBILE_NO']?.toString() ?? '';

    int paymentModeIndex =
        int.tryParse(row['WB_PYMT_MODE']?.toString() ?? '0') ?? 0;
    if (paymentModeIndex >= 0 && paymentModeIndex < paymentModeItems.length) {
      paymentModeWeb = paymentModeItems[paymentModeIndex];
    }

    if (_tabController.index == 0 &&
        row['CAPTCHA_AUTOFILL']?.toString() == '1' &&
        SplashActivity.isGoldUser == 2) {
      autofillCaptchas = true;
    }

    _loadInsuranceData();
  }

  Future<void> _loadPassengers() async {
    final db = await PassengerDB.instance.database;
    final result = await db.query(
      PassengerDB.tableName,
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );
    setState(() {
      passengers = result.map((row) => PassengerModel.fromMap(row)).toList();
      if (passengers.isEmpty) passengers.add(PassengerModel());
      if (passengers.length > maxPassengers) {
        passengers = passengers.sublist(0, maxPassengers);
      }
      passengersLoaded = true;
    });
  }

  Future<void> _loadChildren() async {
    final db = await ChildDB.instance.database;
    final result = await db.query(
      ChildDB.tableName,
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );

    setState(() {
      children = result
          .map(
            (row) => ChildModel(
              name: row['NAME']?.toString() ?? '',
              age: _getKeyFromChildAgeValue(row['AGE']?.toString() ?? '0'),
              gender: _getKeyFromGenderValue(row['GENDER']?.toString() ?? 'M'),
            ),
          )
          .toList();

      childCountDb = children.length;
      childCount = children.length;

      if (children.isEmpty) {
        children.add(ChildModel());
        childCount = 0;
      }

      childrenLoaded = true;
      // Java: equivalent to main_layoutCh.setVisibility based on children existence
      _isChildSectionVisible = children.isNotEmpty;
    });
  }

  String _getKeyFromChildAgeValue(String value) {
    for (final entry in childAgeMap.entries) {
      if (entry.value.toString() == value) {
        return entry.key;
      }
    }
    return childAgeMap.keys.first;
  }

  String _getKeyFromGenderValue(String value) {
    const genderMap = {'M': 'Male', 'F': 'Female', 'T': 'Transgender'};
    return genderMap[value] ?? 'Male';
  }

  Future<bool> _checkInternetConnection() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  Future<bool> _shouldShowTatkalTimeAlert() async {
    if (selectedQuota != 'Tatkal' && selectedQuota != 'Premium Tatkal')
      return false;

    DateTime now = DateTime.now();
    int openHour =
        (selectedClass == 'SL' ||
            selectedClass == 'FC' ||
            selectedClass == '2S')
        ? 11
        : 10;
    int openMin = 0;
    int allowHour = openHour;
    int allowMin = openMin;
    DateTime allowTime = DateTime(
      now.year,
      now.month,
      now.day,
      allowHour,
      allowMin,
    );

    if (now.isBefore(allowTime)) {
      bool res =
          await showDialog<bool>(
            context: context,
            barrierDismissible: false,
            builder: (_) => AlertDialog(
              backgroundColor: kDialogColor,
              title: const Text(
                'Tatkal Booking Time',
                style: TextStyle(color: Colors.white),
              ),
              content: Text(
                'Tatkal/premium bookings open at ${openHour}:00 AM.\nYou appear to be booking before the recommended time.'
                '\n\nDo you still want to continue?',
                style: const TextStyle(color: Colors.white70),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text(
                    'Wait',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text(
                    'Continue',
                    style: TextStyle(color: Colors.amber),
                  ),
                ),
              ],
            ),
          ) ??
          false;
      return !res;
    }
    return false;
  }

  Future<void> _bookNowFlow() async {
    try {
      if (!await _checkInternetConnection()) {
        _showErrorSnack('No internet connection');
        return;
      }

      // Manual payment mode check & confirmation


      final username = usernameController.text.trim();
      final password = passwordController.text;
      if (username.isNotEmpty && password.isNotEmpty) {
        await _saveCredentials();
      }
      MixpanelManager().track('Click Book Now', {
        'Tab': _tabController.index == 0 ? 'Web' : 'RC',
        'PaymentMethod':
            getPymtChoiceMap(context)[checkedPymtMeth] ??
            checkedPymtMeth.toString(),
        'Bank': selectedBankName,
      });
      if (await _shouldShowTatkalTimeAlert()) {
        return;
      }

      bool ok = await saveForm(showSnackbar: true);
      if (!ok) return;

      await _showAutomationProcessingDialog();
      MixpanelManager().track('Start Booking', {
        'Form': widget.formName,
        'Language': widget.lang,
        'Tab': 'Web',
      });
      await _startWebsiteBooking();
    } catch (e) {
      _showErrorSnack('Something went wrong');
    }
  }

  Future<void> _showAutomationProcessingDialog() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: kDialogColor,
        title: const Text(
          'Preparing Booking',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(color: Colors.deepPurpleAccent),
            const SizedBox(height: 16),
            const Text(
              'Setting up automation and preparing form data...',
              style: TextStyle(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );

    await Future.delayed(const Duration(seconds: 2));

    Navigator.of(context).pop();
  }

  Future<void> _startWebsiteBooking() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('FORM_NAME', widget.formName);

      AppOpenManager.setBookingInProgress(true);
      AppOpenManager.setAutoLoginInProgress(true, formName: widget.formName);

      print(
        'TabActivity2: Starting website booking for form: ${widget.formName}',
      );

      Get.toNamed(
        '/booking',
        arguments: {
          'url': 'https://www.irctc.co.in/nget/train-search',
          'formName': widget.formName,
          'lang': widget.lang,
          'username': usernameController.text.trim(),
          'password': passwordController.text,
          'autofillEnabled': autofillCaptchas,
          'bookingOption': selectedBookingOption,
          'autoLoginEnabled': true,
          'autoLoginCredentials': {
            'username': usernameController.text.trim(),
            'password': passwordController.text,
          },
        },
      );
    } catch (e) {
      print('TabActivity2: Error starting website booking: $e');
      AppOpenManager.setBookingInProgress(false);
      AppOpenManager.setAutoLoginInProgress(false);
    }
  }

  Future<void> _saveWebsiteJourneyDetails() async {
    final db = await MainDB.instance.database;
    final journeyData = {
      'FROM_STN': fromStnController.text.trim(),
      'TO_STN': toStnController.text.trim(),
      'TRVL_DATE': journeyDateController.text.trim(),
      'TRAIN': trainController.text.trim(),
      'BOARDING': boardingController.text.trim(),
      'QUOTA': selectedQuota,
      'TRVL_CLASS': selectedClass,
      'FARE_LIMIT': fareLimitController.text.trim(),
      'BOOKING_OPTION': selectedBookingOption,
    };

    await db.update(
      MainDB.tableName,
      journeyData,
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );
    print('✅ Journey details saved for form: ${widget.formName}');
  }

  Future<void> _bookNowRCFlow() async {
    try {
      if (!await _checkInternetConnection()) {
        _showErrorSnack('No internet connection');
        return;
      }

      // Manual payment mode check & confirmation
      if (manualPymtCheckboxFlag) {
        final result = await showDialog<bool>(
          context: context,
          builder: (ctx) => AlertDialog(
            backgroundColor: const Color(0xFF1A1A2E),
            title: const Text(
              'Manual Payment Mode',
              style: TextStyle(color: Colors.white),
            ),
            content: const Text(
              'You have selected manual payment mode. During booking:\n\n'
              '• Payment information will NOT be auto-filled\n'
              '• You will need to enter all payment details manually\n'
              '• This may take more time during the booking process\n'
              '• Make sure you have all payment details ready\n\n'
              'Continue with manual payment?',
              style: TextStyle(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(ctx, false),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.red),
                ),
              ),
              TextButton(
                onPressed: () => Navigator.pop(ctx, true),
                child: const Text(
                  'Continue Manual',
                  style: TextStyle(color: Colors.orange),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(ctx, false);
                  setState(() {
                    manualPymtCheckboxFlag = false;
                    _reloadPaymentDataFromDatabase();
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Payment autofill enabled. Payment data reloaded.',
                      ),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                child: const Text(
                  'Enable Autofill',
                  style: TextStyle(color: Colors.green),
                ),
              ),
            ],
          ),
        );

        if (result != true) {
          // Don't proceed to booking
          return;
        }
      }

      MixpanelManager().track('Click Book Now', {
        'Tab': 'RailConnect',
        'PaymentMethod':
            getPymtChoiceMap(context)[checkedPymtMeth] ??
            checkedPymtMeth.toString(),
        'Bank': selectedBankName,
      });
      bool ok = await saveFormRC(showSnackbar: true);
      if (!ok) return;
      MixpanelManager().track('Start Booking', {
        'Form': widget.formName,
        'Tab': ' RailConnect',
      });
      await _launchRailConnectExternal();
    } catch (e) {
      _showErrorSnack('Something went wrong');
    }
  }

  Future<void> _launchRailConnectExternal() async {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: kDialogColor,
        title: const Text(
          'RailConnect Launch (Placeholder)',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Would launch the RailConnect app here.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK', style: TextStyle(color: Colors.amber)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Wrap the main Scaffold with WillPopScope to enable system back navigation
    return WillPopScope(
      onWillPop: () async => true,
      child: Scaffold(
        backgroundColor: kPrimaryColor,
        appBar: AppBar(
          title: Text(
            widget.formName,
            style: const TextStyle(
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w600,
              fontSize: 18,
              color: Colors.white,
            ),
          ),
          centerTitle: false,
          backgroundColor: const Color(0xFF1F1F2A),
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.white, size: 22),
          actionsIconTheme: const IconThemeData(color: Colors.white, size: 22),
          toolbarHeight: 48,
          actions: [
            IconButton(
              tooltip: _showLoginPassword ? 'Hide passwords' : 'Show passwords',
              icon: Icon(
                  _showLoginPassword ? Icons.visibility_off : Icons.visibility),
              onPressed: _toggleLoginPasswordVisibility,
            ),
            IconButton(
              tooltip: 'Save',
              icon: const Icon(Icons.save),
              onPressed: _saveAllFormSections,
            ),
            IconButton(
              tooltip: _isFormLocked ? 'Unlock' : 'Lock',
              icon: Icon(_isFormLocked ? Icons.lock_open : Icons.lock),
              onPressed: _handleLockUnlock,
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.yellow,
            tabs: const [
              Tab(text: "WEBSITE"),
              Tab(text: "RAILCONNECT"),
            ],
            onTap: (int tabIdx) async {
              if (tabIdx == 1) {
                await loadFormRC();
              }
            },
          ),
        ),
        body: Stack(
          children: [
            Column(
              children: [
                if (showNoCreditsInfo)
                  Container(
                    color: Colors.grey[900],
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.grey[600],
                          size: 30,
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            'You have no credits, Only demo booking available. To enjoy uninterrupted bookings, Upgrade now 👉',
                            style: TextStyle(
                              color: Colors.orange[600],
                              fontSize: 13,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [_buildWebTab(), _buildRCTab()],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(12),
                  child: GestureDetector(
                    onTap: () {
                      MixpanelManager().track('Click Book Now', {
                        'Ticket Booking Method': _tabController.index == 0
                            ? 'Website'
                            : 'RailConnect',
                        'FormName': widget.formName,
                      });
                      if (_tabController.index == 0) {
                        _bookNowFlow();
                      } else {
                        _bookNowRCFlow();
                      }
                    },
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(0),

                      ),
                      child: const Center(
                        child: Text(
                          'Book Now',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontFamily: 'Poppins',
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  height: 60,
                  color: Colors.black,
                  alignment: Alignment.center,
                  child: const Text(
                    'Ad Banner Here',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              ],
            ),
            if (isLoading)
              Container(
                color: Colors.black.withOpacity(0.8),
                child: const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWebTab() {
    if (!dataLoaded) {
      return const Center(child: CircularProgressIndicator());
    }
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _sectionCredentials(),
          _sectionJourneyDetails(),
          _sectionAds(),
          _sectionPassengers(),
          _sectionChildren(),
          _sectionGST(),
          _sectionMiscellaneous(),
          // Hide payment method selection when manual payment is selected
          if (!manualPymtCheckboxFlag) _sectionPaymentMethod(),
          if (!manualPymtCheckboxFlag) _buildSelectedPaymentDetails(),
          _sectionAds(),
        ],
      ),
    );
  }

  Widget _buildRCTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _sectionCredentials(rc: true),
          _sectionJourneyDetailsRC(),
          _sectionAds(),
          _sectionPassengers(),
          _sectionChildren(),
          _sectionGSTRC(),
          _sectionMiscellaneousRC(),
          // Hide payment method selection when manual payment is selected
          if (!manualPymtCheckboxFlagRC) _sectionPaymentMethodRC(),
          if (!manualPymtCheckboxFlagRC) _buildSelectedPaymentDetailsRC(),
          _sectionAds(),
        ],
      ),
    );
  }

  Widget _sectionJourneyDetailsRC() {
    return _sectionJourneyDetails();
  }

  Widget _sectionGSTRC() {
    return _sectionGST();
  }

  Widget _sectionMiscellaneousRC() {
    return _sectionMiscellaneous();
  }

  Widget _sectionPaymentMethodRC() {
    final rcRadioItems = [
      {'index': 10, 'label': 'IRCTC eWallet'},
      {'index': 7, 'label': 'Wallets / Cash Card'},
      {'index': 9, 'label': 'Net Banking'},
      {'index': 8, 'label': 'Other Pay Methods'},
      {'index': 11, 'label': 'IMudra'},
      {'index': 12, 'label': 'BHIM / UPI'},
    ];
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: buildGradientBorderCard(
        color: kDialogColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.only(left: 10),
              child: Text(
                'RC Payment Method',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 20,
                ),
              ),
            ),
            const SizedBox(height: 8),
            // RC manual payment checkbox (mirrors Java behavior)
            CheckboxListTile(
              value: manualPymtCheckboxFlagRC,
              onChanged: (val) async {
                setState(() => manualPymtCheckboxFlagRC = val ?? false);
                // Persist PYMT_AUTO_RC in BOOKING_INFO
                try {
                  final db = await MainDB.instance.database;
                  await db.update(
                    MainDB.tableName,
                    {'PYMT_AUTO_RC': manualPymtCheckboxFlagRC ? 1 : 0},
                    where: 'FORM_NAME = ?',
                    whereArgs: [widget.formName],
                  );
                } catch (_) {}
              },
              title: const Text(
                'I will fill payment information manually (Rail Connect)',
                style: TextStyle(color: Colors.white),
              ),
              activeColor: const Color(0xFF9C5DF7),
              side: const BorderSide(color: Colors.white54, width: 1.2),
              controlAffinity: ListTileControlAffinity.leading,
              dense: true,
            ),
            if (!manualPymtCheckboxFlagRC) ...[
            const SizedBox(height: 15),
            ...rcRadioItems.map(
              (item) => RadioListTile<int>(
                value: item['index'] as int,
                groupValue: checkedPymtMeth,
                activeColor: const Color(0xFF9C5DF7),
                title: Text(
                  item['label'] as String,
                  style: const TextStyle(color: Colors.white),
                ),
                onChanged: (idx) {
                  rcPymtChanged(idx!);
                },
              ),
            ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedPaymentDetailsRC() {
    if (showCardSection) return _sectionPayByCard();
    if (showWalletSection) return _sectionPayByWallet();
    if (showNetbankSection) return _sectionPayByNetBanking();
    if (showIMudraSection) return _sectionPayIMudra();
    if (showUPISection) return _sectionPayByUpiRC();
    if (showMultipleSection) return _sectionPayMultiple();
    return const SizedBox.shrink();
  }

  void rcPymtChanged(int index) {
    setState(() {
      checkedPymtMeth = index;

      showCardSection = index == 10;
      showNetbankSection = index == 9;
      showUPISection = index == 12;
      showWalletSection = index == 7;
      showIMudraSection = index == 11;
      showMultipleSection = false;
    });
  }

  Future<bool> saveFormRC({bool showSnackbar = true}) async {
    try {
      formErrors.clear();
      String mobileText = mobileController.text.trim();
      if (mobileText.isEmpty) {
        formErrors['mobile'] = 'Mobile No required';
      } else if (mobileText.length != 10 || int.tryParse(mobileText) == null) {
        formErrors['mobile'] = 'Enter valid 10-digit mobile';
      }
      if (rcPinController.text.trim().isEmpty) {
        formErrors['rcPin'] = 'Rail Connect PIN required';
      }
      _validateRCJourneyDetails();
      _validateChildDetails();
      _validateRCMiscellaneous();

      if (formErrors.isNotEmpty) {
        if (showSnackbar) _showBookingFormSummaryError();
        return false;
      }
      try {
        final db = await MainDB.instance.database;
        final rcPin = rcPinController.text.trim();
        String rcPymtMethod = getPymtChoiceMap(context)[checkedPymtMeth] ?? '';
        final vpaRC = vpaRCController.text.trim();
        final upiPinRC = upiPinEdRCController.text.trim();
        final autoOpenUPIVal = autoOpenUPIAppRC ? '1' : '0';
        final automateUPICbVal = automateUPICbRC ? '1' : '0';
        final defaultbankCbVal = defaultbankCbRC ? '1' : '0';
        Map<String, dynamic> updateData = {
          'RC_PIN': Cryptography.encryptPassword(rcPin),
          'RC_UPI_VPA': vpaRC,
          'RC_UPI_PIN': upiPinRC,
          'RC_AUTOMATE_UPI': automateUPICbVal,
          'RC_DEFAULT_BANK': defaultbankCbVal,
        };
        await db.update(
          MainDB.tableName,
          updateData,
          where: 'FORM_NAME = ?',
          whereArgs: [widget.formName],
        );
        // Persist PYMT_AUTO_RC according to manual RC checkbox
        await db.update(
          MainDB.tableName,
          {'PYMT_AUTO_RC': manualPymtCheckboxFlagRC ? 1 : 0},
          where: 'FORM_NAME = ?',
          whereArgs: [widget.formName],
        );
        if (showSnackbar) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Rail Connect details saved!'),
              backgroundColor: Colors.green,
            ),
          );
        }
        return true;
      } catch (e) {
        if (showSnackbar) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error saving Rail Connect tab: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return false;
      }
    } catch (e) {
      if (showSnackbar) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Something went wrong'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  Future<void> loadFormRC() async {
    try {
      final db = await MainDB.instance.database;
      final result = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );
      if (result.isNotEmpty) {
        final row = result.first;
        // Initialize RC manual payment flag
        final pAutoRc = row['PYMT_AUTO_RC']?.toString();
        manualPymtCheckboxFlagRC = (pAutoRc == '1');

        rcPinController.text = row['RC_PIN'] != null
            ? Cryptography.decryptPassword(row['RC_PIN'] as String)
            : '';
        vpaRCController.text = row['RC_UPI_VPA']?.toString() ?? '';
        upiPinEdRCController.text = row['RC_UPI_PIN']?.toString() ?? '';
        autoOpenUPIAppRC = row['AUTO_OPEN']?.toString() == '1';
        automateUPICbRC = row['RC_AUTOMATE_UPI']?.toString() == '1';
        defaultbankCbRC = row['RC_DEFAULT_BANK']?.toString() == '1';
        if (row['RC_PYMT_CHOICE'] != null) {
          final rcPymtChoice = row['RC_PYMT_CHOICE'].toString();
          final pymtIdx = getPymtChoiceMap(context).entries
              .firstWhere(
                (e) => e.value == rcPymtChoice,
                orElse: () => const MapEntry(6, 'DEBIT_CARD'),
              )
              .key;
          setState(() {
            checkedPymtMeth = pymtIdx;
            showCardSection = checkedPymtMeth == 10;
            showNetbankSection = checkedPymtMeth == 9;
            showUPISection = checkedPymtMeth == 12;
            showWalletSection = checkedPymtMeth == 7;
            showIMudraSection = checkedPymtMeth == 11;
          });
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading Rail Connect tab: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @deprecated
  Future<void> _onRCBookNow() async {
    _validateRCJourneyDetails();
    _validateChildDetails();
    _validateRCMiscellaneous();
    setState(() {});
    if (formErrors.isNotEmpty) {
      _showBookingFormSummaryError();
      return;
    }
    await saveFormRC(showSnackbar: true);
    _bookNow();
  }

  void _validateRCJourneyDetails() {
    formErrors.clear();
    if (fromStnController.text.trim().isEmpty) {
      formErrors['from'] = 'From station is required';
    }
    if (toStnController.text.trim().isEmpty) {
      formErrors['to'] = 'To station is required';
    }
    if (journeyDateController.text.trim().isEmpty) {
      formErrors['date'] = 'Journey date is required';
    }
    if (selectedQuota.isEmpty) {
      formErrors['quota'] = 'Select a quota';
    }
    if (selectedClass.isEmpty) {
      formErrors['class'] = 'Select a travel class';
    }
    if (trainController.text.trim().isEmpty) {
      formErrors['train'] = 'Train number/name is required';
    }
  }

  void _validateRCMiscellaneous() {
    formErrors.remove('mobile');
    formErrors.remove('rcPin');
    String mobileText = mobileController.text.trim();
    if (mobileText.isEmpty) {
      formErrors['mobile'] = 'Mobile No required';
    } else if (mobileText.length != 10 || int.tryParse(mobileText) == null) {
      formErrors['mobile'] = 'Enter valid 10-digit mobile';
    }
    if (rcPinController.text.trim().isEmpty) {
      formErrors['rcPin'] = 'Rail Connect PIN required';
    }
  }

  Widget _customRadio(
    String value,
    String text,
    String selected,
    void Function(String val) onChanged,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Radio<String>(
          value: value,
          groupValue: selected,
          onChanged: (v) => onChanged(v!),
          activeColor: const Color(0xFF9C5DF7),
        ),
        Flexible(
          child: Text(
            text,
            style: const TextStyle(color: Colors.white, fontSize: 15),
          ),
        ),
      ],
    );
  }

  Widget _sectionHeader(String title) {
    return Container(
      color: Colors.deepPurple.shade700,
      padding: const EdgeInsets.all(12),
      child: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _sectionCredentials({bool rc = false}) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
                left: 22, right: 22, top: 12, bottom: 8),
            child: Text(
              rc ? 'RC Credentials' : 'IRCTC Login',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontFamily: 'Poppins',
                fontSize: 22,
                letterSpacing: 0.5,
              ),
            ),
        ),
        buildGradientBorderCard(
          color: kDialogColor,
          child: StatefulBuilder(
            builder: (context, setState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!rc) ...[
                    const SizedBox(height: 10),
                    const Padding(
                      padding: EdgeInsets.only(left: 30),
                      child: Text(
                        'Username',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                    ),
                    const SizedBox(height: 5),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: TextField(
                        controller: usernameController,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        onEditingComplete: _loadSavedCredentials,
                        onChanged: (val) {
                          if (_credentialsLoaded) {
                            setState(() {
                              passwordController.clear();
                              _credentialsLoaded = false;
                            });
                          }
                          if (_credentialsError.isNotEmpty) {
                            setState(() {
                              _credentialsError = '';
                            });
                          }
                        },
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: kGrayBgEnabled,
                          contentPadding: const EdgeInsets.all(10),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(kBigRadius),
                            borderSide: BorderSide(color: Colors.transparent),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(kBigRadius),
                            borderSide: const BorderSide(
                              color: Colors.deepPurpleAccent,
                            ),
                          ),
                          suffixIcon: usernameController.text.isNotEmpty
                              ? IconButton(
                            icon: const Icon(
                              Icons.refresh,
                              color: Colors.amber,
                              size: 20,
                            ),
                            tooltip: 'Load saved password for this username',
                            onPressed: () async {
                              FocusScope.of(context).requestFocus(FocusNode());
                              await _loadSavedCredentials();
                              setState(() {});
                            },
                          )
                              : null,
                        ),
                      ),
                    ),
                    const SizedBox(height: 10),
                    const Padding(
                      padding: EdgeInsets.only(left: 30),
                      child: Text(
                        'Password',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                    ),
                    const SizedBox(height: 5),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: TextField(
                        controller: passwordController,
                        obscureText: !_showLoginPassword,
                        enableSuggestions: false,
                        autocorrect: false,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        onChanged: (_) {
                          if (!_credentialsLoaded) {
                            setState(() {});
                          }
                          if (_credentialsError.isNotEmpty) {
                            setState(() {
                              _credentialsError = '';
                            });
                          }
                        },
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: kGrayBgEnabled,
                          contentPadding: const EdgeInsets.all(10),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(kBigRadius),
                            borderSide: BorderSide(color: Colors.transparent),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(kBigRadius),
                            borderSide: const BorderSide(color: Colors.white),
                          ),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _showLoginPassword
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                              color: Colors.amber,
                              size: 20,
                            ),
                            onPressed: () =>
                                setState(
                                      () =>
                                  _showLoginPassword = !_showLoginPassword,
                                ),
                          ),
                          errorText: _credentialsError.isNotEmpty
                              ? _credentialsError
                              : null,
                        ),
                      ),
                    ),
                    if (_credentialsLoaded && _credentialsError.isEmpty)
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 32,
                          top: 5,
                          right: 8,
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.check_circle,
                              color: Colors.green,
                              size: 18,
                            ),
                            const SizedBox(width: 5),
                            Text(
                              'Password auto-filled for ${usernameController
                                  .text}.',
                              style: TextStyle(
                                color: Colors.green[300],
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (_isSavingCredentials)
                      const Padding(
                        padding: EdgeInsets.only(left: 36, top: 5, right: 10),
                        child: Row(
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.amber,
                              ),
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Saving credentials...',
                              style: TextStyle(color: Colors.amber),
                            ),
                          ],
                        ),
                      ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton.icon(
                          icon: const Icon(
                            Icons.save,
                            color: Colors.amber,
                            size: 18,
                          ),
                          label: const Text(
                            'Save Credentials',
                            style: TextStyle(color: Colors.amber, fontSize: 13),
                          ),
                          onPressed: _isSavingCredentials
                              ? null
                              : () async {
                            await _saveCredentials();
                            setState(() {});
                          },
                        ),
                        const SizedBox(width: 12),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 30,
                        top: 10,
                        bottom: 6,
                      ),
                      child: Row(
                        children: [
                          Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: 'No IRCTC account? ',
                                  style: TextStyle(
                                    color: Color(0xffcccc00),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 13,
                                  ),
                                ),
                                WidgetSpan(
                                  child: GestureDetector(
                                    onTap: () async {
                                      final url = Uri.parse(
                                        'https://www.irctc.co.in/nget/profile/user-registration',
                                      );
                                      if (await canLaunchUrl(url)) {
                                        await launchUrl(
                                          url,
                                          mode: LaunchMode.externalApplication,
                                        );
                                      } else {
                                        _showErrorSnack(
                                          'Could not launch registration page',
                                        );
                                      }
                                    },
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: const [
                                        Text(
                                          'Register now',
                                          style: TextStyle(
                                            color: Color(0xffcccc00),
                                            fontWeight: FontWeight.bold,
                                            fontSize: 13,
                                            decoration: TextDecoration
                                                .underline,
                                          ),
                                        ),
                                        SizedBox(width: 2),
                                        Icon(
                                          Icons.open_in_new,
                                          size: 13,
                                          color: Color(0xffcccc00),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ] else
                    ...[
                      const SizedBox(height: 10),
                      const Padding(
                        padding: EdgeInsets.only(left: 30),
                        child: Text(
                          'Rail Connect PIN',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 13,
                            fontWeight: FontWeight.normal,
                          ),
                      ),
                    ),
                    const SizedBox(height: 5),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: TextField(
                        controller: rcPinController,
                        obscureText: true,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: Colors.grey[800],
                          contentPadding: const EdgeInsets.all(10),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey[600]!),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(color: Colors.white),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        ),
      ],
    ),
  );
  }

  void _updateQuotaLogic(String quota) {
    setState(() {
      showFareLimit = (quota == 'Premium Tatkal');

      if (quota.contains('Tatkal')) {
        maxPassengers = 4;
      } else {
        maxPassengers = 6;
      }
      if (passengers.length > maxPassengers) {
        passengers = passengers.sublist(0, maxPassengers);
      }

      tatkalHint = '';
      if (quota.contains('Tatkal')) {
        String selectedClassLocal = selectedClass;
        bool isSLClass =
            (selectedClassLocal == 'SL' ||
            selectedClassLocal == 'FC' ||
            selectedClassLocal == '2S');
        if (isSLClass) {
          tatkalHint =
              'It is recommended to start booking after 10:58 AM to avoid logout.';
        } else {
          tatkalHint =
              'It is recommended to start booking after 09:58 AM to avoid logout.';
        }
      }

      if (quota == 'Physically Handicapped') {}
    });
  }

  Widget _sectionJourneyDetails() {
    if (!stationsLoaded || !trainsLoaded) {
      return Container(
        margin: const EdgeInsets.only(top: 40),
        child: Center(child: CircularProgressIndicator()),
      );
    }
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
      const Padding(
      padding: EdgeInsets.only(left: 4, top: 0, bottom: 10),
      child: Text(
        'Journey Details',
        style: TextStyle(
          fontWeight: FontWeight.w700,
          color: Colors.white,
          fontSize: 18,
          fontFamily: 'Poppins',
              ),
            ),
    ),
    buildGradientBorderCard(
    color: Color(0xFF181728),
    child: Padding(
    padding: const EdgeInsets.all(12.5),
    child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
    const SizedBox(height: 0),
              _labelField('From'),
              _stackedInput(
                Autocomplete<String>(
                  optionsBuilder: (TextEditingValue t) => t.text.isEmpty
                      ? const Iterable<String>.empty()
                      : stationList.where(
                          (item) =>
                              item.toLowerCase().contains(t.text.toLowerCase()),
                        ),
                  fieldViewBuilder: (context, controller, focusNode, onEd) {
                    controller.text = fromStnController.text;
                    controller.selection = TextSelection.fromPosition(
                      TextPosition(offset: controller.text.length),
                    );
                    return TextField(
                      controller: controller,
                      focusNode: focusNode,
                      style: const TextStyle(color: Colors.white),
                      textCapitalization: TextCapitalization.characters,
                      decoration: _inputFill(),
                      onChanged: (s) {
                        fromStnController.text = s;
                        _saveWebsiteJourneyDetails();
                      },
                    );
                  },
                  onSelected: (v) {
                    fromStnController.text = v;
                    setState(() {});
                    _saveWebsiteJourneyDetails();
                  },
                ),
              ),
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 3),
                  child: Icon(Icons.swap_vert, color: Colors.white70, size: 22),
                ),
              ),
              _labelField('To'),
              _stackedInput(
                Autocomplete<String>(
                  optionsBuilder: (TextEditingValue t) => t.text.isEmpty
                      ? const Iterable<String>.empty()
                      : stationList.where(
                          (item) =>
                              item.toLowerCase().contains(t.text.toLowerCase()),
                        ),
                  fieldViewBuilder: (context, controller, focusNode, onEd) {
                    controller.text = toStnController.text;
                    controller.selection = TextSelection.fromPosition(
                      TextPosition(offset: controller.text.length),
                    );
                    return TextField(
                      controller: controller,
                      focusNode: focusNode,
                      style: const TextStyle(color: Colors.white),
                      textCapitalization: TextCapitalization.characters,
                      decoration: _inputFill(),
                      onChanged: (s) {
                        toStnController.text = s;
                        _saveWebsiteJourneyDetails();
                      },
                    );
                  },
                  onSelected: (v) {
                    toStnController.text = v;
                    setState(() {});
                    _saveWebsiteJourneyDetails();
                  },
                ),
              ),
              _labelField('Date'),
              _stackedInput(
                TextField(
                  controller: journeyDateController,
                  readOnly: true,
                  style: TextStyle(color: Colors.white),
                  decoration: _inputFill(icon: Icons.calendar_today),
                  onTap: _showDatePicker,
                ),
              ),
              _labelField('Class'),
              _stackedInput(
                DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: classOptions.contains(selectedClass)
                        ? selectedClass
                        : classOptions.first,
                    isExpanded: true,
                    dropdownColor: Color(0xFF28243D),
                    style: const TextStyle(color: Colors.white),
                    items: classOptions
                        .map(
                          (c) => DropdownMenuItem<String>(
                            value: c,
                            child: Text(c),
                          ),
                        )
                        .toList(),
                    onChanged: (v) => setState(() {
                      selectedClass = v ?? classOptions.first;
                      _updateQuotaLogic(selectedQuota);
                      _saveWebsiteJourneyDetails();
                    }),
                  ),
                ),
              ),
              _labelField('Quota'),
              _stackedInput(
                DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: quotaOptions.contains(selectedQuota)
                        ? selectedQuota
                        : quotaOptions.first,
                    isExpanded: true,
                    dropdownColor: Color(0xFF28243D),
                    style: const TextStyle(color: Colors.white),
                    items: quotaOptions
                        .map(
                          (q) => DropdownMenuItem<String>(
                            value: q,
                            child: Text(q),
                          ),
                        )
                        .toList(),
                    onChanged: (v) => setState(() {
                      selectedQuota = v ?? quotaOptions.first;
                      _updateQuotaLogic(selectedQuota);
                      _saveWebsiteJourneyDetails();
                    }),
                  ),
                ),
              ),
              _labelField('Fare Limit'),
              _stackedInput(
                TextField(
                  controller: fareLimitController,
                  keyboardType: TextInputType.number,
                  style: TextStyle(color: Colors.white),
                  decoration: _inputFill(hint: 'Optional'),
                  onChanged: (_) => _saveWebsiteJourneyDetails(),
                ),
              ),
              _labelField('Train No'),
              _stackedInput(
                Autocomplete<String>(
                  optionsBuilder: (TextEditingValue t) => t.text.isEmpty
                      ? const Iterable<String>.empty()
                      : trainList.where(
                          (item) =>
                              item.toLowerCase().contains(t.text.toLowerCase()),
                        ),
                  fieldViewBuilder: (context, controller, focusNode, onEd) {
                    controller.text = trainController.text;
                    controller.selection = TextSelection.fromPosition(
                      TextPosition(offset: controller.text.length),
                    );
                    return TextField(
                      controller: controller,
                      focusNode: focusNode,
                      style: const TextStyle(color: Colors.white),
                      textCapitalization: TextCapitalization.characters,
                      decoration: _inputFill(rightIcon: Icons.search),
                      onChanged: (s) {
                        trainController.text = s;
                        _saveWebsiteJourneyDetails();
                      },
                    );
                  },
                  onSelected: (v) {
                    trainController.text = v;
                    setState(() {});
                    _saveWebsiteJourneyDetails();
                  },
                ),
              ),
              _labelField('Boarding Stn'),
              _stackedInput(
                Autocomplete<String>(
                  optionsBuilder: (TextEditingValue t) => t.text.isEmpty
                      ? const Iterable<String>.empty()
                      : stationList.where(
                          (item) =>
                              item.toLowerCase().contains(t.text.toLowerCase()),
                        ),
                  fieldViewBuilder: (context, controller, focusNode, onEd) {
                    controller.text = boardingController.text;
                    controller.selection = TextSelection.fromPosition(
                      TextPosition(offset: controller.text.length),
                    );
                    return TextField(
                      controller: controller,
                      focusNode: focusNode,
                      style: const TextStyle(color: Colors.white),
                      textCapitalization: TextCapitalization.characters,
                      decoration: _inputFill(hint: 'Optional'),
                      onChanged: (s) {
                        boardingController.text = s;
                        _saveWebsiteJourneyDetails();
                      },
                    );
                  },
                  onSelected: (v) {
                    boardingController.text = v;
                    setState(() {});
                    _saveWebsiteJourneyDetails();
                  },
                ),
              ),
            ],
          ),
        ),
    ),
          ],
      ),
    );
  }

  Widget _stackedInput(Widget child, {double height = 44}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 13),
      decoration: BoxDecoration(
        color: Color(0xFF28243D),
        borderRadius: BorderRadius.circular(16),
      ),
      alignment: Alignment.centerLeft,
      height: height,
      child: child,
    );
  }

  InputDecoration _inputFill({
    String? hint,
    IconData? icon,
    IconData? rightIcon,
  }) {
    return InputDecoration(
      counterText: '',
      filled: true,
      fillColor: kGrayBgEnabled,
      hintText: hint ?? '',
      hintStyle: const TextStyle(color: Colors.white54),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide.none,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 15),
      suffixIcon: icon != null
          ? Icon(icon, color: Colors.white70, size: 20)
          : (rightIcon != null
                ? Icon(rightIcon, color: Colors.white70, size: 20)
                : null),
    );
  }

  Widget _roundedJourneyInput(Widget child) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Color(0xFF28243D),
        borderRadius: BorderRadius.circular(16),
      ),
      child: child,
    );
  }

  // Java: private void showDatePicker(int dateType)
  void showCustomDatePicker(
    int dateType, {
    TextEditingController? targetController,
  }) async {
    DateTime now = DateTime.now();

    DateTime firstDate;
    DateTime lastDate;
    String title = '';

    if (dateType == 1) {
      title = 'Card Validity';
      firstDate = now;
      lastDate = now.add(
        const Duration(days: 3650),
      ); // 10 years for card validity
    } else if (dateType == 2) {
      title = 'Date of Birth';
      firstDate = DateTime(now.year - 100);
      lastDate = now;
    } else {
      title = 'Journey Date';
      firstDate = now;
      lastDate = now.add(Duration(days: AppConsts.Consts.TRAVEL_WINDOW));
    }

    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: now,
      firstDate: firstDate,
      lastDate: lastDate,
      helpText: title,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: const Color(0xFFFE4080)),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      onDateSet(picked.year, picked.month - 1, picked.day);

      if (targetController != null) {
        targetController.text =
            "${picked.day.toString().padLeft(2, '0')}-${picked.month.toString().padLeft(2, '0')}-${picked.year}";
      }
    }
  }

  // Java: @Override public void onDateSet(DatePickerDialog view, int year, int monthOfYear, int dayOfMonth)
  void onDateSet(int year, int monthOfYear, int dayOfMonth) {
    String day;
    if (dayOfMonth < 10) {
      day = "0$dayOfMonth";
    } else {
      day = dayOfMonth.toString();
    }

    String month;
    monthOfYear++; // Java increments monthOfYear since it's 0-based
    if (monthOfYear < 10) {
      month = "0$monthOfYear";
    } else {
      month = monthOfYear.toString();
    }

    String date = "$day-$month-$year";
    journeyDateController.text = date;
  }

  // Backward compatibility function for existing _showDatePicker() calls
  void _showDatePicker() {
    // Call the enhanced showCustomDatePicker with dateType 0 (journey date)
    showCustomDatePicker(0);
  }

  // Java: private void addPassenger(String pQuota)
  void addPassenger(String pQuota) async {
    // Java: if (passengerCount < limit)
    if (passengerCount < maxPassengers) {
      // Java: RelativeLayout child = (RelativeLayout) View.inflate(context, R.layout.passenger_copy, null);
      // Java: main_layout.addView(child, passengerCount + 1);
      setState(() {
        passengers.add(PassengerModel());
        passengerCount++;
      });

      // Java: Animation animation = AnimationUtils.loadAnimation(context, R.anim.slide_down);
      // Java: animation.setStartOffset(0);
      // Java: child.startAnimation(animation);
      // Note: Flutter animations are handled in UI layer with AnimatedList or similar

      // Java: View cView = main_layout.getChildAt(passengerCount + 1);
      // In Flutter, the passenger fields are handled through PassengerModel controllers and properties
      // The complex field assignments and event handlers from Java are managed in the UI widgets

      // Java: InputFilter filter = new InputFilter() { ... }
      // Java: pName[passengerCount].setFilters(new InputFilter[]{filter, new InputFilter.LengthFilter(16)});
      // Note: Flutter TextField InputFormatters handle this in UI

      // Java: final int pCountFinal = passengerCount;
      final int pCountFinal =
          passengerCount - 1; // Adjust for 0-based indexing in Flutter

      // Java: remove.setOnClickListener - handled in UI through _removePassengerWithAnimation

      // Java: String mQuota; if (quota == null) { mQuota = pQuota; } else { mQuota = quota.getSelectedItem().toString(); }
      String mQuota = pQuota; // Use provided quota or get from UI state

      // Java: if (mQuota.equals("Physically Handicapped")) - handle visibility of concession fields
      if (mQuota == "Physically Handicapped") {
        // Set passenger concession fields visibility - handled in PassengerModel and UI
        if (pCountFinal < passengers.length) {
          // Java: concession[passengerCount].setVisibility(View.VISIBLE);
          // Java: nationality[passengerCount].setVisibility(View.VISIBLE);
          // Java: cardNoLbl[passengerCount].setVisibility(View.VISIBLE);
          // Java: cardNoValue[passengerCount].setVisibility(View.VISIBLE);
          // Java: cardDetails[passengerCount].setVisibility(View.VISIBLE);
          // Java: pDob[passengerCount].setVisibility(View.VISIBLE);
          // Note: These visibility states are managed in Flutter UI based on passenger.concession value
        }
      }

      // Java: nationality[passengerCount].setSelection(104); - Set India as default
      if (pCountFinal < passengers.length) {
        passengers[pCountFinal].nationality = "India";
      }

      // All the complex event listeners (nationality spinner, concession spinner, gender spinner, age input, etc.)
      // from Java are handled in Flutter through onChange callbacks in the UI widgets
      // Java: nationality[passengerCount].setOnItemSelectedListener
      // Java: concession[passengerCount].setOnItemSelectedListener
      // Java: pGender[passengerCount].setOnItemSelectedListener
      // Java: pAge[passengerCount].setOnKeyListener
      // Java: pDob[passengerCount].setOnClickListener - calls showDatePicker(2) for DOB
      // Java: cardValidity[passengerCount].setOnClickListener - calls showDatePicker(1) for card validity

      await _savePassengerList();
    } else {
      // Java: if (limit == 6) { Toast.makeText(...max_6_passengers...); } else { Toast.makeText(...max_4_passengers...); }
      String message;
      if (maxPassengers == 6) {
        message =
            "Maximum 6 passengers allowed"; // Java: R.string.max_6_passengers
      } else {
        message =
            "Maximum 4 passengers allowed"; // Java: R.string.max_4_passengers
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), duration: const Duration(seconds: 2)),
      );
    }
  }

  Widget _sectionPassengers() {
    final genderOptions = ['Male', 'Female', 'Transgender'];
    final berthOptions = [
      'No Preference',
      'Lower',
      'Middle',
      'Upper',
      'Side Lower',
      'Side Upper',
    ];
    final mealOptions = ['No Meal', 'Veg', 'Non-Veg', 'Jain'];
    final concessionOptions = [
      '',
      'Handicapped',
      'Escort',
      'Student',
      'Senior',
    ];
    final nationalityOptions = [
      'India',
      'USA',
      'UK',
      'Canada',
      'Australia',
      'Other',
    ];
    final idTypeOptions = [
      '',
      'Aadhaar Card',
      'PAN Card',
      'Voter Card',
      'Driving License',
      'Passport',
    ];

    if (!passengersLoaded) {
      return const Padding(
        padding: EdgeInsets.all(24),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.only(left: 5, top: 0, bottom: 8),
            child: Text(
              'Passenger Details',
              style: TextStyle(
                fontWeight: FontWeight.w700,
                color: Colors.white,
                fontSize: 18,
                fontFamily: 'Poppins',
                letterSpacing: 0.5,
              ),
            ),
          ),
          buildGradientBorderCard(
            color: const Color(0xFF181728),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...List.generate(passengers.length, (i) {
              final pm = passengers[i];
              return Stack(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(right: 0, top: 2),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              flex: 6,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.only(
                                      left: 2,
                                      bottom: 2,
                                    ),
                                    child: Text(
                                      'Name',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 13.5,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  _fieldBox(
                                    TextField(
                                      controller: pm.nameCtrl,
                                      maxLength: 16,
                                      style: const TextStyle(
                                        color: Colors.white,
                                      ),
                                      decoration: _inputFill(),
                                      textCapitalization:
                                          TextCapitalization.words,
                                      onChanged: (v) async {
                                        setState(() {});
                                        await _savePassengerList();
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              flex: 4,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.only(
                                      left: 2,
                                      bottom: 2,
                                    ),
                                    child: Text(
                                      'Age',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 13.5,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  _fieldBox(
                                    TextField(
                                      controller: pm.ageCtrl,
                                      maxLength: 3,
                                      keyboardType: TextInputType.number,
                                      style: const TextStyle(
                                        color: Colors.white,
                                      ),
                                      decoration: _inputFill(),
                                      onChanged: (v) async {
                                        setState(() {});
                                        await _savePassengerList();
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        if (pm.error.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(
                              left: 2,
                              top: 2,
                              bottom: 3,
                            ),
                            child: Text(
                              pm.error,
                              style: const TextStyle(
                                color: Colors.redAccent,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.only(
                                      left: 2,
                                      bottom: 2,
                                      top: 2,
                                    ),
                                    child: Text(
                                      'Opt Berth',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 13.5,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  _fieldBox(
                                    Row(
                                      children: [
                                        Checkbox(
                                          value: pm.optBerth,
                                          onChanged: null,
                                          activeColor: const Color(0xFF9C5DF7),
                                          side: const BorderSide(
                                            color: Colors.white38,
                                            width: 1.2,
                                          ),
                                          visualDensity: VisualDensity.compact,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.only(
                                      left: 2,
                                      bottom: 2,
                                      top: 2,
                                    ),
                                    child: Text(
                                      'Gender',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 13.5,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  _fieldBox(
                                    DropdownButtonHideUnderline(
                                      child: DropdownButton<String>(
                                        value: pm.gender.isNotEmpty
                                            ? pm.gender
                                            : genderOptions[0],
                                        items: genderOptions
                                            .map(
                                              (v) => DropdownMenuItem<String>(
                                                value: v,
                                                child: Text(
                                                  v,
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ),
                                            )
                                            .toList(),
                                        dropdownColor: kGrayBgEnabled,
                                        onChanged: (val) async {
                                          setState(() {
                                            pm.gender = val ?? genderOptions[0];
                                          });
                                          await _savePassengerList();
                                        },
                                        isExpanded: true,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.only(
                                      left: 2,
                                      bottom: 2,
                                      top: 2,
                                    ),
                                    child: Text(
                                      'Berth',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 13.5,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  _fieldBox(
                                    DropdownButtonHideUnderline(
                                      child: DropdownButton<String>(
                                        value: pm.berth.isNotEmpty
                                            ? pm.berth
                                            : berthOptions[0],
                                        items: berthOptions
                                            .map(
                                              (v) => DropdownMenuItem<String>(
                                                value: v,
                                                child: Text(
                                                  v,
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ),
                                            )
                                            .toList(),
                                        dropdownColor: kGrayBgEnabled,
                                        onChanged: (val) async {
                                          setState(
                                            () => pm.berth =
                                                val ?? berthOptions[0],
                                          );
                                          await _savePassengerList();
                                        },
                                        isExpanded: true,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.only(
                                      left: 2,
                                      bottom: 2,
                                      top: 2,
                                    ),
                                    child: Text(
                                      'Meal',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 13.5,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  _fieldBox(
                                    DropdownButtonHideUnderline(
                                      child: DropdownButton<String>(
                                        value: pm.meal.isNotEmpty
                                            ? pm.meal
                                            : mealOptions[0],
                                        items: mealOptions
                                            .map(
                                              (v) => DropdownMenuItem<String>(
                                                value: v,
                                                child: Text(
                                                  v,
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ),
                                            )
                                            .toList(),
                                        dropdownColor: kGrayBgEnabled,
                                        onChanged: (val) async {
                                          setState(
                                            () =>
                                                pm.meal = val ?? mealOptions[0],
                                          );
                                          await _savePassengerList();
                                        },
                                        isExpanded: true,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.only(
                                      left: 2,
                                      bottom: 2,
                                      top: 2,
                                    ),
                                    child: Text(
                                      'Concession',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 13.5,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  _fieldBox(
                                    DropdownButtonHideUnderline(
                                      child: DropdownButton<String>(
                                        value: pm.concession,
                                        items: concessionOptions
                                            .map(
                                              (v) => DropdownMenuItem<String>(
                                                value: v,
                                                child: Text(
                                                  v.isEmpty ? "None" : v,
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ),
                                            )
                                            .toList(),
                                        dropdownColor: kGrayBgEnabled,
                                        onChanged: (val) async {
                                          setState(
                                            () => pm.concession = val ?? "",
                                          );
                                          await _savePassengerList();
                                        },
                                        isExpanded: true,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.only(
                                      left: 2,
                                      bottom: 2,
                                      top: 2,
                                    ),
                                    child: Text(
                                      'Bed Roll',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 13.5,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  _fieldBox(
                                    Row(
                                      children: [
                                        Checkbox(
                                          value: pm.bedroll,
                                          onChanged: (v) => setState(
                                            () => pm.bedroll = v ?? false,
                                          ),
                                          activeColor: const Color(0xFF9C5DF7),
                                          side: const BorderSide(
                                            color: Colors.white38,
                                            width: 1.2,
                                          ),
                                          visualDensity: VisualDensity.compact,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.only(
                                      left: 2,
                                      bottom: 2,
                                      top: 2,
                                    ),
                                    child: Text(
                                      'Nationality',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 13.5,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  _fieldBox(
                                    DropdownButtonHideUnderline(
                                      child: DropdownButton<String>(
                                        value: pm.nationality.isNotEmpty
                                            ? pm.nationality
                                            : nationalityOptions[0],
                                        items: nationalityOptions
                                            .map(
                                              (v) => DropdownMenuItem<String>(
                                                value: v,
                                                child: Text(
                                                  v,
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ),
                                            )
                                            .toList(),
                                        dropdownColor: kGrayBgEnabled,
                                        onChanged: (val) async {
                                          setState(() {
                                            pm.nationality =
                                                val ?? nationalityOptions[0];
                                          });
                                          await _savePassengerList();
                                        },
                                        isExpanded: true,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (pm.nationality != 'India') ...[
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Padding(
                                      padding: EdgeInsets.only(
                                        left: 2,
                                        bottom: 2,
                                        top: 2,
                                      ),
                                      child: Text(
                                        'ID Type',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 13.5,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    _fieldBox(
                                      DropdownButtonHideUnderline(
                                        child: DropdownButton<String>(
                                          value: pm.idType,
                                          items: idTypeOptions
                                              .map(
                                                (v) => DropdownMenuItem<String>(
                                                  value: v,
                                                  child: Text(
                                                    v.isEmpty
                                                        ? "Select ID Type"
                                                        : v,
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                ),
                                              )
                                              .toList(),
                                          dropdownColor: kGrayBgEnabled,
                                          onChanged: (val) async {
                                            setState(
                                              () => pm.idType = val ?? "",
                                            );
                                            await _savePassengerList();
                                          },
                                          isExpanded: true,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                        if (pm.nationality != 'India') ...[
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Padding(
                                      padding: EdgeInsets.only(
                                        left: 2,
                                        bottom: 2,
                                        top: 2,
                                      ),
                                      child: Text(
                                        'ID Number',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 13.5,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    _fieldBox(
                                      TextField(
                                        controller: TextEditingController(
                                          text: pm.idNo,
                                        ),
                                        onChanged: (v) async {
                                          setState(() => pm.idNo = v);
                                          await _savePassengerList();
                                        },
                                        maxLength: 16,
                                        style: const TextStyle(
                                          color: Colors.white,
                                        ),
                                        decoration: _inputFill(hint: 'Card No'),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                        if (pm.concession == 'Handicapped' ||
                            pm.concession == 'Escort') ...[
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Padding(
                                      padding: EdgeInsets.only(
                                        left: 2,
                                        bottom: 2,
                                        top: 2,
                                      ),
                                      child: Text(
                                        'Card No',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 13.5,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    _fieldBox(
                                      TextField(
                                        controller: TextEditingController(
                                          text: pm.cardNo,
                                        ),
                                        onChanged: (v) async {
                                          setState(() => pm.cardNo = v);
                                          await _savePassengerList();
                                        },
                                        style: const TextStyle(
                                          color: Colors.white,
                                        ),
                                        decoration: const InputDecoration(
                                          filled: true,
                                          fillColor: kGrayBgEnabled,
                                          border: InputBorder.none,
                                          enabledBorder: InputBorder.none,
                                          focusedBorder: InputBorder.none,
                                          contentPadding: EdgeInsets.symmetric(
                                            horizontal: 10,
                                            vertical: 3,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Padding(
                                      padding: EdgeInsets.only(
                                        left: 2,
                                        bottom: 2,
                                        top: 2,
                                      ),
                                      child: Text(
                                        'Card Validity',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 13.5,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    _fieldBox(
                                      Stack(
                                        children: [
                                          TextField(
                                            controller: TextEditingController(
                                              text: pm.cardValidity,
                                            ),
                                            readOnly: true,
                                            onTap: () async {
                                              final now = DateTime.now();
                                              final picked =
                                                  await showDatePicker(
                                                    context: context,
                                                    initialDate: now,
                                                    firstDate: now,
                                                    lastDate: now.add(
                                                      const Duration(
                                                        days: 3650,
                                                      ),
                                                    ),
                                                    builder: (context, child) =>
                                                        Theme(
                                                          data:
                                                              ThemeData.dark(),
                                                          child: child!,
                                                        ),
                                                  );
                                              if (picked != null) {
                                                setState(() {
                                                  pm.cardValidity =
                                                      "${picked.day.toString().padLeft(2, '0')}-${picked.month.toString().padLeft(2, '0')}-${picked.year}";
                                                });
                                                await _savePassengerList();
                                              }
                                            },
                                            style: const TextStyle(
                                              color: Colors.white,
                                            ),
                                            decoration: _inputFill(),
                                          ),
                                          Positioned(
                                            right: 7,
                                            top: 4,
                                            bottom: 4,
                                            child: Icon(
                                              Icons.calendar_today,
                                              color: Colors.white70,
                                              size: 17,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Padding(
                                      padding: EdgeInsets.only(
                                        left: 2,
                                        bottom: 2,
                                        top: 2,
                                      ),
                                      child: Text(
                                        'Date of Birth',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 13.5,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    _fieldBox(
                                      Stack(
                                        children: [
                                          TextField(
                                            readOnly: true,
                                            controller: TextEditingController(
                                              text: pm.dob,
                                            ),
                                            onTap: () async {
                                              final initial =
                                                  DateTime.tryParse(pm.dob) ??
                                                  DateTime.now().subtract(
                                                    const Duration(
                                                      days: 365 * 18,
                                                    ),
                                                  );
                                              final picked =
                                                  await showDatePicker(
                                                    context: context,
                                                    initialDate: initial,
                                                    firstDate: DateTime(1900),
                                                    lastDate: DateTime.now(),
                                                    builder: (context, child) =>
                                                        Theme(
                                                          data:
                                                              ThemeData.dark(),
                                                          child: child!,
                                                        ),
                                                  );
                                              if (picked != null) {
                                                setState(() {
                                                  pm.dob =
                                                      "${picked.day.toString().padLeft(2, '0')}-${picked.month.toString().padLeft(2, '0')}-${picked.year}";
                                                });
                                                await _savePassengerList();
                                              }
                                            },
                                            style: const TextStyle(
                                              color: Colors.white,
                                            ),
                                            decoration: _inputFill(),
                                          ),
                                          Positioned(
                                            right: 7,
                                            top: 4,
                                            bottom: 4,
                                            child: Icon(
                                              Icons.calendar_today,
                                              color: Colors.white70,
                                              size: 17,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                        const SizedBox(height: 1),
                        const Divider(
                          color: Color(0xFF23233E),
                          thickness: 0.9,
                          height: 14,
                        ),
                      ],
                    ),
                  ),
                  if (passengers.length > 1)
                    Positioned(
                      right: 0,
                      top: 2,
                      child: GestureDetector(
                        onTap: () async {
                          setState(() {
                            passengers.removeAt(i);
                          });
                          await _savePassengerList();
                        },
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(14),
                            boxShadow: [
                              BoxShadow(color: Colors.black26, blurRadius: 4),
                            ],
                          ),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                ],
              );
            }),
                const SizedBox(height: 16),
                if (passengers.length < maxPassengers)
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () async {
                            if (passengers.length >= maxPassengers) return;
                            setState(() {
                              passengers.add(PassengerModel());
                            });
                            await _savePassengerList();
                          },
                          child: Container(
                            height: 46,
                            margin: const EdgeInsets.only(top: 2, bottom: 3),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                              ),
                              borderRadius: BorderRadius.circular(24),
                              boxShadow: [
                                const BoxShadow(
                                  color: Colors.black26,
                                  blurRadius: 6,
                                ),
                              ],
                            ),
                            alignment: Alignment.center,
                            child: const Icon(
                              Icons.add,
                              color: Colors.white,
                              size: 26,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _fieldBox(Widget child) {
    return Container(
      constraints: const BoxConstraints(minHeight: 40),
      decoration: BoxDecoration(
        color: kGrayBgEnabled,
        borderRadius: BorderRadius.circular(20),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      alignment: Alignment.centerLeft,
      child: child,
    );
  }

  Future<void> _savePassengerList() async {
    final db = await PassengerDB.instance.database;
    await db.delete(
      PassengerDB.tableName,
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );
    for (final passenger in passengers) {
      await db.insert(
        PassengerDB.tableName,
        passenger.toMap(formName: widget.formName),
      );
    }
  }

  Future<void> _saveChildList() async {
    final db = await ChildDB.instance.database;
    await db.delete(
      ChildDB.tableName,
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );
    for (final child in children) {
      final genderValue =
          {'Male': 'M', 'Female': 'F', 'Transgender': 'T'}[child.gender] ?? 'M';
      final ageValue = childAgeMap[child.age] ?? 0;

      await db.insert(ChildDB.tableName, {
        'FORM_NAME': widget.formName,
        'NAME': child.name,
        'AGE': ageValue,
        'GENDER': genderValue,
      });
    }
  }

  void _validateChildDetails() {
    bool foundError = false;
    bool smallCName = false;

    int actualChildCount = 0;
    for (final c in children) {
      if (c.name.trim().isNotEmpty ||
          c.age != 'Below one year' ||
          c.gender != 'Male') {
        actualChildCount++;
      }
    }

    for (int i = 0; i < actualChildCount; i++) {
      if (i >= children.length) break;

      final c = children[i];
      c.error = '';

      if (c.name.trim().isEmpty) {
        c.error = 'Child name required';
        foundError = true;
      } else if (c.name.trim().length < 3) {
        c.error = 'Enter min 3 character name';
        foundError = true;
        smallCName = true;
      }
      if (!childAgeOptions.contains(c.age)) {
        c.error = 'Select valid age';
        foundError = true;
      }

      if (!genderOptions.contains(c.gender)) {
        c.error = 'Select gender';
        foundError = true;
      }
    }

    if (smallCName) {
      formErrors['children'] = 'Child name must be at least 3 characters';
    } else if (foundError) {
      formErrors['children'] = 'Please fix child details errors';
    } else {
      formErrors.remove('children');
    }

    childCount = actualChildCount;
  }

  Widget _sectionChildren() {
    if (!childrenLoaded) {
      return const Padding(
        padding: EdgeInsets.all(24),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.only(left: 10),
            child: Text(
              'Child Details',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 20,
              ),
            ),
          ),

          if (children.isNotEmpty) ...[
            ...List.generate(children.length, (i) {
              return Column(
                children: [
                  if (children.length > 1 && i == 0)
                    Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        margin: const EdgeInsets.only(right: 25, top: 10),
                        child: GestureDetector(
                          onTap: () async {
                            await _removeFirstChild();
                          },
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 15,
                            ),
                          ),
                        ),
                      ),
                    ),

                  if (i > 0)
                    Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        margin: const EdgeInsets.only(right: 25, top: 10),
                        child: GestureDetector(
                          onTap: () async {
                            await _removeChildWithAnimation(i);
                          },
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 15,
                            ),
                          ),
                        ),
                      ),
                    ),

                  Container(
                    margin: const EdgeInsets.only(top: 10),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Container(
                      margin: const EdgeInsets.all(1),
                      padding: const EdgeInsets.fromLTRB(20, 15, 23, 15),
                      decoration: BoxDecoration(
                        color: kPrimaryColor,
                        borderRadius: BorderRadius.circular(14),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Padding(
                            padding: EdgeInsets.only(left: 10),
                            child: Text(
                              'Name',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 13,
                              ),
                            ),
                          ),
                          const SizedBox(height: 5),
                          Container(
                            height: 40,
                            child: TextField(
                              controller: TextEditingController(
                                text: children[i].name,
                              ),
                              onChanged: (v) async {
                                setState(() => children[i].name = v);
                                await _saveChildList();
                              },
                              maxLength: 16,
                              style: const TextStyle(color: Colors.white),
                              textCapitalization: TextCapitalization.words,
                              decoration: InputDecoration(
                                filled: true,
                                fillColor: kGrayBgEnabled,
                                counterText: '',
                                border: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 3,
                                ),
                              ),
                            ),
                          ),

                          if (children[i].error.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(left: 10, top: 2),
                              child: Text(
                                children[i].error,
                                style: const TextStyle(
                                  color: Colors.redAccent,
                                  fontSize: 12,
                                ),
                              ),
                            ),

                          const SizedBox(height: 15),

                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: 180,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Padding(
                                      padding: EdgeInsets.only(left: 10),
                                      child: Text(
                                        'Age',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 13,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 5),
                                    Container(
                                      height: 40,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                      ),
                                      decoration: BoxDecoration(
                                        color: kGrayBgEnabled,
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      child: DropdownButtonHideUnderline(
                                        child: DropdownButton<String>(
                                          value: children[i].age,
                                          isExpanded: true,
                                          dropdownColor: kGrayBgEnabled,
                                          style: const TextStyle(
                                            color: Colors.white,
                                          ),
                                          items: childAgeOptions.map((
                                            String value,
                                          ) {
                                            return DropdownMenuItem<String>(
                                              value: value,
                                              child: Text(value),
                                            );
                                          }).toList(),
                                          onChanged: (String? newValue) async {
                                            setState(() {
                                              children[i].age =
                                                  newValue ?? 'Below one year';
                                            });
                                            await _saveChildList();
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 20),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Padding(
                                      padding: EdgeInsets.only(left: 15),
                                      child: Text(
                                        'Gender',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 13,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 5),
                                    Container(
                                      height: 40,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                      ),
                                      decoration: BoxDecoration(
                                        color: kGrayBgEnabled,
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      child: DropdownButtonHideUnderline(
                                        child: DropdownButton<String>(
                                          value: children[i].gender,
                                          isExpanded: true,
                                          dropdownColor: kGrayBgEnabled,
                                          style: const TextStyle(
                                            color: Colors.white,
                                          ),
                                          items: genderOptions.map((
                                            String value,
                                          ) {
                                            return DropdownMenuItem<String>(
                                              value: value,
                                              child: Text(value),
                                            );
                                          }).toList(),
                                          onChanged: (String? newValue) async {
                                            setState(() {
                                              children[i].gender =
                                                  newValue ?? 'Male';
                                            });
                                            await _saveChildList();
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (i < children.length - 1) const SizedBox(height: 15),
                ],
              );
            }),
          ] else ...[
            Container(
              margin: const EdgeInsets.only(top: 10),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Container(
                margin: const EdgeInsets.all(1),
                padding: const EdgeInsets.fromLTRB(20, 15, 23, 15),
                decoration: BoxDecoration(
                  color: kPrimaryColor,
                  borderRadius: BorderRadius.circular(14),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(left: 10),
                      child: Text(
                        'Name',
                        style: TextStyle(color: Colors.white, fontSize: 13),
                      ),
                    ),
                    const SizedBox(height: 5),
                    Container(
                      height: 40,
                      child: TextField(
                        onChanged: (v) async {
                          if (children.isEmpty) {
                            setState(() {
                              children.add(ChildModel(name: v));
                            });
                          } else {
                            setState(() => children[0].name = v);
                          }
                          await _saveChildList();
                        },
                        maxLength: 16,
                        style: const TextStyle(color: Colors.white),
                        textCapitalization: TextCapitalization.words,
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: kGrayBgEnabled,
                          counterText: '',
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 3,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 15),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 180,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Padding(
                                padding: EdgeInsets.only(left: 10),
                                child: Text(
                                  'Age',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 13,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 5),
                              Container(
                                height: 40,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                ),
                                decoration: BoxDecoration(
                                  color: kGrayBgEnabled,
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<String>(
                                    value: children.isNotEmpty
                                        ? children[0].age
                                        : 'Below one year',
                                    isExpanded: true,
                                    dropdownColor: kGrayBgEnabled,
                                    style: const TextStyle(color: Colors.white),
                                    items: childAgeOptions.map((String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(value),
                                      );
                                    }).toList(),
                                    onChanged: (String? newValue) async {
                                      if (children.isEmpty) {
                                        setState(() {
                                          children.add(
                                            ChildModel(
                                              age: newValue ?? 'Below one year',
                                            ),
                                          );
                                        });
                                      } else {
                                        setState(() {
                                          children[0].age =
                                              newValue ?? 'Below one year';
                                        });
                                      }
                                      await _saveChildList();
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Padding(
                                padding: EdgeInsets.only(left: 15),
                                child: Text(
                                  'Gender',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 13,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 5),
                              Container(
                                height: 40,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                ),
                                decoration: BoxDecoration(
                                  color: kGrayBgEnabled,
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<String>(
                                    value: children.isNotEmpty
                                        ? children[0].gender
                                        : 'Male',
                                    isExpanded: true,
                                    dropdownColor: kGrayBgEnabled,
                                    style: const TextStyle(color: Colors.white),
                                    items: genderOptions.map((String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(value),
                                      );
                                    }).toList(),
                                    onChanged: (String? newValue) async {
                                      if (children.isEmpty) {
                                        setState(() {
                                          children.add(
                                            ChildModel(
                                              gender: newValue ?? 'Male',
                                            ),
                                          );
                                        });
                                      } else {
                                        setState(() {
                                          children[0].gender =
                                              newValue ?? 'Male';
                                        });
                                      }
                                      await _saveChildList();
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],

          const SizedBox(height: 15),

          if (children.length < 2)
            Container(
              height: 40,
              margin: const EdgeInsets.symmetric(horizontal: 40, vertical: 10),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Container(
                margin: const EdgeInsets.all(1),
                decoration: BoxDecoration(
                  color: kPrimaryColor,
                  borderRadius: BorderRadius.circular(14),
                ),
                child: InkWell(
                  onTap: () async {
                    await _addChildWithAnimation();
                  },
                  child: const Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.add, color: Colors.white, size: 20),
                        SizedBox(width: 8),
                        Text(
                          'Add Child',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),

          if (formErrors.containsKey('children'))
            Padding(
              padding: const EdgeInsets.only(left: 20, top: 10),
              child: Text(
                formErrors['children']!,
                style: const TextStyle(
                  color: Colors.redAccent,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Replace the existing _addChildWithAnimation function with Java-equivalent logic
  Future<void> _addChildWithAnimation() async {
    // Java: if (main_layoutCh.getVisibility() == View.GONE)
    if (!_isChildSectionVisible) {
      // Java: main_layoutCh.setVisibility(View.VISIBLE);
      setState(() {
        _isChildSectionVisible = true;
        // Java: childCount = 1;
        childCount = 1;
        // Equivalent to adding first child with animation
        if (children.isEmpty) {
          children.add(ChildModel());
        }
      });

      // Java: Animation animation = AnimationUtils.loadAnimation(context, R.anim.slide_down);
      // Java: animation.setStartOffset(0);
      // Java: main_layoutCh.startAnimation(animation);
      // Note: Flutter handles this through setState and AnimatedContainer/SlideTransition in UI
    } else {
      // Java: if (childCount < 2)
      if (childCount < 2) {
        // Java: RelativeLayout child = (RelativeLayout) View.inflate(context, R.layout.child_copy, null);
        // Java: main_layoutCh.addView(child, childCount + 1);
        setState(() {
          children.add(ChildModel());
          childCount++;
        });

        // Java: Animation animation = AnimationUtils.loadAnimation(context, R.anim.slide_down);
        // Java: animation.setStartOffset(0);
        // Java: child.startAnimation(animation);
        // Note: Flutter animations handled in UI with AnimatedList or similar

        // Java: View cView = main_layoutCh.getChildAt(childCount + 1);
        // Java: final ImageView remove = cView.findViewById(R.id.imageView2);
        // Java: cName[1] = cView.findViewById(R.id.editText6);
        // Java: cAge[1] = cView.findViewById(R.id.spinner10);
        // Java: cGender[1] = cView.findViewById(R.id.spinner4);
        // Note: In Flutter, these are handled through the ChildModel controllers and UI widgets

        // Java: remove.setOnClickListener implementation is handled in the UI _buildChildItem method
      } else {
        // Java: Toast.makeText(context, context.getResources().getString(R.string.max_2_child), Toast.LENGTH_SHORT).show();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Maximum 2 children allowed'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }

    await _saveChildList();
  }

  Future<void> _removeChildWithAnimation(int index) async {
    // Java: View child = (View) view.getParent();
    // In Flutter, we handle the child removal directly through the model

    if (index >= 0 && index < children.length) {
      // Java: Animation animation = AnimationUtils.loadAnimation(context, R.anim.slide_up);
      // Java: animation.setStartOffset(0);
      // Java: child.startAnimation(animation);
      // Note: Flutter animations are handled in UI layer with AnimatedList or similar

      // Java: new Handler().postDelayed(new Runnable() { @Override public void run() { main_layoutCh.removeView(child); } }, 400);
      // Simulate the 400ms delay from Java Handler.postDelayed
      await Future.delayed(const Duration(milliseconds: 400));

      setState(() {
        children.removeAt(index);
        // Java: childCount--;
        childCount--;

        // If no children left, hide the section like Java main_layoutCh visibility
        if (children.isEmpty) {
          _isChildSectionVisible = false;
          childCount = 0;
        }
      });
    }

    await _saveChildList();
  }

  Future<void> _removeFirstChild() async {
    if (children.length == 2) {
      setState(() {
        children.removeAt(1);
        childCount--;
      });
    } else {
      setState(() {
        children[0].name = '';
        children[0].age = 'Below one year';
        children[0].gender = 'Male';
        childCount--;
      });
    }
    await _saveChildList();
  }

  Widget _sectionGST() {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'GST Details (Optional)',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 20,
                  ),
                ),
                Switch(
                  value: gstRequired,
                  onChanged: (value) async {
                    setState(() => gstRequired = value);
                    await _saveMiscellaneousData();
                  },
                  activeColor: Colors.deepPurpleAccent,
                ),
              ],
            ),
          ),
          if (gstRequired)
            Container(
              margin: const EdgeInsets.only(top: 15),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Container(
                margin: const EdgeInsets.all(1),
                padding: const EdgeInsets.only(bottom: 20, left: 20, right: 20),
                decoration: BoxDecoration(
                  color: kPrimaryColor,
                  borderRadius: BorderRadius.circular(14),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 10),

                    const Padding(
                      padding: EdgeInsets.only(left: 10),
                      child: Text(
                        'GSTIN',
                        style: TextStyle(color: Colors.white, fontSize: 13),
                      ),
                    ),
                    const SizedBox(height: 5),
                    Container(
                      height: 40,
                      child: TextField(
                        controller: gstNumberController,
                        enabled: gstRequired,
                        style: TextStyle(
                          color: gstRequired ? Colors.white : Colors.white54,
                        ),
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: kGrayBgEnabled,
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 3,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 15),

                    const Padding(
                      padding: EdgeInsets.only(left: 10),
                      child: Text(
                        'Name',
                        style: TextStyle(color: Colors.white, fontSize: 13),
                      ),
                    ),
                    const SizedBox(height: 5),
                    Container(
                      height: 40,
                      child: TextField(
                        controller: gstCompanyController,
                        enabled: gstRequired,
                        style: TextStyle(
                          color: gstRequired ? Colors.white : Colors.white54,
                        ),
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: kGrayBgEnabled,
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 3,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 15),

                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Flat / Door / Block',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 13,
                                ),
                              ),
                              const SizedBox(height: 5),
                              Container(
                                height: 40,
                                child: TextField(
                                  controller: gstFlatController,
                                  enabled: gstRequired,
                                  style: TextStyle(
                                    color: gstRequired
                                        ? Colors.white
                                        : Colors.white54,
                                  ),
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor: kGrayBgEnabled,
                                    border: InputBorder.none,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 10,
                                      vertical: 3,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Street / Lane',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 13,
                                ),
                              ),
                              const SizedBox(height: 5),
                              Container(
                                height: 40,
                                child: TextField(
                                  controller: gstStreetController,
                                  enabled: gstRequired,
                                  style: TextStyle(
                                    color: gstRequired
                                        ? Colors.white
                                        : Colors.white54,
                                  ),
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor: kGrayBgEnabled,
                                    border: InputBorder.none,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 10,
                                      vertical: 3,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),

                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'PIN',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 13,
                                ),
                              ),
                              const SizedBox(height: 5),
                              Container(
                                height: 40,
                                child: TextField(
                                  controller: gstPinController,
                                  enabled: gstRequired,
                                  keyboardType: TextInputType.number,
                                  maxLength: 6,
                                  style: TextStyle(
                                    color: gstRequired
                                        ? Colors.white
                                        : Colors.white54,
                                  ),
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor: kGrayBgEnabled,
                                    counterText: '',
                                    border: InputBorder.none,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 10,
                                      vertical: 3,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Area / Locality',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 13,
                                ),
                              ),
                              const SizedBox(height: 5),
                              Container(
                                height: 40,
                                child: TextField(
                                  controller: gstAreaController,
                                  enabled: gstRequired,
                                  style: TextStyle(
                                    color: gstRequired
                                        ? Colors.white
                                        : Colors.white54,
                                  ),
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor: kGrayBgEnabled,
                                    border: InputBorder.none,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 10,
                                      vertical: 3,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),

                    const Text(
                      'City',
                      style: TextStyle(color: Colors.white, fontSize: 13),
                    ),
                    const SizedBox(height: 5),
                    Container(
                      height: 40,
                      child: TextField(
                        controller: gstCityController,
                        enabled: gstRequired,
                        style: TextStyle(
                          color: gstRequired ? Colors.white : Colors.white54,
                        ),
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: kGrayBgEnabled,
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 3,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _validateMiscellaneous() {
    formErrors.remove('mobile');
    formErrors.remove('coach');
    formErrors.remove('gst');
    formErrors.remove('misc');

    String mobileText = mobileController.text.trim();
    if (mobileText.isEmpty) {
      formErrors['mobile'] = 'Mobile No required';
    } else if (mobileText.length != 10 || int.tryParse(mobileText) == null) {
      formErrors['mobile'] = 'Enter valid 10-digit mobile';
    }

    if (coachPreferred && coachController.text.trim().isEmpty) {
      formErrors['coach'] = 'Coach Id required if Coach Preferred';
    }

    if (gstRequired) {
      if (gstNumberController.text.trim().isEmpty ||
          gstCompanyController.text.trim().isEmpty ||
          gstFlatController.text.trim().isEmpty ||
          gstPinController.text.trim().isEmpty ||
          gstCityController.text.trim().isEmpty) {
        formErrors['gst'] = 'All GST fields required';
      } else {
        if (gstFlatController.text.trim().length < 3) {
          formErrors['gst'] = 'Flat/Door number too short';
        } else if (gstStreetController.text.trim().isNotEmpty &&
            gstStreetController.text.trim().length < 3) {
          formErrors['gst'] = 'Street too short';
        } else if (gstAreaController.text.trim().isNotEmpty &&
            gstAreaController.text.trim().length < 3) {
          formErrors['gst'] = 'Area/Locality too short';
        } else if (gstPinController.text.trim().length != 6) {
          formErrors['gst'] = 'PIN must be 6 digits';
        } else if (gstCompanyController.text.trim().length < 3) {
          formErrors['gst'] = 'Company name too short';
        }
      }
    }
  }

  Widget _sectionMiscellaneous() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: buildGradientBorderCard(
        color: kDialogColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Miscellaneous',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 20,
              ),
            ),
            const SizedBox(height: 15),

            CheckboxListTile(
              value: autoUpgradation,
              onChanged: (val) async {
                setState(() => autoUpgradation = val!);
                await _saveMiscellaneousData();
              },
              activeColor: const Color(0xFF9C5DF7),
              side: const BorderSide(color: Colors.white54, width: 1.5),
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
              dense: true,
              title: const Text(
                'Consider Auto upgradation',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            CheckboxListTile(
              value: onlyConfirmBerths,
              onChanged: (val) async {
                setState(() => onlyConfirmBerths = val!);
                await _saveMiscellaneousData();
              },
              activeColor: const Color(0xFF9C5DF7),
              side: const BorderSide(color: Colors.white54, width: 1.5),
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
              dense: true,
              title: const Text(
                'Book only if confirm berths are allotted',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            const SizedBox(height: 5),
            Padding(
              padding: const EdgeInsets.only(left: 20, top: 8, bottom: 2),
              child: Row(
                children: [
                  const Text(
                    'Travel Insurance',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(width: 20),
                  Row(
                    children: [
                      Radio<String>(
                        value: 'yes',
                        groupValue: travelInsurance,
                        onChanged: (v) async {
                          setState(() => travelInsurance = v!);
                          await _saveMiscellaneousData();
                        },
                        activeColor: const Color(0xFF9C5DF7),
                      ),
                      const Text(
                        'Yes',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Radio<String>(
                        value: 'no',
                        groupValue: travelInsurance,
                        onChanged: (v) async {
                          setState(() => travelInsurance = v!);
                          await _saveMiscellaneousData();
                        },
                        activeColor: const Color(0xFF9C5DF7),
                      ),
                      const Text(
                        'No',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),

            Padding(
              padding: const EdgeInsets.only(left: 20, top: 5, bottom: 0),
              child: const Text(
                'Booking Option',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 10, right: 6, top: 2),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RadioListTile<String>(
                    value: 'none',
                    groupValue: bookingOption,
                    onChanged: (v) async {
                      setState(() => bookingOption = v!);
                      await _saveMiscellaneousData();
                    },
                    activeColor: const Color(0xFF9C5DF7),
                    contentPadding: EdgeInsets.zero,
                    dense: true,
                    title: const Text(
                      'None',
                      style: TextStyle(color: Colors.white, fontSize: 15),
                    ),
                  ),
                  RadioListTile<String>(
                    value: 'same_coach',
                    groupValue: bookingOption,
                    onChanged: (v) async {
                      setState(() => bookingOption = v!);
                      await _saveMiscellaneousData();
                    },
                    activeColor: const Color(0xFF9C5DF7),
                    contentPadding: EdgeInsets.zero,
                    dense: true,
                    title: const Text(
                      'Book, only if all berths are allotted in same coach.',
                      style: TextStyle(color: Colors.white, fontSize: 15),
                    ),
                  ),
                  RadioListTile<String>(
                    value: 'min_lower_berth',
                    groupValue: bookingOption,
                    onChanged: (v) async {
                      setState(() => bookingOption = v!);
                      await _saveMiscellaneousData();
                    },
                    activeColor: const Color(0xFF9C5DF7),
                    contentPadding: EdgeInsets.zero,
                    dense: true,
                    title: const Text(
                      'Book, only if at least 1 lower berth is allotted.',
                      style: TextStyle(color: Colors.white, fontSize: 15),
                    ),
                  ),
                  RadioListTile<String>(
                    value: 'confirm_2_lower',
                    groupValue: bookingOption,
                    onChanged: (v) async {
                      setState(() => bookingOption = v!);
                      await _saveMiscellaneousData();
                    },
                    activeColor: const Color(0xFF9C5DF7),
                    contentPadding: EdgeInsets.zero,
                    dense: true,
                    title: const Text(
                      'Book, only if 2 lower berths are allotted.',
                      style: TextStyle(color: Colors.white, fontSize: 15),
                    ),
                  ),
                ],
              ),
            ),
            CheckboxListTile(
              value: coachPreferred,
              onChanged: (v) async {
                setState(() {
                  coachPreferred = v ?? false;
                  coachController.text = coachPreferred
                      ? coachController.text
                      : '';
                });
                await _saveMiscellaneousData();
              },
              activeColor: const Color(0xFF9C5DF7),
              side: const BorderSide(color: Colors.white54, width: 1.5),
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
              dense: true,
              title: const Text(
                'Coach Preferred',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            if (formErrors.containsKey('coach'))
              Padding(
                padding: const EdgeInsets.only(left: 36, top: 2),
                child: Text(
                  formErrors['coach']!,
                  style: const TextStyle(color: Colors.redAccent, fontSize: 12),
                ),
              ),
            Padding(
              padding: const EdgeInsets.only(left: 30, top: 15, right: 25),
              child: Row(
                children: [
                  const Text(
                    'Mobile No',
                    style: TextStyle(color: Colors.white, fontSize: 13),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20, top: 5, right: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    controller: mobileController,
                    maxLength: 10,
                    keyboardType: TextInputType.number,
                    style: const TextStyle(color: Colors.white),
                    onChanged: (v) async {
                      setState(() {});
                      await _saveMiscellaneousData();
                    },
                    decoration: InputDecoration(
                      counterText: '',
                      filled: true,
                      fillColor: kGrayBgEnabled,
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(kBigRadius),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(kBigRadius),
                        borderSide: BorderSide(color: Colors.deepPurpleAccent),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 12,
                      ),
                    ),
                  ),
                  if (formErrors.containsKey('mobile'))
                    Padding(
                      padding: const EdgeInsets.only(left: 4, top: 1),
                      child: Text(
                        formErrors['mobile']!,
                        style: const TextStyle(
                          color: Colors.redAccent,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            if (gstRequired && formErrors.containsKey('gst'))
              Padding(
                padding: const EdgeInsets.only(left: 24, top: 4),
                child: Text(
                  formErrors['gst']!,
                  style: const TextStyle(color: Colors.redAccent, fontSize: 12),
                ),
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Radio<String>(
                  value: 'yes',
                  groupValue: travelInsurance,
                  onChanged: (v) async {
                    setState(() => travelInsurance = v!);
                    await _saveMiscellaneousData();
                  },
                  activeColor: const Color(0xFF9C5DF7),
                ),
                const Text(
                  'Insurance Yes',
                  style: TextStyle(color: Colors.white),
                ),
                Radio<String>(
                  value: 'no',
                  groupValue: travelInsurance,
                  onChanged: (v) async {
                    setState(() => travelInsurance = v!);
                    await _saveMiscellaneousData();
                  },
                  activeColor: const Color(0xFF9C5DF7),
                ),
                const Text(
                  'Insurance No',
                  style: TextStyle(color: Colors.white),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(left: 30, top: 12, right: 20),
              child: const Text(
                'Payment',
                style: TextStyle(color: Colors.white, fontSize: 13),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20, right: 20, top: 5),
              child: Container(
                height: 40,
                padding: const EdgeInsets.symmetric(horizontal: 10),
                decoration: BoxDecoration(
                  color: kGrayBgEnabled,
                  borderRadius: BorderRadius.circular(kBigRadius),
                  border: Border.all(color: Colors.transparent),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: paymentModeWeb,
                    isExpanded: true,
                    dropdownColor: kGrayBgEnabled,
                    style: const TextStyle(color: Colors.white),
                    items: paymentModeItems
                        .map(
                          (val) =>
                              DropdownMenuItem(value: val, child: Text(val)),
                        )
                        .toList(),
                    onChanged: (v) async {
                      setState(() => paymentModeWeb = v ?? paymentModeItems[0]);
                      await _saveMiscellaneousData();
                    },
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20, top: 15, right: 20),
              child: Row(
                children: [
                  Checkbox(
                    value: autofillCaptchas,
                    onChanged: (v) {
                      _onAutofillCaptchaChanged(v);
                    },
                    activeColor: const Color(0xFF9C5DF7),
                    side: const BorderSide(color: Colors.white54, width: 1.5),
                  ),
                  const Text(
                    'Autofill Captchas',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(width: 12),
                  GestureDetector(
                    onTap: () {
                      GoldDemoDialog.show(context, "WEB");
                    },
                    child: Container(
                      height: 28,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        color: Colors.black,
                        border: Border.all(color: Colors.grey[700]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      alignment: Alignment.center,
                      child: const Text(
                        '🎞 Demo',
                        style: TextStyle(
                          color: Color(0xFFAAAAAA),
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedPaymentDetails() {
    if (checkedPymtMeth == 15) return _sectionPayByIRCTCiPay();
    if (showCardSection) return _sectionPayByCard();
    if (showUPISection) return _sectionPayByUPI();
    if (showNetbankSection) return _sectionPayByNetBanking();
    if (showWalletSection) return _sectionPayByWallet();
    if (showMultipleSection) return _sectionPayMultiple();
    if (showIMudraSection) return _sectionPayIMudra();
    return const SizedBox.shrink();
  }

  Widget _sectionPayByIRCTCiPay() {
    final List<String> bankItems = [AppLocalizations.of(context)!.ipay_option];
    final List<String> monthList = [
      '01',
      '02',
      '03',
      '04',
      '05',
      '06',
      '07',
      '08',
      '09',
      '10',
      '11',
      '12',
    ];
    final List<String> yearList = [
      for (var y = DateTime.now().year; y < DateTime.now().year + 21; y++)
        y.toString(),
    ];

    if (selectedBankName != bankItems[0]) {
      selectedBankName = bankItems[0];
    }
    return Padding(
      padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 0),
      child: Container(
        decoration: BoxDecoration(color: Colors.transparent),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.only(left: 5, top: 0, bottom: 8),
              child: Text(
                'IRCTC iPay (Credit Card/Debit Card/UPI)',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                  fontSize: 17,
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: Color(0xFF181728),
                borderRadius: BorderRadius.circular(18),
                border: Border.all(color: Color(0xFF5C61FF), width: 1.9),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _labelField('Bank'),
                  Container(
                    height: 42,
                    decoration: BoxDecoration(
                      color: Color(0xFF28243D),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: bankItems[0],
                        isExpanded: true,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                        ),
                        dropdownColor: const Color(0xFF28243D),
                        items: [
                          DropdownMenuItem<String>(
                            value: bankItems[0],
                            child: Text(bankItems[0]),
                          ),
                        ],
                        onChanged: null,
                      ),
                    ),
                  ),
                  const SizedBox(height: 11),
                  _labelField('Card Number'),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      for (var ctrl in [
                        cardNo1Controller,
                        cardNo2Controller,
                        cardNo3Controller,
                        cardNo4Controller,
                      ])
                        Expanded(
                          child: Container(
                            height: 38,
                            margin: EdgeInsets.only(
                              right: ctrl == cardNo4Controller ? 0 : 8,
                            ),
                            decoration: BoxDecoration(
                              color: Color(0xFF28243D),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: TextField(
                              controller: ctrl,
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                              maxLength: 4,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                counterText: '',
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.fromLTRB(
                                  13,
                                  7,
                                  9,
                                  8,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _labelField('Name on Card'),
                  Container(
                    height: 38,
                    decoration: BoxDecoration(
                      color: Color(0xFF28243D),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: TextField(
                      controller: cardHolderController,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.fromLTRB(13, 7, 9, 8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _labelField('Expiry'),
                  Row(
                    children: [
                      Expanded(
                        child: _roundedDropdown(
                          value: selectedExpiryMonth.isEmpty
                              ? monthList[0]
                              : selectedExpiryMonth,
                          items: monthList,
                          onChanged: (v) => setState(
                            () => selectedExpiryMonth = v ?? monthList[0],
                          ),
                          enabled: true,
                          height: 38,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _roundedDropdown(
                          value: selectedExpiryYear.isEmpty
                              ? yearList[0]
                              : selectedExpiryYear,
                          items: yearList,
                          onChanged: (v) => setState(
                            () => selectedExpiryYear = v ?? yearList[0],
                          ),
                          enabled: true,
                          height: 38,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 13),
                  _labelField('CVV'),
                  Row(
                    children: [
                      Container(
                        height: 38,
                        width: 80,
                        decoration: BoxDecoration(
                          color: Color(0xFF28243D),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: TextField(
                          controller: debitCvvController,
                          obscureText: true,
                          maxLength: 3,
                          keyboardType: TextInputType.number,
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                          decoration: InputDecoration(
                            counterText: '',
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.fromLTRB(13, 7, 9, 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Checkbox(
                        value: hdfcAutofill,
                        onChanged: (v) =>
                            setState(() => hdfcAutofill = v ?? false),
                        activeColor: Color(0xFF9C5DF7),
                        side: BorderSide(color: Colors.white38, width: 1.2),
                      ),
                      const Text(
                        'Autofill Captcha & Password',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w700,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  _labelField('Static Password ( for HDFC Bank )'),
                  Container(
                    height: 38,
                    decoration: BoxDecoration(
                      color: Color(0xFF28243D),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: TextField(
                      controller: staticPassController,
                      enabled: hdfcAutofill,
                      obscureText: false,
                      style: TextStyle(
                        color: hdfcAutofill ? Colors.white : Color(0xFF78758A),
                        fontWeight: FontWeight.w600,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Leave blank to pay by OTP',
                        hintStyle: TextStyle(
                          color: Color(0xFF888699),
                          fontWeight: FontWeight.w400,
                          fontSize: 15,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.fromLTRB(13, 7, 9, 8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _sectionPaymentMethod() {
    final radioItems = [
      {'index': 15, 'label': AppLocalizations.of(context)!.irctc_ipay},
      {'index': 11, 'label': AppLocalizations.of(context)!.upi_bhim_ussd},
      {'index': 9, 'label': AppLocalizations.of(context)!.net_banking},
      {'index': 7, 'label': AppLocalizations.of(context)!.cash_card_wallets},
      {'index': 14, 'label': AppLocalizations.of(context)!.irctc_prepaid},
      {'index': 8, 'label': AppLocalizations.of(context)!.irctc_ewallet},
      {
        'index': 6,
        'label': AppLocalizations.of(context)!.payment_gateway_credit_card,
      },
    ];

    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: kPrimaryColor,
          borderRadius: BorderRadius.circular(5),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 10, top: 10),
              child: Text(
                AppLocalizations.of(context)!.payment_option,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 20,
                ),
              ),
            ),
            const SizedBox(height: 15),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.only(bottom: 20),
              margin: const EdgeInsets.only(top: 15),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFA34BFF), Color(0xFF224FF9)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Container(
                margin: const EdgeInsets.all(1),
                decoration: BoxDecoration(
                  color: kPrimaryColor,
                  borderRadius: BorderRadius.circular(14),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 10, top: 15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: radioItems
                            .map(
                              (item) => RadioListTile<int>(
                                value: item['index'] as int,
                                groupValue: checkedPymtMeth,
                                activeColor: const Color(0xFF9C5DF7),
                                title: Text(
                                  item['label'] as String,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                  ),
                                ),
                                contentPadding: const EdgeInsets.only(left: 10),
                                dense: true,
                                onChanged: (idx) {
                                  webPymtChanged(idx!);
                                },
                              ),
                            )
                            .toList(),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(
                        left: 20,
                        right: 30,
                        top: 10,
                      ),
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: Center(
                        child: Text(
                          AppLocalizations.of(
                            context,
                          )!.hdfc_static_password_hint,
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(left: 20, top: 10),
                      child: Row(
                        children: [
                          Checkbox(
                            value: manualPymtCheckboxFlag,
                            onChanged: (val) => setState(() {
                              manualPymtCheckboxFlag = val!;

                              // When manual payment is selected, clear all autofilled payment data
                              if (manualPymtCheckboxFlag) {
                                _clearAllPaymentData();
                                // Show message to user
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Payment information cleared. You will fill it manually during booking.',
                                    ),
                                    backgroundColor: Colors.orange,
                                    duration: Duration(seconds: 3),
                                  ),
                                );
                              } else {
                                // When unchecked, reload payment data from database
                                _reloadPaymentDataFromDatabase();
                                // Show message to user
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Payment information will be auto-filled from saved data.',
                                    ),
                                    backgroundColor: Colors.green,
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              }
                            }),
                            activeColor: const Color(0xFF9C5DF7),
                            side: const BorderSide(
                              color: Colors.white54,
                              width: 1.5,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 10),
                            child: Text(
                              AppLocalizations.of(context)!.manual_payment,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(left: 20, top: 15),
                      child: Row(
                        children: [
                          Checkbox(
                            value: autofillOTP,
                            onChanged: (val) =>
                                setState(() => autofillOTP = val!),
                            activeColor: const Color(0xFF9C5DF7),
                            side: const BorderSide(
                              color: Colors.white54,
                              width: 1.5,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 10),
                            child: Text(
                              AppLocalizations.of(context)!.autofill_otp,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: kDialogColor,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[700]!),
                      ),
                      child: Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                showAdvancedOptions = !showAdvancedOptions;
                              });
                            },
                            child: Container(
                              padding: const EdgeInsets.all(15),
                              child: Row(
                                children: [
                                  Icon(
                                    showAdvancedOptions
                                        ? Icons.keyboard_arrow_up
                                        : Icons.keyboard_arrow_down,
                                    color: const Color(0xFF9C5DF7),
                                    size: 20,
                                  ),
                                  const SizedBox(width: 10),
                                  Text(
                                    AppLocalizations.of(context)!.advanced,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          if (showAdvancedOptions)
                            Container(
                              padding: const EdgeInsets.only(bottom: 10),
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 3,
                                    child: Padding(
                                      padding: EdgeInsets.all(10),
                                      child: Text(
                                        AppLocalizations.of(
                                          context,
                                        )!.payment_selection_time,
                                        style: TextStyle(
                                          color: Color(0xFFDDDDDD),
                                          fontSize: 14,
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: Container(
                                      height: 40,
                                      margin: const EdgeInsets.only(
                                        top: 10,
                                        right: 10,
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                      ),
                                      decoration: BoxDecoration(
                                        color: kGrayBgEnabled,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: DropdownButtonHideUnderline(
                                        child: DropdownButton<String>(
                                          value: selectedDelayTime,
                                          isExpanded: true,
                                          dropdownColor: kGrayBgEnabled,
                                          style: const TextStyle(
                                            color: Colors.white,
                                          ),
                                          items: delayOptions.map((
                                            String value,
                                          ) {
                                            return DropdownMenuItem<String>(
                                              value: value,
                                              child: Text(value),
                                            );
                                          }).toList(),
                                          onChanged: (String? newValue) {
                                            setState(() {
                                              selectedDelayTime =
                                                  newValue ?? '5 seconds';
                                            });
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 20, right: 20),
                      child: Text(
                        AppLocalizations.of(context)!.payment_info_encrypted,
                        style: TextStyle(
                          color: Color(0xFFBBBBBB),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Manual payment indicator, correctly inserted in the Column
            if (manualPymtCheckboxFlag)
              Container(
                margin: const EdgeInsets.only(left: 20, right: 20, top: 10),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  border: Border.all(color: Colors.orange, width: 1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.orange, size: 20),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        'Manual payment mode active. Payment options are hidden. You will enter payment details during booking.',
                        style: TextStyle(
                          color: Colors.orange,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _sectionPayByCard() {
    final List<String> bankItems = [
      'ICICI Bank',
      'Axis Bank',
      'HDFC Bank',
      'Punjab National Bank',
      'SBI',
      'Other',
      'American Express',
      'International cards (Powered by ATOM)',
    ];
    final List<String> monthList = [
      '01',
      '02',
      '03',
      '04',
      '05',
      '06',
      '07',
      '08',
      '09',
      '10',
      '11',
      '12',
    ];
    final List<String> yearList = [
      for (var y = DateTime.now().year; y < DateTime.now().year + 21; y++)
        y.toString(),
    ];

    bool showCardType = [
      'ICICI Bank',
      'Axis Bank',
      'HDFC Bank',
      'Visa/Master Card(Powered By ICICI BANK)',
      'Visa/Master Card(Powered By HDFC BANK)',
      'Visa/Master Card(Powered By AXIS BANK)',
    ].contains(selectedBankName);

    // Java parity: Show PIN only for ICICI and Axis (demo parity)
    bool showPin = ['ICICI Bank', 'Axis Bank'].contains(selectedBankName);

    return Padding(
      padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 0),
      child: Container(
        decoration: BoxDecoration(color: Colors.transparent),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.only(left: 5, top: 0, bottom: 8),
              child: Text(
                'Payment Gateway / Credit Card / Debit Card',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: Color(0xFF181728),
                borderRadius: BorderRadius.circular(18),
                border: Border.all(color: Color(0xFF2F2953), width: 1.5),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _labelField('Bank'),
                  _roundedDropdown(
                    value: selectedBankName.isEmpty
                        ? bankItems[0]
                        : selectedBankName,
                    items: bankItems,
                    onChanged: (v) {
                      setState(() {
                        selectedBankName = v ?? bankItems[0];
                        cardTypeShow = [
                          'ICICI Bank',
                          'Axis Bank',
                          'HDFC Bank',
                          'Visa/Master Card(Powered By ICICI BANK)',
                          'Visa/Master Card(Powered By HDFC BANK)',
                          'Visa/Master Card(Powered By AXIS BANK)',
                        ].contains(selectedBankName);
                        if (!cardTypeShow) cardType = cardTypeMap[0];
                      });
                    },
                    enabled: true,
                    height: 42,
                  ),
                  if (cardTypeShow) ...[
                    const SizedBox(height: 13),
                    _labelField('Card Type'),
                    Container(
                      height: 38,
                      decoration: BoxDecoration(
                        color: Color(0xFF28243D),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: cardType,
                          isExpanded: true,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                          icon: const Icon(
                            Icons.arrow_drop_down,
                            color: Colors.white70,
                          ),
                          dropdownColor: const Color(0xFF28243D),
                          items: cardTypeMap
                              .map(
                                (ct) => DropdownMenuItem<String>(
                                  value: ct,
                                  child: Text(ct),
                                ),
                              )
                              .toList(),
                          onChanged: (val) =>
                              setState(() => cardType = val ?? cardTypeMap[0]),
                        ),
                      ),
                    ),
                  ],
                  const SizedBox(height: 11),
                  _labelField('Card Number'),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // First card number field with focus logic
                      Expanded(
                        child: Container(
                          height: 38,
                          margin: const EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                            color: Color(0xFF28243D),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: TextField(
                            controller: cardNo1Controller,
                            focusNode: cardNo1Focus,
                            maxLength: 4,
                            keyboardType: TextInputType.number,
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                            decoration: InputDecoration(
                              counterText: '',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.fromLTRB(13, 7, 9, 8),
                            ),
                            onChanged: (val) {
                              if (val.length == 4) {
                                FocusScope.of(
                                  context,
                                ).requestFocus(cardNo2Focus);
                              }
                            },
                          ),
                        ),
                      ),
                      // Second card number field
                      Expanded(
                        child: Container(
                          height: 38,
                          margin: const EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                            color: Color(0xFF28243D),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: TextField(
                            controller: cardNo2Controller,
                            focusNode: cardNo2Focus,
                            maxLength: 4,
                            keyboardType: TextInputType.number,
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                            decoration: InputDecoration(
                              counterText: '',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.fromLTRB(13, 7, 9, 8),
                            ),
                            onChanged: (val) {
                              if (val.length == 4) {
                                FocusScope.of(
                                  context,
                                ).requestFocus(cardNo3Focus);
                              }
                            },
                          ),
                        ),
                      ),
                      // Third card number field with auto-focus to cardNo4
                      Expanded(
                        child: Container(
                          height: 38,
                          margin: const EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                            color: Color(0xFF28243D),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: TextField(
                            controller: cardNo3Controller,
                            focusNode: cardNo3Focus,
                            maxLength: 4,
                            keyboardType: TextInputType.number,
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                            decoration: InputDecoration(
                              counterText: '',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.fromLTRB(13, 7, 9, 8),
                            ),
                            onChanged: (val) {
                              if (val.length == 4) {
                                FocusScope.of(
                                  context,
                                ).requestFocus(cardNo4Focus);
                              }
                            },
                          ),
                        ),
                      ),
                      // Fourth card number field
                      Expanded(
                        child: Container(
                          height: 38,
                          margin: EdgeInsets.only(right: 0),
                          decoration: BoxDecoration(
                            color: Color(0xFF28243D),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: TextField(
                            controller: cardNo4Controller,
                            focusNode: cardNo4Focus,
                            maxLength: 4,
                            keyboardType: TextInputType.number,
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                            decoration: InputDecoration(
                              counterText: '',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.fromLTRB(13, 7, 9, 8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _labelField('Name on Card'),
                  Container(
                    height: 38,
                    decoration: BoxDecoration(
                      color: Color(0xFF28243D),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: TextField(
                      controller: cardHolderController,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.fromLTRB(13, 7, 9, 8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  _labelField('Expiry'),
                  Row(
                    children: [
                      Expanded(
                        child: _roundedDropdown(
                          value: selectedExpiryMonth.isEmpty
                              ? monthList[0]
                              : selectedExpiryMonth,
                          items: monthList,
                          onChanged: (v) => setState(
                            () => selectedExpiryMonth = v ?? monthList[0],
                          ),
                          enabled: true,
                          height: 38,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _roundedDropdown(
                          value: selectedExpiryYear.isEmpty
                              ? yearList[0]
                              : selectedExpiryYear,
                          items: yearList,
                          onChanged: (v) => setState(
                            () => selectedExpiryYear = v ?? yearList[0],
                          ),
                          enabled: true,
                          height: 38,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 13),
                  _labelField('CVV'),
                  Row(
                    children: [
                      Container(
                        height: 38,
                        width: 80,
                        decoration: BoxDecoration(
                          color: Color(0xFF28243D),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: TextField(
                          controller: debitCvvController,
                          obscureText: true,
                          maxLength: 3,
                          keyboardType: TextInputType.number,
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                          decoration: InputDecoration(
                            counterText: '',
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.fromLTRB(13, 7, 9, 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (showPin) ...[
                    const SizedBox(height: 13),
                    _labelField('PIN'),
                    Container(
                      height: 38,
                      decoration: BoxDecoration(
                        color: Color(0xFF28243D),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: TextField(
                        controller: pinController,
                        obscureText: true,
                        maxLength: 6,
                        keyboardType: TextInputType.number,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                        decoration: InputDecoration(
                          counterText: '',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.fromLTRB(13, 7, 9, 8),
                        ),
                      ),
                    ),
                  ],
                  Row(
                    children: [
                      Checkbox(
                        value: hdfcAutofill,
                        onChanged: (v) =>
                            setState(() => hdfcAutofill = v ?? false),
                        activeColor: Color(0xFF9C5DF7),
                        side: BorderSide(color: Colors.white38, width: 1.2),
                      ),
                      const Text(
                        'Autofill Captcha & Password',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w700,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  _labelField('Static Password ( for HDFC Bank )'),
                  Container(
                    height: 38,
                    decoration: BoxDecoration(
                      color: Color(0xFF28243D),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: TextField(
                      controller: staticPassController,
                      enabled: hdfcAutofill,
                      obscureText: false,
                      style: TextStyle(
                        color: hdfcAutofill ? Colors.white : Color(0xFF78758A),
                        fontWeight: FontWeight.w600,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Leave blank to pay by OTP',
                        hintStyle: TextStyle(
                          color: Color(0xFF888699),
                          fontWeight: FontWeight.w400,
                          fontSize: 15,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.fromLTRB(13, 7, 9, 8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _labelField(String label) {
    return Padding(
      padding: const EdgeInsets.only(left: 2, bottom: 2, top: 0),
      child: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12.5,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget _roundedDropdown({
    required String value,
    required List<String> items,
    required void Function(String?) onChanged,
    bool enabled = true,
    double height = 42,
  }) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: Color(0xFF28243D),
        borderRadius: BorderRadius.circular(16),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          isExpanded: true,
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
            fontSize: 15,
          ),
          icon: Icon(Icons.arrow_drop_down, color: Colors.white70),
          dropdownColor: Color(0xFF28243D),
          items: items
              .map(
                (item) => DropdownMenuItem<String>(
                  value: item,
                  child: Text(
                    item,
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              )
              .toList(),
          onChanged: enabled ? onChanged : null,
        ),
      ),
    );
  }

  InputDecoration _inputDecoration(String label) {
    return InputDecoration(
      counterText: '',
      filled: true,
      fillColor: kGrayBgEnabled,
      hintText: label.isNotEmpty ? label : '',
      hintStyle: const TextStyle(color: Colors.white54),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(20),
        borderSide: BorderSide.none,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 15),
    );
  }

  void _toggleLoginPasswordVisibility() {
    setState(() {
      _showLoginPassword = !_showLoginPassword;
    });
  }

  Future<void> _saveAllFormSections() async {
    try {
      await _saveWebsiteJourneyDetails();
      await _saveMiscellaneousData();
      await _savePassengerList();
      await _saveChildList();
      await _saveCredentials();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Form saved')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Save failed: $e')),
        );
      }
    }
  }

  Future<void> _handleLockUnlock() async {
    final prefs = await SharedPreferences.getInstance();
    final saved = prefs.getString(_formLockStorageKey);
    final bool isSet = (saved != null && saved.isNotEmpty);
    final TextEditingController input = TextEditingController();

    final bool? ok = await showDialog<bool>(
      context: context,
      builder: (ctx) =>
          AlertDialog(
            backgroundColor: kDialogColor,
            title: Text(isSet ? 'Unlock Form' : 'Set Password',
                style: const TextStyle(color: Colors.white)),
            content: TextField(
              controller: input,
              obscureText: true,
              decoration: InputDecoration(
                labelText: isSet ? 'Enter password' : 'Set a password',
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(ctx).pop(false),
                child: const Text(
                    'Cancel', style: TextStyle(color: Colors.white)),
              ),
              TextButton(
                onPressed: () => Navigator.of(ctx).pop(true),
                child: Text(isSet ? 'Unlock' : 'Set Password',
                    style: const TextStyle(color: Colors.amber)),
              ),
            ],
          ),
    );

    if (ok != true) return;

    if (isSet) {
      if (input.text == saved) {
        await prefs.remove(_formLockStorageKey);
        setState(() => _isFormLocked = false);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Unlocked. You can now edit.')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Incorrect password')),
          );
        }
      }
    } else {
      if (input.text.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Password cannot be empty')),
          );
        }
        return;
      }
      await prefs.setString(_formLockStorageKey, input.text);
      setState(() => _isFormLocked = true);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Password set. Form is now locked.')),
        );
      }
    }
  }

  Future<void> _saveDebitCardWithPinToDb(Map<String, dynamic> cv) async {
    // === Save using Java-style cardType var ===
    final enc = Cryptography.encryptPassword;
    cv['CARD_NO'] = enc(
      cardNo1Controller.text +
          cardNo2Controller.text +
          cardNo3Controller.text +
          cardNo4Controller.text,
    );
    cv['CARD_TYPE'] = enc(cardType);
    cv['CARD_HOLDER'] = enc(cardHolderController.text);
    cv['EXP_MON'] = enc(selectedExpiryMonth);
    cv['EXP_YR'] = enc(selectedExpiryYear);
    cv['BANK_NAME'] = enc(selectedBankName);
    cv['PIN'] = enc(pinController.text);
    cv['CVV'] = enc(debitCvvController.text);
    cv['STATIC_PASS'] = enc(staticPassController.text);
    cv['HDFC_AUTOFILL'] = hdfcAutofill ? '1' : '0';
    final db = await MainDB.instance.database;
    await db.update(
      MainDB.tableName,
      cv,
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );

    await db.update(
      MainDB.tableName,
      {
        'CARD_NO': cv['CARD_NO'],
        'CARD_TYPE': cv['CARD_TYPE'],
        'CARD_HOLDER': cv['CARD_HOLDER'],
        'EXP_MON': cv['EXP_MON'],
        'EXP_YR': cv['EXP_YR'],
        'BANK_NAME': cv['BANK_NAME'],
        'PIN': cv['PIN'],
        'CVV': cv['CVV'],
        'STATIC_PASS': cv['STATIC_PASS'],
        'HDFC_AUTOFILL': cv['HDFC_AUTOFILL'],
      },
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );
  }

  void _loadDebitCardWithPinFromDb(Map<String, dynamic> row) {
    // Don't autofill if manual payment is selected
    if (manualPymtCheckboxFlag) {
      print('Manual payment selected - skipping card autofill');
      return;
    }
    if ((row['CARD_NO'] as String?)?.isNotEmpty ?? false) {
      String cardNo = Cryptography.decryptPassword(row['CARD_NO']);
      cardNo1Controller.text = cardNo.length >= 4 ? cardNo.substring(0, 4) : '';
      cardNo2Controller.text = cardNo.length >= 8 ? cardNo.substring(4, 8) : '';
      cardNo3Controller.text = cardNo.length >= 12
          ? cardNo.substring(8, 12)
          : '';
      cardNo4Controller.text = cardNo.length >= 16
          ? cardNo.substring(12, 16)
          : '';
    } else {
      cardNo1Controller.text = '';
      cardNo2Controller.text = '';
      cardNo3Controller.text = '';
      cardNo4Controller.text = '';
    }
    // --- Java cardType logic and state management ---
    cardType =
        Cryptography.decryptPassword(row['CARD_TYPE'] ?? '') ?? cardTypeMap[0];
    cardHolderController.text =
        Cryptography.decryptPassword(row['CARD_HOLDER'] ?? '') ?? '';
    selectedExpiryMonth =
        Cryptography.decryptPassword(row['EXP_MON'] ?? '') ?? '';
    selectedExpiryYear =
        Cryptography.decryptPassword(row['EXP_YR'] ?? '') ?? '';
    selectedBankName =
        Cryptography.decryptPassword(row['BANK_NAME'] ?? '') ?? '';
    pinController.text = Cryptography.decryptPassword(row['PIN'] ?? '') ?? '';
    debitCvvController.text =
        Cryptography.decryptPassword(row['CVV'] ?? '') ?? '';
    staticPassController.text =
        Cryptography.decryptPassword(row['STATIC_PASS'] ?? '') ?? '';
    hdfcAutofill = row['HDFC_AUTOFILL'] == '1';
    // Restore cardTypeShow UI logic exactly as Java for supported banks
    if (selectedBankName == 'ICICI Bank' ||
        selectedBankName == 'Axis Bank' ||
        selectedBankName == 'HDFC Bank' ||
        selectedBankName == 'Visa/Master Card(Powered By ICICI BANK)' ||
        selectedBankName == 'Visa/Master Card(Powered By HDFC BANK)' ||
        selectedBankName == 'Visa/Master Card(Powered By AXIS BANK)') {
      cardTypeShow = true;
    } else {
      cardTypeShow = false;
      cardType = cardTypeMap[0];
    }
  }

  Widget _sectionPayByUPI() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: buildGradientBorderCard(
        color: Colors.grey[900]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.upi_bhim_ussd,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 20,
              ),
            ),
            const SizedBox(height: 10),

            // === Dropdown for UPI sub-options (exactly like the image)
            const Text(
              'Payment Method',
              style: TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 2),
            Container(
              height: 40,
              margin: const EdgeInsets.symmetric(vertical: 6),
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: kGrayBgEnabled,
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Color(0xFF9475ED), width: 2),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<int>(
                  value: upiSubOption,
                  isExpanded: true,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                  dropdownColor: kGrayBgEnabled,
                  iconEnabledColor: Color(0xFF9475ED),
                  borderRadius: BorderRadius.circular(10),
                  items: List.generate(
                    upiSubOptionList.length,
                    (i) => DropdownMenuItem<int>(
                      value: i,
                      child: Text(
                        upiSubOptionList[i],
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  onChanged: (v) {
                    setState(() {
                      upiSubOption = v ?? 0;
                      vpaController.clear();
                      selectedUpiBank = '';
                      upiPinEdController.clear();
                    });
                  },
                ),
              ),
            ),

            const Text(
              'UPI ID',
              style: TextStyle(color: Colors.white, fontSize: 13),
            ),
            const SizedBox(height: 3),
            TextField(
              controller: vpaController,
              style: const TextStyle(color: Colors.white),
              onChanged: (v) async {
                _onUpiIdChanged(v);
                await saveForm(showSnackbar: false);
              },
              decoration: InputDecoration(
                filled: true,
                fillColor: kGrayBgEnabled,
                hintText: 'Enter UPI ID (e.g., user@paytm)',
                hintStyle: const TextStyle(color: Colors.white54),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 15),
              ),
            ),
            const SizedBox(height: 10),

            if (autoOpenUPIApp)
              CheckboxListTile(
                value: autoOpenUPIApp,
                onChanged: (val) =>
                    setState(() => autoOpenUPIApp = val ?? false),
                activeColor: const Color(0xFF9C5DF7),
                side: const BorderSide(color: Colors.white54, width: 1.5),
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
                dense: true,
                title: Text(
                  _getAutoOpenText(),
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                ),
              ),

            if (autoOpenUPIApp && automateUPICb) ...[
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[600]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CheckboxListTile(
                      value: automateUPICb,
                      onChanged: _onAutomateUpiChanged,
                      activeColor: const Color(0xFF9C5DF7),
                      side: const BorderSide(color: Colors.white54, width: 1.5),
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                      dense: true,
                      title: const Text(
                        'Automate UPI app with UPI PIN',
                        style: TextStyle(color: Colors.white, fontSize: 14),
                      ),
                    ),

                    if (automateUPICb) ...[
                      const SizedBox(height: 10),

                      const Text(
                        'UPI PIN',
                        style: TextStyle(color: Colors.white, fontSize: 13),
                      ),
                      const SizedBox(height: 5),
                      TextField(
                        controller: upiPinEdController,
                        obscureText: true,
                        maxLength: 6,
                        keyboardType: TextInputType.number,
                        style: const TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: kGrayBgEnabled,
                          counterText: '',
                          hintText: 'Enter 4-6 digit UPI PIN',
                          hintStyle: const TextStyle(color: Colors.white54),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(20),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(20),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 15,
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),

                      CheckboxListTile(
                        value: defaultbankCb,
                        onChanged: (val) =>
                            setState(() => defaultbankCb = val ?? false),
                        activeColor: const Color(0xFF9C5DF7),
                        side: const BorderSide(
                          color: Colors.white54,
                          width: 1.5,
                        ),
                        controlAffinity: ListTileControlAffinity.leading,
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                        title: const Text(
                          'Select default bank in UPI app',
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ),

                      if (defaultbankCb)
                        const Padding(
                          padding: EdgeInsets.only(left: 32, top: 5),
                          child: Text(
                            'Note: This will automatically select the first bank in your UPI app',
                            style: TextStyle(
                              color: Colors.amber,
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                    ],
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _onUpiIdChanged(String upiId) {
    String upiApp = _detectUpiApp(upiId);
    bool isAppInstalled = _isUpiAppInstalled(upiApp);

    setState(() {
      if (isAppInstalled && upiApp.isNotEmpty && upiApp != 'BHIM') {
        autoOpenUPIApp = true;
      } else {
        autoOpenUPIApp = false;
        automateUPICb = false;
        defaultbankCb = false;
      }
    });
  }

  String _detectUpiApp(String upiId) {
    if (upiId.contains('@ok')) return 'Google Pay';
    if (upiId.contains('@pt')) return 'Paytm';
    if (upiId.contains('@iPayUpi')) return 'BHIM';
    if (upiId.contains('@ybl') ||
        upiId.contains('@ibl') ||
        upiId.contains('@axl'))
      return 'PhonePe';
    return '';
  }

  bool _isUpiAppInstalled(String upiApp) {
    return upiApp.isNotEmpty;
  }

  String _getAutoOpenText() {
    String upiApp = _detectUpiApp(vpaController.text);
    if (upiApp.isNotEmpty) {
      return 'Automatically open $upiApp app after submitting UPI ID';
    }
    return 'Auto open UPI app';
  }

  void _onAutomateUpiChanged(bool? value) {
    if (value == true) {
      if (SplashActivity.isGoldUser != 2 && !isLoadingUpi) {
        if (APIConsts.UPI_TRIAL_OPTED != 'N') {
          _showUpiTrialExpiredDialog();
          return;
        } else {
          _showUpiTrialDialog();
          return;
        }
      }
    }

    setState(() {
      automateUPICb = value ?? false;
      if (!automateUPICb) {
        defaultbankCb = false;
      }
    });
  }

  void _showUpiTrialExpiredDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: kDialogColor,
          title: const Text(
            'Trial Expired',
            style: TextStyle(color: Colors.white),
          ),
          content: const Text(
            'Your free trial for UPI app automation is expired.\n\n'
            'Buy GOLD Pack now to enjoy this feature with unlimited tickets and all the other premium features for lifetime.',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  automateUPICb = false;
                });
              },
              child: const Text(
                'Disable UPI Autofill',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  automateUPICb = false;
                });
                _navigateToPremium('UPI trial expired');
              },
              child: const Text(
                'Buy GOLD Pack',
                style: TextStyle(color: Colors.amber),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showUpiTrialDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: kDialogColor,
          title: const Text(
            'Try UPI Autofill',
            style: TextStyle(color: Colors.white),
          ),
          content: const Text(
            'UPI app automation with UPI PIN auto submit is available only in GOLD Pack. '
            'But you can try this feature for free for 1 ticket booking.\n\n'
            'Do you want to try this feature now or buy GOLD Pack with lifetime validity with all the unlimited features unlocked.',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  automateUPICb = true;
                });
              },
              child: const Text(
                'Try Once',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  automateUPICb = false;
                });
                _navigateToPremium('Before UPI trial');
              },
              child: const Text(
                'Buy GOLD Pack',
                style: TextStyle(color: Colors.amber),
              ),
            ),
          ],
        );
      },
    );
  }

  void _navigateToPremium(String source) {
    // Set the payment source just like Java
    AppConsts.Consts.paymentSource = source;
    // Track mixpanel event just like Java
    MixpanelManager().track('GOLD Pack alert show', {"Source": source});
    // Navigate to PremiumScreen with Extra (as in Java Intent extra GOLD = Y)
    Get.to(() => const PremiumScreen(goldFlag: 'Y'));
  }

  Future<void> _saveUpiToDb(Map<String, dynamic> cv) async {
    final enc = Cryptography.encryptPassword;
    cv['VPA'] = enc(vpaController.text);
    cv['UPI_BANK'] = enc(selectedUpiBank);
    cv['AUTO_OPEN'] = autoOpenUPIApp ? '1' : '0';
    cv['UPI_AUTOMATE'] = automateUPICb ? '1' : '0';
    cv['UPI_DEFAULT_BANK'] = defaultbankCb ? '1' : '0';
    cv['UPI_PIN'] = enc(upiPinEdController.text);
  }

  /// Enhanced payment data loading methods to respect manual payment flag
  void _loadUpiFromDb(Map<String, dynamic> row) {
    // Don't autofill if manual payment is selected
    if (manualPymtCheckboxFlag) {
      print('Manual payment selected - skipping UPI autofill');
      return;
    }
    if (selectedPaymentMethod == 'UPI_VPA') {
      final dec = Cryptography.decryptPassword;

      vpaController.text = dec(row['VPA'] ?? '');
      selectedUpiBank = dec(row['UPI_BANK'] ?? '');

      final autoOpen = row['AUTO_OPEN']?.toString() == '1';
      final automateUpi = row['UPI_AUTOMATE']?.toString() == '1';
      final defaultBank = row['UPI_DEFAULT_BANK']?.toString() == '1';
      final upiPin = dec(row['UPI_PIN'] ?? '');

      setState(() {
        autoOpenUPIApp = autoOpen;
        if (automateUpi &&
            (SplashActivity.isGoldUser == 2 ||
                APIConsts.UPI_TRIAL_OPTED == 'N')) {
          isLoadingUpi = true;
          automateUPICb = true;
          isLoadingUpi = false;
        }
        defaultbankCb = defaultBank;

        String formattedPin = upiPin;
        if (formattedPin.length == 3 || formattedPin.length == 5) {
          formattedPin = '0$formattedPin';
        }
        upiPinEdController.text = formattedPin;
      });

      _onUpiIdChanged(vpaController.text);
    }
  }

  Widget _sectionPayByNetBanking() {
    final List<String> netBankItems = [
      'Select Your Bank',
      'State Bank of India and Associates',
      'HDFC Bank',
      'ICICI Bank',
      'Axis Bank',
      'Punjab National Bank',
      'Bank of Baroda',
      'Canara Bank',
      'Union Bank of India',
      'Bank of India',
      'Indian Bank',
      'CITI Bank',
      'United Bank of India',
    ];
    final List<String> bankTypeItems = ['Personal', 'Corporate'];
    final List<String> sbiBankNameItems = [
      'State Bank of Bikaner and Jaipur',
      'State Bank of Patiala',
      'State Bank of Hyderabad',
      'State Bank of Travancore',
      'State Bank of Mysore',
    ];
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: buildGradientBorderCard(
        color: Colors.grey[900]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.net_banking,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 20,
              ),
            ),
            const SizedBox(height: 10),
            // --- Bank Name ScrollView for Net Banking ---
            const Text(
              'Bank Name',
              style: TextStyle(color: Colors.white, fontSize: 13),
            ),
            const SizedBox(height: 3),
            Container(
              height: 40,
              margin: const EdgeInsets.only(bottom: 10),
              decoration: BoxDecoration(
                color: kGrayBgEnabled,
                borderRadius: BorderRadius.circular(20),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: netBankName.isEmpty ? netBankItems[0] : netBankName,
                  isExpanded: true,
                  dropdownColor: kGrayBgEnabled,
                  style: const TextStyle(color: Colors.white),
                  onChanged: (val) {
                    setState(() {
                      netBankName = val ?? netBankItems[0];
                      if (netBankName == 'State Bank of India and Associates') {
                        sbiBankNameVisible = true;
                      } else {
                        sbiBankNameVisible = false;
                        sbiBankName = '';
                      }
                    });
                  },
                  items: netBankItems
                      .map(
                        (item) => DropdownMenuItem<String>(
                          value: item,
                          child: Text(item),
                        ),
                      )
                      .toList(),
                ),
              ),
            ),
            if (sbiBankNameVisible) ...[
              const Text(
                'SBI Bank Name',
                style: TextStyle(color: Colors.white, fontSize: 13),
              ),
              const SizedBox(height: 3),
              Container(
                height: 40,
                margin: const EdgeInsets.only(bottom: 10),
                decoration: BoxDecoration(
                  color: kGrayBgEnabled,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: sbiBankName.isEmpty
                        ? sbiBankNameItems[0]
                        : sbiBankName,
                    isExpanded: true,
                    dropdownColor: kGrayBgEnabled,
                    style: const TextStyle(color: Colors.white),
                    onChanged: (val) {
                      setState(() {
                        sbiBankName = val ?? sbiBankNameItems[0];
                      });
                    },
                    items: sbiBankNameItems
                        .map(
                          (item) => DropdownMenuItem<String>(
                            value: item,
                            child: Text(item),
                          ),
                        )
                        .toList(),
                  ),
                ),
              ),
            ],
            const Text(
              'Banking Type',
              style: TextStyle(color: Colors.white, fontSize: 13),
            ),
            const SizedBox(height: 3),
            Container(
              height: 40,
              margin: const EdgeInsets.only(bottom: 10),
              decoration: BoxDecoration(
                color: kGrayBgEnabled,
                borderRadius: BorderRadius.circular(20),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: netBankingType,
                  isExpanded: true,
                  dropdownColor: kGrayBgEnabled,
                  style: const TextStyle(color: Colors.white),
                  onChanged: (val) {
                    setState(() {
                      netBankingType = val ?? 'Personal';
                      if (netBankingType == 'Corporate') {
                        corpIdVisible = true;
                      } else {
                        corpIdVisible = false;
                        corpIdController.clear();
                      }
                    });
                  },
                  items: bankTypeItems
                      .map(
                        (item) => DropdownMenuItem<String>(
                          value: item,
                          child: Text(item),
                        ),
                      )
                      .toList(),
                ),
              ),
            ),
            if (corpIdVisible) ...[
              const Text(
                'Corporate ID',
                style: TextStyle(color: Colors.white, fontSize: 13),
              ),
              const SizedBox(height: 3),
              TextField(
                controller: corpIdController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  filled: true,
                  fillColor: kGrayBgEnabled,
                  hintText: 'Enter Corporate ID',
                  hintStyle: const TextStyle(color: Colors.white54),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 15),
                ),
              ),
              const SizedBox(height: 9),
            ],
            const Text(
              'Username',
              style: TextStyle(color: Colors.white, fontSize: 13),
            ),
            const SizedBox(height: 3),
            TextField(
              controller: nbUsernameController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                filled: true,
                fillColor: kGrayBgEnabled,
                hintText: 'Enter Username',
                hintStyle: const TextStyle(color: Colors.white54),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 15),
              ),
            ),
            const SizedBox(height: 9),
            const Text(
              'Password',
              style: TextStyle(color: Colors.white, fontSize: 13),
            ),
            const SizedBox(height: 3),
            TextField(
              controller: nbPasswordController,
              obscureText: true,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                filled: true,
                fillColor: kGrayBgEnabled,
                hintText: 'Enter Password',
                hintStyle: const TextStyle(color: Colors.white54),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 15),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _sectionPayByWallet() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: buildGradientBorderCard(
        color: Colors.grey[900]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.cash_card_wallets,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Wallet Provider',
                labelStyle: TextStyle(color: Colors.white70),
                filled: true,
                fillColor: Colors.black38,
              ),
              value: selectedWallet.isNotEmpty ? selectedWallet : null,
              items: walletOptions.map((wallet) {
                return DropdownMenuItem<String>(
                  value: wallet,
                  child: Text(
                    wallet,
                    style: const TextStyle(color: Colors.white),
                  ),
                );
              }).toList(),
              onChanged: (v) => setState(() => selectedWallet = v ?? ''),
              dropdownColor: Colors.grey[900],
            ),
            const SizedBox(height: 12),
            TextField(
              controller: walletUsernameController,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                labelText: "Wallet Username",
                labelStyle: TextStyle(color: Colors.white54),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: walletPasswordController,
              obscureText: true,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                labelText: "Wallet Password",
                labelStyle: TextStyle(color: Colors.white54),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _sectionPayByIRCTCWallet() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: buildGradientBorderCard(
        color: Colors.grey[900]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.irctc_prepaid,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: irctcWalletPinController,
              obscureText: true,
              maxLength: 6,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                counterText: '',
                labelText: "eWallet PIN",
                labelStyle: TextStyle(color: Colors.white54),
                hintText: 'Enter your eWallet/Prepaid PIN',
                hintStyle: TextStyle(color: Colors.white54),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _sectionPayIMudra() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: buildGradientBorderCard(
        color: Colors.grey[900]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.cash_card_wallets,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 20,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'Mobile No',
              style: TextStyle(color: Colors.white, fontSize: 13),
            ),
            const SizedBox(height: 3),
            TextField(
              controller: podMobileController,
              keyboardType: TextInputType.phone,
              maxLength: 10,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                counterText: '',
                filled: true,
                fillColor: kGrayBgEnabled,
                hintText: '',
                hintStyle: const TextStyle(color: Colors.white54),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 15),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Strict parity with Java: multiple payment method options
  final List<String> multiplePymtOptions = [
    'Select Payment Method',
    'Debit Card',
    'Credit Card',
    'Net Banking',
    'UPI',
    'Wallet',
    'IRCTC eWallet',
  ];

  Widget _sectionPayMultiple() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: buildGradientBorderCard(
        color: Colors.grey[900]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.irctc_prepaid,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 20,
              ),
            ),
            const SizedBox(height: 10),

            const Text(
              'Payment Method',
              style: TextStyle(color: Colors.white, fontSize: 13),
            ),
            const SizedBox(height: 3),
            Container(
              height: 40,
              margin: const EdgeInsets.only(bottom: 10),
              decoration: BoxDecoration(
                color: kGrayBgEnabled,
                borderRadius: BorderRadius.circular(20),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: selectedMultiplePaymentOption.isEmpty
                      ? multiplePymtOptions[0]
                      : selectedMultiplePaymentOption,
                  isExpanded: true,
                  dropdownColor: kGrayBgEnabled,
                  style: const TextStyle(color: Colors.white),
                  onChanged: (val) {
                    setState(() {
                      selectedMultiplePaymentOption =
                          val ?? multiplePymtOptions[0];
                    });
                  },
                  items: multiplePymtOptions
                      .map(
                        (item) =>
                            DropdownMenuItem(value: item, child: Text(item)),
                      )
                      .toList(),
                ),
              ),
            ),

            const Text(
              'Amount',
              style: TextStyle(color: Colors.white, fontSize: 13),
            ),
            const SizedBox(height: 3),
            TextField(
              controller: multiplePaymentAmountController,
              keyboardType: TextInputType.number,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                filled: true,
                fillColor: kGrayBgEnabled,
                hintText: 'Enter amount',
                hintStyle: const TextStyle(color: Colors.white54),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 15),
              ),
              // (Removed onChanged: setState(() {}), because it is not needed and causes UI/key lag)
            ),
            const SizedBox(height: 15),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[600]!),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Remaining Amount:',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '₹ ${_calculateRemainingAmount()}',
                    style: const TextStyle(
                      color: Colors.green,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),

            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: const Text(
                'Note: You can split your payment across multiple methods. The remaining amount will be automatically calculated.',
                style: TextStyle(color: Colors.blue, fontSize: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _calculateRemainingAmount() {
    String entered = multiplePaymentAmountController.text;
    if (entered.isEmpty) return "--";
    try {
      double amt = double.tryParse(entered) ?? 0;
      if (amt < 0) return "--";
      double total = 1000;
      double rem = total - amt;
      if (rem < 0) rem = 0;
      return rem.toStringAsFixed(2);
    } catch (e) {
      return "--";
    }
  }

  Future<bool> saveForm({bool showSnackbar = true}) async {
    _validateJourneyDetails();
    _validateChildDetails();
    _validateMiscellaneous();
    if (formErrors.isNotEmpty) {
      if (showSnackbar) _showBookingFormSummaryError();
      return false;
    }
    try {
      await _saveWebsiteJourneyDetails();
      await _saveMiscellaneousData();
      await _savePassengerList();
      await _saveChildList();

      List<String> errors = [];
      // When user chooses to fill payment manually, skip payment-field validations
      if (manualPymtCheckboxFlag) {
        // No payment validations in manual mode
      } else {
        if (showCardSection) {
          if (cardNo1Controller.text.length != 4)
            errors.add('Enter first 4 digits of card');
          if (cardNo2Controller.text.length != 4)
            errors.add('Enter second 4 digits of card');
          if (cardNo3Controller.text.length != 4)
            errors.add('Enter third 4 digits of card');
          if (cardNo4Controller.text.length != 4)
            errors.add('Enter last 4 digits of card');
          if (selectedExpiryMonth.trim().isEmpty ||
              selectedExpiryYear.trim().isEmpty)
            errors.add('Expiry month/year required');
          if (cardHolderController.text.trim().isEmpty)
            errors.add('Cardholder name required');
          if (debitCvvController.text.length != 3 ||
              int.tryParse(debitCvvController.text) == null)
            errors.add('Valid 3-digit CVV required');
        }
        if (showUPISection) {
          if (vpaController.text.trim().isEmpty ||
              !vpaController.text.contains('@')) {
            errors.add('Enter valid UPI VPA');
          }
          // === Java-style splitting for UPI suboption
          switch (upiSubOption) {
            case 0:
              // PhonePe flow, add validation if needed
              break;
            case 1:
              // BHIM/Paytm flow, add validation if needed
              break;
            case 2:
              // IRCTC iPay UPI suboption
              break;
          }
        }
        if (showNetbankSection) {
          if (netBankName.isEmpty || netBankName == 'Select Your Bank') {
            errors.add('Please select your bank');
          }
          if (nbUsernameController.text.trim().isEmpty) {
            errors.add('Netbanking username required');
          }
          if (nbPasswordController.text.trim().isEmpty) {
            errors.add('Netbanking password required');
          }
          if (corpIdVisible && corpIdController.text.trim().isEmpty) {
            errors.add('Corporate ID required');
          }
          if (sbiBankNameVisible && sbiBankName.isEmpty) {
            errors.add('Select SBI Bank Name');
          }
        }
        if (showWalletSection) {
          if (walletUsernameController.text.trim().isEmpty) {
            errors.add('Wallet username required');
          }
          if (selectedWallet != 'OLAMONEY Wallet' &&
              walletPasswordController.text.trim().isEmpty) {
            errors.add('Wallet password required');
          }
        }
        // --- Java parity for IRCTC eWallet (index 8) ---
        if (checkedPymtMeth == 8) {
          if (irctcWalletPinController.text.trim().isEmpty) {
            errors.add('Transaction password required');
          }
        }
        if (showIMudraSection) {
          if (podMobileController.text.trim().isEmpty) {
            errors.add('iMudra mobile required');
          }
        }
        // --- Java parity validation for multiple payment ---
        // Skip payment validation when manual payment is selected
        if (showMultipleSection && !manualPymtCheckboxFlag) {
          if (selectedMultiplePaymentOption == multiplePymtOptions[0]) {
            errors.add('Please select a payment method for multiple payment');
          }
          double amt =
              double.tryParse(multiplePaymentAmountController.text.trim()) ??
              -1;
          if (multiplePaymentAmountController.text.trim().isEmpty) {
            errors.add('Please enter amount for multiple payment');
          } else if (amt <= 0) {
            errors.add('Amount must be greater than 0');
          }
        }
      }

      if (errors.isNotEmpty) {
        if (showSnackbar) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errors.join("\n")),
              backgroundColor: Colors.red,
            ),
          );
        }
        return false;
      }
      if (showCardSection && checkedPymtMeth == 6) {
        try {
          final db = await MainDB.instance.database;
          Map<String, dynamic> cv = {};
          _saveDebitCardWithPinToDb(cv);
          await db.update(
            MainDB.tableName,
            cv,
            where: 'FORM_NAME = ?',
            whereArgs: [widget.formName],
          );
        } catch (e) {
          if (showSnackbar) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Could not save card data: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
          return false;
        }
      }

      if (showWalletSection && checkedPymtMeth == 7) {
        await _saveWalletPaymentToDatabase();
      }
      // --- Java parity for IRCTC eWallet save (index 8) ---
      if (checkedPymtMeth == 8) {
        await _saveIRCTCeWalletToDatabase();
      }
      if (showMultipleSection && checkedPymtMeth == 14) {
        await _saveMultiplePaymentToDatabase();
      }
      if (showUPISection && checkedPymtMeth == 11) {
        // Branch UPI save logic by suboption (Java-style)
        if (upiSubOption == 0) {
          // TODO: Save PhonePe flow data (you may implement as needed)
          await _saveUpiPaymentToDatabase();
        } else if (upiSubOption == 1) {
          // TODO: Save BHIM/Paytm data
          await _saveUpiPaymentToDatabase();
        } else if (upiSubOption == 2) {
          // TODO: Save IRCTC iPay UPI data
          await _saveUpiPaymentToDatabase();
        }
      }
      return true;
    } catch (e) {
      if (showSnackbar) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not save form: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  void _loadWalletPaymentData(Map<String, dynamic> row) {
    // Don't autofill if manual payment is selected
    if (manualPymtCheckboxFlag) {
      print('Manual payment selected - skipping wallet autofill');
      return;
    }
    final dec = Cryptography.decryptPassword;
    setState(() {
      selectedWallet = dec(row['WALLET'] ?? '');
      walletUsernameController.text = dec(row['W_USER'] ?? '');
      walletPasswordController.text = dec(row['W_PASS'] ?? '');
    });
  }

  Future<void> _saveWalletPaymentToDatabase() async {
    try {
      final db = await MainDB.instance.database;
      final walletProviderEnc = Cryptography.encryptPassword(selectedWallet);
      final wUsernameEnc = Cryptography.encryptPassword(
        walletUsernameController.text,
      );
      final wPasswordEnc = Cryptography.encryptPassword(
        walletPasswordController.text,
      );
      final updateData = {
        'PYMT_CHOICE': 'CASH_CARD',
        'WALLET': walletProviderEnc,
        'W_USER': wUsernameEnc,
      };
      if (selectedWallet != 'OLAMONEY Wallet') {
        updateData['W_PASS'] = wPasswordEnc;
      }
      await db.update(
        MainDB.tableName,
        updateData,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );
      await db.update(
        MainDB.tableName,
        {
          'WALLET': walletProviderEnc,
          'W_USER': wUsernameEnc,
          'W_PASS': selectedWallet != 'OLAMONEY Wallet' ? wPasswordEnc : null,
        },
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );
    } catch (e) {
      print('Error saving wallet data: $e');
    }
  }

  // --- Save IRCTC eWallet PIN if this method is selected ---
  Future<void> _saveIRCTCeWalletToDatabase() async {
    try {
      final db = await MainDB.instance.database;
      final txnPwdEnc = Cryptography.encryptPassword(
        irctcWalletPinController.text.trim(),
      );
      await db.update(
        MainDB.tableName,
        {'TXN_PWD': txnPwdEnc},
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );
    } catch (e) {
      print('Error saving IRCTC eWallet txn password: $e');
    }
  }

  Future<void> _saveUpiPaymentToDatabase() async {
    try {
      final db = await MainDB.instance.database;

      final vpaBytes = Cryptography.encryptPassword(vpaController.text);
      final upiBankBytes = Cryptography.encryptPassword(selectedUpiBank);
      final upiPinBytes = Cryptography.encryptPassword(upiPinEdController.text);

      await db.update(
        MainDB.tableName,
        {
          'PYMT_AUTO': manualPymtCheckboxFlag.toString(),
          'PYMT_CHOICE': 'UPI_VPA',
          'VPA': vpaBytes,
          'UPI_BANK': upiBankBytes,
          'AUTO_OPEN': autoOpenUPIApp ? '1' : '0',
          'UPI_AUTOMATE': automateUPICb ? '1' : '0',
          'UPI_DEFAULT_BANK': defaultbankCb ? '1' : '0',
          'UPI_PIN': upiPinBytes,
        },
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );
      await db.update(
        MainDB.tableName,
        {
          'PYMT_AUTO': manualPymtCheckboxFlag.toString(),
          'VPA': vpaBytes,
          'UPI_BANK': upiBankBytes,
          'AUTO_OPEN': autoOpenUPIApp ? '1' : '0',
          'UPI_AUTOMATE': automateUPICb ? '1' : '0',
          'UPI_DEFAULT_BANK': defaultbankCb ? '1' : '0',
          'UPI_PIN': upiPinBytes,
        },
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );
    } catch (e) {
      print('Error saving UPI payment data: $e');
    }
  }

  // --- Load IRCTC eWallet PIN if saved in DB ---
  Future<void> _loadIRCTCeWalletFromDatabase() async {
    // Don't autofill if manual payment is selected
    if (manualPymtCheckboxFlag) {
      print('Manual payment selected - skipping IRCTC eWallet autofill');
      return;
    }
    try {
      final db = await MainDB.instance.database;
      final result = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );
      if (result.isNotEmpty) {
        final row = result.first;
        final txnPwdEnc = row['TXN_PWD'] as String?;
        if (txnPwdEnc != null && txnPwdEnc.isNotEmpty) {
          final decryptedPwd = Cryptography.decryptPassword(txnPwdEnc);
          irctcWalletPinController.text = decryptedPwd;
        }
      }
    } catch (e) {
      print('Error loading IRCTC eWallet txn password: $e');
    }
  }

  void webPymtChanged(int index) {
    setState(() {
      checkedPymtMeth = index;

      showCardSection = (index == 6);
      showNetbankSection = (index == 9);
      showUPISection = (index == 11);
      showWalletSection = (index == 7);
      showMultipleSection = (index == 14);
      showIMudraSection = (index == 12);

      // --- Java CardType reset logic ---
      if (!showCardSection) {
        cardNo1Controller.clear();
        cardNo2Controller.clear();
        cardNo3Controller.clear();
        cardNo4Controller.clear();
        cardHolderController.clear();
        cardType = cardTypeMap[0];
        cardTypeShow = false;
        selectedBankName = '';
        selectedExpiryMonth = '';
        selectedExpiryYear = '';
        debitCvvController.clear();
        pinController.clear();
        staticPassController.clear();
        hdfcAutofill = false;
      }

      if (!showNetbankSection) {
        netBankName = '';
        netBankingType = 'Personal';
        sbiBankName = '';
        sbiBankNameVisible = false;
        corpIdVisible = false;
        nbUsernameController.clear();
        nbPasswordController.clear();
        corpIdController.clear();
      }

      if (!showUPISection) {
        vpaController.clear();
        selectedUpiBank = '';
        autoOpenUPIApp = false;
        automateUPICb = false;
        defaultbankCb = false;
        upiPinEdController.clear();
      }

      if (!showWalletSection) {
        selectedWallet = '';
        walletUsernameController.clear();
        walletPasswordController.clear();
      }

      if (!showIMudraSection) {
        podMobileController.clear();
      }

      if (!showMultipleSection) {
        selectedMultiplePaymentOption = 'Select Payment Method';
        multiplePaymentAmountController.clear();
      }

      // If switching payment method, if manual payment is active, always clear payment autofill
      if (manualPymtCheckboxFlag) {
        _clearAllPaymentData();
      }
    });
  }

  Future<void> _saveMultiplePaymentToDatabase() async {
    try {
      final db = await MainDB.instance.database;

      final multiplePymtChoiceEnc = Cryptography.encryptPassword(
        selectedMultiplePaymentOption,
      );
      final multiplePymtAmount = multiplePaymentAmountController.text.trim();

      await db.update(
        MainDB.tableName,
        {
          'MULTIPLE_PYMT_CHOICE': multiplePymtChoiceEnc,
          'MULTIPLE_PYMT_AMOUNT': multiplePymtAmount,
        },
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );
    } catch (e) {
      print('Error saving multiple payment data: $e');
    }
  }

  Future<void> _loadMultiplePaymentData() async {
    // Don't autofill if manual payment is selected
    if (manualPymtCheckboxFlag) {
      print('Manual payment selected - skipping multiple payment autofill');
      return;
    }
    try {
      final db = await MainDB.instance.database;
      final result = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );

      if (result.isNotEmpty) {
        final row = result.first;

        final paymentChoice = row['PYMT_CHOICE']?.toString() ?? '';
        final paymentAutofillEnabled = row['PYMT_AUTO']?.toString() == 'true';

        if (!paymentAutofillEnabled && paymentChoice == 'MULTIPLE_GATEWAY') {
          final multiplePymtChoiceBytes =
              row['MULTIPLE_PYMT_CHOICE'] as String?;
          if (multiplePymtChoiceBytes != null &&
              multiplePymtChoiceBytes.isNotEmpty) {
            try {
              final decryptedChoice = Cryptography.decryptPassword(
                multiplePymtChoiceBytes,
              );
              setState(() {
                selectedMultiplePaymentOption = decryptedChoice;
              });
            } catch (e) {
              print('Error decrypting multiple payment choice: $e');
            }
          }

          final amount = row['MULTIPLE_PYMT_AMOUNT']?.toString() ?? '';
          if (amount.isNotEmpty) {
            multiplePaymentAmountController.text = amount;
          }
        }

        if (!paymentAutofillEnabled && paymentChoice == 'UPI_VPA') {
          _loadUpiFromDb(row);
        }

        if (!paymentAutofillEnabled && paymentChoice == 'CASH_CARD') {
          _loadWalletPaymentData(row);
        }
      }
    } catch (e) {
      print('Error loading multiple payment data: $e');
    }
  }

  Future<void> _loadCardPaymentData() async {
    // Don't autofill if manual payment is selected
    if (manualPymtCheckboxFlag) {
      print('Manual payment selected - skipping card payment autofill');
      return;
    }
    try {
      final db = await MainDB.instance.database;
      final result = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );

      if (result.isNotEmpty) {
        final row = result.first;

        if (row['CARD_NO'] != null &&
            row['PYMT_AUTO']?.toString() != 'true' &&
            (row['PYMT_CHOICE']?.toString() == 'DEBIT_CARD' ||
                row['PYMT_CHOICE']?.toString() == 'CREDIT_CARD' ||
                row['PYMT_CHOICE']?.toString() == 'IRCTC_IPAY')) {
          _loadDebitCardWithPinFromDb(row);

          if (row['PYMT_CHOICE']?.toString() == 'CREDIT_CARD') {
            creditCardSet = true;
          }
        }
      }
    } catch (e) {
      print('Error loading card payment data: $e');
    }
  }

  Future<void> _loadWebWalletData() async {
    // Don't autofill if manual payment is selected
    if (manualPymtCheckboxFlag) {
      print('Manual payment selected - skipping web wallet autofill');
      return;
    }
    try {
      final db = await MainDB.instance.database;
      final result = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );

      if (result.isNotEmpty) {
        final row = result.first;

        if (row['PYMT_AUTO']?.toString() != 'true' &&
            row['PYMT_CHOICE']?.toString() == 'CASH_CARD') {
          _loadWalletPaymentData(row);
        }
      }
      // --- Java parity: Load IRCTC eWallet PIN on load ---
      await _loadIRCTCeWalletFromDatabase();
    } catch (e) {
      print('Error loading web wallet data: $e');
    }
  }

  Future<void> _loadIrctcEWalletData() async {
    try {
      final db = await MainDB.instance.database;
      final result = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );

      if (result.isNotEmpty) {
        final row = result.first;

        if (row['PYMT_AUTO']?.toString() != 'true' &&
            row['PYMT_CHOICE']?.toString() == 'E_WALLET') {
          final txnPasswordBytes = row['TXN_PWD'] as String?;
          if (txnPasswordBytes != null && txnPasswordBytes.isNotEmpty) {
            final decryptedPassword = Cryptography.decryptPassword(
              txnPasswordBytes,
            );
            irctcWalletPinController.text = decryptedPassword;
          }
        }
      }
    } catch (e) {
      print('Error loading IRCTC eWallet data: $e');
    }
  }

  Future<void> _loadNetbankingData() async {
    // Don't autofill if manual payment is selected
    if (manualPymtCheckboxFlag) {
      print('Manual payment selected - skipping netbanking autofill');
      return;
    }
    try {
      final db = await MainDB.instance.database;
      final result = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );

      if (result.isNotEmpty) {
        final row = result.first;

        int selectedTab = _tabController.index;
        if (selectedTab == 0) {
          if (row['PYMT_AUTO']?.toString() != 'true' &&
              row['PYMT_CHOICE']?.toString() == 'NETBANKING') {
            final corpIdBytes = row['CORP_ID'] as String?;
            final sbiBankNameBytes = row['SBI_NAME'] as String?;
            final netBankNameBytes = row['NB_NAME'] as String?;
            final netBankingTypeBytes = row['NB_TYPE'] as String?;
            final nbUsernameBytes = row['NB_USER'] as String?;
            final nbPasswordBytes = row['NB_PWD'] as String?;

            if (netBankingTypeBytes != null) {
              final decryptedType = Cryptography.decryptPassword(
                netBankingTypeBytes,
              );
              setState(() {
                netBankingType =
                    getKeyFromValue({
                      'Personal': 'R',
                      'Corporate': 'C',
                    }, decryptedType) ??
                    'Personal';
              });
            }

            if (sbiBankNameBytes != null) {
              final decryptedSbiName = Cryptography.decryptPassword(
                sbiBankNameBytes,
              );

              sbiBankName = getKeyFromValue(sbiBankMap, decryptedSbiName) ?? '';
            }

            if (corpIdBytes != null) {
              corpIdController.text = Cryptography.decryptPassword(corpIdBytes);
            }

            if (nbUsernameBytes != null) {
              nbUsernameController.text = Cryptography.decryptPassword(
                nbUsernameBytes,
              );
            }

            if (nbPasswordBytes != null) {
              nbPasswordController.text = Cryptography.decryptPassword(
                nbPasswordBytes,
              );
            }

            if (netBankNameBytes != null) {
              final decryptedBankName = Cryptography.decryptPassword(
                netBankNameBytes,
              );
              netBankName = decryptedBankName;
            }
          }
        } else if (selectedTab == 1) {
          await _loadRCNetbankingData();
        }
      }
    } catch (e) {
      print('Error loading netbanking data: $e');
    }
  }

  Future<void> _loadRCNetbankingData() async {
    try {
      final db = await RCPaymentDB.instance.database;
      final result = await db.query(
        RCPaymentDB.tableName,
        where: 'FORM_NAME = ? AND PYMT_ENTITY LIKE ?',
        whereArgs: [widget.formName, '%NETBANKING%'],
      );

      if (result.isNotEmpty) {
        final row = result.first;

        final corpIdBytes = row['CORP_ID'] as String?;
        final netBankNameBytes = row['NB_NAME'] as String?;
        final netBankingTypeBytes = row['NB_TYPE'] as String?;
        final nbUsernameBytes = row['NB_USER'] as String?;
        final nbPasswordBytes = row['NB_PWD'] as String?;

        if (netBankingTypeBytes != null) {
          final decryptedType = Cryptography.decryptPassword(
            netBankingTypeBytes,
          );
          setState(() {
            netBankingType =
                getKeyFromValue({
                  'Personal': 'R',
                  'Corporate': 'C',
                }, decryptedType) ??
                'Personal';
          });
        }

        if (corpIdBytes != null) {
          corpIdController.text = Cryptography.decryptPassword(corpIdBytes);
        }

        if (nbUsernameBytes != null) {
          nbUsernameController.text = Cryptography.decryptPassword(
            nbUsernameBytes,
          );
        }

        if (nbPasswordBytes != null) {
          nbPasswordController.text = Cryptography.decryptPassword(
            nbPasswordBytes,
          );
        }

        if (netBankNameBytes != null) {
          final decryptedBankName = Cryptography.decryptPassword(
            netBankNameBytes,
          );
          netBankName = decryptedBankName;
        }
      }
    } catch (e) {
      print('Error loading RC netbanking data: $e');
    }
  }

  Future<void> _loadWebUpiData() async {
    try {
      final db = await MainDB.instance.database;
      final result = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );

      if (result.isNotEmpty) {
        final row = result.first;

        if (row['PYMT_AUTO']?.toString() != 'true' &&
            row['PYMT_CHOICE']?.toString() == 'UPI_VPA') {
          _loadUpiFromDb(row);
        }
      }
    } catch (e) {
      print('Error loading web UPI data: $e');
    }
  }

  Future<void> _loadPodData() async {
    try {
      final mainDb = await MainDB.instance.database;
      final mainResult = await mainDb.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );

      if (mainResult.isNotEmpty) {
        final row = mainResult.first;

        if (row['PYMT_AUTO']?.toString() != 'true' &&
            row['PYMT_CHOICE']?.toString() == 'COD') {
          final podOptBytes = row['POD'] as String?;
          if (podOptBytes != null && podOptBytes.isNotEmpty) {
            final decryptedOpt = Cryptography.decryptPassword(podOptBytes);
            selectedMultiplePaymentOption = decryptedOpt;
          }
        }
      }

      final podDb = await PodDB.instance.database;
      final podResult = await podDb.query(
        PodDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );

      if (podResult.isNotEmpty) {
        final podRow = podResult.first;
        final mobile = podRow['MOBILE']?.toString() ?? '';
        if (mobile.isNotEmpty) {
          podMobileController.text = mobile;
        }
      }
    } catch (e) {
      print('Error loading POD data: $e');
    }
  }

  Future<void> _loadMultiplePymtData() async {
    try {
      final db = await MainDB.instance.database;
      final result = await db.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );

      if (result.isNotEmpty) {
        final row = result.first;

        if (row['PYMT_AUTO']?.toString() != 'true' &&
            row['PYMT_CHOICE']?.toString() == 'MULTIPLE_GATEWAY') {
          final multiplePymtChoiceBytes =
              row['MULTIPLE_PYMT_CHOICE'] as String?;
          if (multiplePymtChoiceBytes != null &&
              multiplePymtChoiceBytes.isNotEmpty) {
            final decryptedChoice = Cryptography.decryptPassword(
              multiplePymtChoiceBytes,
            );
            setState(() {
              selectedMultiplePaymentOption = decryptedChoice;
            });
          }

          final amount = row['MULTIPLE_PYMT_AMOUNT']?.toString() ?? '';
          if (amount.isNotEmpty) {
            multiplePaymentAmountController.text = amount;
          }
        }
      }
    } catch (e) {
      print('Error loading multiple payment data: $e');
    }
  }

  Future<void> _loadRCWalletData() async {
    // Don't autofill if manual payment is selected
    if (manualPymtCheckboxFlag) {
      print('Manual payment selected - skipping RC wallet autofill');
      return;
    }
    try {
      final db = await RCPaymentDB.instance.database;
      final result = await db.query(
        RCPaymentDB.tableName,
        where: 'FORM_NAME = ? AND PYMT_ENTITY LIKE ?',
        whereArgs: [widget.formName, '%EWALLET%'],
      );

      if (result.isNotEmpty) {
        final row = result.first;

        final walletBytes = row['WALLET'] as String?;
        final wUsernameBytes = row['W_USER'] as String?;

        if (wUsernameBytes != null && wUsernameBytes.isNotEmpty) {
          walletUsernameController.text = Cryptography.decryptPassword(
            wUsernameBytes,
          );
        }

        if (walletBytes != null && walletBytes.isNotEmpty) {
          final decryptedWallet = Cryptography.decryptPassword(walletBytes);
          selectedWallet = decryptedWallet;
        }
      }
    } catch (e) {
      print('Error loading RC wallet data: $e');
    }
  }

  Future<void> _loadRCUpiData() async {
    // Don't autofill if manual payment is selected
    if (manualPymtCheckboxFlag) {
      print('Manual payment selected - skipping RC UPI autofill');
      return;
    }
    try {
      final rcDb = await RCPaymentDB.instance.database;
      final rcResult = await rcDb.query(
        RCPaymentDB.tableName,
        where: 'FORM_NAME = ? AND PYMT_ENTITY LIKE ?',
        whereArgs: [widget.formName, '%UPI%'],
      );

      if (rcResult.isNotEmpty) {
        final row = rcResult.first;

        final vpaBytes = row['UPI_ID'] as String?;
        if (vpaBytes != null && vpaBytes.isNotEmpty) {
          vpaRCController.text = Cryptography.decryptPassword(vpaBytes);
        }
      }

      final mainDb = await MainDB.instance.database;
      final mainResult = await mainDb.query(
        MainDB.tableName,
        where: 'FORM_NAME = ?',
        whereArgs: [widget.formName],
      );

      if (mainResult.isNotEmpty) {
        final row = mainResult.first;

        final autoOpen = row['AUTO_OPEN']?.toString() == '1';
        final automateUpi = row['UPI_AUTOMATE']?.toString() == '1';
        final defaultBank = row['UPI_DEFAULT_BANK']?.toString() == '1';
        String upiPin = row['UPI_PIN']?.toString() ?? '';

        setState(() {
          if (automateUpi &&
              (SplashActivity.isGoldUser == 2 ||
                  APIConsts.UPI_TRIAL_OPTED == 'N')) {
            isLoadingUpi = true;
            automateUPICbRC = true;
            isLoadingUpi = false;
          }

          defaultbankCbRC = defaultBank;

          if (upiPin.length == 3 || upiPin.length == 5) {
            upiPin = '0$upiPin';
          }
          upiPinEdRCController.text = upiPin;

          if (autoOpen) {
            autoOpenUPIAppRC = true;
          } else {
            final vpa = vpaRCController.text.toLowerCase();
            final hasValidUpiApp =
                vpa.endsWith('@ybl') ||
                vpa.contains('@pt') ||
                vpa.endsWith('@iPayUpi') ||
                vpa.endsWith('@ok') ||
                vpa.endsWith('@ibl') ||
                vpa.contains('@axl');

            if (hasValidUpiApp && vpaRCController.text.isNotEmpty) {
              autoOpenUPIAppRC = true;
            } else {
              autoOpenUPIAppRC = false;
            }
          }
        });
      }
    } catch (e) {
      print('Error loading RC UPI data: $e');
    }
  }

  Future<void> _loadiMudraData() async {
    try {
      final db = await RCPaymentDB.instance.database;
      final result = await db.query(
        RCPaymentDB.tableName,
        where: 'FORM_NAME = ? AND PYMT_ENTITY LIKE ?',
        whereArgs: [widget.formName, '%IMUDRA%'],
      );

      if (result.isNotEmpty) {
        final row = result.first;

        final txnPasswordBytes = row['IMOBILE_MOB'] as String?;
        if (txnPasswordBytes != null && txnPasswordBytes.isNotEmpty) {
          final decryptedMobile = Cryptography.decryptPassword(
            txnPasswordBytes,
          );
          podMobileController.text = decryptedMobile;
        }
      }
    } catch (e) {
      print('Error loading iMudra data: $e');
    }
  }

  String? getKeyFromValue(Map<String, dynamic> map, String value) {
    for (final entry in map.entries) {
      if (entry.value.toString() == value) {
        return entry.key;
      }
    }
    return map.keys.isNotEmpty ? map.keys.first : null;
  }

  Widget _sectionPaymentRC() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: buildGradientBorderCard(
        color: Colors.grey[900]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.payment_option,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontFamily: 'Poppins',
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'Rail Connect payment options',
              style: TextStyle(color: Colors.white70),
            ),
          ],
        ),
      ),
    );
  }

  @deprecated
  void _bookNow() {
    setState(() {
      isLoading = true;
    });

    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Booking initiated!'),
          backgroundColor: Colors.green,
        ),
      );
    });
  }

  Widget _sectionAds() {
    // TODO: Inline and/or banner ads here
    return Container(
      height: 60,
      color: Colors.black,
      alignment: Alignment.center,
      child: const Text('Ad Banner Here', style: TextStyle(color: Colors.grey)),
    );
  }

  Widget buildGradientBorderCard({
    required Widget child,
    Color color = kDialogColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        color: Color(0xFF181728),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(color: Color(0xFF5C61FF), width: 1.9),
      ),
      child: Padding(padding: const EdgeInsets.all(12.5), child: child),
    );
  }

  Future<void> _loadSavedCredentials() async {
    if (usernameController.text.trim().isEmpty) {
      setState(() {
        _credentialsLoaded = false;
        _credentialsError = '';
      });
      return;
    }
    final db = await MainDB.instance.database;
    final result = await db.query(
      MainDB.tableName,
      columns: ['PASSWORD'],
      where: 'USERNAME = ?',
      whereArgs: [usernameController.text.trim()],
      limit: 1,
    );
    if (result.isNotEmpty) {
      final encrypted = result.first['PASSWORD']?.toString() ?? '';
      String decrypted;
      try {
        decrypted = Cryptography.decryptPassword(encrypted);
      } catch (e) {
        decrypted = '';
      }
      if (decrypted.isNotEmpty) {
        passwordController.text = decrypted;
        setState(() {
          _credentialsLoaded = true;
          _credentialsError = '';
        });
        return;
      }
    }
    setState(() {
      passwordController.text = '';
      _credentialsLoaded = false;
      _credentialsError = 'No saved password found for this username';
    });
  }

  Future<void> _saveCredentials() async {
    setState(() {
      _isSavingCredentials = true;
      _credentialsError = '';
    });
    final username = usernameController.text.trim();
    final password = passwordController.text;

    if (username.isEmpty || password.isEmpty) {
      setState(() {
        _isSavingCredentials = false;
        _credentialsError = 'Username and Password required';
      });
      _showErrorSnack('Username and Password required');
      return;
    }
    if (!Cryptography.isValidUsername(username)) {
      setState(() {
        _isSavingCredentials = false;
        _credentialsError = 'Invalid username format.';
      });
      _showErrorSnack(
        'Invalid username format! Must be 3+ chars, no symbols except .-_',
      );
      return;
    }

    final encrypted = Cryptography.encryptPassword(password);
    final db = await MainDB.instance.database;
    final result = await db.query(
      MainDB.tableName,
      where: 'USERNAME = ?',
      whereArgs: [username],
    );
    if (result.isNotEmpty) {
      await db.update(
        MainDB.tableName,
        {'PASSWORD': encrypted},
        where: 'USERNAME = ?',
        whereArgs: [username],
      );
    } else {
      await db.insert(MainDB.tableName, {
        'USERNAME': username,
        'PASSWORD': encrypted,
      });
    }
    await db.update(
      MainDB.tableName,
      {'USERNAME': username, 'PASSWORD': encrypted},
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );
    setState(() {
      _isSavingCredentials = false;
      _credentialsLoaded = true;
      _credentialsError = '';
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Credentials saved!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  bool _validateCredentials({bool showSnackbar = true}) {
    final username = usernameController.text.trim();
    final password = passwordController.text;
    if (username.isEmpty) {
      setState(() => _credentialsError = 'Username required');
      if (showSnackbar) _showErrorSnack('Please enter your IRCTC username');
      return false;
    }
    if (password.isEmpty) {
      setState(() => _credentialsError = 'Password required');
      if (showSnackbar) _showErrorSnack('Please enter your IRCTC password');
      return false;
    }
    if (!Cryptography.isValidUsername(username)) {
      setState(() => _credentialsError = 'Invalid username format');
      if (showSnackbar) _showErrorSnack('Invalid IRCTC username format');
      return false;
    }
    setState(() => _credentialsError = '');
    return true;
  }

  void _showErrorSnack(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _validateJourneyDetails() {
    formErrors.clear();

    if (fromStnController.text.trim().isEmpty) {
      formErrors['from'] = 'From station is required';
    }

    if (toStnController.text.trim().isEmpty) {
      formErrors['to'] = 'To station is required';
    }

    if (journeyDateController.text.trim().isEmpty) {
      formErrors['date'] = 'Journey date is required';
    }

    String mTrainNo = trainController.text.trim();
    if (mTrainNo.isEmpty) {
      formErrors['train'] = 'Train number/name is required';
    } else {
      if (mTrainNo.contains('-')) {
        mTrainNo = mTrainNo.split('-')[0].trim();
      }

      if (!RegExp(r'^[0-9]+$').hasMatch(mTrainNo)) {
        formErrors['train'] = 'Please enter a valid train number';
      }
    }

    if (selectedQuota.isEmpty) {
      formErrors['quota'] = 'Select a quota';
    }

    if (selectedClass.isEmpty) {
      formErrors['class'] = 'Select a travel class';
    }

    if (showFareLimit && fareLimitController.text.trim().isNotEmpty) {
      try {
        int fareLimit = int.parse(fareLimitController.text.trim());
        if (fareLimit <= 0) {
          formErrors['fare'] = 'Enter a valid fare limit';
        }
      } catch (e) {
        formErrors['fare'] = 'Enter a valid fare limit';
      }
    }

    if (passengers.length > maxPassengers) {
      if (maxPassengers == 6) {
        formErrors['passengers'] = 'Maximum 6 passengers allowed';
      } else {
        formErrors['passengers'] = 'Maximum 4 passengers allowed for Tatkal';
      }
    }

    bool foundError = false;
    bool handicappedTravelling = false;
    bool escortTravelling = false;
    bool olderMaleInLadies = false;
    bool shortAge = false;
    bool smallPName = false;

    for (int i = 0; i < passengers.length; i++) {
      final p = passengers[i];
      p.error = '';

      final name = p.nameCtrl.text.trim();
      final ageText = p.ageCtrl.text.trim();

      if (name.isEmpty) {
        p.error = 'Name required';
        foundError = true;
      } else if (name.length < 3) {
        p.error = 'Enter min 3 character name';
        foundError = true;
        smallPName = true;
      }

      if (ageText.isEmpty) {
        p.error = 'Age required';
        foundError = true;
      } else {
        int? ageInt = int.tryParse(ageText);
        if (ageInt == null) {
          p.error = 'Enter valid age';
          foundError = true;
        } else {
          if (ageInt < 5) {
            p.error = 'Age must be at least 5 years';
            foundError = true;
            shortAge = true;
          } else if (ageInt > 125) {
            p.error = 'Age cannot exceed 125 years';
            foundError = true;
          }

          if (selectedQuota == 'Ladies' && p.gender == 'Male' && ageInt > 11) {
            p.error = 'Males >11 not allowed in Ladies quota';
            foundError = true;
            olderMaleInLadies = true;
          }

          if ((p.gender == 'Male' && ageInt >= 60) ||
              (p.gender == 'Female' && ageInt >= 58)) {}

          if (ageInt >= 5 && ageInt <= 11) {}
        }
      }

      if (p.gender.isEmpty) {
        p.error = 'Gender required';
        foundError = true;
      }

      if (p.nationality.isEmpty) {
        p.error = 'Nationality required';
        foundError = true;
      }

      if (p.nationality != 'India' &&
          (p.idType.isEmpty || p.idNo.trim().isEmpty)) {
        p.error = 'ID required for non-Indians';
        foundError = true;
      }

      if (selectedQuota == 'Physically Handicapped') {
        if (p.cardNo.trim().isEmpty) {
          p.error = 'Card number required for handicapped quota';
          foundError = true;
        }
        if (p.cardValidity.trim().isEmpty) {
          p.error = 'Card validity required for handicapped quota';
          foundError = true;
        }
        if (p.dob.trim().isEmpty) {
          p.error = 'Date of birth required for handicapped quota';
          foundError = true;
        }

        if (p.concession == 'Escort') {
          escortTravelling = true;
          if (p.idType.isEmpty || p.idType == 'ID Type') {
            p.error = 'ID Type required for escort';
            foundError = true;
          }
          if (p.idNo.trim().isEmpty) {
            p.error = 'ID Number required for escort';
            foundError = true;
          }
        } else {
          handicappedTravelling = true;
        }
      }
    }

    if (!handicappedTravelling && escortTravelling) {
      formErrors['passengers'] =
          'Escort must travel with handicapped passenger';
      foundError = true;
    }

    if (olderMaleInLadies) {
      formErrors['passengers'] =
          'Males above 11 years not allowed in Ladies quota';
      foundError = true;
    }

    if (shortAge) {
      formErrors['passengers'] = 'Passenger age must be at least 5 years';
      foundError = true;
    }

    if (smallPName) {
      formErrors['passengers'] = 'Passenger name must be at least 3 characters';
      foundError = true;
    }

    if (foundError && !formErrors.containsKey('passengers')) {
      formErrors['passengers'] = 'Please fix errors in passenger details';
    }
  }

  void _showBookingFormSummaryError() {
    final firstError = formErrors.values.isNotEmpty
        ? formErrors.values.first
        : 'Please fix form errors';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(firstError), backgroundColor: Colors.red),
    );
  }

  @deprecated
  Future<void> _onBookNow() async {
    if (!_validateCredentials()) return;
    _validateJourneyDetails();
    _validateChildDetails();
    _validateMiscellaneous();
    await saveForm(showSnackbar: true);
    setState(() {});
    if (formErrors.isNotEmpty) {
      _showBookingFormSummaryError();

      if (formErrors.containsKey('from')) {
        FocusScope.of(context).requestFocus(FocusNode());
      } else if (formErrors.containsKey('to')) {
      } else if (formErrors.containsKey('date')) {
      } else if (formErrors.containsKey('train')) {}
      return;
    }

    await _saveMiscellaneousData();

    _bookNow();
  }

  Future<void> _saveMiscellaneousData() async {
    final db = await MainDB.instance.database;

    int bookingOptValue = 0;
    switch (bookingOption) {
      case 'none':
        bookingOptValue = 0;
        break;
      case 'same_coach':
        bookingOptValue = 1;
        break;
      case 'min_lower_berth':
        bookingOptValue = 2;
        break;
      case 'confirm_2_lower':
        bookingOptValue = 3;
        break;
    }

    int paymentModeIndex = paymentModeItems.indexOf(paymentModeWeb);
    if (paymentModeIndex == -1) paymentModeIndex = 0;

    await db.update(
      MainDB.tableName,
      {
        'PYMT_AUTO': manualPymtCheckboxFlag.toString(),
        'COACH_PREF': coachPreferred ? '1' : '0',
        'COACH_ID': coachPreferred ? coachController.text : null,
        'VIKALP': vikalp ? '1' : '0',
        'AUTO_UPG': autoUpgradation ? '1' : '0',
        'ONLY_CONFIRM': onlyConfirmBerths ? '1' : '0',
        'BOOKING_OPT': bookingOptValue,
        'MOBILE_NO': mobileController.text,
        'WB_PYMT_MODE': paymentModeIndex,
        'CAPTCHA_AUTOFILL': autofillCaptchas ? '1' : '0',
        'OTP_AUTOFILL': autofillOTP ? '1' : '0',
        'P_COUNT': passengers.length,
        'C_COUNT': childCount,
      },
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );

    await _saveInsuranceData();
  }

  Future<void> _saveInsuranceData() async {
    final db = await MainDB.instance.database;
    await db.update(
      MainDB.tableName,
      {'INSURANCE': travelInsurance == 'yes' ? '1' : '2'},
      where: 'FORM_NAME = ?',
      whereArgs: [widget.formName],
    );
  }

  /// Clears all payment data when manual payment is selected
  void _clearAllPaymentData() {
    // Clear card payment data
    cardNo1Controller.clear();
    cardNo2Controller.clear();
    cardNo3Controller.clear();
    cardNo4Controller.clear();
    cardHolderController.clear();
    selectedBankName = '';
    selectedExpiryMonth = '';
    selectedExpiryYear = '';
    debitCvvController.clear();
    pinController.clear();
    staticPassController.clear();
    hdfcAutofill = false;

    // Clear UPI data
    vpaController.clear();
    selectedUpiBank = '';
    autoOpenUPIApp = false;
    automateUPICb = false;
    defaultbankCb = false;
    upiPinEdController.clear();
    upiSubOption = 0;

    // Clear wallet data
    selectedWallet = '';
    walletUsernameController.clear();
    walletPasswordController.clear();

    // Clear netbanking data
    netBankName = '';
    netBankingType = 'Personal';
    sbiBankName = '';
    sbiBankNameVisible = false;
    corpIdVisible = false;
    nbUsernameController.clear();
    nbPasswordController.clear();
    corpIdController.clear();

    // Clear multiple payment data
    selectedMultiplePaymentOption = 'Select Payment Method';
    multiplePaymentAmountController.clear();

    // Clear IRCTC eWallet PIN
    irctcWalletPinController.clear();

    // Clear IMudra payment
    podMobileController.clear();

    // Clear RC payment data
    vpaRCController.clear();
    upiPinEdRCController.clear();
    autoOpenUPIAppRC = false;
    automateUPICbRC = false;
    defaultbankCbRC = false;
    rcPinController.clear();

    // Reset payment method selections to default
    checkedPymtMeth = 6; // Default to payment gateway
    selectedPaymentMethod = 'payment_gateway';

    // Clear any other payment related fields

    print('All payment data cleared for manual filling');
  }

  /// Reloads payment data from database when manual payment is unchecked
  void _reloadPaymentDataFromDatabase() {
    // Only reload if manual payment is not active
    if (!manualPymtCheckboxFlag) {
      _loadCardPaymentData();
      _loadWebWalletData();
      _loadIrctcEWalletData();
      _loadNetbankingData();
      _loadWebUpiData();
      _loadPodData();
      _loadMultiplePymtData();
      _loadRCWalletData();
      _loadRCUpiData();
      _loadiMudraData();
      _loadMultiplePaymentData();

      print('Payment data reloaded from database');
    }
  }

  /// Checks if manual payment mode is properly configured for booking
  bool _validateManualPaymentMode() {
    if (manualPymtCheckboxFlag) {
      // Manual payment is selected - show warning to user
      showDialog(
        context: context,
        builder: (ctx) => AlertDialog(
          backgroundColor: const Color(0xFF1A1A2E),
          title: const Text(
            'Manual Payment Mode',
            style: TextStyle(color: Colors.white),
          ),
          content: const Text(
            'You have selected manual payment mode. During booking:\n\n'
            '• Payment information will NOT be auto-filled\n'
            '• You will need to enter all payment details manually\n'
            '• This may take more time during the booking process\n'
            '• Make sure you have all payment details ready\n\n'
            'Continue with manual payment?',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(ctx, false),
              child: const Text('Cancel', style: TextStyle(color: Colors.red)),
            ),
            TextButton(
              onPressed: () => Navigator.pop(ctx, true),
              child: const Text(
                'Continue Manual',
                style: TextStyle(color: Colors.orange),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(ctx, false);
                // Auto-enable payment autofill
                setState(() {
                  manualPymtCheckboxFlag = false;
                  _reloadPaymentDataFromDatabase();
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Payment autofill enabled. Payment data reloaded.',
                    ),
                    backgroundColor: Colors.green,
                    duration: Duration(seconds: 2),
                  ),
                );
              },
              child: const Text(
                'Enable Autofill',
                style: TextStyle(color: Colors.green),
              ),
            ),
          ],
        ),
      ).then((result) => result ?? false);

      return false; // Will be handled by dialog result
    }

    return true; // Normal autofill mode - proceed
  }

  /// Gets payment mode status for booking data
  Map<String, dynamic> getPaymentModeData() {
    return {
      'manualPaymentMode': manualPymtCheckboxFlag,
      'paymentAutofill': !manualPymtCheckboxFlag,
      'paymentMethod': getPaymentMethodForBooking(),
      'paymentChoice':
          getPymtChoiceMap(context)[checkedPymtMeth] ?? 'PAYMENT_GATEWAY',
    };
  }

  /// Gets the current payment method for booking based on manual mode
  String getPaymentMethodForBooking() {
    if (manualPymtCheckboxFlag) {
      return 'MANUAL';
    }
    return getPymtChoiceMap(context)[checkedPymtMeth] ?? 'PAYMENT_GATEWAY';
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
