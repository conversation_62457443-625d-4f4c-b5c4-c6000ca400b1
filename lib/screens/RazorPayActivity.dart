import 'package:flutter/material.dart';

class RazorPayActivity extends StatefulWidget {
  const RazorPayActivity({Key? key}) : super(key: key);

  @override
  State<RazorPayActivity> createState() => _RazorPayActivityState();
}

class _RazorPayActivityState extends State<RazorPayActivity>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this); // 2 tabs demo
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColor,
      body: Stack(
        children: [
          Column(
            children: [
              Container(
                color: Theme.of(context).primaryColor,
                child: TabBar(
                  controller: _tabController,
                  indicatorColor: Color(0xFFFF3377),
                  labelColor: Color(0xFFFFFF00),
                  unselectedLabelColor: Colors.white,
                  tabs: [
                    Tab(text: 'Tab 1'),
                    Tab(text: 'Tab 2'),
                  ],
                ),
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    Center(
                      child: Text(
                        'Tab 1 Content',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    Center(
                      child: Text(
                        'Tab 2 Content',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
              // RelativeLayout for Book Now
              Container(
                margin: EdgeInsets.only(
                  bottom: 10,
                  left: 10,
                  right: 10,
                  top: 10,
                ),
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: Center(
                  child: Text(
                    'Book Now',
                    style: TextStyle(
                      fontSize: 26,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              // AdView placeholder (can use google_mobile_ads package)
              Container(
                width: double.infinity,
                height: 50,
                color: Colors.transparent,
                child: Center(
                  child: Text(
                    'Ad Placeholder',
                    style: TextStyle(color: Colors.white70),
                  ),
                ),
              ),
            ],
          ),
          _loading
              ? Center(
                  child: SizedBox(
                    width: 60,
                    height: 60,
                    child: CircularProgressIndicator(
                      color: Theme.of(context).primaryColor,
                      strokeWidth: 6,
                    ),
                  ),
                )
              : SizedBox(),
        ],
      ),
    );
  }
}
