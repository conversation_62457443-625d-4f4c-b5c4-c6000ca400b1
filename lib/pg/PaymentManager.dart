import 'dart:async';

import 'package:flutter/material.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../core/Consts.dart';
import '../screens/splash_screen.dart';
import '../core/helper/mixpanel_manager.dart';
import '../server/FirestoreFunctions.dart';

class PaymentManager {
  static Razorpay? _razorpay;
  static bool paytmTxnCompleteFlag = false;
  static String paytmOrderId = "";
  static String orderId = "";
  static int tickets = 0;
  static int amount = 0;
  static String packName = "";
  static BuildContext? currentContext;

  static final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  static late StreamSubscription<List<PurchaseDetails>> _subscription;
  static bool _isAvailable = false;

  static void init() {
    _razorpay = Razorpay();
    _razorpay!.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay!.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay!.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);

    _initGooglePlayBilling();
  }

  static void dispose() {
    _razorpay?.clear();
    _subscription.cancel();
  }

  static Future<void> _initGooglePlayBilling() async {
    _isAvailable = await _inAppPurchase.isAvailable();
    if (_isAvailable) {
      _subscription = _inAppPurchase.purchaseStream.listen(
        (List<PurchaseDetails> purchaseDetailsList) {
          _listenToPurchaseUpdated(purchaseDetailsList);
        },
        onDone: () {
          _subscription.cancel();
        },
        onError: (error) {
          // Handle error
        },
      );
    }
  }

  static Future<void> initiateRazorpayPayment(
    BuildContext context,
    int paymentAmount,
    int ticketCount,
    String pName,
  ) async {
    currentContext = context;
    amount = paymentAmount;
    tickets = ticketCount;
    packName = pName;
    Consts.PACK_NAME = pName;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 20),
            Text("Loading Payment Gateway..."),
          ],
        ),
      ),
    );

    try {
      MixpanelManager().track("Launch razorpay", {
        "Amount": paymentAmount.toString(),
        "Pack name": pName,
      });

      await getOrderId(paymentAmount.toString());
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context);
      }
      _showError(context, "Error loading payment gateway. Please try again");

      if (tickets == 1) {
        await makePurchase(_getSKUFromPackName(pName));
      } else {
        await makeSubscription(_getSKUFromPackName(pName));
      }
    }
  }

  static Future<void> getOrderId(String amount) async {
    try {
      final response = await http.post(
        Uri.parse(
          'https://www.afrestudios.com/quick-tatkal/razorpay/pay_3.php',
        ),
        body: {'amount': amount},
      );

      if (response.statusCode == 200) {
        final orderId = response.body;
        if (!orderId.startsWith("ERROR")) {
          MixpanelManager().track("Razorpay order created", {
            "Amount": amount,
            "Pack name": packName,
          });
          await startPayment(orderId);
        } else {
          throw Exception(orderId);
        }
      } else {
        throw Exception("Server error: ${response.statusCode}");
      }
    } catch (e) {
      if (currentContext != null && currentContext!.mounted) {
        Navigator.pop(currentContext!); // Close loading dialog
        MixpanelManager().track("Switch to Google payment", {
          "Mode": "Razorpay",
          "Error": "getOrderId: ${e.toString()}",
        });

        if (tickets == 1) {
          await makePurchase(_getSKUFromPackName(packName));
        } else {
          await makeSubscription(_getSKUFromPackName(packName));
        }
      }
    }
  }

  static Future<void> startPayment(String orderId) async {
    if (currentContext == null || !currentContext!.mounted) return;

    Navigator.pop(currentContext!);

    var options = {
      'key': 'rzp_live_zh7pz4UZgGxkhm',
      'amount': amount * 100,
      'name': 'Quick Tatkal',
      'order_id': orderId,
      'description': packName,
      'theme': {'color': '#9C5DF7'},
      'retry': {'enabled': true, 'max_count': 4},
    };

    if (SplashScreenState.EMAIL != "NA") {
      options['prefill'] = {'email': SplashScreenState.EMAIL};
    }

    if (SplashScreenState.MOBILE_NO.startsWith("91") &&
        SplashScreenState.MOBILE_NO.length == 12) {
      options['prefill'] = options['prefill'] ?? {};
      (options['prefill'] as Map)['contact'] = SplashScreenState.MOBILE_NO
          .substring(2);
    }

    try {
      _razorpay?.open(options);
    } catch (e) {
      MixpanelManager().track("Switch to Google payment", {
        "Mode": "Razorpay",
        "Error": "startPayment: ${e.toString()}",
      });

      // Fallback to Google Play
      if (tickets == 1) {
        await makePurchase(_getSKUFromPackName(packName));
      } else {
        await makeSubscription(_getSKUFromPackName(packName));
      }
    }
  }

  static Future<void> initiatePaytmPayment(
    BuildContext context,
    int paymentAmount,
    int ticketCount,
    String pName,
  ) async {
    currentContext = context;
    amount = paymentAmount;
    tickets = ticketCount;
    packName = pName;
    Consts.PACK_NAME = pName;
    paytmTxnCompleteFlag = false;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 20),
            Text("Loading Payment Gateway..."),
          ],
        ),
      ),
    );

    try {
      MixpanelManager().track("Launch paytm", {
        "Amount": paymentAmount.toString(),
        "Pack name": pName,
      });

      await generatePaytmTxn(paymentAmount.toString());
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context); // Close loading dialog
        _showError(context, "Error loading payment gateway. Please try again");

        // Fallback to Google Play
        if (tickets == 1) {
          await makePurchase(_getSKUFromPackName(pName));
        } else {
          await makeSubscription(_getSKUFromPackName(pName));
        }
      }
    }
  }

  static Future<void> generatePaytmTxn(String amount) async {
    try {
      String userid = SplashScreenState.TID != "0"
          ? SplashScreenState.TID
          : "12345";

      final response = await http.post(
        Uri.parse(
          'https://www.afrestudios.com/quick-tatkal/paytm/init_txn.php',
        ),
        body: {
          'amount': amount,
          'userid': userid,
          'tickets': tickets.toString(),
        },
      );

      if (currentContext != null && currentContext!.mounted) {
        Navigator.pop(currentContext!); // Close loading dialog
      }

      if (response.statusCode == 200) {
        final result = response.body;
        if (result.startsWith("ERROR")) {
          throw Exception(result);
        } else if (result.contains("|")) {
          final parts = result.split("|");
          paytmOrderId = parts[1];
          orderId = paytmOrderId;

          MixpanelManager().track("Paytm order created", {
            "Amount": amount,
            "Pack name": packName,
          });

          await payWithPaytm(parts[1], parts[0], amount);
        } else {
          throw Exception("Invalid response format");
        }
      } else {
        throw Exception("Server error: ${response.statusCode}");
      }
    } catch (e) {
      if (currentContext != null && currentContext!.mounted) {
        MixpanelManager().track("Switch to Google payment", {
          "Mode": "Paytm",
          "Error": "generatePaytmTxn: ${e.toString()}",
        });

        // Fallback to Google Play
        if (tickets == 1) {
          await makePurchase(_getSKUFromPackName(packName));
        } else {
          await makeSubscription(_getSKUFromPackName(packName));
        }
      }
    }
  }

  static Future<void> payWithPaytm(
    String orderIdString,
    String txnTokenString,
    String txnAmountString,
  ) async {
    bool production = true;
    String midString = production
        ? "MnOKdt18048848664763"
        : "IFRmTf05358847421355";
    String host = production
        ? "https://securegw.paytm.in/"
        : "https://securegw-stage.paytm.in/";
    String callBackUrl = "$host/theia/paytmCallback?ORDER_ID=$orderIdString";

    try {
      // If Paytm SDK is not available, show error
      _showError(
        currentContext!,
        "Paytm payment not supported in this version",
      );

      /* Uncomment when Paytm SDK is available
      var response = await Paytm.startTransaction(
        mid: midString,
        orderId: orderIdString,
        amount: txnAmountString,
        txnToken: txnTokenString,
        callbackUrl: callBackUrl,
        isStaging: !production,
      );

      if (response != null) {
        _showLoadingDialog();
        await getPaytmTxnStatus();
      } else {
        MixpanelManager().track("Paytm error", {
          "Error": "Response null",
        });
        _showError(currentContext!, "Transaction cancelled");
      }
      */
    } catch (e) {
      MixpanelManager().track("Paytm error", {"Error": e.toString()});
      _showError(currentContext!, "Payment failed. Please try again");
    }
  }

  static Future<void> getPaytmTxnStatus() async {
    try {
      final response = await http.post(
        Uri.parse(
          'https://www.afrestudios.com/quick-tatkal/paytm/txn_status_2.php',
        ),
        body: {'orderid': paytmOrderId},
      );

      if (currentContext != null && currentContext!.mounted) {
        Navigator.pop(currentContext!); // Close loading dialog
      }

      if (!paytmTxnCompleteFlag) {
        paytmTxnCompleteFlag = true;

        if (response.statusCode == 200) {
          final output = response.body;
          if (output.startsWith("ERROR")) {
            MixpanelManager().track("Paytm server error", {"Output": output});
            _showError(
              currentContext!,
              "Payment pending. Please check back after sometime",
            );
            await _setPendingPayment();
          } else if (output.contains("|")) {
            final parts = output.split("|");
            if (parts[0] == "1") {
              // Success
              await onPaymentSuccess("PAYTM");
            } else if (parts[0] == "2") {
              // Pending
              MixpanelManager().track("Paytm payment pending");
              _showError(
                currentContext!,
                "Payment pending. Please check back after sometime",
              );
              await _setPendingPayment();
            } else {
              // Error
              MixpanelManager().track("Paytm server error", {"Output": output});
              if (Consts.isPaymentRestoring) {
                MixpanelManager().track("Paytm restore failed");
                _showError(currentContext!, "Last payment status: FAILED");
                final firestoreFunctions = FirestoreFunctions(currentContext);
                firestoreFunctions.deletePendingPayment();
              } else {
                _showError(currentContext!, "Payment Failed");
              }
            }
          } else {
            MixpanelManager().track("Paytm server error", {"Output": output});
            _showError(
              currentContext!,
              "Payment pending. Please check back after sometime",
            );
            await _setPendingPayment();
          }
        }
      }
    } catch (e) {
      if (currentContext != null && currentContext!.mounted) {
        Navigator.pop(currentContext!); // Close loading dialog
      }
      _showError(currentContext!, "Error checking payment status");
    }
  }

  // Google Play Billing
  static Future<void> initiateGooglePlayPayment(
    BuildContext context,
    int ticketCount,
    String pName,
  ) async {
    currentContext = context;
    tickets = ticketCount;
    packName = pName;
    Consts.PACK_NAME = pName;

    String sku = _getSKUFromPackName(pName);

    if (tickets == 1) {
      await makePurchase(sku);
    } else {
      await makeSubscription(sku);
    }
  }

  static Future<void> makePurchase(String skuPack) async {
    if (!_isAvailable) return;

    final ProductDetailsResponse response = await _inAppPurchase
        .queryProductDetails({skuPack});

    if (response.notFoundIDs.isNotEmpty) {
      _showError(currentContext!, "Product not found");
      return;
    }

    final ProductDetails productDetails = response.productDetails.first;
    final PurchaseParam purchaseParam = PurchaseParam(
      productDetails: productDetails,
    );

    MixpanelManager().track("Launch purchase flow", {
      "Pack name": packName,
      "Source": Consts.paymentSource,
    });

    await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
  }

  static Future<void> makeSubscription(String skuPack) async {
    if (!_isAvailable) return;

    final ProductDetailsResponse response = await _inAppPurchase
        .queryProductDetails({skuPack});

    if (response.notFoundIDs.isNotEmpty) {
      _showError(currentContext!, "Subscription not found");
      return;
    }

    final ProductDetails productDetails = response.productDetails.first;
    final PurchaseParam purchaseParam = PurchaseParam(
      productDetails: productDetails,
    );

    await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
  }

  static void _listenToPurchaseUpdated(
    List<PurchaseDetails> purchaseDetailsList,
  ) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        // Handle pending purchase
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        // Handle error
        _showError(currentContext!, "Purchase failed");
      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        // Handle successful purchase
        orderId = purchaseDetails.purchaseID ?? "";
        onPaymentSuccess("GOOGLE");

        if (purchaseDetails.pendingCompletePurchase) {
          _inAppPurchase.completePurchase(purchaseDetails);
        }
      }
    }
  }

  // Payment Success Handling
  static Future<void> onPaymentSuccess(String paymentMethod) async {
    try {
      MixpanelManager().track("Payment Success", {
        "Payment Method": paymentMethod,
        "Amount": amount.toString(),
        "Pack Name": packName,
        "Order ID": orderId,
      });

      // Update user tickets and subscription
      await _updateUserSubscription();

      // Save payment details to Firebase
      await _savePaymentToFirebase(paymentMethod);

      // Update UI and user status
      await _updateUserStatus();

      // Show success message
      if (currentContext != null && currentContext!.mounted) {
        _showSuccess(
          currentContext!,
          "Congratulations! You just bought $packName",
        );
      }
    } catch (e) {
      _showError(
        currentContext!,
        "Payment completed but failed to update account. Please contact support.",
      );
    }
  }

  // Event Handlers for Razorpay
  static void _handlePaymentSuccess(PaymentSuccessResponse response) {
    orderId = response.paymentId ?? "";
    onPaymentSuccess("RAZORPAY");
  }

  static void _handlePaymentError(PaymentFailureResponse response) {
    MixpanelManager().track("Razorpay payment error", {
      "Code": response.code.toString(),
      "Message": response.message ?? "",
    });
    _showError(currentContext!, "Payment failed: ${response.message}");
  }

  static void _handleExternalWallet(ExternalWalletResponse response) {
    MixpanelManager().track("External wallet selected", {
      "Wallet": response.walletName ?? "",
    });
  }

  // Helper Methods
  static String _getSKUFromPackName(String packName) {
    if (packName.contains("GOLD")) {
      return "gold_yearly";
    } else if (packName.contains("Premium")) {
      return "premium_monthly";
    } else if (packName.contains("Starter")) {
      return "starter_monthly";
    }
    return "basic_pack";
  }

  static Future<void> _updateUserSubscription() async {
    // Update user type based on pack
    if (packName.contains("GOLD")) {
      Consts.USER_TYPE = "GOLD_USER";
      SplashScreenState.isGoldUser = 2;
    } else if (packName.contains("Premium")) {
      Consts.USER_TYPE = "PREMIUM_USER";
    } else if (packName.contains("Starter")) {
      Consts.USER_TYPE = "STARTER_USER";
    }

    // Update tickets
    if (tickets == 9999) {
      // Gold pack
      SplashScreenState.ticketsLeft = 9999;
    } else {
      SplashScreenState.ticketsLeft += tickets;
    }

    // Save to SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('TICKETS_LEFT', SplashScreenState.ticketsLeft);
    await prefs.setString('USER_TYPE', Consts.USER_TYPE);
    await prefs.setInt('IS_GOLD_USER', SplashScreenState.isGoldUser);
  }

  static Future<void> _savePaymentToFirebase(String paymentMethod) async {
    try {
      final firestoreFunctions = FirestoreFunctions(currentContext);
      await firestoreFunctions.savePayment(
        packName,
        amount,
        orderId,
        0, // renew flag
        paymentMethod,
      );
    } catch (e) {
      print('Error saving payment to Firebase: $e');
    }
  }

  static Future<void> _updateUserStatus() async {
    try {
      final firestoreFunctions = FirestoreFunctions(currentContext);
      // The updateTickets method will handle the user status update
      await firestoreFunctions.updateTickets();
    } catch (e) {
      print('Error updating user status: $e');
    }
  }

  static Future<void> _setPendingPayment() async {
    final sp = await SharedPreferences.getInstance();
    await sp.setBool("PENDING_PYMT_PAYTM", true);

    if (!Consts.isPaymentRestoring) {
      final firestoreFunctions = FirestoreFunctions(currentContext);
      await firestoreFunctions.insertPendingPymt(
        paytmOrderId,
        packName,
        amount,
      );
    }
  }

  static void _showLoadingDialog() {
    if (currentContext != null && currentContext!.mounted) {
      showDialog(
        context: currentContext!,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text("Processing payment..."),
            ],
          ),
        ),
      );
    }
  }

  static void _showError(BuildContext? context, String message) {
    if (context != null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  static void _showSuccess(BuildContext? context, String message) {
    if (context != null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  static Future<void> getPendingPymtStatus(
    int ticketCount,
    String orderid,
  ) async {
    tickets = ticketCount;
    paytmOrderId = orderid;
    orderId = paytmOrderId;
    await getPaytmTxnStatus();
  }
}
