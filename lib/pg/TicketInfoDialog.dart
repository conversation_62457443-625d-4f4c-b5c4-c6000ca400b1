import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:quick_tatkal_v2/screens/QuickTatkalScreen.dart';
import 'package:quick_tatkal_v2/screens/PremiumScreen.dart';

import '../core/Consts.dart';
import '../screens/splash_screen.dart';

class TicketInfoDialog extends StatefulWidget {
  final int type;

  const TicketInfoDialog({super.key, required this.type});

  @override
  State<TicketInfoDialog> createState() => _TicketInfoDialogState();
}

class _TicketInfoDialogState extends State<TicketInfoDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;
  late TextEditingController _ticketBuyController;
  bool validData = false;
  int amount = 0;

  @override
  void initState() {
    super.initState();
    _ticketBuyController = TextEditingController();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 750),
      vsync: this,
    )..repeat();

    _rotationAnimation = Tween<double>(begin: 0, end: 1).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    _ticketBuyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF2A2A2A), Color(0xFF1A1A1A)],
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: const Text(
                "Info",
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFBBBBBB),
                ),
              ),
            ),

            // Content area with rotation animation
            Container(
              height: 150,
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  RotationTransition(
                    turns: _rotationAnimation,
                    child: const Icon(
                      Icons.airplane_ticket,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildTicketStatusInfo(),
                ],
              ),
            ),

            SizedBox(
              height: 50,
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Color(0xFF9934FF), Color(0xFF5533EA)],
                        ),
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(15),
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            QuickTatkalApp.closeActivity = false;
                            Consts.paymentSource = Consts.subPaymentSource;
                            Navigator.of(context).pop();
                            Get.to(() => const PremiumScreen());
                          },
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(15),
                          ),
                          child: const Center(
                            child: Text(
                              "Buy Tickets",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Container(width: 1, color: Colors.black),
                  Expanded(
                    child: Container(
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Color(0xFF9934FF), Color(0xFF5533EA)],
                        ),
                        borderRadius: BorderRadius.only(
                          bottomRight: Radius.circular(15),
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          borderRadius: const BorderRadius.only(
                            bottomRight: Radius.circular(15),
                          ),
                          child: const Center(
                            child: Text(
                              "Cancel",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTicketStatusInfo() {
    String text = "";
    Color textColor = Colors.white;
    String expiryText = "";

    if (widget.type == 0) {
      switch (Consts.USER_TYPE) {
        case "GOLD_USER":
          text = "You have active GOLD Pack subscription";
          textColor = const Color(0xFFD1A700);
          break;
        case "PREMIUM_USER":
          text = "You have active Premium Pack subscription";
          textColor = const Color(0xFFB61B72);
          break;
        case "STARTER_USER":
          text = "You have active Starter Pack subscription";
          textColor = const Color(0xFFA593FB);
          break;
        default:
          if (SplashScreenState.ticketsLeft > 0) {
            text = "You have ${SplashScreenState.ticketsLeft} tickets left";
            textColor = const Color(0xFFFFFFFF);
          } else {
            text = "You have no tickets left";
            textColor = const Color(0xFFFF0000);
          }
      }

      if (Consts.PACK_EXPIRY_DATE.isNotEmpty) {
        expiryText = "Pack valid till ${Consts.PACK_EXPIRY_DATE}";
      }
    } else if (widget.type == 1 || SplashScreenState.ticketsLeft == 0) {
      text = "You have no tickets left";
      textColor = const Color(0xFFFF0000);
    }

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Text(
            text,
            style: TextStyle(
              fontSize: 16,
              color: textColor,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        if (expiryText.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 5),
            child: Text(
              expiryText,
              style: const TextStyle(fontSize: 16, color: Color(0xFFAAAAAA)),
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );
  }
}
