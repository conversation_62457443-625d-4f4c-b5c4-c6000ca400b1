import 'package:flutter/material.dart';
import '../core/Consts.dart';

class BuyTicketsDialog extends StatefulWidget {
  const BuyTicketsDialog({super.key});

  @override
  State<BuyTicketsDialog> createState() => _BuyTicketsDialogState();

  // Static method to show dialog
  static void show(
    BuildContext context, {
    String? userType,
    String? packExpiryDate,
    int? ticketsLeft,
    int type = 0,
  }) {
    showDialog(
      context: context,
      builder: (context) => const BuyTicketsDialog(),
    );
  }
}

class _BuyTicketsDialogState extends State<BuyTicketsDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF2A2A2A), Color(0xFF1A1A1A)],
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: const Text(
                "Info",
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFBBBBBB),
                ),
              ),
            ),

            // Content area
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
              child: Column(
                children: [
                  Text(
                    "You have active GOLD Pack subscription",
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFD1A700),
                    ),
                  ),
                  if (Consts.PACK_EXPIRY_DATE.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 5),
                      child: Text(
                        "Pack valid till ${Consts.PACK_EXPIRY_DATE}",
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFFAAAAAA),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
            ),

            // Button
            Container(
              margin: const EdgeInsets.all(20),
              child: SizedBox(
                width: 200,
                height: 40,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFF9934FF), Color(0xFF5533EA)],
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      borderRadius: BorderRadius.circular(8),
                      child: const Center(
                        child: Text(
                          "OK",
                          style: TextStyle(color: Colors.white, fontSize: 18),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
