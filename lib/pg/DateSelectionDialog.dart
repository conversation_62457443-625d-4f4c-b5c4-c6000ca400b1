import 'package:flutter/material.dart';

class DateSelectionDialog extends StatefulWidget {
  final String trainDates; // Pipe-separated string like in Java
  final Function(String date, int index) onDateSelected;

  const DateSelectionDialog({
    super.key,
    required this.trainDates,
    required this.onDateSelected,
  });

  @override
  State<DateSelectionDialog> createState() => _DateSelectionDialogState();
}

class _DateSelectionDialogState extends State<DateSelectionDialog> {
  late List<String> dateList;

  @override
  void initState() {
    super.initState();
    // Split trainDates just like in Java: trainDates.split('|')
    dateList = widget.trainDates.split('|');
    // Remove empty strings if any
    dateList = dateList.where((date) => date.isNotEmpty).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF2A2A2A), Color(0xFF1A1A1A)],
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        width: double.maxFinite,
        height: 300,
        child: ListView.builder(
          itemCount: dateList.length,
          itemBuilder: (context, index) {
            return Container(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Colors.white.withOpacity(0.1),
                    width: 0.5,
                  ),
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    widget.onDateSelected(dateList[index], index);
                    Navigator.pop(context); // dismiss the dialog
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 12.0,
                    ),
                    child: Text(
                      dateList[index],
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
