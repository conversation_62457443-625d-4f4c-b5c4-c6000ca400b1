import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class RCInfoDialog extends StatefulWidget {
  const RCInfoDialog({super.key});

  @override
  State<RCInfoDialog> createState() => _RCInfoDialogState();
}

class _RCInfoDialogState extends State<RCInfoDialog> {
  void _launchURL() async {
    const url = 'https://quicktatkal.in/apk';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Could not launch URL')));
    }
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF2A2A2A), Color(0xFF1A1A1A)],
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: const Text(
                "Info",
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFBBBBBB),
                ),
              ),
            ),

            // Content area
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
              child: Text(
                "Rail connect app doesn't open if Quick Tatkal is installed. But you can install Quick Tatkal clone app to book tickets using Rail Connect",
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFD1A700),
                ),
              ),
            ),

            // Button
            Container(
              margin: const EdgeInsets.all(20),
              child: SizedBox(
                width: 200,
                height: 40,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFF9934FF), Color(0xFF5533EA)],
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: _launchURL,
                      borderRadius: BorderRadius.circular(8),
                      child: const Center(
                        child: Text(
                          "Get clone app",
                          style: TextStyle(color: Colors.white, fontSize: 18),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
