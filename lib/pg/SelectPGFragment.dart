import 'package:flutter/material.dart';
import '../core/Consts.dart';
import '../screens/splash_screen.dart';
import '../screens/QuickTatkalScreen.dart';
import 'PaymentManager.dart';

class SelectPGFragment extends StatefulWidget {
  final int amount;
  final int tickets;
  final String packName;

  const SelectPGFragment({
    super.key,
    required this.amount,
    required this.tickets,
    required this.packName,
  });

  @override
  State<SelectPGFragment> createState() => _SelectPGFragmentState();
}

class _SelectPGFragmentState extends State<SelectPGFragment> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF2A2A2A), Color(0xFF1A1A1A)],
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Select Payment Method',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // Razorpay Option
            GestureDetector(
              onTap: () {
                Navigator.pop(context);
                PaymentManager.initiateRazorpayPayment(
                  context,
                  widget.amount,
                  widget.tickets,
                  widget.packName,
                );
              },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(15),
                margin: const EdgeInsets.only(bottom: 15),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/razorpay_logo.png',
                      height: 30,
                      width: 30,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.credit_card,
                          color: Colors.blue,
                          size: 30,
                        );
                      },
                    ),
                    const SizedBox(width: 15),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Razorpay',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            'Credit/Debit Card, UPI, Net Banking',
                            style: TextStyle(color: Colors.grey, fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Paytm Option
            GestureDetector(
              onTap: () {
                Navigator.pop(context);
                PaymentManager.initiatePaytmPayment(
                  context,
                  widget.amount,
                  widget.tickets,
                  widget.packName,
                );
              },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(15),
                margin: const EdgeInsets.only(bottom: 15),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/paytm_logo.png',
                      height: 30,
                      width: 30,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.account_balance_wallet,
                          color: Colors.blue,
                          size: 30,
                        );
                      },
                    ),
                    const SizedBox(width: 15),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Paytm',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            'Paytm Wallet, UPI',
                            style: TextStyle(color: Colors.grey, fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Google Play Billing Option (if not paying with PG)
            if (!Consts.payWithPG)
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                  PaymentManager.initiateGooglePlayPayment(
                    context,
                    widget.tickets,
                    widget.packName,
                  );
                },
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.play_circle_filled,
                        color: Colors.green,
                        size: 30,
                      ),
                      const SizedBox(width: 15),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Google Play',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              'Google Play Store subscription/in-app purchases',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 15),

            // Cancel Button
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white70),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
